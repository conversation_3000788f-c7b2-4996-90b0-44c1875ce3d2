"""
Design Tokens per ExProject UI System
Ispirato a shadcn/ui con supporto per dark mode e white label
"""

# Palette colori base (shadcn/ui inspired)
DEFAULT_COLORS = {
    # Grigi neutri
    "background": "hsl(0 0% 100%)",
    "foreground": "hsl(222.2 84% 4.9%)",
    "card": "hsl(0 0% 100%)",
    "card-foreground": "hsl(222.2 84% 4.9%)",
    "popover": "hsl(0 0% 100%)",
    "popover-foreground": "hsl(222.2 84% 4.9%)",
    
    # Colori primari
    "primary": "hsl(221.2 83.2% 53.3%)",
    "primary-foreground": "hsl(210 40% 98%)",
    
    # Colori secondari
    "secondary": "hsl(210 40% 96%)",
    "secondary-foreground": "hsl(222.2 84% 4.9%)",
    
    # Colori muted
    "muted": "hsl(210 40% 96%)",
    "muted-foreground": "hsl(215.4 16.3% 46.9%)",
    
    # Colori accent
    "accent": "hsl(210 40% 96%)",
    "accent-foreground": "hsl(222.2 84% 4.9%)",
    
    # Colori destructive
    "destructive": "hsl(0 84.2% 60.2%)",
    "destructive-foreground": "hsl(210 40% 98%)",
    
    # Bordi e input
    "border": "hsl(214.3 31.8% 91.4%)",
    "input": "hsl(214.3 31.8% 91.4%)",
    "ring": "hsl(221.2 83.2% 53.3%)",
    
    # Colori di stato
    "success": "hsl(142.1 76.2% 36.3%)",
    "success-foreground": "hsl(355.7 100% 97.3%)",
    "warning": "hsl(32.1 94.6% 43.7%)",
    "warning-foreground": "hsl(355.7 100% 97.3%)",
    "info": "hsl(221.2 83.2% 53.3%)",
    "info-foreground": "hsl(210 40% 98%)",
}

# Dark mode colors
DARK_COLORS = {
    "background": "hsl(222.2 84% 4.9%)",
    "foreground": "hsl(210 40% 98%)",
    "card": "hsl(222.2 84% 4.9%)",
    "card-foreground": "hsl(210 40% 98%)",
    "popover": "hsl(222.2 84% 4.9%)",
    "popover-foreground": "hsl(210 40% 98%)",
    
    "primary": "hsl(217.2 91.2% 59.8%)",
    "primary-foreground": "hsl(222.2 84% 4.9%)",
    
    "secondary": "hsl(217.2 32.6% 17.5%)",
    "secondary-foreground": "hsl(210 40% 98%)",
    
    "muted": "hsl(217.2 32.6% 17.5%)",
    "muted-foreground": "hsl(215 20.2% 65.1%)",
    
    "accent": "hsl(217.2 32.6% 17.5%)",
    "accent-foreground": "hsl(210 40% 98%)",
    
    "destructive": "hsl(0 62.8% 30.6%)",
    "destructive-foreground": "hsl(210 40% 98%)",
    
    "border": "hsl(217.2 32.6% 17.5%)",
    "input": "hsl(217.2 32.6% 17.5%)",
    "ring": "hsl(224.3 76.3% 94.1%)",
    
    "success": "hsl(142.1 70.6% 45.3%)",
    "success-foreground": "hsl(144.9 80.4% 10%)",
    "warning": "hsl(32.1 94.6% 43.7%)",
    "warning-foreground": "hsl(20.5 90.2% 48.2%)",
    "info": "hsl(217.2 91.2% 59.8%)",
    "info-foreground": "hsl(222.2 84% 4.9%)",
}

# Tipografia
DEFAULT_TYPOGRAPHY = {
    "font-family": {
        "sans": ["Inter", "ui-sans-serif", "system-ui", "sans-serif"],
        "mono": ["JetBrains Mono", "ui-monospace", "monospace"],
    },
    "font-size": {
        "xs": "0.75rem",
        "sm": "0.875rem", 
        "base": "1rem",
        "lg": "1.125rem",
        "xl": "1.25rem",
        "2xl": "1.5rem",
        "3xl": "1.875rem",
        "4xl": "2.25rem",
        "5xl": "3rem",
    },
    "font-weight": {
        "normal": "400",
        "medium": "500",
        "semibold": "600",
        "bold": "700",
    },
    "line-height": {
        "tight": "1.25",
        "normal": "1.5",
        "relaxed": "1.75",
    },
}

# Spacing system
DEFAULT_SPACING = {
    "0": "0px",
    "1": "0.25rem",
    "2": "0.5rem", 
    "3": "0.75rem",
    "4": "1rem",
    "5": "1.25rem",
    "6": "1.5rem",
    "8": "2rem",
    "10": "2.5rem",
    "12": "3rem",
    "16": "4rem",
    "20": "5rem",
    "24": "6rem",
}

# Border radius
DEFAULT_BORDERS = {
    "radius": {
        "none": "0px",
        "sm": "0.125rem",
        "md": "0.375rem", 
        "lg": "0.5rem",
        "xl": "0.75rem",
        "2xl": "1rem",
        "full": "9999px",
    },
    "width": {
        "0": "0px",
        "1": "1px",
        "2": "2px",
        "4": "4px",
        "8": "8px",
    }
}

# Shadows
DEFAULT_SHADOWS = {
    "sm": "0 1px 2px 0 rgb(0 0 0 / 0.05)",
    "md": "0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",
    "lg": "0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",
    "xl": "0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)",
    "2xl": "0 25px 50px -12px rgb(0 0 0 / 0.25)",
    "inner": "inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",
    "none": "0 0 #0000",
}

# Animazioni e transizioni
DEFAULT_ANIMATIONS = {
    "duration": {
        "75": "75ms",
        "100": "100ms",
        "150": "150ms",
        "200": "200ms",
        "300": "300ms",
        "500": "500ms",
        "700": "700ms",
        "1000": "1000ms",
    },
    "timing": {
        "linear": "linear",
        "in": "cubic-bezier(0.4, 0, 1, 1)",
        "out": "cubic-bezier(0, 0, 0.2, 1)",
        "in-out": "cubic-bezier(0.4, 0, 0.2, 1)",
    }
}

# Configurazione default completa
DEFAULT_THEME_CONFIG = {
    "name": "ExProject Default",
    "slug": "exproject-default",
    "colors": DEFAULT_COLORS,
    "dark_colors": DARK_COLORS,
    "typography": DEFAULT_TYPOGRAPHY,
    "spacing": DEFAULT_SPACING,
    "borders": DEFAULT_BORDERS,
    "shadows": DEFAULT_SHADOWS,
    "animations": DEFAULT_ANIMATIONS,
}

# Temi predefiniti per diversi clienti
THEME_PRESETS = {
    "municipality": {
        "name": "Tema Comunale",
        "colors": {
            **DEFAULT_COLORS,
            "primary": "hsl(142.1 76.2% 36.3%)",  # Verde istituzionale
            "primary-foreground": "hsl(355.7 100% 97.3%)",
        }
    },
    "province": {
        "name": "Tema Provinciale", 
        "colors": {
            **DEFAULT_COLORS,
            "primary": "hsl(262.1 83.3% 57.8%)",  # Viola istituzionale
            "primary-foreground": "hsl(210 40% 98%)",
        }
    },
    "region": {
        "name": "Tema Regionale",
        "colors": {
            **DEFAULT_COLORS,
            "primary": "hsl(0 84.2% 60.2%)",  # Rosso istituzionale
            "primary-foreground": "hsl(210 40% 98%)",
        }
    }
}


def get_css_variables(theme_config):
    """Genera CSS custom properties dal tema"""
    css_vars = []
    
    # Colori
    for name, value in theme_config.get("colors", {}).items():
        css_vars.append(f"  --{name}: {value};")
    
    # Dark mode colors
    dark_colors = theme_config.get("dark_colors", {})
    if dark_colors:
        css_vars.append("\n@media (prefers-color-scheme: dark) {")
        css_vars.append("  :root {")
        for name, value in dark_colors.items():
            css_vars.append(f"    --{name}: {value};")
        css_vars.append("  }")
        css_vars.append("}")
    
    return "\n".join([":root {"] + css_vars + ["}"])


def get_tailwind_config(theme_config):
    """Genera configurazione Tailwind dal tema"""
    return {
        "theme": {
            "extend": {
                "colors": {
                    name.replace("-", ""): f"hsl(var(--{name}))"
                    for name in theme_config.get("colors", {}).keys()
                },
                "fontFamily": theme_config.get("typography", {}).get("font-family", {}),
                "fontSize": theme_config.get("typography", {}).get("font-size", {}),
                "spacing": theme_config.get("spacing", {}),
                "borderRadius": theme_config.get("borders", {}).get("radius", {}),
                "boxShadow": theme_config.get("shadows", {}),
            }
        }
    }
