"""
Servizi per la gestione del design system e configurazioni white label
"""
import json
import os
from django.conf import settings
from django.core.cache import cache
from django.template.loader import render_to_string
from .models import Theme, ComponentLibrary, LayoutConfiguration
from .design_tokens import DEFAULT_THEME_CONFIG, THEME_PRESETS, get_css_variables, get_tailwind_config


class ThemeService:
    """Servizio per la gestione dei temi"""
    
    CACHE_KEY_PREFIX = 'ui_theme_'
    CACHE_TIMEOUT = 3600  # 1 ora
    
    @classmethod
    def get_active_theme_config(cls):
        """Restituisce la configurazione del tema attivo"""
        cache_key = f"{cls.CACHE_KEY_PREFIX}active_config"
        config = cache.get(cache_key)
        
        if config is None:
            theme = Theme.get_active_theme()
            if theme:
                config = {
                    'name': theme.name,
                    'slug': theme.slug,
                    'colors': theme.colors,
                    'typography': theme.typography,
                    'spacing': theme.spacing,
                    'borders': theme.borders,
                    'shadows': theme.shadows,
                    'branding': {
                        'logo_url': theme.logo_url,
                        'favicon_url': theme.favicon_url,
                        'brand_name': theme.brand_name,
                        'brand_tagline': theme.brand_tagline,
                    },
                    'custom_css': theme.custom_css,
                    'component_overrides': theme.component_overrides,
                }
            else:
                config = DEFAULT_THEME_CONFIG
            
            cache.set(cache_key, config, cls.CACHE_TIMEOUT)
        
        return config
    
    @classmethod
    def generate_css(cls, theme_config=None):
        """Genera CSS custom properties dal tema"""
        if theme_config is None:
            theme_config = cls.get_active_theme_config()
        
        return get_css_variables(theme_config)
    
    @classmethod
    def generate_tailwind_config(cls, theme_config=None):
        """Genera configurazione Tailwind dal tema"""
        if theme_config is None:
            theme_config = cls.get_active_theme_config()
        
        return get_tailwind_config(theme_config)
    
    @classmethod
    def create_theme_from_preset(cls, preset_name, name=None, user=None):
        """Crea un tema da un preset predefinito"""
        if preset_name not in THEME_PRESETS:
            raise ValueError(f"Preset '{preset_name}' non trovato")
        
        preset = THEME_PRESETS[preset_name]
        theme_name = name or preset['name']
        
        theme = Theme.objects.create(
            name=theme_name,
            slug=theme_name.lower().replace(' ', '-'),
            colors=preset.get('colors', DEFAULT_THEME_CONFIG['colors']),
            typography=preset.get('typography', DEFAULT_THEME_CONFIG['typography']),
            spacing=preset.get('spacing', DEFAULT_THEME_CONFIG['spacing']),
            borders=preset.get('borders', DEFAULT_THEME_CONFIG['borders']),
            shadows=preset.get('shadows', DEFAULT_THEME_CONFIG['shadows']),
            created_by=user,
        )
        
        cls.clear_cache()
        return theme
    
    @classmethod
    def clear_cache(cls):
        """Pulisce la cache dei temi"""
        cache.delete_pattern(f"{cls.CACHE_KEY_PREFIX}*")


class ComponentService:
    """Servizio per la gestione dei componenti"""
    
    @classmethod
    def render_component(cls, component_slug, props=None, variant=None):
        """Renderizza un componente con le proprietà specificate"""
        try:
            component = ComponentLibrary.objects.get(slug=component_slug, is_active=True)
        except ComponentLibrary.DoesNotExist:
            return f"<!-- Componente '{component_slug}' non trovato -->"
        
        # Merge props con defaults
        context = {}
        if props:
            context.update(props)
        
        # Applica variante se specificata
        if variant and variant in component.variants:
            variant_props = component.variants[variant]
            context.update(variant_props)
        
        # Renderizza template
        try:
            return render_to_string(
                f'ui/components/{component_slug}.html',
                context
            )
        except Exception as e:
            return f"<!-- Errore rendering componente '{component_slug}': {str(e)} -->"
    
    @classmethod
    def get_component_variants(cls, component_slug):
        """Restituisce le varianti disponibili per un componente"""
        try:
            component = ComponentLibrary.objects.get(slug=component_slug, is_active=True)
            return component.variants
        except ComponentLibrary.DoesNotExist:
            return {}
    
    @classmethod
    def get_components_by_category(cls, category):
        """Restituisce tutti i componenti di una categoria"""
        return ComponentLibrary.objects.filter(
            category=category,
            is_active=True
        ).order_by('name')


class LayoutService:
    """Servizio per la gestione dei layout"""
    
    @classmethod
    def get_layout_config(cls, layout_type, layout_name=None):
        """Restituisce la configurazione di un layout"""
        query = LayoutConfiguration.objects.filter(
            layout_type=layout_type,
            is_active=True
        )
        
        if layout_name:
            query = query.filter(slug=layout_name)
        
        return query.first()
    
    @classmethod
    def render_layout(cls, layout_type, context=None, layout_name=None):
        """Renderizza un layout con il contesto specificato"""
        layout_config = cls.get_layout_config(layout_type, layout_name)
        
        if not layout_config:
            return f"<!-- Layout '{layout_type}' non trovato -->"
        
        template_context = {
            'layout_config': layout_config,
            'sidebar_enabled': layout_config.sidebar_enabled,
            'sidebar_position': layout_config.sidebar_position,
            'header_style': layout_config.header_style,
            'footer_enabled': layout_config.footer_enabled,
            'container_max_width': layout_config.container_max_width,
            'content_padding': layout_config.content_padding,
            'grid_columns': layout_config.grid_columns,
            'components': layout_config.components,
        }
        
        if context:
            template_context.update(context)
        
        try:
            return render_to_string(
                f'ui/layouts/{layout_type}.html',
                template_context
            )
        except Exception as e:
            return f"<!-- Errore rendering layout '{layout_type}': {str(e)} -->"


class WhiteLabelService:
    """Servizio per la configurazione white label"""
    
    @classmethod
    def get_client_config(cls, client_slug=None):
        """Restituisce la configurazione per un cliente specifico"""
        # Se non specificato, usa il tema attivo
        if not client_slug:
            return ThemeService.get_active_theme_config()
        
        # Cerca tema specifico per cliente
        try:
            theme = Theme.objects.get(slug=client_slug, is_active=True)
            return {
                'name': theme.name,
                'slug': theme.slug,
                'colors': theme.colors,
                'typography': theme.typography,
                'spacing': theme.spacing,
                'borders': theme.borders,
                'shadows': theme.shadows,
                'branding': {
                    'logo_url': theme.logo_url,
                    'favicon_url': theme.favicon_url,
                    'brand_name': theme.brand_name,
                    'brand_tagline': theme.brand_tagline,
                },
                'custom_css': theme.custom_css,
                'component_overrides': theme.component_overrides,
            }
        except Theme.DoesNotExist:
            return ThemeService.get_active_theme_config()
    
    @classmethod
    def generate_client_css(cls, client_slug=None):
        """Genera CSS personalizzato per un cliente"""
        config = cls.get_client_config(client_slug)
        css = ThemeService.generate_css(config)
        
        # Aggiungi CSS personalizzato se presente
        if config.get('custom_css'):
            css += f"\n\n/* CSS Personalizzato */\n{config['custom_css']}"
        
        return css
    
    @classmethod
    def export_theme_config(cls, theme_slug):
        """Esporta la configurazione di un tema in formato JSON"""
        try:
            theme = Theme.objects.get(slug=theme_slug)
            config = {
                'name': theme.name,
                'slug': theme.slug,
                'colors': theme.colors,
                'typography': theme.typography,
                'spacing': theme.spacing,
                'borders': theme.borders,
                'shadows': theme.shadows,
                'branding': {
                    'logo_url': theme.logo_url,
                    'favicon_url': theme.favicon_url,
                    'brand_name': theme.brand_name,
                    'brand_tagline': theme.brand_tagline,
                },
                'custom_css': theme.custom_css,
                'component_overrides': theme.component_overrides,
                'version': '1.0.0',
                'exported_at': theme.updated_at.isoformat(),
            }
            return json.dumps(config, indent=2, ensure_ascii=False)
        except Theme.DoesNotExist:
            return None
    
    @classmethod
    def import_theme_config(cls, config_json, user=None):
        """Importa una configurazione tema da JSON"""
        try:
            config = json.loads(config_json)
            
            theme = Theme.objects.create(
                name=config['name'],
                slug=config['slug'],
                colors=config.get('colors', {}),
                typography=config.get('typography', {}),
                spacing=config.get('spacing', {}),
                borders=config.get('borders', {}),
                shadows=config.get('shadows', {}),
                logo_url=config.get('branding', {}).get('logo_url'),
                favicon_url=config.get('branding', {}).get('favicon_url'),
                brand_name=config.get('branding', {}).get('brand_name'),
                brand_tagline=config.get('branding', {}).get('brand_tagline'),
                custom_css=config.get('custom_css', ''),
                component_overrides=config.get('component_overrides', {}),
                created_by=user,
            )
            
            ThemeService.clear_cache()
            return theme
        except (json.JSONDecodeError, KeyError) as e:
            raise ValueError(f"Configurazione non valida: {str(e)}")


# Utility functions per template tags
def get_component_classes(component_type, variant='default', size='default'):
    """Restituisce le classi CSS per un componente"""
    # Implementazione delle classi base per ogni componente
    base_classes = {
        'button': 'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
        'input': 'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
        'card': 'rounded-lg border bg-card text-card-foreground shadow-sm',
        'badge': 'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
    }
    
    return base_classes.get(component_type, '')
