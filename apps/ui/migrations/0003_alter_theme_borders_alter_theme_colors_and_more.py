# Generated by Django 5.2.1 on 2025-06-20 21:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('ui', '0002_alter_theme_component_overrides'),
    ]

    operations = [
        migrations.AlterField(
            model_name='theme',
            name='borders',
            field=models.JSONField(blank=True, default=dict, help_text='Configurazione bordi e radius'),
        ),
        migrations.AlterField(
            model_name='theme',
            name='colors',
            field=models.JSONField(blank=True, default=dict, help_text='Palette colori del tema'),
        ),
        migrations.AlterField(
            model_name='theme',
            name='shadows',
            field=models.JSONField(blank=True, default=dict, help_text='Sistema di ombre'),
        ),
        migrations.AlterField(
            model_name='theme',
            name='spacing',
            field=models.JSONField(blank=True, default=dict, help_text='Sistema di spacing'),
        ),
        migrations.AlterField(
            model_name='theme',
            name='typography',
            field=models.JSONField(blank=True, default=dict, help_text='Configurazione tipografia'),
        ),
    ]
