from django.db import models
from django.contrib.auth.models import User
import json


class Theme(models.Model):
    """Configurazione tema per white label"""
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=100, unique=True)
    is_active = models.BooleanField(default=False)
    is_default = models.BooleanField(default=False)
    
    # Design tokens
    colors = models.JSONField(default=dict, blank=True, help_text="Palette colori del tema")
    typography = models.JSONField(default=dict, blank=True, help_text="Configurazione tipografia")
    spacing = models.JSONField(default=dict, blank=True, help_text="Sistema di spacing")
    borders = models.JSONField(default=dict, blank=True, help_text="Configurazione bordi e radius")
    shadows = models.JSONField(default=dict, blank=True, help_text="Sistema di ombre")
    
    # Branding
    logo_url = models.URLField(blank=True, null=True)
    favicon_url = models.URLField(blank=True, null=True)
    brand_name = models.CharField(max_length=100, blank=True)
    brand_tagline = models.CharField(max_length=200, blank=True)
    
    # Configurazioni avanzate
    custom_css = models.TextField(blank=True, help_text="CSS personalizzato")
    component_overrides = models.JSONField(default=dict, blank=True, help_text="Override componenti")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    
    class Meta:
        verbose_name = "Tema"
        verbose_name_plural = "Temi"
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        if self.is_default:
            # Assicura che ci sia solo un tema default
            Theme.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)
    
    @classmethod
    def get_active_theme(cls):
        """Restituisce il tema attivo o quello di default"""
        return cls.objects.filter(is_active=True).first() or cls.objects.filter(is_default=True).first()


class ComponentLibrary(models.Model):
    """Libreria di componenti UI riutilizzabili"""
    name = models.CharField(max_length=100)
    slug = models.SlugField(max_length=100, unique=True)
    category = models.CharField(max_length=50, choices=[
        ('atoms', 'Atoms'),
        ('molecules', 'Molecules'), 
        ('organisms', 'Organisms'),
        ('templates', 'Templates'),
    ])
    
    # Template del componente
    template_content = models.TextField(help_text="Template HTML del componente")
    css_classes = models.TextField(blank=True, help_text="Classi CSS specifiche")
    javascript = models.TextField(blank=True, help_text="JavaScript del componente")
    
    # Configurazione
    props_schema = models.JSONField(default=dict, help_text="Schema delle proprietà")
    variants = models.JSONField(default=dict, help_text="Varianti del componente")
    examples = models.JSONField(default=list, help_text="Esempi di utilizzo")
    
    # Metadati
    description = models.TextField(blank=True)
    documentation = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    version = models.CharField(max_length=20, default="1.0.0")
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    
    class Meta:
        verbose_name = "Componente"
        verbose_name_plural = "Componenti"
        ordering = ['category', 'name']
    
    def __str__(self):
        return f"{self.get_category_display()} - {self.name}"


class LayoutConfiguration(models.Model):
    """Configurazioni layout per diverse sezioni"""
    name = models.CharField(max_length=100)
    slug = models.SlugField(max_length=100, unique=True)
    layout_type = models.CharField(max_length=50, choices=[
        ('dashboard', 'Dashboard'),
        ('list', 'Lista/Tabella'),
        ('detail', 'Dettaglio'),
        ('form', 'Form'),
        ('report', 'Report'),
        ('public', 'Pagina Pubblica'),
    ])
    
    # Configurazione layout
    sidebar_enabled = models.BooleanField(default=True)
    sidebar_position = models.CharField(max_length=10, choices=[('left', 'Sinistra'), ('right', 'Destra')], default='left')
    header_style = models.CharField(max_length=20, choices=[('fixed', 'Fisso'), ('static', 'Statico')], default='fixed')
    footer_enabled = models.BooleanField(default=True)
    
    # Grid e spacing
    container_max_width = models.CharField(max_length=20, default='7xl')
    content_padding = models.CharField(max_length=20, default='6')
    grid_columns = models.JSONField(default=dict, help_text="Configurazione colonne responsive")
    
    # Componenti inclusi
    components = models.JSONField(default=list, help_text="Lista componenti da includere")
    
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = "Layout"
        verbose_name_plural = "Layout"
        ordering = ['layout_type', 'name']
    
    def __str__(self):
        return f"{self.get_layout_type_display()} - {self.name}"
