from django import template
from django.utils.safestring import mark_safe
from django.template.loader import render_to_string
import json

register = template.Library()


@register.inclusion_tag('ui/components/button.html')
def ui_button(text="Button", variant="default", size="default", **kwargs):
    """
    Componente Button con varianti shadcn/ui style
    
    Varianti: default, destructive, outline, secondary, ghost, link
    Sizes: default, sm, lg, icon
    """
    return {
        'text': text,
        'variant': variant,
        'size': size,
        'attrs': kwargs
    }


@register.inclusion_tag('ui/components/card.html')
def ui_card(title=None, description=None, **kwargs):
    """
    Componente Card con header, content e footer
    """
    return {
        'title': title,
        'description': description,
        'attrs': kwargs
    }


@register.inclusion_tag('ui/components/badge.html')
def ui_badge(text, variant="default", **kwargs):
    """
    Componente Badge per status e labels
    
    Varianti: default, secondary, destructive, outline
    """
    return {
        'text': text,
        'variant': variant,
        'attrs': kwargs
    }


@register.inclusion_tag('ui/components/input.html')
def ui_input(name, label=None, placeholder=None, type="text", **kwargs):
    """
    Componente Input con label e validazione
    """
    return {
        'name': name,
        'label': label,
        'placeholder': placeholder,
        'type': type,
        'attrs': kwargs
    }


@register.inclusion_tag('ui/components/select.html')
def ui_select(name, options, label=None, **kwargs):
    """
    Componente Select dropdown
    """
    return {
        'name': name,
        'options': options,
        'label': label,
        'attrs': kwargs
    }


@register.inclusion_tag('ui/components/table.html')
def ui_table(headers, rows, **kwargs):
    """
    Componente Table responsive
    """
    return {
        'headers': headers,
        'rows': rows,
        'attrs': kwargs
    }


@register.inclusion_tag('ui/components/alert.html')
def ui_alert(message, variant="default", title=None, **kwargs):
    """
    Componente Alert per messaggi
    
    Varianti: default, destructive, warning, success
    """
    return {
        'message': message,
        'variant': variant,
        'title': title,
        'attrs': kwargs
    }


@register.inclusion_tag('ui/components/dialog.html')
def ui_dialog(title, content, trigger_text="Open", **kwargs):
    """
    Componente Dialog/Modal
    """
    return {
        'title': title,
        'content': content,
        'trigger_text': trigger_text,
        'attrs': kwargs
    }


@register.inclusion_tag('ui/components/tabs.html')
def ui_tabs(tabs, **kwargs):
    """
    Componente Tabs
    
    tabs: [{'id': 'tab1', 'label': 'Tab 1', 'content': 'Content 1'}]
    """
    return {
        'tabs': tabs,
        'attrs': kwargs
    }


@register.inclusion_tag('ui/components/progress.html')
def ui_progress(value, max_value=100, **kwargs):
    """
    Componente Progress bar
    """
    percentage = (value / max_value) * 100 if max_value > 0 else 0
    return {
        'value': value,
        'max_value': max_value,
        'percentage': percentage,
        'attrs': kwargs
    }


@register.inclusion_tag('ui/components/skeleton.html')
def ui_skeleton(width="100%", height="20px", **kwargs):
    """
    Componente Skeleton per loading states
    """
    return {
        'width': width,
        'height': height,
        'attrs': kwargs
    }


@register.inclusion_tag('ui/components/avatar.html')
def ui_avatar(src=None, alt="Avatar", fallback=None, size="default", **kwargs):
    """
    Componente Avatar
    
    Sizes: sm, default, lg
    """
    return {
        'src': src,
        'alt': alt,
        'fallback': fallback,
        'size': size,
        'attrs': kwargs
    }


@register.inclusion_tag('ui/components/separator.html')
def ui_separator(orientation="horizontal", **kwargs):
    """
    Componente Separator/Divider
    """
    return {
        'orientation': orientation,
        'attrs': kwargs
    }


@register.inclusion_tag('ui/layouts/dashboard.html')
def ui_dashboard_layout(title, **kwargs):
    """
    Layout Dashboard con sidebar e header
    """
    return {
        'title': title,
        'attrs': kwargs
    }


@register.inclusion_tag('ui/layouts/form.html')
def ui_form_layout(title, **kwargs):
    """
    Layout per form con validazione
    """
    return {
        'title': title,
        'attrs': kwargs
    }


@register.simple_tag
def ui_icon(name, size="4", **kwargs):
    """
    Componente Icon usando Heroicons
    """
    # Mapping degli icon names più comuni
    icons = {
        'plus': 'M12 4.5v15m7.5-7.5h-15',
        'edit': 'm16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10',
        'delete': 'm14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0',
        'view': 'M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z',
        'search': 'm21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z',
        'check': 'm4.5 12.75 6 6 9-13.5',
        'x': 'M6 18 18 6M6 6l12 12',
        'chevron-down': 'm19.5 8.25-7.5 7.5-7.5-7.5',
        'chevron-up': 'm4.5 15.75 7.5-7.5 7.5 7.5',
        'chevron-left': 'M15.75 19.5 8.25 12l7.5-7.5',
        'chevron-right': 'm8.25 4.5 7.5 7.5-7.5 7.5',
    }
    
    path = icons.get(name, icons['plus'])  # Default to plus icon
    
    classes = f"h-{size} w-{size}"
    if 'class' in kwargs:
        classes += f" {kwargs['class']}"
    
    svg = f'''
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="{classes}">
        <path stroke-linecap="round" stroke-linejoin="round" d="{path}" />
    </svg>
    '''
    
    return mark_safe(svg)


@register.simple_tag
def ui_theme_css():
    """
    Genera CSS custom properties per il tema attivo
    """
    from apps.ui.models import Theme
    from apps.ui.design_tokens import get_css_variables, DEFAULT_THEME_CONFIG
    
    theme = Theme.get_active_theme()
    if theme:
        theme_config = {
            "colors": theme.colors,
            "dark_colors": theme.colors.get("dark", {}),
            "typography": theme.typography,
            "spacing": theme.spacing,
            "borders": theme.borders,
            "shadows": theme.shadows,
        }
    else:
        theme_config = DEFAULT_THEME_CONFIG
    
    css = get_css_variables(theme_config)
    return mark_safe(f"<style>{css}</style>")


@register.filter
def ui_variant_classes(component_type, variant):
    """
    Restituisce le classi CSS per una variante di componente
    """
    variant_map = {
        'button': {
            'default': 'bg-primary text-primary-foreground hover:bg-primary/90',
            'destructive': 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
            'outline': 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
            'secondary': 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
            'ghost': 'hover:bg-accent hover:text-accent-foreground',
            'link': 'text-primary underline-offset-4 hover:underline',
        },
        'badge': {
            'default': 'bg-primary text-primary-foreground hover:bg-primary/80',
            'secondary': 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
            'destructive': 'bg-destructive text-destructive-foreground hover:bg-destructive/80',
            'outline': 'text-foreground border border-input',
        },
        'alert': {
            'default': 'bg-background text-foreground border',
            'destructive': 'border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive',
            'warning': 'border-warning/50 text-warning dark:border-warning [&>svg]:text-warning',
            'success': 'border-success/50 text-success dark:border-success [&>svg]:text-success',
        }
    }
    
    return variant_map.get(component_type, {}).get(variant, '')
