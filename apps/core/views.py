from django.shortcuts import render, redirect
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db import models
from django.http import HttpResponse
import logging
from django.utils import timezone
import random

# Configurazione del logger
logger = logging.getLogger(__name__)

@login_required
def dashboard(request):
    """Vista principale della dashboard"""
    try:
        # Recuperiamo progetti recenti
        from .models import Project, Activity
        
        # Recuperiamo progetti (con gestione eccezioni)
        try:
            recent_projects = Project.objects.all().order_by('-updated_at')[:5]
        except Exception as e:
            logger.error(f"Errore nel recupero dei progetti: {str(e)}")
            recent_projects = []
        
        # Recuperiamo attività recenti (con gestione eccezioni)
        try:
            recent_activities = Activity.objects.all().order_by('-created_at')[:10]
            
            # Se non ci sono attività, creiamo alcuni esempi
            if not recent_activities.exists():
                _create_sample_activities(request.user)
                recent_activities = Activity.objects.all().order_by('-created_at')[:10]
        except Exception as e:
            logger.error(f"Errore nel recupero delle attività: {str(e)}")
            recent_activities = []
        
        # Registriamo un'attività di visualizzazione dashboard
        try:
            Activity.objects.create(
                user=request.user,
                activity_type='view',
                description=f"Visualizzazione dashboard",
                object_name='Dashboard'
            )
        except Exception as e:
            logger.error(f"Errore nella creazione dell'attività: {str(e)}")
        
        return render(request, 'core/dashboard.html', {
            'recent_projects': recent_projects,
            'recent_activities': recent_activities
        })
    except Exception as e:
        logger.error(f"Errore generale nella dashboard: {str(e)}")
        messages.error(request, "Si è verificato un errore nel caricamento della dashboard")
        return render(request, 'core/dashboard.html', {
            'recent_projects': [],
            'recent_activities': []
        })

def _create_sample_activities(user):
    """Crea alcune attività di esempio"""
    from .models import Activity
    
    sample_descriptions = [
        "Login al sistema",
        "Visualizzazione progetto 'Raddoppio linea ferroviaria'",
        "Creazione nuovo documento per particella 25/B",
        "Modifica stato particella 12/A",
        "Calcolo indennità per particella 18/C",
        "Download documento decreto di esproprio",
        "Visualizzazione mappa GIS",
        "Consultazione dati catastali"
    ]
    
    sample_types = ['login', 'view', 'create', 'update', 'other', 'download']
    
    # Crea 8 attività di esempio con date distribuite negli ultimi 3 giorni
    for i, desc in enumerate(sample_descriptions):
        # Crea data casuale negli ultimi 3 giorni
        days_ago = random.randint(0, 2)
        hours_ago = random.randint(0, 23)
        minutes_ago = random.randint(0, 59)
        
        activity_time = timezone.now() - timezone.timedelta(
            days=days_ago, 
            hours=hours_ago,
            minutes=minutes_ago
        )
        
        Activity.objects.create(
            user=user,
            activity_type=random.choice(sample_types),
            description=desc,
            object_name=f"Esempio {i+1}",
            created_at=activity_time
        )

@login_required
def activities(request):
    """Vista per recuperare le attività recenti (utilizzata con HTMX)"""
    try:
        from .models import Activity
        
        # Recuperiamo le attività recenti (globali o filtrate per utente)
        recent_activities = Activity.objects.all().order_by('-created_at')[:10]
        
        # Se non ci sono attività, mostriamo un messaggio vuoto
        if not recent_activities.exists():
            return HttpResponse(
                '<div class="text-center py-8">'
                '<svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-400 mx-auto mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">'
                '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />'
                '</svg>'
                '<p class="text-gray-500">Nessuna attività recente</p>'
                '</div>'
            )
        
        return render(request, 'core/activities.html', {
            'recent_activities': recent_activities
        })
    except Exception as e:
        logger.error(f"Errore nella vista activities: {str(e)}")
        return HttpResponse(
            '<div class="text-center py-8">'
            '<p class="text-red-500">Errore nel caricamento delle attività</p>'
            '</div>'
        )

def login_view(request):
    if request.method == 'POST':
        try:
            username = request.POST.get('username')
            password = request.POST.get('password')
            
            if not username or not password:
                messages.error(request, 'Username e password sono obbligatori')
                return render(request, 'core/login.html')
            
            user = authenticate(request, username=username, password=password)
            if user:
                if user.is_active:
                    login(request, user)
                    
                    # Registriamo l'attività di login
                    try:
                        from .models import Activity
                        Activity.objects.create(
                            user=user,
                            activity_type='login',
                            description=f"Login effettuato da {user.username}",
                            object_name=user.username
                        )
                    except Exception as e:
                        logger.error(f"Errore nella registrazione attività login: {str(e)}")
                    
                    return redirect('core:dashboard')
                else:
                    messages.error(request, 'Account disattivato')
            else:
                messages.error(request, 'Username o password non corretti')
                
        except Exception as e:
            logger.error(f"Errore durante il login: {str(e)}")
            messages.error(request, 'Si è verificato un errore durante il login. Riprova.')
    
    return render(request, 'core/login.html')

def logout_view(request):
    # Registriamo l'attività di logout
    if request.user.is_authenticated:
        from .models import Activity
        Activity.objects.create(
            user=request.user,
            activity_type='logout',
            description=f"Logout effettuato da {request.user.username}",
            object_name=request.user.username
        )
    
    logout(request)
    return redirect('public:home')

@login_required
def search(request):
    query = request.GET.get('q', '').strip()
    results = {
        'projects': [],
        'parcels': [],
        'documents': []
    }
    
    if query:
        from .models import Project, Parcel, Document
        
        # Ricerca progetti
        results['projects'] = Project.objects.filter(
            models.Q(name__icontains=query) |
            models.Q(description__icontains=query)
        )[:5]
        
        # Ricerca particelle
        results['parcels'] = Parcel.objects.filter(
            models.Q(cadastral_id__icontains=query) |
            models.Q(project__name__icontains=query)
        ).select_related('project')[:5]
        
        # Ricerca documenti
        results['documents'] = Document.objects.filter(
            models.Q(parcel__cadastral_id__icontains=query) |
            models.Q(template__name__icontains=query) |
            models.Q(content__icontains=query)
        ).select_related('parcel', 'template')[:5]
    
    return render(request, 'core/search.html', {
        'query': query,
        'results': results
    })

@login_required
def notifications(request):
    """View per ottenere le notifiche dell'utente"""
    notifications = request.user.notifications.filter(is_read=False).order_by('-created_at')[:5]
    
    if not notifications:
        return HttpResponse('<div class="p-4 text-center text-gray-500">Nessuna notifica</div>')
    
    return render(request, 'core/notifications.html', {
        'notifications': notifications
    })

@login_required
def mark_notification_read(request, notification_id):
    """View per segnare una notifica come letta"""
    try:
        notification = request.user.notifications.get(id=notification_id)
        notification.is_read = True
        notification.save()
        return HttpResponse(status=204)
    except Notification.DoesNotExist:
        return HttpResponse(status=404)

@login_required
def profile(request):
    """View per la gestione del profilo utente"""
    if request.method == 'POST':
        # Gestione aggiornamento profilo
        user = request.user
        user.first_name = request.POST.get('first_name', user.first_name)
        user.last_name = request.POST.get('last_name', user.last_name)
        user.email = request.POST.get('email', user.email)
        
        # Gestione cambio password
        current_password = request.POST.get('current_password')
        new_password = request.POST.get('new_password')
        if current_password and new_password:
            if user.check_password(current_password):
                user.set_password(new_password)
                messages.success(request, 'Password aggiornata con successo')
            else:
                messages.error(request, 'Password attuale non corretta')
                return redirect('core:profile')
        
        user.save()
        messages.success(request, 'Profilo aggiornato con successo')
        return redirect('core:profile')
    
    return render(request, 'core/profile.html')

@login_required
def settings(request):
    """View per le impostazioni dell'utente"""
    if request.method == 'POST':
        # Gestione preferenze utente
        user = request.user
        # Qui possiamo aggiungere la gestione delle preferenze utente
        # quando implementeremo il modello UserPreferences
        messages.success(request, 'Impostazioni aggiornate con successo')
        return redirect('core:settings')
    
    return render(request, 'core/settings.html')

def design_comparison(request):
    """View per confrontare il vecchio e nuovo design system"""
    return render(request, 'core/dashboard_comparison.html')

@login_required
def dashboard_modern(request):
    """Dashboard moderna con il nuovo design system"""
    from .models import Project, Activity

    # Statistiche
    stats = {
        'active_projects': Project.objects.filter(status='active').count(),
        'pending_workflows': 15,  # Placeholder
        'documents_generated': 42,  # Placeholder
        'total_value': 2450000,  # Placeholder
    }

    # Progetti recenti
    recent_projects = Project.objects.all().order_by('-created_at')[:5]

    # Attività recenti
    try:
        recent_activities = Activity.objects.all().order_by('-created_at')[:5]
        if not recent_activities.exists():
            _create_sample_activities(request.user)
            recent_activities = Activity.objects.all().order_by('-created_at')[:5]
    except Exception as e:
        logger.error(f"Errore nel recupero delle attività: {str(e)}")
        recent_activities = []

    # Prepara dati per la tabella
    table_headers = ['Nome', 'Stato', 'Data', 'Azioni']
    table_rows = []
    for project in recent_projects:
        table_rows.append([
            project.name,
            project.get_status_display(),
            project.created_at.strftime('%d/%m/%Y'),
            f'<a href="/projects/{project.id}/" class="text-primary hover:underline">Visualizza</a>'
        ])

    return render(request, 'ui/layouts/dashboard_modern.html', {
        'stats': stats,
        'recent_projects': recent_projects,
        'recent_activities': recent_activities,
        'table_headers': table_headers,
        'table_rows': table_rows,
    })
