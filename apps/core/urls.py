from django.urls import path
from . import views

app_name = 'core'

urlpatterns = [
    path('', views.dashboard, name='dashboard'),
    path('login/', views.login_view, name='login'),
    path('logout/', views.logout_view, name='logout'),
    path('search/', views.search, name='search'),
    path('notifications/', views.notifications, name='notifications'),
    path('notifications/<int:notification_id>/read/', views.mark_notification_read, name='mark_notification_read'),
    path('profile/', views.profile, name='profile'),
    path('settings/', views.settings, name='settings'),
    path('activities/', views.activities, name='activities'),
    path('design-comparison/', views.design_comparison, name='design_comparison'),
    path('dashboard-modern/', views.dashboard_modern, name='dashboard_modern'),
]