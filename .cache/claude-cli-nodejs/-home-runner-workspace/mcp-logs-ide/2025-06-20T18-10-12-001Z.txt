[{"debug": "Calling MCP tool: getDiagnostics", "timestamp": "2025-06-20T18:11:29.217Z", "sessionId": "e2ef418a-7f5f-494c-a18e-4bfbabd9a47e", "cwd": "/home/<USER>/workspace"}, {"debug": "Tool call succeeded: {\"content\":[{\"type\":\"text\",\"text\":\"[\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-server-ready/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/extension-editing/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/terminal-suggest/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/configuration-editing/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": [\\n      {\\n        \\\"message\\\": \\\"Using '*' activation is usually a bad idea as it impacts performance.\\\",\\n        \\\"severity\\\": \\\"Information\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 0,\\n            \\\"character\\\": 207\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 0,\\n            \\\"character\\\": 210\\n          }\\n        },\\n        \\\"code\\\": \\\"[object Object]\\\"\\n      }\\n    ]\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": [\\n      {\\n        \\\"message\\\": \\\"Using '*' activation is usually a bad idea as it impacts performance.\\\",\\n        \\\"severity\\\": \\\"Information\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 0,\\n            \\\"character\\\": 316\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 0,\\n            \\\"character\\\": 319\\n          }\\n        },\\n        \\\"code\\\": \\\"[object Object]\\\"\\n      }\\n    ]\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/server/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/php-language-features/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/emmet/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-auto-launch/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/tunnel-forwarding/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/merge-conflict/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json\\\",\\n    \\\"diagnostics\\\": [\\n      {\\n        \\\"message\\\": \\\"Using '*' activation is usually a bad idea as it impacts performance.\\\",\\n        \\\"severity\\\": \\\"Information\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 0,\\n            \\\"character\\\": 316\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 0,\\n            \\\"character\\\": 319\\n          }\\n        },\\n        \\\"code\\\": \\\"[object Object]\\\"\\n      }\\n    ]\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-server-ready/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/extension-editing/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json\\\",\\n    \\\"diagnostics\\\": [\\n      {\\n        \\\"message\\\": \\\"Using '*' activation is usually a bad idea as it impacts performance.\\\",\\n        \\\"severity\\\": \\\"Information\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 0,\\n            \\\"character\\\": 207\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 0,\\n            \\\"character\\\": 210\\n          }\\n        },\\n        \\\"code\\\": \\\"[object Object]\\\"\\n      }\\n    ]\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": [\\n      {\\n        \\\"message\\\": \\\"Using '*' activation is usually a bad idea as it impacts performance.\\\",\\n        \\\"severity\\\": \\\"Information\\\",\\n        \\\"range\\\": {\\n          \\\"start\\\": {\\n            \\\"line\\\": 0,\\n            \\\"character\\\": 961\\n          },\\n          \\\"end\\\": {\\n            \\\"line\\\": 0,\\n            \\\"character\\\": 964\\n          }\\n        },\\n        \\\"code\\\": \\\"[object Object]\\\"\\n      }\\n    ]\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/server/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/gulp/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  },\\n  {\\n    \\\"uri\\\": \\\"file:///home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/package.json\\\",\\n    \\\"diagnostics\\\": []\\n  }\\n]\"}]}", "timestamp": "2025-06-20T18:11:29.232Z", "sessionId": "e2ef418a-7f5f-494c-a18e-4bfbabd9a47e", "cwd": "/home/<USER>/workspace"}]