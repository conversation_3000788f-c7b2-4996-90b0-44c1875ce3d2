{"runs": [{"id": "module:python-3.11/runner:python", "name": "Python 3.11", "fileParam": true, "language": "python3", "fileTypeAttrs": {}, "displayVersion": "3.11.10", "run": {"command": {"args": ["sh", "-c", "/nix/store/rbg00q7j8bflgr0vqq1557m45lzljfav-python3-wrapper/bin/python3 $file"]}}, "defaultEntrypoints": ["main.py", "app.py", "run.py"]}, {"id": "module:nodejs-20/runner:nodeJS", "name": "Node.js", "fileParam": true, "language": "javascript", "fileTypeAttrs": {}, "displayVersion": "20.18.1", "run": {"command": {"args": ["sh", "-c", "/nix/store/rrz8cqhldyl17bbs60g7d8vbaadkxc40-nodejs-20.18.1-wrapped/bin/node $file"]}}, "defaultEntrypoints": ["index.js", "main.js"]}], "debuggers": [{"id": "module:python-3.11/debugger:dapPython", "name": "debugpy", "fileParam": true, "language": "python3", "fileTypeAttrs": {}, "displayVersion": "1.8.9"}, {"id": "module:nodejs-20/debugger:nodeDAP", "name": "Node DAP", "fileParam": true, "language": "javascript", "fileTypeAttrs": {}}], "languageServers": [{"id": "module:nodejs-20/languageServer:typescript-language-server", "name": "TypeScript Language Server", "language": "javascript", "fileTypeAttrs": {"extensions": [".js", ".jsx", ".ts", ".tsx", ".mjs", ".mts", ".cjs", ".cts", ".es6", ".json"]}, "config": {"startCommand": {"args": ["sh", "-c", "/nix/store/9cd76kqpml5gkw8jjnjx0flwdf0a1gv1-typescript-language-server-4.3.3/bin/typescript-language-server --stdio"]}, "initializationOptionsJson": "{\"tsserver\":{\"fallbackPath\":\"/nix/store/g6ns6m42fvybfzb2xjppcsfmb6k0jv5x-typescript-5.6.3/lib/node_modules/typescript/lib\"}}"}, "displayVersion": "4.3.3"}, {"id": "module:python-3.11/languageServer:pyright-extended", "name": "pyright-extended", "language": "python3", "fileTypeAttrs": {}, "config": {"startCommand": {"args": ["sh", "-c", "/nix/store/4j8q64nnfyc3dwhwdlfg8c5px736iypg-pyright-extended-2.0.13/bin/langserver.index.js --stdio"]}}, "displayVersion": "2.0.13"}, {"id": "module:replit/languageServer:dotreplit-lsp", "name": ".replit LSP", "language": "dotreplit", "fileTypeAttrs": {}, "config": {"startCommand": {"args": ["sh", "-c", "/nix/store/bz8k1njgmm249fr5krhaq1jsi7jrhx5k-taplo-0.patched/bin/taplo lsp -c /nix/store/2zhz6va20gizdlqmvryab9b7pn6dp0v1-taplo-config.toml stdio"]}}}], "packagers": [{"id": "module:nodejs-20/packager:upmNodejs", "name": "Node.js packager (npm, yarn, pnpm, bun)", "language": "nodejs", "packageSearch": true, "guessImports": true}, {"id": "module:python-3.11/packager:upmPython", "name": "Python packager", "language": "python3", "packageSearch": true, "guessImports": true}], "formatters": [{"id": "module:nodejs-20/formatter:prettier", "name": "<PERSON>ttier", "startCommand": {"args": ["/nix/store/070ycjyhpfv8n895zq7yz6z0pp57g0q9-run-prettier/bin/run-prettier"], "lifecycle": "STDIN", "splitStderr": true}, "fileTypeAttrs": {"extensions": [".js", ".jsx", ".ts", ".tsx", ".json", ".html"]}, "displayVersion": "3.3.3", "supportsRangeFormatting": true}, {"id": "module:nodejs-20/languageServer:typescript-language-server", "name": "TypeScript Language Server", "fileTypeAttrs": {"extensions": [".js", ".jsx", ".ts", ".tsx", ".mjs", ".mts", ".cjs", ".cts", ".es6", ".json"]}, "displayVersion": "4.3.3"}, {"id": "module:python-3.11/languageServer:pyright-extended", "name": "pyright-extended", "fileTypeAttrs": {}, "displayVersion": "2.0.13"}, {"id": "module:replit/languageServer:dotreplit-lsp", "name": ".replit LSP", "fileTypeAttrs": {}}]}