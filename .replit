modules = ["nodejs-20", "postgresql-16", "python-3.11"]

[nix]
channel = "stable-24_05"

[deployment]
deploymentTarget = "autoscale"
run = ["python", "manage.py", "runserver", "0.0.0.0:5000"]

[workflows]
runButton = "Project"

[[workflows.workflow]]
name = "Project"
mode = "parallel"
author = "agent"

[[workflows.workflow.tasks]]
task = "workflow.run"
args = "Start application"

[[workflows.workflow]]
name = "Start application"
author = "agent"

[[workflows.workflow.tasks]]
task = "shell.exec"
args = "python manage.py runserver 0.0.0.0:5000"
waitForPort = 5000

[[ports]]
localPort = 5000
externalPort = 80

[[ports]]
localPort = 5746
externalPort = 3000

[[ports]]
localPort = 8000
externalPort = 8000
exposeLocalhost = true

[[ports]]
localPort = 8001
externalPort = 3001

[[ports]]
localPort = 8080
externalPort = 8080

[[ports]]
localPort = 25828
externalPort = 4200
exposeLocalhost = true

[[ports]]
localPort = 38523
externalPort = 3003
exposeLocalhost = true

[[ports]]
localPort = 39209
externalPort = 3002
exposeLocalhost = true
