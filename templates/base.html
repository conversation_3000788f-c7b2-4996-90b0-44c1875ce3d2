<!DOCTYPE html>
<html lang="it">
<head>
    {% load static %}
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ExProject{% endblock %}</title>
    
    <!-- Favicon e PWA -->
    <link rel="icon" href="{% static 'img/favicon.ico' %}" type="image/x-icon">
    <meta name="theme-color" content="#2563eb">
    
    <!-- HTMX e Alpine.js per interattività -->
    <script src="https://cdn.jsdelivr.net/npm/htmx.org@1.9.10"></script>
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js" defer></script>
    
    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#2563eb',
                        'primary-dark': '#1d4ed8',
                        'primary-light': '#3b82f6'
                    }
                }
            }
        }
    </script>
    
    <link rel="stylesheet" href="{% static 'base.css' %}">
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-gray-50 min-h-screen flex flex-col"
      hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
      x-data="{}">
    
    <!-- Overlay per mobile sidebar -->
    <div id="sidebar-overlay" 
         class="fixed inset-0 bg-black opacity-50 z-20 hidden md:hidden" 
         @click="document.getElementById('mobile-menu').classList.add('hidden');
                this.classList.add('hidden');">
    </div>
    
    {% block header %}
    <header class="bg-blue-600 text-white shadow-lg sticky top-0 z-30">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <!-- Hamburger menu per mobile -->
                    <button class="mr-2 md:hidden text-white" 
                            @click="document.getElementById('mobile-menu').classList.toggle('hidden');
                                   document.getElementById('sidebar-overlay').classList.toggle('hidden');">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                    
                    <h1 class="text-2xl font-bold flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-9 w-9 mr-2" viewBox="0 0 80 80" fill="none">
                            <!-- Logo per ExProject - Sistema gestione espropri -->
                            <!-- Sfondo circolare -->
                            <circle cx="40" cy="40" r="36" fill="white" />
                            
                            <!-- Edificio stilizzato -->
                            <path d="M40 12L60 28V64H20V28L40 12Z" fill="#1d4ed8" />
                            <path d="M40 12L60 28V64H40V12Z" fill="#2563eb" />
                            
                            <!-- Dettagli dell'edificio -->
                            <rect x="26" y="36" width="8" height="12" rx="1" fill="white" />
                            <rect x="46" y="36" width="8" height="12" rx="1" fill="white" />
                            <rect x="36" y="48" width="8" height="16" rx="1" fill="white" />
                            
                            <!-- Mappa / Griglia catastale -->
                            <path d="M20 52L32 40L38 46L48 36L60 48" stroke="white" stroke-width="2" stroke-linecap="round" />
                            
                            <!-- Contorno -->
                            <circle cx="40" cy="40" r="36" stroke="#1d4ed8" stroke-width="2" />
                        </svg>
                        <a href="{% url 'public:home' %}" class="hover:text-blue-100">ExProject</a>
                    </h1>
                    <p class="text-blue-100 text-sm ml-2 hidden sm:block">Sistema Gestione Espropri</p>
                </div>
                
                <!-- Barra di ricerca -->
                {% if user.is_authenticated %}
                <div class="hidden md:block flex-1 max-w-md mx-4">
                    <form class="relative" method="get" action="{% url 'core:search' %}">
                        <input type="text" name="q" placeholder="Cerca progetto, particella, documento..." 
                               class="w-full py-1 pl-3 pr-10 rounded-full text-sm bg-blue-700 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white">
                        <button type="submit" class="absolute right-0 top-0 mt-1 mr-3">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </button>
                    </form>
                </div>
                {% endif %}
                
                <div class="flex items-center space-x-2">
                    {% if user.is_authenticated %}
                        <!-- Notifiche -->
                        <div class="relative" x-data="{ open: false }">
                            <button class="p-1 rounded-full hover:bg-blue-700 relative notifications-button" 
                                    @click="open = !open"
                                    hx-get="{% url 'core:notifications' %}"
                                    hx-trigger="click"
                                    hx-target="#notifications-dropdown"
                                    hx-swap="innerHTML">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                </svg>
                                <span class="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-blue-600"></span>
                            </button>
                            
                            <div id="notifications-dropdown" 
                                 class="absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg py-1 z-50"
                                 x-show="open"
                                 @click.away="open = false"
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 style="display: none;">
                                <!-- Contenuto caricato via HTMX -->
                            </div>
                        </div>
                        
                        <!-- Menu utente -->
                        <div class="relative ml-3" x-data="{ open: false }">
                            <button type="button" 
                                    class="flex text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-white user-menu-button"
                                    @click="open = !open">
                                <span class="sr-only">Menu utente</span>
                                <div class="h-8 w-8 rounded-full bg-blue-700 flex items-center justify-center">
                                    <span class="text-white font-medium">{{ user.username|slice:":1" }}</span>
                                </div>
                            </button>
                            
                            <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 user-menu-dropdown"
                                 x-show="open"
                                 @click.away="open = false"
                                 x-transition:enter="transition ease-out duration-100"
                                 x-transition:enter-start="transform opacity-0 scale-95"
                                 x-transition:enter-end="transform opacity-100 scale-100"
                                 style="display: none;">
                                <div class="block px-4 py-2 text-xs text-gray-500">
                                    {{ user.get_full_name|default:user.username }}
                                </div>
                                <a href="{% url 'core:dashboard' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Dashboard</a>
                                <a href="{% url 'core:profile' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Profilo</a>
                                <a href="{% url 'core:settings' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Impostazioni</a>
                                <div class="border-t border-gray-100"></div>
                                <a href="{% url 'core:logout' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Logout</a>
                            </div>
                        </div>
                    {% else %}
                        <a href="{% url 'core:login' %}" class="bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm">Area Riservata</a>
                    {% endif %}
                </div>
            </div>
            
            <!-- Navbar integrata nell'header -->
            {% if user.is_authenticated %}
                {% if request.resolver_match.namespace == 'core' or request.resolver_match.namespace == 'projects' or request.resolver_match.namespace == 'workflow' or request.resolver_match.namespace == 'documents' or request.resolver_match.namespace == 'economic' or request.resolver_match.namespace == 'integrations' %}
                <!-- Navbar amministrativa -->
                <div class="border-t border-blue-500 mt-3 pt-2">
                    <ul class="flex space-x-6 py-2 overflow-x-auto scrollbar-hide">
                        <li><a href="{% url 'core:dashboard' %}" class="hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'dashboard' %}font-bold{% endif %}">Dashboard</a></li>
                        <li><a href="{% url 'projects:list' %}" class="hover:text-blue-100 py-2 {% if request.resolver_match.namespace == 'projects' %}font-bold{% endif %}">Progetti</a></li>
                        <li><a href="{% url 'workflow:list' %}" class="hover:text-blue-100 py-2 {% if request.resolver_match.namespace == 'workflow' %}font-bold{% endif %}">Workflow</a></li>
                        <li><a href="{% url 'documents:list' %}" class="hover:text-blue-100 py-2 {% if request.resolver_match.namespace == 'documents' %}font-bold{% endif %}">Documenti</a></li>
                        <li><a href="{% url 'economic:list' %}" class="hover:text-blue-100 py-2 {% if request.resolver_match.namespace == 'economic' %}font-bold{% endif %}">Economico</a></li>
                        <li><a href="{% url 'integrations:list' %}" class="hover:text-blue-100 py-2 {% if request.resolver_match.namespace == 'integrations' %}font-bold{% endif %}">Integrazioni</a></li>
                        <li><a href="{% url 'ui:design_system_docs' %}" class="hover:text-blue-100 py-2 {% if request.resolver_match.namespace == 'ui' %}font-bold{% endif %}">Design System</a></li>
                        <li><a href="{% url 'public:home' %}" class="hover:text-blue-100 py-2">Sito Pubblico</a></li>
                    </ul>
                </div>
                {% else %}
                <!-- Navbar pubblica -->
                <div class="border-t border-blue-500 mt-3 pt-2">
                    <ul class="flex space-x-6 py-2 overflow-x-auto scrollbar-hide">
                        <li><a href="{% url 'public:home' %}" class="hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'home' %}font-bold{% endif %}">Home</a></li>
                        <li><a href="{% url 'public:about' %}" class="hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'about' %}font-bold{% endif %}">Chi Siamo</a></li>
                        <li><a href="{% url 'public:procedures' %}" class="hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'procedures' %}font-bold{% endif %}">Procedure</a></li>
                        <li><a href="{% url 'public:news' %}" class="hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'news' %}font-bold{% endif %}">News</a></li>
                        <li><a href="{% url 'public:contacts' %}" class="hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'contacts' %}font-bold{% endif %}">Contatti</a></li>
                        <li><a href="{% url 'core:dashboard' %}" class="hover:text-blue-100 py-2 bg-blue-700 px-3 rounded">Dashboard</a></li>
                    </ul>
                </div>
                {% endif %}
            {% else %}
            <!-- Navbar pubblica per utenti non autenticati -->
            <div class="border-t border-blue-500 mt-3 pt-2">
                <ul class="flex space-x-6 py-2 overflow-x-auto scrollbar-hide">
                    <li><a href="{% url 'public:home' %}" class="hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'home' %}font-bold{% endif %}">Home</a></li>
                    <li><a href="{% url 'public:about' %}" class="hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'about' %}font-bold{% endif %}">Chi Siamo</a></li>
                    <li><a href="{% url 'public:procedures' %}" class="hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'procedures' %}font-bold{% endif %}">Procedure</a></li>
                    <li><a href="{% url 'public:news' %}" class="hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'news' %}font-bold{% endif %}">News</a></li>
                    <li><a href="{% url 'public:contacts' %}" class="hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'contacts' %}font-bold{% endif %}">Contatti</a></li>
                </ul>
            </div>
            {% endif %}
        </div>
    </header>
    {% endblock %}

    <!-- Mobile sidebar menu -->
    <div id="mobile-menu" class="fixed inset-y-0 left-0 w-64 bg-white shadow-lg z-30 transform transition-transform duration-300 ease-in-out hidden md:hidden">
        <div class="flex items-center justify-between p-4 border-b">
            <h2 class="font-semibold text-lg">Menu</h2>
            <button @click="document.getElementById('mobile-menu').classList.add('hidden');
                              document.getElementById('sidebar-overlay').classList.add('hidden');">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        
        <nav class="mt-2 px-4">
            {% block mobile_nav %}
            <ul class="space-y-1">
                {% block nav_mobile_items %}
                {% if user.is_authenticated %}
                    {% if request.resolver_match.namespace == 'core' or request.resolver_match.namespace == 'projects' or request.resolver_match.namespace == 'workflow' or request.resolver_match.namespace == 'documents' or request.resolver_match.namespace == 'economic' or request.resolver_match.namespace == 'integrations' %}
                    <!-- Menu mobile amministrativo -->
                    <li><a href="{% url 'core:dashboard' %}" class="block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'dashboard' %}text-blue-600 font-medium bg-blue-50{% endif %}">Dashboard</a></li>
                    <li><a href="{% url 'projects:list' %}" class="block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.namespace == 'projects' %}text-blue-600 font-medium bg-blue-50{% endif %}">Progetti</a></li>
                    <li><a href="{% url 'workflow:list' %}" class="block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.namespace == 'workflow' %}text-blue-600 font-medium bg-blue-50{% endif %}">Workflow</a></li>
                    <li><a href="{% url 'documents:list' %}" class="block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.namespace == 'documents' %}text-blue-600 font-medium bg-blue-50{% endif %}">Documenti</a></li>
                    <li><a href="{% url 'economic:list' %}" class="block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.namespace == 'economic' %}text-blue-600 font-medium bg-blue-50{% endif %}">Economico</a></li>
                    <li><a href="{% url 'integrations:list' %}" class="block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.namespace == 'integrations' %}text-blue-600 font-medium bg-blue-50{% endif %}">Integrazioni</a></li>
                    <li class="border-t my-2 pt-2">
                        <a href="{% url 'public:home' %}" class="block py-2 px-4 rounded bg-gray-100 text-gray-700 hover:bg-gray-200">Sito Pubblico</a>
                    </li>
                    {% else %}
                    <!-- Menu mobile pubblico per utenti autenticati -->
                    <li><a href="{% url 'public:home' %}" class="block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'home' %}text-blue-600 font-medium bg-blue-50{% endif %}">Home</a></li>
                    <li><a href="{% url 'public:about' %}" class="block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'about' %}text-blue-600 font-medium bg-blue-50{% endif %}">Chi Siamo</a></li>
                    <li><a href="{% url 'public:procedures' %}" class="block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'procedures' %}text-blue-600 font-medium bg-blue-50{% endif %}">Procedure</a></li>
                    <li><a href="{% url 'public:news' %}" class="block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'news' %}text-blue-600 font-medium bg-blue-50{% endif %}">News</a></li>
                    <li><a href="{% url 'public:contacts' %}" class="block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'contacts' %}text-blue-600 font-medium bg-blue-50{% endif %}">Contatti</a></li>
                    <li class="border-t my-2 pt-2">
                        <a href="{% url 'core:dashboard' %}" class="block py-2 px-4 rounded bg-blue-100 text-blue-700 hover:bg-blue-200">Dashboard</a>
                    </li>
                    {% endif %}
                {% else %}
                <!-- Menu mobile per utenti non autenticati -->
                <li><a href="{% url 'public:home' %}" class="block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'home' %}text-blue-600 font-medium bg-blue-50{% endif %}">Home</a></li>
                <li><a href="{% url 'public:about' %}" class="block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'about' %}text-blue-600 font-medium bg-blue-50{% endif %}">Chi Siamo</a></li>
                <li><a href="{% url 'public:procedures' %}" class="block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'procedures' %}text-blue-600 font-medium bg-blue-50{% endif %}">Procedure</a></li>
                <li><a href="{% url 'public:news' %}" class="block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'news' %}text-blue-600 font-medium bg-blue-50{% endif %}">News</a></li>
                <li><a href="{% url 'public:contacts' %}" class="block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'contacts' %}text-blue-600 font-medium bg-blue-50{% endif %}">Contatti</a></li>
                {% endif %}
                {% endblock %}
            </ul>
            {% endblock %}
        </nav>
    </div>

    {% block navigation %}
    <!-- Navigation is now integrated in the header -->
    {% endblock %}

    {% block messages %}
    <div id="messages-container" class="container mx-auto px-4 py-2">
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} bg-{% if message.tags == 'error' %}red{% elif message.tags == 'warning' %}yellow{% else %}green{% endif %}-100 border border-{% if message.tags == 'error' %}red{% elif message.tags == 'warning' %}yellow{% else %}green{% endif %}-400 text-{% if message.tags == 'error' %}red{% elif message.tags == 'warning' %}yellow{% else %}green{% endif %}-700 px-4 py-3 rounded mb-2 flex justify-between items-center fade-in"
                 hx-swap-oob="true">
                <span>{{ message }}</span>
                <button type="button" class="text-gray-500 hover:text-gray-700" onclick="this.parentElement.remove()">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
        {% endfor %}
    {% endif %}
    </div>
    {% endblock %}

    <main class="flex-1">
        {% block main %}
        <div class="container mx-auto px-4 py-6">
            {% block content %}{% endblock %}
        </div>
        {% endblock %}
    </main>

    {% block footer %}
    <footer class="bg-gray-800 text-white mt-auto">
        <div class="container mx-auto px-4 py-6">
            <div class="md:flex md:justify-between">
                <div class="mb-4 md:mb-0">
                    <h2 class="text-lg font-semibold mb-2">ExProject</h2>
                    <p class="text-gray-400 text-sm">Sistema Gestione Espropri secondo DPR 327/2001</p>
                </div>
                <div>
                    <h3 class="text-sm font-semibold mb-2 text-gray-300">Collegamenti Rapidi</h3>
                    <ul class="text-sm text-gray-400 space-y-1">
                        <li><a href="{% url 'public:about' %}" class="hover:text-white">Chi Siamo</a></li>
                        <li><a href="{% url 'public:procedures' %}" class="hover:text-white">Procedure</a></li>
                        <li><a href="{% url 'public:contacts' %}" class="hover:text-white">Contatti</a></li>
                    </ul>
                </div>
                <div class="mt-4 md:mt-0">
                    <h3 class="text-sm font-semibold mb-2 text-gray-300">Informazioni Legali</h3>
                    <ul class="text-sm text-gray-400 space-y-1">
                        <li><a href="#" class="hover:text-white">Privacy Policy</a></li>
                        <li><a href="#" class="hover:text-white">Termini di Servizio</a></li>
                        <li><a href="#" class="hover:text-white">Cookie Policy</a></li>
                    </ul>
                </div>
            </div>
            <div class="mt-8 border-t border-gray-700 pt-4 text-center text-gray-400 text-sm">
                <p>&copy; 2025 ExProject - Tutti i diritti riservati</p>
                <p class="mt-2">Sviluppato con Django, HTMX e TailwindCSS</p>
            </div>
        </div>
    </footer>
    {% endblock %}

    <!-- Script di supporto per HTMX -->
    <script>
        // Handler per mostrare messaggi temporanei
        document.addEventListener('htmx:afterSwap', function(event) {
            if (event.detail.target.id === 'messages-container') {
                setTimeout(function() {
                    const messages = document.querySelectorAll('.alert');
                    messages.forEach(msg => {
                        msg.classList.add('opacity-0');
                        setTimeout(() => msg.remove(), 300);
                    });
                }, 5000);
            }
        });
        
        // Indicate loading state
        document.addEventListener('htmx:beforeRequest', function(event) {
            const target = event.detail.target;
            if (target.tagName === 'BUTTON' || target.tagName === 'A') {
                const spinner = document.createElement('span');
                spinner.classList.add('htmx-indicator');
                spinner.innerHTML = `
                    <svg class="animate-spin -ml-1 mr-2 h-4 w-4 inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                `;
                target.appendChild(spinner);
            }
        });
        
        document.addEventListener('htmx:afterRequest', function(event) {
            const target = event.detail.target;
            const indicators = target.querySelectorAll('.htmx-indicator');
            indicators.forEach(i => i.remove());
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>