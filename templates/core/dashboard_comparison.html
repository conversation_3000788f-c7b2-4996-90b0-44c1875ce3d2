{% extends 'ui/base_modern.html' %}
{% load ui_components %}

{% block title %}Confronto Design - ExProject{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold tracking-tight mb-2">Confronto Design System</h1>
        <p class="text-muted-foreground">Confronto tra il vecchio design e il nuovo design system moderno</p>
    </div>
    
    <!-- Toggle per confronto -->
    <div class="mb-8" x-data="{ showOld: false }">
        <div class="flex items-center space-x-4">
            <span class="text-sm font-medium">Mostra design:</span>
            <button @click="showOld = false" 
                    :class="!showOld ? 'bg-primary text-primary-foreground' : 'bg-secondary text-secondary-foreground'"
                    class="px-3 py-1 rounded-md text-sm font-medium transition-colors">
                Nuovo (shadcn/ui)
            </button>
            <button @click="showOld = true"
                    :class="showOld ? 'bg-primary text-primary-foreground' : 'bg-secondary text-secondary-foreground'" 
                    class="px-3 py-1 rounded-md text-sm font-medium transition-colors">
                Vecchio (TailwindCSS base)
            </button>
        </div>
        
        <!-- Nuovo Design -->
        <div x-show="!showOld" class="mt-8">
            <h2 class="text-2xl font-bold mb-6">✨ Nuovo Design System (shadcn/ui inspired)</h2>
            
            <!-- Buttons -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Buttons</h3>
                <div class="flex flex-wrap gap-2">
                    {% ui_button "Primary" variant="default" %}
                    {% ui_button "Secondary" variant="secondary" %}
                    {% ui_button "Outline" variant="outline" %}
                    {% ui_button "Ghost" variant="ghost" %}
                    {% ui_button "Destructive" variant="destructive" %}
                    {% ui_button "Con Icona" variant="default" icon_left="plus" %}
                    {% ui_button "Loading" variant="default" loading=True %}
                </div>
            </div>
            
            <!-- Cards -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Cards</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% ui_card title="Progetti Attivi" description="Panoramica progetti in corso" %}
                        {% block card_content %}
                        <div class="text-2xl font-bold">24</div>
                        <p class="text-xs text-muted-foreground">
                            {% ui_icon 'trending-up' size="3" class="inline mr-1" %}
                            +12% dal mese scorso
                        </p>
                        {% endblock %}
                    {% endui_card %}
                    
                    {% ui_card title="Documenti Generati" %}
                        {% block card_content %}
                        <div class="text-2xl font-bold">156</div>
                        <p class="text-xs text-muted-foreground">
                            {% ui_icon 'document' size="3" class="inline mr-1" %}
                            +8% dal mese scorso
                        </p>
                        {% endblock %}
                    {% endui_card %}
                    
                    {% ui_card title="Valore Totale" %}
                        {% block card_content %}
                        <div class="text-2xl font-bold">€2.4M</div>
                        <p class="text-xs text-muted-foreground">
                            {% ui_icon 'euro' size="3" class="inline mr-1" %}
                            +15% dal mese scorso
                        </p>
                        {% endblock %}
                    {% endui_card %}
                </div>
            </div>
            
            <!-- Badges -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Badges</h3>
                <div class="flex flex-wrap gap-2">
                    {% ui_badge "Attivo" variant="default" %}
                    {% ui_badge "In Attesa" variant="secondary" %}
                    {% ui_badge "Completato" variant="outline" %}
                    {% ui_badge "Errore" variant="destructive" %}
                    {% ui_badge "Con Icona" variant="default" icon="check" %}
                </div>
            </div>
            
            <!-- Alerts -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Alerts</h3>
                <div class="space-y-4">
                    {% ui_alert "Questo è un messaggio informativo" variant="default" title="Informazione" %}
                    {% ui_alert "Operazione completata con successo" variant="success" title="Successo" %}
                    {% ui_alert "Attenzione: verifica i dati inseriti" variant="warning" title="Attenzione" %}
                    {% ui_alert "Si è verificato un errore" variant="destructive" title="Errore" %}
                </div>
            </div>
            
            <!-- Inputs -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Form Elements</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl">
                    {% ui_input "name" label="Nome Progetto" placeholder="Inserisci il nome del progetto" %}
                    {% ui_input "email" label="Email" placeholder="<EMAIL>" type="email" %}
                    {% ui_input "budget" label="Budget" placeholder="0.00" type="number" help_text="Inserisci il budget in euro" %}
                    {% ui_input "error_field" label="Campo con Errore" placeholder="Valore non valido" error="Questo campo è obbligatorio" %}
                </div>
            </div>
        </div>
        
        <!-- Vecchio Design -->
        <div x-show="showOld" class="mt-8">
            <h2 class="text-2xl font-bold mb-6">🔧 Vecchio Design (TailwindCSS base)</h2>
            
            <!-- Old Buttons -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Buttons</h3>
                <div class="flex flex-wrap gap-2">
                    <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Primary</button>
                    <button class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">Secondary</button>
                    <button class="border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-bold py-2 px-4 rounded">Outline</button>
                    <button class="text-blue-600 hover:bg-blue-100 font-bold py-2 px-4 rounded">Ghost</button>
                    <button class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Destructive</button>
                </div>
            </div>
            
            <!-- Old Cards -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Cards</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="bg-white rounded-lg shadow p-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Progetti Attivi</h4>
                        <p class="text-gray-600 text-sm mb-4">Panoramica progetti in corso</p>
                        <div class="text-2xl font-bold text-blue-600">24</div>
                        <p class="text-xs text-gray-500">+12% dal mese scorso</p>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Documenti Generati</h4>
                        <div class="text-2xl font-bold text-green-600">156</div>
                        <p class="text-xs text-gray-500">+8% dal mese scorso</p>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Valore Totale</h4>
                        <div class="text-2xl font-bold text-purple-600">€2.4M</div>
                        <p class="text-xs text-gray-500">+15% dal mese scorso</p>
                    </div>
                </div>
            </div>
            
            <!-- Old Badges -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Badges</h3>
                <div class="flex flex-wrap gap-2">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Attivo</span>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">In Attesa</span>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Completato</span>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Errore</span>
                </div>
            </div>
            
            <!-- Old Alerts -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Alerts</h3>
                <div class="space-y-4">
                    <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
                        <strong>Informazione:</strong> Questo è un messaggio informativo
                    </div>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                        <strong>Successo:</strong> Operazione completata con successo
                    </div>
                    <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
                        <strong>Attenzione:</strong> Verifica i dati inseriti
                    </div>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                        <strong>Errore:</strong> Si è verificato un errore
                    </div>
                </div>
            </div>
            
            <!-- Old Inputs -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Form Elements</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nome Progetto</label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Inserisci il nome del progetto">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input type="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="<EMAIL>">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Budget</label>
                        <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="0.00">
                        <p class="text-xs text-gray-500 mt-1">Inserisci il budget in euro</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Campo con Errore</label>
                        <input type="text" class="w-full px-3 py-2 border border-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500" placeholder="Valore non valido">
                        <p class="text-xs text-red-500 mt-1">Questo campo è obbligatorio</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Differenze principali -->
    <div class="mt-12 p-6 bg-muted rounded-lg">
        <h2 class="text-xl font-bold mb-4">🎯 Principali Differenze</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="font-semibold text-green-600 mb-2">✅ Nuovo Design System</h3>
                <ul class="text-sm space-y-1">
                    <li>• Design tokens consistenti</li>
                    <li>• Componenti riutilizzabili</li>
                    <li>• Varianti predefinite</li>
                    <li>• Dark mode ready</li>
                    <li>• Accessibilità migliorata</li>
                    <li>• Animazioni fluide</li>
                    <li>• White label support</li>
                </ul>
            </div>
            <div>
                <h3 class="font-semibold text-orange-600 mb-2">⚠️ Vecchio Design</h3>
                <ul class="text-sm space-y-1">
                    <li>• Stili hardcoded</li>
                    <li>• Inconsistenze visive</li>
                    <li>• Difficile manutenzione</li>
                    <li>• No standardizzazione</li>
                    <li>• Accessibilità limitata</li>
                    <li>• Personalizzazione complessa</li>
                    <li>• No sistema di temi</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
