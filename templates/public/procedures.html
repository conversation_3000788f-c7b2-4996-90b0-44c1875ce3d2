{% extends 'ui/base_modern.html' %}
{% load ui_components %}

{% block title %}{{ title }} - ExProject{% endblock %}

{% block content %}
<div class="flex-1 space-y-8 p-8 pt-6">
    <!-- Header -->
    <div class="text-center">
        <h1 class="text-4xl font-bold tracking-tight mb-4">{{ title }}</h1>
        <p class="text-xl text-muted-foreground max-w-3xl mx-auto">
            Le procedure di esproprio secondo il DPR 327/2001, gestite in modo semplice e automatizzato
        </p>
    </div>
    
    <!-- Procedures Timeline -->
    <div class="max-w-4xl mx-auto">
        <div class="space-y-8">
            {% for procedure in procedures %}
                <div class="relative">
                    <!-- Timeline Line -->
                    {% if not forloop.last %}
                        <div class="absolute left-6 top-16 w-0.5 h-full bg-border"></div>
                    {% endif %}
                    
                    <!-- Timeline Node -->
                    <div class="flex items-start space-x-6">
                        <div class="flex-shrink-0 w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                            {{ forloop.counter }}
                        </div>
                        
                        <!-- Content -->
                        <div class="flex-1">
                            {% ui_card 
                                title=procedure.title
                                content="
                                <div class='space-y-4'>
                                    <p class='text-muted-foreground'>{{ procedure.description }}</p>
                                    <div>
                                        <h4 class='font-medium mb-2'>Fasi operative:</h4>
                                        <ul class='space-y-1'>
                                            {% for step in procedure.steps %}
                                                <li class='flex items-center space-x-2'>
                                                    <svg class='w-4 h-4 text-green-600 flex-shrink-0' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                                        <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M5 13l4 4L19 7'></path>
                                                    </svg>
                                                    <span class='text-sm'>{{ step }}</span>
                                                </li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                </div>
                                "
                            %}
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- Key Features -->
    <div class="max-w-6xl mx-auto">
        <h2 class="text-3xl font-bold text-center mb-8">Vantaggi della Digitalizzazione</h2>
        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {% ui_card 
                title="Automazione Completa"
                content="
                <div class='space-y-4'>
                    <div class='w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center'>
                        <svg class='w-6 h-6 text-blue-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z'></path>
                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M15 12a3 3 0 11-6 0 3 3 0 016 0z'></path>
                        </svg>
                    </div>
                    <p class='text-muted-foreground'>
                        Workflow automatizzati che guidano attraverso ogni fase del processo di esproprio.
                    </p>
                </div>
                "
            %}
            
            {% ui_card 
                title="Conformità Normativa"
                content="
                <div class='space-y-4'>
                    <div class='w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center'>
                        <svg class='w-6 h-6 text-green-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z'></path>
                        </svg>
                    </div>
                    <p class='text-muted-foreground'>
                        Piena conformità al DPR 327/2001 e aggiornamenti automatici alle normative.
                    </p>
                </div>
                "
            %}
            
            {% ui_card 
                title="Tracciabilità Completa"
                content="
                <div class='space-y-4'>
                    <div class='w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center'>
                        <svg class='w-6 h-6 text-purple-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01'></path>
                        </svg>
                    </div>
                    <p class='text-muted-foreground'>
                        Registro completo di tutte le operazioni con timestamp e responsabilità.
                    </p>
                </div>
                "
            %}
            
            {% ui_card 
                title="Riduzione Tempi"
                content="
                <div class='space-y-4'>
                    <div class='w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center'>
                        <svg class='w-6 h-6 text-yellow-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z'></path>
                        </svg>
                    </div>
                    <p class='text-muted-foreground'>
                        Riduzione significativa dei tempi di gestione grazie all'automazione.
                    </p>
                </div>
                "
            %}
            
            {% ui_card 
                title="Gestione Errori"
                content="
                <div class='space-y-4'>
                    <div class='w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center'>
                        <svg class='w-6 h-6 text-red-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z'></path>
                        </svg>
                    </div>
                    <p class='text-muted-foreground'>
                        Controlli automatici e validazioni per prevenire errori procedurali.
                    </p>
                </div>
                "
            %}
            
            {% ui_card 
                title="Reportistica Avanzata"
                content="
                <div class='space-y-4'>
                    <div class='w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center'>
                        <svg class='w-6 h-6 text-indigo-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                            <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'></path>
                        </svg>
                    </div>
                    <p class='text-muted-foreground'>
                        Report dettagliati e dashboard per monitorare l'avanzamento dei progetti.
                    </p>
                </div>
                "
            %}
        </div>
    </div>
    
    <!-- CTA Section -->
    <div class="max-w-4xl mx-auto text-center">
        {% ui_card 
            content="
            <div class='space-y-6'>
                <h2 class='text-2xl font-bold'>Inizia a Digitalizzare le Tue Procedure</h2>
                <p class='text-muted-foreground'>
                    Scopri come ExProject può semplificare e automatizzare la gestione dei tuoi progetti di esproprio
                </p>
                <div class='flex flex-col sm:flex-row gap-4 justify-center'>
                    <a href='{% url 'core:login' %}' class='inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2'>
                        Accedi alla Piattaforma
                    </a>
                    <a href='{% url 'public:contacts' %}' class='inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2'>
                        Richiedi Demo
                    </a>
                </div>
            </div>
            "
        %}
    </div>
</div>
{% endblock %}
