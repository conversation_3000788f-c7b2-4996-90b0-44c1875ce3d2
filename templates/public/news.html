{% extends 'ui/base_modern.html' %}
{% load ui_components %}

{% block title %}{{ title }} - ExProject{% endblock %}

{% block content %}
<div class="flex-1 space-y-8 p-8 pt-6">
    <!-- Header -->
    <div class="text-center">
        <h1 class="text-4xl font-bold tracking-tight mb-4">{{ title }}</h1>
        <p class="text-xl text-muted-foreground max-w-3xl mx-auto">
            Rimani aggiornato sulle ultime novità di ExProject e del settore espropri
        </p>
    </div>
    
    <!-- News Grid -->
    <div class="max-w-6xl mx-auto">
        <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {% for news_item in news_items %}
                {% ui_card 
                    content="
                    <article class='space-y-4'>
                        <!-- News Image Placeholder -->
                        <div class='w-full h-48 bg-muted rounded-lg flex items-center justify-center'>
                            <svg class='w-12 h-12 text-muted-foreground' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z'></path>
                            </svg>
                        </div>
                        
                        <!-- News Content -->
                        <div class='space-y-2'>
                            <div class='flex items-center space-x-2 text-sm text-muted-foreground'>
                                <svg class='w-4 h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                    <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'></path>
                                </svg>
                                <time datetime='{{ news_item.date }}'>{{ news_item.date|date:'d M Y' }}</time>
                            </div>
                            
                            <h2 class='text-xl font-semibold leading-tight'>{{ news_item.title }}</h2>
                            
                            <p class='text-muted-foreground'>{{ news_item.summary }}</p>
                            
                            <div class='pt-2'>
                                <a href='#' class='inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800'>
                                    Leggi di più
                                    <svg class='w-4 h-4 ml-1' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                        <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 5l7 7-7 7'></path>
                                    </svg>
                                </a>
                            </div>
                        </div>
                    </article>
                    "
                %}
            {% endfor %}
        </div>
    </div>
    
    <!-- Newsletter Subscription -->
    <div class="max-w-4xl mx-auto">
        {% ui_card 
            title="Rimani Aggiornato"
            content="
            <div class='space-y-6'>
                <p class='text-muted-foreground text-center'>
                    Iscriviti alla nostra newsletter per ricevere aggiornamenti su nuove funzionalità, 
                    modifiche normative e best practice del settore.
                </p>
                
                <form class='flex flex-col sm:flex-row gap-4 max-w-md mx-auto'>
                    <div class='flex-1'>
                        <input 
                            type='email' 
                            placeholder='La tua email' 
                            class='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                            required
                        >
                    </div>
                    <button 
                        type='submit'
                        class='inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2'
                    >
                        Iscriviti
                    </button>
                </form>
                
                <p class='text-xs text-muted-foreground text-center'>
                    Rispettiamo la tua privacy. Nessuno spam, solo contenuti di valore.
                </p>
            </div>
            "
        %}
    </div>
    
    <!-- Related Links -->
    <div class="max-w-6xl mx-auto">
        <h2 class="text-2xl font-bold text-center mb-6">Risorse Utili</h2>
        <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {% ui_card 
                title="Documentazione"
                content="
                <div class='space-y-2'>
                    <p class='text-sm text-muted-foreground'>Guide complete per utilizzare ExProject</p>
                    <a href='#' class='text-sm text-blue-600 hover:text-blue-800'>Vai alla documentazione →</a>
                </div>
                "
            %}
            
            {% ui_card 
                title="Video Tutorial"
                content="
                <div class='space-y-2'>
                    <p class='text-sm text-muted-foreground'>Tutorial video passo-passo</p>
                    <a href='#' class='text-sm text-blue-600 hover:text-blue-800'>Guarda i video →</a>
                </div>
                "
            %}
            
            {% ui_card 
                title="FAQ"
                content="
                <div class='space-y-2'>
                    <p class='text-sm text-muted-foreground'>Risposte alle domande più frequenti</p>
                    <a href='#' class='text-sm text-blue-600 hover:text-blue-800'>Leggi le FAQ →</a>
                </div>
                "
            %}
            
            {% ui_card 
                title="Supporto"
                content="
                <div class='space-y-2'>
                    <p class='text-sm text-muted-foreground'>Contatta il nostro team di supporto</p>
                    <a href='{% url 'public:contacts' %}' class='text-sm text-blue-600 hover:text-blue-800'>Contattaci →</a>
                </div>
                "
            %}
        </div>
    </div>
</div>
{% endblock %}
