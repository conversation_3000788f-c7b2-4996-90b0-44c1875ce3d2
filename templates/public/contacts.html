{% extends 'ui/base_modern.html' %}
{% load ui_components %}

{% block title %}{{ title }} - ExProject{% endblock %}

{% block content %}
<div class="flex-1 space-y-8 p-8 pt-6">
    <!-- Header -->
    <div class="text-center">
        <h1 class="text-4xl font-bold tracking-tight mb-4">{{ title }}</h1>
        <p class="text-xl text-muted-foreground max-w-3xl mx-auto">
            Siamo qui per aiutarti. Contattaci per qualsiasi domanda o per richiedere una demo personalizzata.
        </p>
    </div>
    
    <!-- Contact Grid -->
    <div class="max-w-6xl mx-auto grid gap-8 lg:grid-cols-2">
        <!-- Contact Information -->
        <div class="space-y-6">
            {% ui_card 
                title="Informazioni di Contatto"
                content="
                <div class='space-y-6'>
                    <!-- Address -->
                    <div class='flex items-start space-x-4'>
                        <div class='w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0'>
                            <svg class='w-5 h-5 text-blue-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z'></path>
                                <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M15 11a3 3 0 11-6 0 3 3 0 016 0z'></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class='font-medium'>Indirizzo</h3>
                            <p class='text-muted-foreground'>{{ contact_info.address }}</p>
                        </div>
                    </div>
                    
                    <!-- Phone -->
                    <div class='flex items-start space-x-4'>
                        <div class='w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0'>
                            <svg class='w-5 h-5 text-green-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z'></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class='font-medium'>Telefono</h3>
                            <p class='text-muted-foreground'>{{ contact_info.phone }}</p>
                        </div>
                    </div>
                    
                    <!-- Email -->
                    <div class='flex items-start space-x-4'>
                        <div class='w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0'>
                            <svg class='w-5 h-5 text-purple-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z'></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class='font-medium'>Email</h3>
                            <p class='text-muted-foreground'>{{ contact_info.email }}</p>
                        </div>
                    </div>
                    
                    <!-- Hours -->
                    <div class='flex items-start space-x-4'>
                        <div class='w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center flex-shrink-0'>
                            <svg class='w-5 h-5 text-yellow-600' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
                                <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z'></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class='font-medium'>Orari</h3>
                            <p class='text-muted-foreground'>{{ contact_info.hours }}</p>
                        </div>
                    </div>
                </div>
                "
            %}
            
            <!-- Social Media -->
            {% ui_card 
                title="Seguici sui Social"
                content="
                <div class='flex space-x-4'>
                    <a href='{{ contact_info.social.linkedin }}' class='w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center text-white hover:bg-blue-700 transition-colors'>
                        <svg class='w-5 h-5' fill='currentColor' viewBox='0 0 24 24'>
                            <path d='M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z'/>
                        </svg>
                    </a>
                    
                    <a href='{{ contact_info.social.twitter }}' class='w-10 h-10 bg-sky-500 rounded-lg flex items-center justify-center text-white hover:bg-sky-600 transition-colors'>
                        <svg class='w-5 h-5' fill='currentColor' viewBox='0 0 24 24'>
                            <path d='M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z'/>
                        </svg>
                    </a>
                    
                    <a href='{{ contact_info.social.facebook }}' class='w-10 h-10 bg-blue-800 rounded-lg flex items-center justify-center text-white hover:bg-blue-900 transition-colors'>
                        <svg class='w-5 h-5' fill='currentColor' viewBox='0 0 24 24'>
                            <path d='M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z'/>
                        </svg>
                    </a>
                </div>
                "
            %}
        </div>
        
        <!-- Contact Form -->
        <div>
            {% ui_card 
                title="Invia un Messaggio"
                content="
                <form class='space-y-6'>
                    <div class='grid gap-4 md:grid-cols-2'>
                        <div>
                            <label for='first_name' class='block text-sm font-medium mb-2'>Nome</label>
                            <input 
                                type='text' 
                                id='first_name' 
                                name='first_name'
                                class='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                                required
                            >
                        </div>
                        <div>
                            <label for='last_name' class='block text-sm font-medium mb-2'>Cognome</label>
                            <input 
                                type='text' 
                                id='last_name' 
                                name='last_name'
                                class='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                                required
                            >
                        </div>
                    </div>
                    
                    <div>
                        <label for='email' class='block text-sm font-medium mb-2'>Email</label>
                        <input 
                            type='email' 
                            id='email' 
                            name='email'
                            class='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                            required
                        >
                    </div>
                    
                    <div>
                        <label for='company' class='block text-sm font-medium mb-2'>Azienda (opzionale)</label>
                        <input 
                            type='text' 
                            id='company' 
                            name='company'
                            class='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                        >
                    </div>
                    
                    <div>
                        <label for='subject' class='block text-sm font-medium mb-2'>Oggetto</label>
                        <select 
                            id='subject' 
                            name='subject'
                            class='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                            required
                        >
                            <option value=''>Seleziona un oggetto</option>
                            <option value='demo'>Richiesta Demo</option>
                            <option value='info'>Informazioni Generali</option>
                            <option value='support'>Supporto Tecnico</option>
                            <option value='partnership'>Partnership</option>
                            <option value='other'>Altro</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for='message' class='block text-sm font-medium mb-2'>Messaggio</label>
                        <textarea 
                            id='message' 
                            name='message' 
                            rows='4'
                            class='flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                            placeholder='Descrivi la tua richiesta...'
                            required
                        ></textarea>
                    </div>
                    
                    <div class='flex items-center space-x-2'>
                        <input 
                            type='checkbox' 
                            id='privacy' 
                            name='privacy'
                            class='rounded border-input'
                            required
                        >
                        <label for='privacy' class='text-sm text-muted-foreground'>
                            Accetto il trattamento dei dati personali secondo la Privacy Policy
                        </label>
                    </div>
                    
                    <button 
                        type='submit'
                        class='w-full inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2'
                    >
                        Invia Messaggio
                    </button>
                </form>
                "
            %}
        </div>
    </div>
</div>
{% endblock %}
