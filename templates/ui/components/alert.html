{% load ui_components %}
{# Componente Alert ispirato a shadcn/ui #}
<div class="relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground
    {% ui_variant_classes 'alert' variant %}
    {% if attrs.class %}{{ attrs.class }}{% endif %}"
    {% for key, value in attrs.items %}{% if key != 'class' %}{{ key }}="{{ value }}"{% endif %}{% endfor %}>
    
    {% if variant == 'destructive' %}
        {% ui_icon 'x-circle' size="4" %}
    {% elif variant == 'warning' %}
        {% ui_icon 'exclamation-triangle' size="4" %}
    {% elif variant == 'success' %}
        {% ui_icon 'check-circle' size="4" %}
    {% else %}
        {% ui_icon 'info' size="4" %}
    {% endif %}
    
    <div>
        {% if title %}
        <h5 class="mb-1 font-medium leading-none tracking-tight">{{ title }}</h5>
        {% endif %}
        <div class="text-sm [&_p]:leading-relaxed">{{ message }}</div>
    </div>
    
    {% if attrs.dismissible %}
    <button class="absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
            onclick="this.parentElement.remove()">
        {% ui_icon 'x' size="4" %}
        <span class="sr-only">Chiudi</span>
    </button>
    {% endif %}
</div>
