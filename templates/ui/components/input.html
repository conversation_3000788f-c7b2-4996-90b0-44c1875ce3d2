{% load ui_components %}
{# Componente Input ispirato a shadcn/ui #}
<div class="grid w-full max-w-sm items-center gap-1.5">
    {% if label %}
    <label for="{{ name }}" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
        {{ label }}
        {% if attrs.required %}<span class="text-destructive">*</span>{% endif %}
    </label>
    {% endif %}
    
    <input 
        type="{{ type }}"
        id="{{ name }}"
        name="{{ name }}"
        {% if placeholder %}placeholder="{{ placeholder }}"{% endif %}
        class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50
        {% if attrs.error %}border-destructive{% endif %}
        {% if attrs.class %}{{ attrs.class }}{% endif %}"
        {% for key, value in attrs.items %}{% if key not in 'class,error,required' %}{{ key }}="{{ value }}"{% endfor %}
    />
    
    {% if attrs.error %}
    <p class="text-sm text-destructive">{{ attrs.error }}</p>
    {% endif %}
    
    {% if attrs.help_text %}
    <p class="text-sm text-muted-foreground">{{ attrs.help_text }}</p>
    {% endif %}
</div>
