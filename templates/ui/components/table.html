{% load ui_components %}
{# Componente Table ispirato a shadcn/ui #}
<div class="relative w-full overflow-auto">
    <table class="w-full caption-bottom text-sm {% if attrs.class %}{{ attrs.class }}{% endif %}"
           {% for key, value in attrs.items %}{% if key != 'class' %}{{ key }}="{{ value }}"{% endif %}{% endfor %}>
        
        {% if headers %}
        <thead class="[&_tr]:border-b">
            <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                {% for header in headers %}
                <th class="h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0">
                    {{ header }}
                </th>
                {% endfor %}
            </tr>
        </thead>
        {% endif %}
        
        <tbody class="[&_tr:last-child]:border-0">
            {% for row in rows %}
            <tr class="border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted">
                {% for cell in row %}
                <td class="p-4 align-middle [&:has([role=checkbox])]:pr-0">
                    {{ cell }}
                </td>
                {% endfor %}
            </tr>
            {% empty %}
            <tr>
                <td colspan="{{ headers|length }}" class="p-8 text-center text-muted-foreground">
                    <div class="flex flex-col items-center space-y-2">
                        {% ui_icon 'search' size="8" class="text-muted-foreground/50" %}
                        <p>Nessun dato disponibile</p>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
