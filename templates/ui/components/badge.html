{% load ui_components %}
{# Componente Badge ispirato a shadcn/ui #}
<div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2
    {% ui_variant_classes 'badge' variant %}
    {% if attrs.class %}{{ attrs.class }}{% endif %}"
    {% for key, value in attrs.items %}{% if key != 'class' %}{{ key }}="{{ value }}"{% endif %}{% endfor %}>
    
    {% if attrs.icon %}
        {% ui_icon attrs.icon size="3" class="mr-1" %}
    {% endif %}
    
    {{ text }}
    
    {% if attrs.removable %}
        <button class="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2" 
                onclick="this.parentElement.remove()">
            {% ui_icon 'x' size="3" %}
        </button>
    {% endif %}
</div>
