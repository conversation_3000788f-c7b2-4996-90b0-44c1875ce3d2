{% load ui_components %}
{# Componente But<PERSON> ispirato a shadcn/ui #}
<button 
    class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50
    {% if size == 'sm' %}h-9 rounded-md px-3{% elif size == 'lg' %}h-11 rounded-md px-8{% elif size == 'icon' %}h-10 w-10{% else %}h-10 px-4 py-2{% endif %}
    {% ui_variant_classes 'button' variant %}"
    {% for key, value in attrs.items %}{{ key }}="{{ value }}"{% endfor %}
>
    {% if attrs.icon_left %}
        {% ui_icon attrs.icon_left size="4" class="mr-2" %}
    {% endif %}
    
    {{ text }}
    
    {% if attrs.icon_right %}
        {% ui_icon attrs.icon_right size="4" class="ml-2" %}
    {% endif %}
    
    {% if attrs.loading %}
        <svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
    {% endif %}
</button>
