{% load ui_components %}
{# Componente Card ispirato a shadcn/ui #}
<div class="rounded-lg border bg-card text-card-foreground shadow-sm {% if attrs.class %}{{ attrs.class }}{% endif %}"
     {% for key, value in attrs.items %}{% if key != 'class' %}{{ key }}="{{ value }}"{% endif %}{% endfor %}>
    
    {% if title or description %}
    <div class="flex flex-col space-y-1.5 p-6">
        {% if title %}
        <h3 class="text-2xl font-semibold leading-none tracking-tight">{{ title }}</h3>
        {% endif %}
        
        {% if description %}
        <p class="text-sm text-muted-foreground">{{ description }}</p>
        {% endif %}
    </div>
    {% endif %}
    
    <div class="p-6 pt-0">
        {% block card_content %}
        <!-- Contenuto della card -->
        {% endblock %}
    </div>
    
    {% block card_footer %}
    <!-- Footer opzionale -->
    {% endblock %}
</div>
