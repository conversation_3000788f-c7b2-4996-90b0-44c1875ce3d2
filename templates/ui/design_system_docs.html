{% extends 'ui/base_modern.html' %}
{% load ui_components %}

{% block title %}Design System - ExProject{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-4xl font-bold tracking-tight mb-2">Design System</h1>
            <p class="text-xl text-muted-foreground">
                Componenti riutilizzabili e design tokens per ExProject
            </p>
        </div>
        
        <!-- Navigation -->
        <div class="mb-8">
            <nav class="flex space-x-4 border-b">
                <a href="#colors" class="px-3 py-2 text-sm font-medium border-b-2 border-primary text-primary">Colori</a>
                <a href="#typography" class="px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground">Tipografia</a>
                <a href="#components" class="px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground">Componenti</a>
                <a href="#layouts" class="px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground">Layout</a>
            </nav>
        </div>
        
        <!-- Sezione Colori -->
        <section id="colors" class="mb-12">
            <h2 class="text-2xl font-bold mb-4">Palette Colori</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <!-- Colori primari -->
                <div class="space-y-2">
                    <h3 class="font-semibold">Primari</h3>
                    <div class="space-y-1">
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 rounded bg-primary border"></div>
                            <span class="text-sm">Primary</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 rounded bg-primary-foreground border"></div>
                            <span class="text-sm">Primary Foreground</span>
                        </div>
                    </div>
                </div>
                
                <!-- Colori secondari -->
                <div class="space-y-2">
                    <h3 class="font-semibold">Secondari</h3>
                    <div class="space-y-1">
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 rounded bg-secondary border"></div>
                            <span class="text-sm">Secondary</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 rounded bg-secondary-foreground border"></div>
                            <span class="text-sm">Secondary Foreground</span>
                        </div>
                    </div>
                </div>
                
                <!-- Colori di stato -->
                <div class="space-y-2">
                    <h3 class="font-semibold">Stati</h3>
                    <div class="space-y-1">
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 rounded bg-destructive border"></div>
                            <span class="text-sm">Destructive</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 rounded bg-success border"></div>
                            <span class="text-sm">Success</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="w-8 h-8 rounded bg-warning border"></div>
                            <span class="text-sm">Warning</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Sezione Tipografia -->
        <section id="typography" class="mb-12">
            <h2 class="text-2xl font-bold mb-4">Tipografia</h2>
            
            <div class="space-y-4">
                <div>
                    <h1 class="text-4xl font-bold">Heading 1</h1>
                    <code class="text-sm text-muted-foreground">text-4xl font-bold</code>
                </div>
                <div>
                    <h2 class="text-3xl font-bold">Heading 2</h2>
                    <code class="text-sm text-muted-foreground">text-3xl font-bold</code>
                </div>
                <div>
                    <h3 class="text-2xl font-bold">Heading 3</h3>
                    <code class="text-sm text-muted-foreground">text-2xl font-bold</code>
                </div>
                <div>
                    <p class="text-base">Paragrafo normale con testo di esempio per mostrare la tipografia di base.</p>
                    <code class="text-sm text-muted-foreground">text-base</code>
                </div>
                <div>
                    <p class="text-sm text-muted-foreground">Testo secondario più piccolo e meno prominente.</p>
                    <code class="text-sm text-muted-foreground">text-sm text-muted-foreground</code>
                </div>
            </div>
        </section>
        
        <!-- Sezione Componenti -->
        <section id="components" class="mb-12">
            <h2 class="text-2xl font-bold mb-4">Componenti</h2>
            
            <!-- Buttons -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-3">Buttons</h3>
                <div class="flex flex-wrap gap-2 mb-4">
                    <button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90">Primary</button>
                    <button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 bg-secondary text-secondary-foreground hover:bg-secondary/80">Secondary</button>
                    <button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground">Outline</button>
                    <button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 hover:bg-accent hover:text-accent-foreground">Ghost</button>
                    <button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 bg-destructive text-destructive-foreground hover:bg-destructive/90">Destructive</button>
                </div>
                <details class="mb-4">
                    <summary class="cursor-pointer text-sm font-medium">Mostra codice</summary>
                    <pre class="mt-2 p-4 bg-muted rounded text-sm overflow-x-auto"><code>{% templatetag openblock %} ui_button "Primary Button" variant="default" {% templatetag closeblock %}
{% templatetag openblock %} ui_button "Secondary Button" variant="secondary" {% templatetag closeblock %}
{% templatetag openblock %} ui_button "Outline Button" variant="outline" {% templatetag closeblock %}</code></pre>
                </details>
            </div>
            
            <!-- Badges -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-3">Badges</h3>
                <div class="flex flex-wrap gap-2 mb-4">
                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-primary text-primary-foreground hover:bg-primary/80">Default</div>
                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-secondary text-secondary-foreground hover:bg-secondary/80">Secondary</div>
                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground border-input">Outline</div>
                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-destructive text-destructive-foreground hover:bg-destructive/80">Destructive</div>
                </div>
                <details class="mb-4">
                    <summary class="cursor-pointer text-sm font-medium">Mostra codice</summary>
                    <pre class="mt-2 p-4 bg-muted rounded text-sm overflow-x-auto"><code>{% templatetag openblock %} ui_badge "Default" variant="default" {% templatetag closeblock %}
{% templatetag openblock %} ui_badge "Secondary" variant="secondary" {% templatetag closeblock %}</code></pre>
                </details>
            </div>
            
            <!-- Inputs -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-3">Inputs</h3>
                <div class="space-y-4 max-w-md mb-4">
                    <div class="grid w-full max-w-sm items-center gap-1.5">
                        <label class="text-sm font-medium leading-none">Email</label>
                        <input type="email" placeholder="Inserisci la tua email" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
                    </div>
                    <div class="grid w-full max-w-sm items-center gap-1.5">
                        <label class="text-sm font-medium leading-none">Password</label>
                        <input type="password" placeholder="Inserisci la password" class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
                    </div>
                    <div class="grid w-full max-w-sm items-center gap-1.5">
                        <label class="text-sm font-medium leading-none">Ricerca</label>
                        <input type="text" placeholder="Cerca..." class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2">
                    </div>
                </div>
                <details class="mb-4">
                    <summary class="cursor-pointer text-sm font-medium">Mostra codice</summary>
                    <pre class="mt-2 p-4 bg-muted rounded text-sm overflow-x-auto"><code>{% templatetag openblock %} ui_input "email" label="Email" placeholder="Inserisci la tua email" type="email" {% templatetag closeblock %}</code></pre>
                </details>
            </div>
            
            <!-- Cards -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-3">Cards</h3>
                <div class="max-w-md mb-4">
                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                        <div class="flex flex-col space-y-1.5 p-6">
                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Titolo Card</h3>
                            <p class="text-sm text-muted-foreground">Descrizione della card con testo di esempio.</p>
                        </div>
                        <div class="p-6 pt-0">
                            <p class="text-sm">Contenuto della card con informazioni aggiuntive.</p>
                        </div>
                    </div>
                </div>
                <details class="mb-4">
                    <summary class="cursor-pointer text-sm font-medium">Mostra codice</summary>
                    <pre class="mt-2 p-4 bg-muted rounded text-sm overflow-x-auto"><code>&lt;div class="rounded-lg border bg-card text-card-foreground shadow-sm"&gt;
    &lt;div class="flex flex-col space-y-1.5 p-6"&gt;
        &lt;h3 class="text-2xl font-semibold leading-none tracking-tight"&gt;Titolo Card&lt;/h3&gt;
        &lt;p class="text-sm text-muted-foreground"&gt;Descrizione della card&lt;/p&gt;
    &lt;/div&gt;
    &lt;div class="p-6 pt-0"&gt;
        &lt;p&gt;Contenuto della card&lt;/p&gt;
    &lt;/div&gt;
&lt;/div&gt;</code></pre>
                </details>
            </div>
            
            <!-- Alerts -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-3">Alerts</h3>
                <div class="space-y-2 mb-4">
                    <div class="relative w-full rounded-lg border p-4 bg-background text-foreground">
                        <div>
                            <h5 class="mb-1 font-medium leading-none tracking-tight">Informazione</h5>
                            <div class="text-sm">Questo è un messaggio informativo</div>
                        </div>
                    </div>
                    <div class="relative w-full rounded-lg border p-4 border-destructive/50 text-destructive">
                        <div>
                            <h5 class="mb-1 font-medium leading-none tracking-tight">Errore</h5>
                            <div class="text-sm">Questo è un messaggio di errore</div>
                        </div>
                    </div>
                    <div class="relative w-full rounded-lg border p-4 border-warning/50 text-warning">
                        <div>
                            <h5 class="mb-1 font-medium leading-none tracking-tight">Attenzione</h5>
                            <div class="text-sm">Questo è un messaggio di attenzione</div>
                        </div>
                    </div>
                </div>
                <details class="mb-4">
                    <summary class="cursor-pointer text-sm font-medium">Mostra codice</summary>
                    <pre class="mt-2 p-4 bg-muted rounded text-sm overflow-x-auto"><code>{% templatetag openblock %} ui_alert "Messaggio di default" variant="default" {% templatetag closeblock %}
{% templatetag openblock %} ui_alert "Messaggio di errore" variant="destructive" {% templatetag closeblock %}</code></pre>
                </details>
            </div>
        </section>
        
        <!-- Sezione Layout -->
        <section id="layouts" class="mb-12">
            <h2 class="text-2xl font-bold mb-4">Layout</h2>
            
            <div class="space-y-6">
                <div>
                    <h3 class="text-lg font-semibold mb-2">Dashboard Layout</h3>
                    <p class="text-muted-foreground mb-4">Layout principale per dashboard con sidebar e header fisso.</p>
                    <div class="border rounded-lg p-4 bg-muted/50">
                        <div class="text-sm font-mono">templates/ui/layouts/dashboard_modern.html</div>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-2">Form Layout</h3>
                    <p class="text-muted-foreground mb-4">Layout ottimizzato per form con validazione e feedback.</p>
                    <div class="border rounded-lg p-4 bg-muted/50">
                        <div class="text-sm font-mono">templates/ui/layouts/form.html</div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Footer -->
        <div class="border-t pt-8 mt-12">
            <p class="text-sm text-muted-foreground">
                Design System basato su shadcn/ui e Tailwind CSS. 
                <a href="{% url 'ui:component_library' %}" class="text-primary hover:underline">
                    Visualizza libreria componenti completa
                </a>
            </p>
        </div>
    </div>
</div>

<script>
// Smooth scroll per navigation
document.querySelectorAll('nav a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Highlight active section in navigation
window.addEventListener('scroll', () => {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('nav a[href^="#"]');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (scrollY >= sectionTop - 200) {
            current = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('border-primary', 'text-primary');
        link.classList.add('text-muted-foreground');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('border-primary', 'text-primary');
            link.classList.remove('text-muted-foreground');
        }
    });
});
</script>
{% endblock %}
