{% extends 'ui/base_modern.html' %}
{% load ui_components %}

{% block title %}{{ form_title }}{% endblock %}

{% block content %}
<div class="flex-1 space-y-4 p-8 pt-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <h2 class="text-3xl font-bold tracking-tight">{{ form_title }}</h2>
        <div class="flex items-center space-x-2">
            {% ui_button "Cancel" variant="outline" onclick="history.back()" %}
        </div>
    </div>
    
    <!-- Form Card -->
    <div class="max-w-2xl">
        {% ui_card 
            title="Details"
            content="
            <form method='post' enctype='multipart/form-data' class='space-y-6'>
                {% csrf_token %}
                
                <!-- Form Fields -->
                {% for field in form %}
                    <div class='space-y-2'>
                        <label for='{{ field.id_for_label }}' class='text-sm font-medium'>
                            {{ field.label }}
                            {% if field.field.required %}<span class='text-red-500'>*</span>{% endif %}
                        </label>
                        {{ field }}
                        {% if field.errors %}
                            <div class='text-sm text-red-600'>
                                {% for error in field.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}
                        {% if field.help_text %}
                            <p class='text-sm text-muted-foreground'>{{ field.help_text }}</p>
                        {% endif %}
                    </div>
                {% endfor %}
                
                <!-- Form Actions -->
                <div class='flex justify-end space-x-2 pt-6 border-t'>
                    {% ui_button 'Cancel' variant='outline' onclick='history.back()' %}
                    {% ui_button 'Save' type='submit' variant='default' %}
                </div>
            </form>
            "
        %}
    </div>
    
    <!-- Messages -->
    {% if messages %}
        <div class="space-y-2">
            {% for message in messages %}
                {% ui_alert message.message variant=message.tags %}
            {% endfor %}
        </div>
    {% endif %}
</div>
{% endblock %}
