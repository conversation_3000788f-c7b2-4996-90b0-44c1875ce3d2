{% extends 'ui/base_modern.html' %}
{% load ui_components %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="flex-1 space-y-4 p-8 pt-6">
    <!-- Header with Actions -->
    <div class="flex items-center justify-between">
        <h2 class="text-3xl font-bold tracking-tight">{{ page_title }}</h2>
        <div class="flex items-center space-x-2">
            {% if create_url %}
                {% ui_button "Add New" variant="default" onclick="location.href='{{ create_url }}'" %}
            {% endif %}
            {% if export_enabled %}
                {% ui_button "Export" variant="outline" %}
            {% endif %}
            {% if bulk_actions %}
                {% ui_button "Bulk Actions" variant="secondary" %}
            {% endif %}
        </div>
    </div>
    
    <!-- Filters and Search -->
    {% if filters_enabled %}
    <div class="flex flex-col space-y-4 md:flex-row md:items-center md:space-y-0 md:space-x-4">
        <!-- Search -->
        <div class="flex-1">
            <form method="get" class="flex space-x-2">
                {% ui_input name="search" placeholder="Search..." value=request.GET.search %}
                {% ui_button "Search" type="submit" variant="secondary" %}
            </form>
        </div>
        
        <!-- Filters -->
        <div class="flex space-x-2">
            {% if status_filter %}
                {% ui_select name="status" options=status_options value=request.GET.status %}
            {% endif %}
            {% if date_filter %}
                {% ui_input name="date_from" type="date" placeholder="From" value=request.GET.date_from %}
                {% ui_input name="date_to" type="date" placeholder="To" value=request.GET.date_to %}
            {% endif %}
            {% ui_button "Filter" variant="outline" %}
            {% if request.GET %}
                {% ui_button "Clear" variant="ghost" onclick="location.href='{{ request.path }}'" %}
            {% endif %}
        </div>
    </div>
    {% endif %}
    
    <!-- Data Table -->
    {% ui_card 
        content="
        <div class='overflow-x-auto'>
            <table class='w-full'>
                <thead>
                    <tr class='border-b'>
                        {% if bulk_select %}
                            <th class='text-left p-4'>
                                <input type='checkbox' id='select-all' class='rounded border-input'>
                            </th>
                        {% endif %}
                        {% for header in table_headers %}
                            <th class='text-left p-4 font-medium'>{{ header.title }}</th>
                        {% endfor %}
                        {% if row_actions %}
                            <th class='text-right p-4 font-medium'>Actions</th>
                        {% endif %}
                    </tr>
                </thead>
                <tbody>
                    {% for item in object_list %}
                        <tr class='border-b hover:bg-muted/50'>
                            {% if bulk_select %}
                                <td class='p-4'>
                                    <input type='checkbox' name='selected_items' value='{{ item.pk }}' class='rounded border-input'>
                                </td>
                            {% endif %}
                            {% for field in table_fields %}
                                <td class='p-4'>{{ item|lookup:field }}</td>
                            {% endfor %}
                            {% if row_actions %}
                                <td class='p-4 text-right'>
                                    <div class='flex justify-end space-x-2'>
                                        <a href='{{ item.get_absolute_url }}' class='text-blue-600 hover:text-blue-800'>View</a>
                                        {% if item.can_edit %}
                                            <a href='{{ item.get_edit_url }}' class='text-green-600 hover:text-green-800'>Edit</a>
                                        {% endif %}
                                        {% if item.can_delete %}
                                            <a href='{{ item.get_delete_url }}' class='text-red-600 hover:text-red-800'>Delete</a>
                                        {% endif %}
                                    </div>
                                </td>
                            {% endif %}
                        </tr>
                    {% empty %}
                        <tr>
                            <td colspan='{{ table_headers|length|add:2 }}' class='p-8 text-center text-muted-foreground'>
                                No items found.
                                {% if create_url %}
                                    <a href='{{ create_url }}' class='text-blue-600 hover:text-blue-800 ml-2'>Create the first one</a>
                                {% endif %}
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        "
    %}
    
    <!-- Pagination -->
    {% if is_paginated %}
    <div class="flex items-center justify-between">
        <p class="text-sm text-muted-foreground">
            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ paginator.count }} results
        </p>
        <div class="flex items-center space-x-2">
            {% if page_obj.has_previous %}
                {% ui_button "Previous" variant="outline" onclick="location.href='?page={{ page_obj.previous_page_number }}'" %}
            {% endif %}
            
            <span class="text-sm text-muted-foreground">
                Page {{ page_obj.number }} of {{ paginator.num_pages }}
            </span>
            
            {% if page_obj.has_next %}
                {% ui_button "Next" variant="outline" onclick="location.href='?page={{ page_obj.next_page_number }}'" %}
            {% endif %}
        </div>
    </div>
    {% endif %}
    
    <!-- Messages -->
    {% if messages %}
        <div class="space-y-2">
            {% for message in messages %}
                {% ui_alert message.message variant=message.tags %}
            {% endfor %}
        </div>
    {% endif %}
</div>
{% endblock %}
