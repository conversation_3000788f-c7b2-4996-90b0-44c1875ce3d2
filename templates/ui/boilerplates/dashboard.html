{% extends 'ui/base_modern.html' %}
{% load ui_components %}

{% block title %}Dashboard{% endblock %}

{% block content %}
<div class="flex-1 space-y-4 p-8 pt-6">
    <!-- Header Section -->
    <div class="flex items-center justify-between">
        <h2 class="text-3xl font-bold tracking-tight">{{ page_title|default:"Dashboard" }}</h2>
        <div class="flex items-center space-x-2">
            {% if create_url %}
                {% ui_button "New Item" variant="default" onclick="location.href='{{ create_url }}'" %}
            {% endif %}
            {% if export_enabled %}
                {% ui_button "Export" variant="outline" %}
            {% endif %}
        </div>
    </div>
    
    <!-- Stats Grid -->
    {% if stats %}
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {% for stat in stats %}
            {% ui_card 
                title=stat.title
                content="<div class='text-2xl font-bold text-{{ stat.color|default:'foreground' }}'>{{ stat.value }}</div>"
            %}
        {% endfor %}
    </div>
    {% endif %}
    
    <!-- Main Content Grid -->
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <!-- Primary Content -->
        <div class="col-span-4">
            {% ui_card 
                title=main_section.title
                content=main_section.content
            %}
        </div>
        
        <!-- Sidebar -->
        <div class="col-span-3">
            {% if sidebar_sections %}
                {% for section in sidebar_sections %}
                    {% ui_card 
                        title=section.title
                        content=section.content
                    %}
                {% endfor %}
            {% endif %}
        </div>
    </div>
    
    <!-- Alerts/Messages -->
    {% if messages %}
        <div class="space-y-2">
            {% for message in messages %}
                {% ui_alert message.message variant=message.tags %}
            {% endfor %}
        </div>
    {% endif %}
</div>
{% endblock %}
