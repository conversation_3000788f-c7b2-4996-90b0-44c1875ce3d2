[["b6cd70c4-c431-4ba7-96d6-26e9eee1f692", {"value": {"selectedCode": "", "prefix": "# ExProject - WebApp Gestione Espropri\n", "suffix": "## Piano di Sviluppo Completo\n\n---\n\n## 📋 Overview del Progetto\n\n**ExProject** è una WebApp SaaS per la digitalizzazione completa dei procedimenti espropriativi secondo la normativa italiana (DPR 327/2001). \n\nIl sistema gestisce l'intero ciclo di vita dall'autorizzazione rilievi preliminari fino alla conclusione dell'esproprio, con workflow guidato, documentale integrato, controllo economico automatizzato e funzionalità AI assistite.\n\n**Obiettivo**: Trasformare processi cartacei complessi in workflow digitali efficienti, conformi e tracciabili.\n\n---\n\n## 🎯 Caratteristiche Principali\n\n### Gestione Multi-Ente\n- **Collaborazione controllata** tra enti (non full multitenancy)\n- **Permessi cross-tenant** su progetti specifici\n- **Ruoli granulari** per ogni figura professionale\n\n### Workflow Procedurale Completo\n- **Stati automatici** particelle secondo DPR 327/2001\n- **Scadenze monitorate** con notifiche proattive\n- **Template personalizzabili** per ogni tipologia atto\n- **Compilazione batch** per comunicazioni massive\n\n### Gestione Economica Avanzata\n- **Calcolo automatico indennità** (VAM, OMI, perizie)\n- **Controllo budget** e previsioni costi\n- **Gestione pagamenti/depositi** con integrazione CDP\n- **Report economici** dettagliati\n\n### AI Integration\n- **OCR automatico** documenti scannerizzati\n- **NLP classificazione** osservazioni cittadini\n- **Suggerimenti normativi** contestuali\n- **Predizione rischi** contenzioso\n- **Assistente compilazione** atti\n\n### Integrazioni Esterne\n- **Catasto/Sister** per dati particelle\n- **Agenzia Entrate** per anagrafe tributaria\n- **Protocollo informatico** enti\n- **Sistemi GIS/CAD** per cartografia\n\n---\n\n## 👥 Key Users\n\n### Area Amministrativa\n- **RUP (Responsabile Unico Progetto)**: coordinamento generale, controllo avanzamento\n- **RPE (Responsabile Procedimento Espropriativo)**: gestione operativa, workflow\n- **Tecnici**: rilievi, frazionamenti catastali, sopralluoghi, elaborati cartografici\n- **Legali**: redazione atti, gestione contenzioso, comunicazioni formali\n- **Dirigenti**: approvazioni, firme digitali, controllo conformità\n\n### Area Pubblica\n- **Cittadini**: consultazione informazioni generiche, contatti uffici\n\n---\n\n## ⚙️ Constraint Tecnologiche\n\n### Compliance PA Italiana\n- **WCAG 2.1 AA**: accessibilità obbligatoria\n- **GDPR**: privacy by design, audit trail\n- **Sicurezza**: crittografia, autenticazione robusta\n- **CAD**: firma digitale, conservazione sostitutiva\n\n### Performance Requirements\n- **Response time**: <2 secondi operazioni comuni\n- **Uptime**: ≥99.5% disponibilità\n- **Scalabilità**: progetti con 5000+ particelle\n- **Concorrenza**: 50+ utenti simultanei\n\n### Integrazioni Obbligatorie\n- **Sister/Catasto**: dati particelle e proprietari\n- **SPID**: autenticazione cittadini (area pubblica futura)\n- **PEC**: comunicazioni ufficiali\n- **Standards PA**: interoperabilità sistemi pubblici\n\n---\n\n## 🏗️ Stack Tecnologico\n\n### Core Stack\n```\nBackend:     Django 4.2+ + Django REST Framework\nFrontend:    HTMX + Tailwind CSS\nDatabase:    PostgreSQL 14+ + PostGIS\nAI APIs:     OpenAI + Perplexity\nStorage:     Django FileField (filesystem locale)\nDeploy:      Docker + Nginx + Gunicorn\n```\n\n### Development Tools\n```\nIDE:         Replit (prototipo) → VSCode (development)\nVCS:         GitHub con CI/CD\nTesting:     pytest + coverage\nMonitoring:  Django admin + logging\n```\n\n### Estensioni Future (Opzionali)\n```\nStorage:     MinIO (S3-compatible) per scale enterprise\nQueue:       Celery + Redis per task pesanti\nSearch:      Elasticsearch per ricerca full-text\nCache:       Redis per performance\nCDN:         CloudFlare per asset statici\n```\n\n---\n\n## 📦 Moduli da Sviluppare\n\n### 1. Core Foundation\n- **Authentication & Authorization**: Django auth + ruoli custom\n- **Multi-tenant Management**: isolamento dati per ente\n- **Project Management**: creazione/gestione progetti espropri\n- **Dashboard**: KPI, scadenze, stato avanzamento\n\n### 2. Workflow Engine\n- **State Machine**: stati particelle secondo normativa\n- **Timeline Management**: scadenze automatiche e notifiche\n- **Task Assignment**: assegnazione compiti per ruolo\n- **Approval Flows**: flussi approvazione atti\n\n### 3. Gestione Documentale\n- **Template Engine**: modelli atti personalizzabili\n- **Document Generation**: compilazione automatica batch\n- **Repository**: archiviazione strutturata con versionamento\n- **Digital Signature**: integrazione firma digitale\n\n### 4. Modulo Economico\n- **Budget Planning**: stanziamenti e controllo costi\n- **Indemnity Calculator**: calcolo automatico indennità\n- **Payment Tracking**: monitoraggio pagamenti/depositi\n- **Financial Reports**: reportistica economica\n\n### 5. Anagrafica & Catasto\n- **Owners Registry**: anagrafica proprietari\n- **Parcels Management**: gestione particelle catastali\n- **Sister Integration**: sincronizzazione dati catasto\n- **GIS Viewer**: visualizzazione mappe integrate\n\n### 6. Comunicazioni\n- **PEC Integration**: invio comunicazioni certificate\n- **Notification System**: notifiche interne/esterne\n- **Observations Management**: gestione osservazioni cittadini\n- **Publishing Tools**: albo pretorio automatico\n\n### 7. AI Services\n- **OCR Engine**: digitalizzazione documenti\n- **NLP Classifier**: categorizzazione automatica testi\n- **Risk Predictor**: algoritmi predizione rischi\n- **Legal Assistant**: suggerimenti normativi\n\n### 8. Mobile Sopralluoghi\n- **Responsive UI**: interfaccia ottimizzata mobile\n- **Offline Sync**: lavoro senza connessione\n- **Photo Geotagging**: foto georeferenziate\n- **Voice Reports**: compilazione verbali vocale\n\n### 9. Integrazioni Esterne\n- **Sister/Catasto API**: dati particelle e volture\n- **Agenzia Entrate**: anagrafe tributaria, OMI\n- **CDP Integration**: depositi indennità\n- **Protocol Systems**: protocollo informatico enti\n\n### 10. Area Pubblica\n- **Public Website**: sito vetrina informativo\n- **Contact Forms**: moduli contatti generici\n- **News System**: comunicazioni pubbliche\n- **Legal Guides**: guide procedurali cittadini\n\n---\n\n## 🗓️ Roadmap di Sviluppo\n\n### Fase 1: MVP Foundation (3 mesi)\n**Obiettivo**: Sistema base funzionante per gestione progetti semplici\n\n#### Sprint 1-2: Setup & Core (6 settimane)\n- Setup Django + PostgreSQL + deployment base\n- Sistema autenticazione e autorizzazioni\n- Modelli base (Progetti, Particelle, Proprietari)\n- Dashboard progetti con KPI essenziali\n\n#### Sprint 3: Workflow Base (3 settimane)\n- State machine particelle (stati principali)\n- Timeline scadenze base\n- Template system per 5 atti fondamentali\n- Generazione documenti singoli\n\n#### Sprint 4: Mobile Responsive (3 settimane)\n- UI HTMX responsive per mobile\n- Form sopralluoghi base\n- Upload foto con metadati\n- Sincronizzazione online basic\n\n**Deliverable**: Sistema per gestire 10-20 particelle con workflow semplificato\n\n### Fase 2: Core Complete (3 mesi)\n**Obiettivo**: Sistema completo per uso professionale\n\n#### Sprint 5-6: Modulo Economico (6 settimane)\n- Calcolo automatico indennità (VAM, OMI)\n- Budget tracking e reporting\n- Integrazione con sistemi contabili base\n- Export dati per pagamenti\n\n#### Sprint 7: Documentale Avanzato (3 settimane)\n- Template per tutti gli atti normativi\n- Compilazione batch automatica\n- Repository con ricerca e versionamento\n- Workflow approvazioni interno\n\n#### Sprint 8: Sister Integration (3 settimane)\n- API Sister per import dati catastali\n- Sincronizzazione anagrafica proprietari\n- Gestione volture e frazionamenti\n- Validazione dati catastali\n\n**Deliverable**: Sistema production-ready per progetti reali fino a 500 particelle\n\n### Fase 3: Advanced Features (3 mesi)\n**Obiettivo**: Funzionalità avanzate e AI integration\n\n#### Sprint 9: AI Core (3 settimane)\n- OCR integration per documenti\n- NLP base per classificazione testi\n- Assistente suggerimenti normativi\n- Setup infrastruttura AI\n\n#### Sprint 10: Area Pubblica (3 settimane)\n- Sito vetrina con informazioni generiche\n- Sistema news e comunicazioni\n- Form contatti e guide procedurali\n- SEO e performance optimization\n\n#### Sprint 11-12: Integrazioni Enterprise (6 settimane)\n- Agenzia Entrate API (anagrafe tributaria)\n- CDP integration per depositi\n- Protocollo informatico standard\n- PEC integration avanzata\n\n**Deliverable**: Sistema enterprise con AI e integrazioni complete\n\n### Fase 4: Scale & Production (2 mesi)\n**Obiettivo**: Ottimizzazioni e deployment production\n\n#### Sprint 13: Performance & Scale (4 settimane)\n- Ottimizzazioni database e query\n- Caching strategy\n- Load testing fino a 1000+ utenti\n- Monitoring e alerting avanzato\n\n#### Sprint 14: Production Deploy (4 settimane)\n- Setup production su Aruba Cloud\n- CI/CD pipeline completa\n- Backup strategy e disaster recovery\n- Documentazione deployment\n\n**Deliverable**: Sistema scalabile in production con SLA enterprise\n\n---\n\n## 🚀 Strategie di Deploy\n\n### Development Environment\n```bash\n# Replit per prototipo rapido\n- Setup: 1-click Django + PostgreSQL\n- Collaboration: real-time coding\n- Testing: branch preview\n- Constraints: risorse limitate\n\n# Local Development\n- Docker Compose: Django + PostgreSQL + Redis\n- Hot reload: Django runserver\n- Debug: Django Debug Toolbar\n- Testing: pytest con coverage\n```\n\n### Staging Environment\n```bash\n# Aruba Cloud VM\n- OS: Ubuntu 22.04 LTS\n- Stack: Docker + Docker Compose\n- Database: PostgreSQL in container\n- Proxy: Nginx + SSL certificato\n- Monitoring: basic health checks\n```\n\n### Production Environment\n```bash\n# Aruba Cloud Infrastructure\n- Load Balancer: Nginx (2 istanze)\n- App Servers: Django + Gunicorn (3 istanze)\n- Database: PostgreSQL cluster HA\n- Storage: NFS shared per media files\n- Backup: automated daily + weekly full\n- SSL: certificati wildcard\n- Monitoring: logs centralizzati + alerting\n```\n\n### CI/CD Pipeline\n```yaml\n# GitHub Actions\nTrigger: push to main/develop\nSteps:\n  1. Run tests (pytest + coverage)\n  2. Security scan (bandit + safety)\n  3. Build Docker images\n  4. Deploy to staging (auto)\n  5. Deploy to production (manual approval)\n  6. Health checks post-deploy\n  7. Rollback automation on failure\n```\n\n---\n\n## 🔧 Manutenzione\n\n### Operativa (Daily/Weekly)\n- **Health Monitoring**: uptime, performance, errors\n- **Backup Verification**: test restore procedures\n- **Security Updates**: OS e dipendenze critiche\n- **User Support**: help desk integrato\n- **Data Integrity**: check consistency database\n\n### Evolutiva (Monthly/Quarterly)\n- **Feature Updates**: nuove funzionalità user-driven\n- **Legal Compliance**: aggiornamenti normativi DPR 327\n- **Performance Optimization**: analisi bottleneck\n- **Security Audits**: penetration testing\n- **AI Model Updates**: miglioramenti algoritmi\n\n### Strategica (Yearly)\n- **Technology Refresh**: upgrade major dependencies\n- **Architecture Review**: scalabilità e modernizzazione\n- **Compliance Audit**: certificazioni sicurezza\n- **Disaster Recovery Test**: test completo procedure\n- **User Training**: formazione nuove funzionalità\n\n### Support Structure\n```\nLevel 1: Help desk utenti (response time: 4h)\nLevel 2: Technical support (response time: 24h)\nLevel 3: Development team (response time: 72h)\nEmergency: Critical issues (response time: 1h)\n```\n\n---\n\n## 📊 Metriche di Successo\n\n### KPI Tecnici\n- **Uptime**: >99.5%\n- **Response Time**: <2s (95th percentile)\n- **Error Rate**: <0.1%\n- **Security Incidents**: 0\n\n### KPI Business\n- **User Adoption**: 80% enti target primo anno\n- **Process Efficiency**: -50% tempo medio procedimenti\n- **Document Accuracy**: -90% errori procedurali\n- **Cost Reduction**: -30% costi amministrativi\n\n### KPI User Experience\n- **User Satisfaction**: >4.5/5\n- **Training Time**: <8h per nuovo utente\n- **Support Tickets**: <5% utenti attivi/mese\n- **Feature Adoption**: 70% nuove feature utilizzate\n\n---\n\n## 💰 Budget Stimato\n\n### Sviluppo (6-9 mesi)\n- **Team**: 2-3 sviluppatori fullstack\n- **Infrastructure**: Replit + Aruba staging\n- **Tools & Licenses**: GitHub, testing tools\n- **AI APIs**: OpenAI + Perplexity credits\n\n### Production (Annuale)\n- **Hosting**: Aruba Cloud production setup\n- **Maintenance**: 20% effort sviluppo\n- **Support**: help desk + training\n- **Compliance**: audit e certificazioni\n\n### ROI Atteso\n- **Break-even**: 12-18 mesi\n- **Scalabilità**: marginalità crescente per ente\n- **Mercato**: 8000+ comuni italiani + enti sovra-comunali\n\n---\n\n*Documento generato il 1 Giugno 2025 - Versione 1.0*", "path": "docs/exproject_plan.md", "language": "markdown", "prefixBegin": 0, "suffixEnd": 52}}], ["1d0f6d2f-be33-42e9-b302-8649a85b2a71", {"value": {"selectedCode": "", "prefix": "from django import template\nfrom django.utils.safestring import mark_safe\nfrom django.template.loader import render_to_string\nimport json\n\nregister = template.Library()\n\n\****************sion_tag('ui/components/button.html')\ndef ui_button(text=\"Button\", variant=\"default\", size=\"default\", **kwargs):\n    \"\"\"\n    Componente Button con varianti shadcn/ui style\n    \n    Varianti: default, destructive, outline, secondary, ghost, link\n    Sizes: default, sm, lg, icon\n    \"\"\"\n    return {\n        'text': text,\n        'variant': variant,\n        'size': size,\n        'attrs': kwargs\n    }\n\n\****************sion_tag('ui/components/card.html')\ndef ui_card(title=None, description=None, **kwargs):\n    \"\"\"\n    Componente Card con header, content e footer\n    \"\"\"\n    return {\n", "suffix": "        'title': title,\n        'description': description,\n        'attrs': kwargs\n    }\n\n\****************sion_tag('ui/components/badge.html')\ndef ui_badge(text, variant=\"default\", **kwargs):\n    \"\"\"\n    Componente Badge per status e labels\n    \n    Varianti: default, secondary, destructive, outline\n    \"\"\"\n    return {\n        'text': text,\n        'variant': variant,\n        'attrs': kwargs\n    }\n\n\****************sion_tag('ui/components/input.html')\ndef ui_input(name, label=None, placeholder=None, type=\"text\", **kwargs):\n    \"\"\"\n    Componente Input con label e validazione\n    \"\"\"\n    return {\n        'name': name,\n        'label': label,\n        'placeholder': placeholder,\n        'type': type,\n        'attrs': kwargs\n    }\n\n\****************sion_tag('ui/components/select.html')\ndef ui_select(name, options, label=None, **kwargs):\n    \"\"\"\n    Componente Select dropdown\n    \"\"\"\n    return {\n        'name': name,\n        'options': options,\n        'label': label,\n        'attrs': kwargs\n    }\n\n\****************sion_tag('ui/components/table.html')\ndef ui_table(headers, rows, **kwargs):\n    \"\"\"\n    Componente Table responsive\n    \"\"\"\n    return {\n        'headers': headers,\n        'rows': rows,\n        'attrs': kwargs\n    }\n\n\****************sion_tag('ui/components/alert.html')\ndef ui_alert(message, variant=\"default\", title=None, **kwargs):\n    \"\"\"\n    Componente Alert per messaggi\n    \n    Varianti: default, destructive, warning, success\n    \"\"\"\n    return {\n        'message': message,\n        'variant': variant,\n        'title': title,\n        'attrs': kwargs\n    }\n\n\****************sion_tag('ui/components/dialog.html')\ndef ui_dialog(title, content, trigger_text=\"Open\", **kwargs):\n    \"\"\"\n    Componente Dialog/Modal\n    \"\"\"\n    return {\n        'title': title,\n        'content': content,\n        'trigger_text': trigger_text,\n        'attrs': kwargs\n    }\n\n\****************sion_tag('ui/components/tabs.html')\ndef ui_tabs(tabs, **kwargs):\n    \"\"\"\n    Componente Tabs\n    \n    tabs: [{'id': 'tab1', 'label': 'Tab 1', 'content': 'Content 1'}]\n    \"\"\"\n    return {\n        'tabs': tabs,\n        'attrs': kwargs\n    }\n\n\****************sion_tag('ui/components/progress.html')\ndef ui_progress(value, max_value=100, **kwargs):\n    \"\"\"\n    Componente Progress bar\n    \"\"\"\n    percentage = (value / max_value) * 100 if max_value > 0 else 0\n    return {\n        'value': value,\n        'max_value': max_value,\n        'percentage': percentage,\n        'attrs': kwargs\n    }\n\n\****************sion_tag('ui/components/skeleton.html')\ndef ui_skeleton(width=\"100%\", height=\"20px\", **kwargs):\n    \"\"\"\n    Componente Skeleton per loading states\n    \"\"\"\n    return {\n        'width': width,\n        'height': height,\n        'attrs': kwargs\n    }\n\n\****************sion_tag('ui/components/avatar.html')\ndef ui_avatar(src=None, alt=\"Avatar\", fallback=None, size=\"default\", **kwargs):\n    \"\"\"\n    Componente Avatar\n    \n    Sizes: sm, default, lg\n    \"\"\"\n    return {\n        'src': src,\n        'alt': alt,\n        'fallback': fallback,\n        'size': size,\n        'attrs': kwargs\n    }\n\n\****************sion_tag('ui/components/separator.html')\ndef ui_separator(orientation=\"horizontal\", **kwargs):\n    \"\"\"\n    Componente Separator/Divider\n    \"\"\"\n    return {\n        'orientation': orientation,\n        'attrs': kwargs\n    }\n\n\****************sion_tag('ui/layouts/dashboard.html')\ndef ui_dashboard_layout(title, **kwargs):\n    \"\"\"\n    Layout Dashboard con sidebar e header\n    \"\"\"\n    return {\n        'title': title,\n        'attrs': kwargs\n    }\n\n\****************sion_tag('ui/layouts/form.html')\ndef ui_form_layout(title, **kwargs):\n    \"\"\"\n    Layout per form con validazione\n    \"\"\"\n    return {\n        'title': title,\n        'attrs': kwargs\n    }\n\n\n@register.simple_tag\ndef ui_icon(name, size=\"4\", **kwargs):\n    \"\"\"\n    Componente Icon usando Heroicons\n    \"\"\"\n    # Mapping degli icon names più comuni\n    icons = {\n        'plus': 'M12 4.5v15m7.5-7.5h-15',\n        'edit': 'm16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10',\n        'delete': 'm14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0',\n        'view': 'M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z',\n        'search': 'm21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z',\n        'check': 'm4.5 12.75 6 6 9-13.5',\n        'x': 'M6 18 18 6M6 6l12 12',\n        'chevron-down': 'm19.5 8.25-7.5 7.5-7.5-7.5',\n        'chevron-up': 'm4.5 15.75 7.5-7.5 7.5 7.5',\n        'chevron-left': 'M15.75 19.5 8.25 12l7.5-7.5',\n        'chevron-right': 'm8.25 4.5 7.5 7.5-7.5 7.5',\n    }\n    \n    path = icons.get(name, icons['plus'])  # Default to plus icon\n    \n    classes = f\"h-{size} w-{size}\"\n    if 'class' in kwargs:\n        classes += f\" {kwargs['class']}\"\n    \n    svg = f'''\n    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"{classes}\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"{path}\" />\n    </svg>\n    '''\n    \n    return mark_safe(svg)\n\n\n@register.simple_tag\ndef ui_theme_css():\n    \"\"\"\n    Genera CSS custom properties per il tema attivo\n    \"\"\"\n    from apps.ui.models import Theme\n    from apps.ui.design_tokens import get_css_variables, DEFAULT_THEME_CONFIG\n    \n    theme = Theme.get_active_theme()\n    if theme:\n        theme_config = {\n            \"colors\": theme.colors,\n            \"dark_colors\": theme.colors.get(\"dark\", {}),\n            \"typography\": theme.typography,\n            \"spacing\": theme.spacing,\n            \"borders\": theme.borders,\n            \"shadows\": theme.shadows,\n        }\n    else:\n        theme_config = DEFAULT_THEME_CONFIG\n    \n    css = get_css_variables(theme_config)\n    return mark_safe(f\"<style>{css}</style>\")\n\n\*****************\ndef ui_variant_classes(component_type, variant):\n    \"\"\"\n    Restituisce le classi CSS per una variante di componente\n    \"\"\"\n    variant_map = {\n        'button': {\n            'default': 'bg-primary text-primary-foreground hover:bg-primary/90',\n            'destructive': 'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n            'outline': 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\n            'secondary': 'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n            'ghost': 'hover:bg-accent hover:text-accent-foreground',\n            'link': 'text-primary underline-offset-4 hover:underline',\n        },\n        'badge': {\n            'default': 'bg-primary text-primary-foreground hover:bg-primary/80',\n            'secondary': 'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n            'destructive': 'bg-destructive text-destructive-foreground hover:bg-destructive/80',\n            'outline': 'text-foreground border border-input',\n        },\n        'alert': {\n            'default': 'bg-background text-foreground border',\n            'destructive': 'border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive',\n            'warning': 'border-warning/50 text-warning dark:border-warning [&>svg]:text-warning',\n            'success': 'border-success/50 text-success dark:border-success [&>svg]:text-success',\n        }\n    }\n    \n    return variant_map.get(component_type, {}).get(variant, '')\n", "path": "apps/ui/templatetags/ui_components.py", "language": "python", "prefixBegin": 0, "suffixEnd": 0}}], ["2d4e7309-f929-43e3-a6e0-5ca3ecb2f2f1", {"value": {"selectedCode": "", "prefix": "{% extends 'ui/base_modern.html' %}\n", "suffix": "{% load ui_components %}\n\n{% block title %}Dashboard - ExProject{% endblock %}\n\n\n\n{% block content %}\n<div class=\"flex-1 space-y-4 p-8 pt-6\">\n    <!-- Header Dashboard -->\n    <div class=\"flex items-center justify-between space-y-2\">\n        <h2 class=\"text-3xl font-bold tracking-tight\">Dashboard</h2>\n        <div class=\"flex items-center space-x-2\">\n            {% ui_button \"Nuovo Progetto\" variant=\"default\" hx_get=\"/projects/create/\" hx_target=\"#main-content\" %}\n            {% ui_button \"Altre Azioni\" variant=\"outline\" %}\n        </div>\n    </div>\n\n    <!-- Statistiche principali -->\n    <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n        {% ui_card title=\"Progetti Totali\" content=\"<div class='text-2xl font-bold'>{{ stats.projects_total|default:0 }}</div><p class='text-xs text-muted-foreground'><svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' class='inline h-3 w-3 mr-1'><path stroke-linecap='round' stroke-linejoin='round' d='M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941' /></svg>+20.1% dal mese scorso</p>\" %}\n\n        {% ui_card title=\"In Lavorazione\" content=\"<div class='text-2xl font-bold'>{{ stats.projects_active|default:0 }}</div><p class='text-xs text-muted-foreground'><svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' class='inline h-3 w-3 mr-1'><path stroke-linecap='round' stroke-linejoin='round' d='M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z' /></svg>+180.1% dal mese scorso</p>\" %}\n\n        {% ui_card title=\"Particelle Totali\" content=\"<div class='text-2xl font-bold'>{{ stats.parcels_total|default:0 }}</div><p class='text-xs text-muted-foreground'><svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' class='inline h-3 w-3 mr-1'><path stroke-linecap='round' stroke-linejoin='round' d='M9 6.75V15m6-6v8.25m.503 3.498l4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.38-1.628-1.006l-3.869 1.934c-.317.159-.69.159-1.006 0L9.503 3.252a1.125 1.125 0 00-1.006 0L3.622 5.689C3.24 5.88 3 6.27 3 6.695V19.18c0 .836.88 1.38 1.628 1.006l3.869-1.934c.317-.159.69-.159 1.006 0l4.994 2.497c.317.158.69.158 1.007 0z' /></svg>Visualizza mappa</p>\" %}\n\n        {% ui_card title=\"In Scadenza\" content=\"<div class='text-2xl font-bold'>{{ stats.expiring_tasks|default:0 }}</div><p class='text-xs text-muted-foreground'><svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' class='inline h-3 w-3 mr-1'><path stroke-linecap='round' stroke-linejoin='round' d='M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z' /></svg>Gestisci scadenze</p>\" %}\n    </div>\n\n    <!-- Moduli Principali -->\n    <div class=\"mb-8\">\n        <h2 class=\"text-xl font-semibold text-gray-900 mb-4\">Moduli Operativi</h2>\n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div class=\"module-card bg-white rounded-lg shadow border border-gray-100 overflow-hidden\">\n                <div class=\"h-2 bg-blue-600\"></div>\n                <div class=\"p-6\">\n                    <div class=\"flex items-center mb-4\">\n                        <div class=\"p-2 rounded-full bg-blue-100 mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n                            </svg>\n                        </div>\n                        <h3 class=\"text-lg font-semibold text-gray-900\">Progetti</h3>\n                    </div>\n                    <p class=\"text-gray-600 mb-4\">Gestione completa dei progetti espropriativi: creazione, pianificazione, monitoraggio avanzamento.</p>\n                    <div class=\"flex justify-between\">\n                        <a href=\"#\" hx-get=\"{% url 'projects:list' %}\" hx-target=\"#main-content\" hx-push-url=\"true\"\n                           class=\"text-blue-600 hover:text-blue-800 font-medium\">Vai ai progetti</a>\n                        <span class=\"text-gray-500\">{{ stats.projects_total|default:\"0\" }} progetti</span>\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"module-card bg-white rounded-lg shadow border border-gray-100 overflow-hidden\">\n                <div class=\"h-2 bg-green-600\"></div>\n                <div class=\"p-6\">\n                    <div class=\"flex items-center mb-4\">\n                        <div class=\"p-2 rounded-full bg-green-100 mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-green-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\" />\n                            </svg>\n                        </div>\n                        <h3 class=\"text-lg font-semibold text-gray-900\">Workflow</h3>\n                    </div>\n                    <p class=\"text-gray-600 mb-4\">Automazione dei flussi procedurali secondo DPR 327/2001 con controlli integrati e scadenzario.</p>\n                    <div class=\"flex justify-between\">\n                        <a href=\"#\" hx-get=\"{% url 'workflow:list' %}\" hx-target=\"#main-content\" hx-push-url=\"true\"\n                           class=\"text-green-600 hover:text-green-800 font-medium\">Gestione workflow</a>\n                        <span class=\"text-gray-500\">{{ stats.active_workflows|default:\"0\" }} attivi</span>\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"module-card bg-white rounded-lg shadow border border-gray-100 overflow-hidden\">\n                <div class=\"h-2 bg-purple-600\"></div>\n                <div class=\"p-6\">\n                    <div class=\"flex items-center mb-4\">\n                        <div class=\"p-2 rounded-full bg-purple-100 mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-purple-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2\" />\n                            </svg>\n                        </div>\n                        <h3 class=\"text-lg font-semibold text-gray-900\">Documenti</h3>\n                    </div>\n                    <p class=\"text-gray-600 mb-4\">Gestione documentale con template personalizzabili, generazione automatica e firma digitale.</p>\n                    <div class=\"flex justify-between\">\n                        <a href=\"#\" hx-get=\"{% url 'documents:list' %}\" hx-target=\"#main-content\" hx-push-url=\"true\"\n                           class=\"text-purple-600 hover:text-purple-800 font-medium\">Archivio documenti</a>\n                        <span class=\"text-gray-500\">{{ stats.documents_total|default:\"0\" }} documenti</span>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        <!-- Progetti Recenti -->\n        <div class=\"lg:col-span-2 bg-white rounded-lg shadow\">\n            <div class=\"px-6 py-4 border-b flex justify-between items-center\">\n                <h3 class=\"text-lg font-semibold text-gray-900\">Progetti Recenti</h3>\n                <a href=\"#\" hx-get=\"{% url 'projects:list' %}\" hx-target=\"#main-content\" hx-push-url=\"true\"\n                   class=\"text-sm text-blue-600 hover:text-blue-800\">Vedi tutti</a>\n            </div>\n            <div class=\"p-6\">\n                {% if recent_projects %}\n                <div class=\"overflow-x-auto\">\n                    <table class=\"min-w-full divide-y divide-gray-200\">\n                        <thead>\n                            <tr>\n                                <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Progetto</th>\n                                <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Stato</th>\n                                <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Budget</th>\n                                <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Ente</th>\n                                <th class=\"px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Azioni</th>\n                            </tr>\n                        </thead>\n                        <tbody class=\"bg-white divide-y divide-gray-200\">\n                            {% for project in recent_projects %}\n                            <tr class=\"hover:bg-gray-50\">\n                                <td class=\"px-6 py-4 whitespace-nowrap\">\n                                    <div class=\"flex items-center\">\n                                        <div class=\"flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium\">\n                                            {{ project.name|slice:\":1\" }}\n                                        </div>\n                                        <div class=\"ml-3\">\n                                            <a href=\"#\" class=\"text-sm font-medium text-gray-900 hover:text-blue-600\">{{ project.name }}</a>\n                                            <div class=\"text-xs text-gray-500\">Creato: {{ project.created_at|date:\"d/m/Y\" }}</div>\n                                        </div>\n                                    </div>\n                                </td>\n                                <td class=\"px-6 py-4 whitespace-nowrap\">\n                                    <span class=\"project-status-{{ project.status|lower }}\">\n                                        {{ project.get_status_display }}\n                                    </span>\n                                </td>\n                                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-700\">\n                                    € {{ project.budget|floatformat:2 }}\n                                </td>\n                                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-700\">\n                                    {{ project.entities.first.name|default:\"—\" }}\n                                </td>\n                                <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                                    <div class=\"flex justify-end space-x-2\">\n                                        <a href=\"#\" class=\"text-blue-600 hover:text-blue-900\" title=\"Visualizza\">\n                                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                                            </svg>\n                                        </a>\n                                        <a href=\"#\" class=\"text-gray-600 hover:text-gray-900\" title=\"Modifica\">\n                                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                                            </svg>\n                                        </a>\n                                    </div>\n                                </td>\n                            </tr>\n                            {% endfor %}\n                        </tbody>\n                    </table>\n                </div>\n                {% else %}\n                <div class=\"flex flex-col items-center justify-center py-8 text-center\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12 text-gray-400 mb-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n                    </svg>\n                    <p class=\"text-gray-500 mb-2\">Nessun progetto presente</p>\n                    <p class=\"text-gray-400 text-sm mb-4\">Inizia creando il tuo primo progetto espropriativo.</p>\n                    <button class=\"btn btn-primary flex items-center\"\n                            hx-get=\"{% url 'projects:create' %}\" \n                            hx-target=\"#main-content\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z\" clip-rule=\"evenodd\" />\n                        </svg>\n                        Crea Progetto\n                    </button>\n                </div>\n                {% endif %}\n            </div>\n        </div>\n\n        <!-- Notifiche e Attività Recenti -->\n        <div class=\"bg-white rounded-lg shadow\">\n            <div class=\"px-6 py-4 border-b\">\n                <h3 class=\"text-lg font-semibold text-gray-900\">Attività Recenti</h3>\n            </div>\n            <div class=\"p-6\" id=\"activities-container\">\n                {% if recent_activities %}\n                <div class=\"space-y-6\">\n                    {% for activity in recent_activities %}\n                    <div class=\"flex\">\n                        <div class=\"flex-shrink-0\">\n                            <div class=\"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600\">\n                                {{ activity.user.username|slice:\":1\" }}\n                            </div>\n                        </div>\n                        <div class=\"ml-4\">\n                            <p class=\"text-sm font-medium text-gray-900\">{{ activity.description }}</p>\n                            <p class=\"text-xs text-gray-500\">{{ activity.created_at|date:\"d/m/Y H:i\" }}</p>\n                        </div>\n                    </div>\n                    {% endfor %}\n                </div>\n                {% else %}\n                <div class=\"text-center py-8\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-10 w-10 text-gray-400 mx-auto mb-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                    <p class=\"text-gray-500\">Nessuna attività recente</p>\n                </div>\n                {% endif %}\n            </div>\n            <div class=\"px-6 py-3 bg-gray-50 border-t\">\n                <div class=\"flex justify-between items-center\">\n                    <div class=\"text-xs text-gray-500\">Aggiornato: {% now \"H:i\" %}</div>\n                    <div class=\"flex space-x-2\">\n                        <button class=\"text-sm text-blue-600 hover:text-blue-800\"\n                                hx-get=\"/exproject/activities/\" \n                                hx-target=\"#activities-container\" \n                                hx-swap=\"innerHTML\"\n                                hx-trigger=\"click\"\n                                id=\"refresh-activities-btn\"\n                                onclick=\"refreshActivities()\">\n                            Aggiorna\n                        </button>\n\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <!-- Moduli Aggiuntivi -->\n    <div class=\"mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <div class=\"bg-white rounded-lg shadow p-4 flex items-center\">\n            <div class=\"p-3 rounded-full bg-yellow-100 mr-3\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-yellow-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n            </div>\n            <div>\n                <h4 class=\"font-medium text-gray-900\">Modulo Economico</h4>\n                <a href=\"#\" class=\"text-sm text-yellow-600 hover:text-yellow-800\">Gestisci indennità →</a>\n            </div>\n        </div>\n        \n        <div class=\"bg-white rounded-lg shadow p-4 flex items-center\">\n            <div class=\"p-3 rounded-full bg-indigo-100 mr-3\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-indigo-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7\" />\n                </svg>\n            </div>\n            <div>\n                <h4 class=\"font-medium text-gray-900\">Mappa Particelle</h4>\n                <a href=\"#\" class=\"text-sm text-indigo-600 hover:text-indigo-800\">Visualizza mappa →</a>\n            </div>\n        </div>\n        \n        <div class=\"bg-white rounded-lg shadow p-4 flex items-center\">\n            <div class=\"p-3 rounded-full bg-pink-100 mr-3\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-pink-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z\" />\n                </svg>\n            </div>\n            <div>\n                <h4 class=\"font-medium text-gray-900\">Integrazioni</h4>\n                <a href=\"#\" class=\"text-sm text-pink-600 hover:text-pink-800\">Sister/Catasto →</a>\n            </div>\n        </div>\n        \n        <div class=\"bg-white rounded-lg shadow p-4 flex items-center\">\n            <div class=\"p-3 rounded-full bg-red-100 mr-3\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n            </div>\n            <div>\n                <h4 class=\"font-medium text-gray-900\">Report e Statistiche</h4>\n                <a href=\"#\" class=\"text-sm text-red-600 hover:text-red-800\">Genera report →</a>\n            </div>\n        </div>\n    </div>\n</div>\n{% endblock %}\n\n{% block extra_js %}\n<script>\n    document.addEventListener('DOMContentLoaded', function() {\n        // Debug per htmx\n        console.log('DOMContentLoaded triggered');\n        \n        // Verifica se htmx è disponibile\n        if (typeof htmx !== 'undefined') {\n            console.log('HTMX caricato correttamente', htmx.version);\n            \n            // Aggiungi un listener per gli eventi htmx\n            document.body.addEventListener('htmx:afterRequest', function(event) {\n                console.log('HTMX request completata:', event.detail);\n            });\n            \n            document.body.addEventListener('htmx:beforeRequest', function(event) {\n                console.log('HTMX request in corso:', event.detail);\n            });\n            \n            document.body.addEventListener('htmx:responseError', function(event) {\n                console.error('HTMX error:', event.detail);\n            });\n        } else {\n            console.error('HTMX non disponibile!');\n            \n            // Se HTMX non è disponibile, sostituisci con AJAX standard per aggiornare le attività\n            const refreshBtn = document.getElementById('refresh-activities-btn');\n            if (refreshBtn) {\n                refreshBtn.addEventListener('click', function(e) {\n                    e.preventDefault();\n                    const xhr = new XMLHttpRequest();\n                    xhr.open('GET', '/exproject/activities/');\n                    xhr.onload = function() {\n                        if (xhr.status === 200) {\n                            document.getElementById('activities-container').innerHTML = xhr.responseText;\n                        }\n                    };\n                    xhr.send();\n                });\n            }\n        }\n        \n        // Intervallo per aggiornare le attività ogni 5 minuti\n        setInterval(function() {\n            refreshActivities();\n        }, 300000); // 5 minuti\n    });\n    \n    // Funzione per aggiornare le attività (fallback JavaScript)\n    function refreshActivities() {\n        const activityBtn = document.getElementById('refresh-activities-btn');\n        if (activityBtn) {\n            try {\n                // Prova ad usare HTMX\n                if (typeof htmx !== 'undefined') {\n                    console.log('Triggering htmx refresh via refreshActivities()');\n                    htmx.trigger(activityBtn, 'click');\n                } else {\n                    // Fallback con XHR\n                    const xhr = new XMLHttpRequest();\n                    xhr.open('GET', '/exproject/activities/');\n                    xhr.onload = function() {\n                        if (xhr.status === 200) {\n                            document.getElementById('activities-container').innerHTML = xhr.responseText;\n                        }\n                    };\n                    xhr.send();\n                }\n            } catch (e) {\n                console.warn('Errore durante l\\'aggiornamento delle attività', e);\n            }\n        }\n    }\n</script>\n{% endblock %}", "path": "templates/core/dashboard.html", "language": "html", "prefixBegin": 0, "suffixEnd": 14}}]]