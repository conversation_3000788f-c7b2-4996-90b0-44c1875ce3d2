{"/home/<USER>/workspace/.cache/replit/toolchain.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/toolchain.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-global-state/fuzzyFsFilesIndex.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-global-state/fuzzyFsFilesIndex.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/vscode.lock": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/vscode.lock"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/agent-edits/manifest/agent-edit-shard-storage-manifest.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/92540331-b9c9-4c10-a20e-5d547fa77ed5": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/92540331-b9c9-4c10-a20e-5d547fa77ed5"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/05b8b332-d93d-4460-99a8-6715560f6616": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/05b8b332-d93d-4460-99a8-6715560f6616"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/2bee9e0f-f6cc-4cb4-bf82-780673b6e54f": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/2bee9e0f-f6cc-4cb4-bf82-780673b6e54f"}, "/home/<USER>/workspace/.cache/replit/env/latest": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/env/latest"}, "/home/<USER>/workspace/.cache/replit/env/latest.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/env/latest.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/augment.vscode-augment/augment-global-state/terminalSettings.json"}, "/home/<USER>/workspace/.gitignore": {"rootPath": "/home/<USER>/workspace", "relPath": ".giti<PERSON>re"}, "/home/<USER>/workspace/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": "package.json"}, "/home/<USER>/workspace/tests/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/__init__.py"}, "/home/<USER>/workspace/apps/core/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/core/__init__.py"}, "/home/<USER>/workspace/apps/core/migrations/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/core/migrations/__init__.py"}, "/home/<USER>/workspace/.taskmaster/templates/example_prd.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".taskmaster/templates/example_prd.txt"}, "/home/<USER>/workspace/.local/state/replit/agent/.latest.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/.latest.json"}, "/home/<USER>/workspace/.local/state/replit/agent/rapid_build_started": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/rapid_build_started"}, "/home/<USER>/workspace/.local/state/replit/agent/rapid_build_success": {"rootPath": "/home/<USER>/workspace", "relPath": ".local/state/replit/agent/rapid_build_success"}, "/home/<USER>/workspace/.cursor/mcp.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cursor/mcp.json"}, "/home/<USER>/workspace/.cursor/rules/cursor_rules.mdc": {"rootPath": "/home/<USER>/workspace", "relPath": ".cursor/rules/cursor_rules.mdc"}, "/home/<USER>/workspace/.cursor/rules/dev_workflow.mdc": {"rootPath": "/home/<USER>/workspace", "relPath": ".cursor/rules/dev_workflow.mdc"}, "/home/<USER>/workspace/.cursor/rules/self_improve.mdc": {"rootPath": "/home/<USER>/workspace", "relPath": ".cursor/rules/self_improve.mdc"}, "/home/<USER>/workspace/.cursor/rules/taskmaster.mdc": {"rootPath": "/home/<USER>/workspace", "relPath": ".cursor/rules/taskmaster.mdc"}, "/home/<USER>/workspace/.config/crossnote/config.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/crossnote/config.js"}, "/home/<USER>/workspace/.config/crossnote/head.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/crossnote/head.html"}, "/home/<USER>/workspace/.config/crossnote/parser.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/crossnote/parser.js"}, "/home/<USER>/workspace/.config/crossnote/style.less": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/crossnote/style.less"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/changelog.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/changelog.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.cs.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.cs.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.de.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.de.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.es.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.es.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.fr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.fr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.it.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.it.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.ja.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.ja.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.ko.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.ko.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.pl.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.pl.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.pt-br.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.pt-br.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.qps-ploc.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.qps-ploc.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.ru.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.ru.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.tr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.tr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.zh-cn.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.zh-cn.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.zh-tw.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/package.nls.zh-tw.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/telemetry.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/telemetry.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.cs.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.cs.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.de.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.de.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.es.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.es.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.fr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.fr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.it.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.it.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.ja.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.ja.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.ko.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.ko.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.pl.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.pl.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.pt-br.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.pt-br.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.qps-ploc.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.qps-ploc.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.ru.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.ru.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.tr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.tr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.zh-cn.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.zh-cn.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.zh-tw.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/l10n/bundle.l10n.zh-tw.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/dist/copilotDebugCommand.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/dist/copilotDebugCommand.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/dist/diffWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/dist/diffWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/dist/tfidfWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/dist/tfidfWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/dist/tikTokenizerWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/dist/tikTokenizerWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/dist/worker2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/dist/worker2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/assets/bing.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/assets/bing.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/assets/debug-icon.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/assets/debug-icon.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/assets/vscode-chat-avatar-insiders.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/assets/vscode-chat-avatar-insiders.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.27.2/assets/vscode-chat-avatar-stable.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.27.2/assets/vscode-chat-avatar-stable.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.336.0/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.336.0/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.0/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.0/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.0/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.0/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.0/NOTICE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.0/NOTICE.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.0/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.0/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.0/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.0/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.0/syntaxes/ref.tmGrammar.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.0/syntaxes/ref.tmGrammar.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.0/dist/indexWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.0/dist/indexWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.0/dist/indexWorker.js.map": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.0/dist/indexWorker.js.map"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.0/dist/language-server.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.0/dist/language-server.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.0/dist/suggestionsPanelWebview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.0/dist/suggestionsPanelWebview.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.0/dist/suggestionsPanelWebview.js.map": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.0/dist/suggestionsPanelWebview.js.map"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.326.0/assets/status/documentation.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.326.0/assets/status/documentation.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/LICENSE": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/LICENSE"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/product.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/product.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/bootstrap-fork.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/bootstrap-fork.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-cli.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/server-cli.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.fish": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.fish"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/terminal/node/ptyHostMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/files/node/watcher/watcherMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/platform/files/node/watcher/watcherMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/base/node/cpuUsage.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/base/node/cpuUsage.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/base/node/ps.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/out/vs/base/node/ps.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/jsconfig.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/jsconfig.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/tsconfig.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/schemas/tsconfig.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/create-a-js-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/create-a-js-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/debug-and-run.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/debug-and-run.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/install-node-js.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/install-node-js.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/learn-more.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/resources/walkthroughs/learn-more.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/dist/extension.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/typescript-language-features/dist/extension.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/tunnel-forwarding/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/.gitignore": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/.gitignore"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/dist/fig/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/terminal-suggest/dist/fig/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/codicon.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/codicon.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/main.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/main.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/preview-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/preview-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/preview-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/media/preview-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/simple-browser/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/syntaxes/searchResult.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/syntaxes/searchResult.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/dist/media/refresh-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/dist/media/refresh-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/dist/media/refresh-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/search-result/dist/media/refresh-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/references-view/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/dist/phpMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/php-language-features/dist/phpMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/images/code.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/images/code.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/dist/npmMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/dist/npmMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/dist/npmMain.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/npm/dist/npmMain.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/ci.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/ci.yml"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/tsconfig.browser.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/tsconfig.browser.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/425.heapsnapshotWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/425.heapsnapshotWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/848.extension.web.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/848.extension.web.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/cpu-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/cpu-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.web.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.web.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heap-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heap-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshot-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshot-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshotWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshotWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/SECURITY.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/SECURITY.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/ci.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/ci.yml"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/eslint.config.mjs": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/eslint.config.mjs"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/out/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug-companion/out/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/telemetry.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/telemetry.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/bootloader.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/bootloader.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/diagnosticTool.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/diagnosticTool.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/diagnosticTool.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/diagnosticTool.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/hash.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/hash.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/renameWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/renameWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/watchdog.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/watchdog.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/vendor/acorn-loose.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/vendor/acorn-loose.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/vendor/acorn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/vendor/acorn.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/targets/node/terminateProcess.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/src/targets/node/terminateProcess.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/logo.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/logo.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/configure.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/configure.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/connect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/connect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/disconnect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/disconnect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/node.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/node.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/open-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/open-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/page.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/page.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/pause.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/pause.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/restart.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/restart.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/resume.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/resume.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/service-worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/service-worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/stop.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/stop.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/light/worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/configure.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/configure.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/connect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/connect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/disconnect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/disconnect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/node.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/node.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/open-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/open-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/page.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/page.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/pause.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/pause.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/restart.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/restart.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/resume.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/resume.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/service-worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/service-worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/stop-profiling.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/stop-profiling.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/stop.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/stop.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ms-vscode.js-debug/resources/dark/worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/media/auth.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/media/auth.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/media/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/media/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/dist/extension.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/microsoft-authentication/dist/extension.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/dist/mergeConflictMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/dist/mergeConflictMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/dist/mergeConflictMain.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/merge-conflict/dist/mergeConflictMain.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/audioPreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/audioPreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/audioPreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/audioPreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/imagePreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/imagePreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/imagePreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/imagePreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading-hc.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading-hc.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/loading.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/videoPreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/videoPreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/videoPreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/media/videoPreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/media-preview/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-block.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-block.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-fence.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-fence.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-inline.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math-inline.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/syntaxes/md-math.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/preview-styles/index.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/preview-styles/index.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/notebook-out/katex.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/notebook-out/katex.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/notebook-out/katex.min.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/notebook-out/katex.min.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-math/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/notebook-out/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/notebook-out/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/highlight.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/highlight.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/markdown.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/markdown.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/pre.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/pre.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/preview-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/preview-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/preview-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/media/preview-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/dist/extension.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/dist/extension.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/dist/serverWorkerMain.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/markdown-language-features/dist/serverWorkerMain.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/dist/node/774.jsonServerMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/dist/node/774.jsonServerMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/dist/node/875.jsonServerMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/dist/node/875.jsonServerMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/dist/node/jsonServerMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/server/dist/node/jsonServerMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/client/dist/node/jsonClientMain.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/json-language-features/client/dist/node/jsonClientMain.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/dist/main.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/jake/dist/main.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/notebook-out/cellAttachmentRenderer.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/notebook-out/cellAttachmentRenderer.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/dist/ipynbMain.node.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/dist/ipynbMain.node.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/dist/notebookSerializerWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/ipynb/dist/notebookSerializerWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/lib/jquery.d.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/lib/jquery.d.ts"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/dist/node/421.htmlServerMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/dist/node/421.htmlServerMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/dist/node/490.htmlServerMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/dist/node/490.htmlServerMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/dist/node/htmlServerMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/server/dist/node/htmlServerMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/client/dist/node/htmlClientMain.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/html-language-features/client/dist/node/htmlClientMain.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/dist/main.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/gulp/dist/main.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/dist/main.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/grunt/dist/main.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/media/auth.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/media/auth.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/media/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/media/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/dist/extension.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github-authentication/dist/extension.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/markdown.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/markdown.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/some-markdown.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/some-markdown.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/dist/extension.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/github/dist/extension.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/git-commit.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/git-commit.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/git-rebase.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/git-rebase.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/ignore.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/syntaxes/ignore.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/git-commit.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/git-commit.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/git-rebase.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/git-rebase.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/ignore.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/languages/ignore.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git-base/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/emojis.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/emojis.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-added.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-added.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-conflict.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-conflict.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-copied.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-copied.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-deleted.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-deleted.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-ignored.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-ignored.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-modified.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-modified.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-renamed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-renamed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-type-changed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-type-changed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-untracked.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/light/status-untracked.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-added.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-added.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-conflict.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-conflict.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-copied.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-copied.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-deleted.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-deleted.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-ignored.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-ignored.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-modified.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-modified.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-renamed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-renamed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-type-changed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-type-changed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-untracked.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/resources/icons/dark/status-untracked.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/askpass-empty.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/askpass-empty.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/askpass-main.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/askpass-main.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/askpass.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/askpass.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/git-editor-empty.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/git-editor-empty.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/git-editor-main.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/git-editor-main.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/git-editor.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/git-editor.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/main.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/main.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/ssh-askpass-empty.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/ssh-askpass-empty.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/ssh-askpass.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/git/dist/ssh-askpass.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/extension-editing/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/extension-editing/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/extension-editing/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/extension-editing/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/extension-editing/dist/extensionEditingMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/extension-editing/dist/extensionEditingMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/dist/node/emmetNodeMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/emmet/dist/node/emmetNodeMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-server-ready/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-server-ready/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-server-ready/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-server-ready/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-server-ready/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-server-ready/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/debug-auto-launch/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/server/dist/node/85.cssServerMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/server/dist/node/85.cssServerMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/server/dist/node/cssServerMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/server/dist/node/cssServerMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/client/dist/node/cssClientMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/css-language-features/client/dist/node/cssClientMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/schemas/attachContainer.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/schemas/attachContainer.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/dist/configurationEditingMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/extensions/configuration-editing/dist/configurationEditingMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/code-server": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/code-server"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/remote-cli/code": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/remote-cli/code"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/helpers/browser.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/helpers/browser.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/helpers/check-requirements.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/server/bin/helpers/check-requirements.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/tunnel-forwarding/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/tunnel-forwarding/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/terminal-suggest/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/terminal-suggest/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/references-view/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/references-view/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/php-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/php-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/merge-conflict/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/merge-conflict/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/jake/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/jake/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/grunt/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/grunt/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/extension-editing/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/extension-editing/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/emmet/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/emmet/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-server-ready/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-server-ready/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-auto-launch/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-auto-launch/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/server/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/server/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/configuration-editing/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/configuration-editing/package.json"}, "/home/<USER>/workspace/.cache/replit/modules.stamp": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules.stamp"}, "/home/<USER>/workspace/.cache/replit/modules/nodejs-20.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/nodejs-20.res"}, "/home/<USER>/workspace/.cache/replit/modules/postgresql-16.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/postgresql-16.res"}, "/home/<USER>/workspace/.cache/replit/modules/python-3.11.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/python-3.11.res"}, "/home/<USER>/workspace/.cache/replit/modules/replit.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/replit.res"}, "/home/<USER>/workspace/.cache/pip/http-v2/1/6/f/0/e/16f0e53ff20915d12eea5b53ad5e60d26f47e0f8cf805168537c9b3a.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/1/6/f/0/e/16f0e53ff20915d12eea5b53ad5e60d26f47e0f8cf805168537c9b3a.body"}, "/home/<USER>/workspace/.replit": {"rootPath": "/home/<USER>/workspace", "relPath": ".replit"}, "/home/<USER>/workspace/CLAUDE.md": {"rootPath": "/home/<USER>/workspace", "relPath": "CLAUDE.md"}, "/home/<USER>/workspace/CONTRIBUTING.md": {"rootPath": "/home/<USER>/workspace", "relPath": "CONTRIBUTING.md"}, "/home/<USER>/workspace/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": "README.md"}, "/home/<USER>/workspace/cookies.txt": {"rootPath": "/home/<USER>/workspace", "relPath": "cookies.txt"}, "/home/<USER>/workspace/manage.py": {"rootPath": "/home/<USER>/workspace", "relPath": "manage.py"}, "/home/<USER>/workspace/package-lock.json": {"rootPath": "/home/<USER>/workspace", "relPath": "package-lock.json"}, "/home/<USER>/workspace/schema.yml": {"rootPath": "/home/<USER>/workspace", "relPath": "schema.yml"}, "/home/<USER>/workspace/working_schema.yml": {"rootPath": "/home/<USER>/workspace", "relPath": "working_schema.yml"}, "/home/<USER>/workspace/tests/test_login.py": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/test_login.py"}, "/home/<USER>/workspace/tests/test_minimal_api.py": {"rootPath": "/home/<USER>/workspace", "relPath": "tests/test_minimal_api.py"}, "/home/<USER>/workspace/templates/base.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/base.html"}, "/home/<USER>/workspace/templates/workflow/list.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/workflow/list.html"}, "/home/<USER>/workspace/templates/public/about.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/public/about.html"}, "/home/<USER>/workspace/templates/public/contacts.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/public/contacts.html"}, "/home/<USER>/workspace/templates/public/home.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/public/home.html"}, "/home/<USER>/workspace/templates/public/news.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/public/news.html"}, "/home/<USER>/workspace/templates/public/procedures.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/public/procedures.html"}, "/home/<USER>/workspace/templates/projects/create.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/projects/create.html"}, "/home/<USER>/workspace/templates/projects/delete.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/projects/delete.html"}, "/home/<USER>/workspace/templates/projects/detail.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/projects/detail.html"}, "/home/<USER>/workspace/templates/projects/edit.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/projects/edit.html"}, "/home/<USER>/workspace/templates/projects/list.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/projects/list.html"}, "/home/<USER>/workspace/templates/integrations/list.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/integrations/list.html"}, "/home/<USER>/workspace/templates/economic/list.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/economic/list.html"}, "/home/<USER>/workspace/templates/documents/list.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/documents/list.html"}, "/home/<USER>/workspace/templates/core/activities.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/activities.html"}, "/home/<USER>/workspace/templates/core/dashboard.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/dashboard.html"}, "/home/<USER>/workspace/templates/core/login.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/login.html"}, "/home/<USER>/workspace/templates/core/notifications.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/notifications.html"}, "/home/<USER>/workspace/templates/core/profile.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/profile.html"}, "/home/<USER>/workspace/templates/core/search.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/search.html"}, "/home/<USER>/workspace/templates/core/settings.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/settings.html"}, "/home/<USER>/workspace/static/base.css": {"rootPath": "/home/<USER>/workspace", "relPath": "static/base.css"}, "/home/<USER>/workspace/exproject/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "exproject/__init__.py"}, "/home/<USER>/workspace/exproject/asgi.py": {"rootPath": "/home/<USER>/workspace", "relPath": "exproject/asgi.py"}, "/home/<USER>/workspace/exproject/urls.py": {"rootPath": "/home/<USER>/workspace", "relPath": "exproject/urls.py"}, "/home/<USER>/workspace/exproject/wsgi.py": {"rootPath": "/home/<USER>/workspace", "relPath": "exproject/wsgi.py"}, "/home/<USER>/workspace/exproject/settings/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "exproject/settings/__init__.py"}, "/home/<USER>/workspace/exproject/settings/prod.py": {"rootPath": "/home/<USER>/workspace", "relPath": "exproject/settings/prod.py"}, "/home/<USER>/workspace/apps/workflow/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/workflow/__init__.py"}, "/home/<USER>/workspace/apps/workflow/admin.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/workflow/admin.py"}, "/home/<USER>/workspace/apps/workflow/apps.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/workflow/apps.py"}, "/home/<USER>/workspace/apps/workflow/models.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/workflow/models.py"}, "/home/<USER>/workspace/apps/workflow/tests.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/workflow/tests.py"}, "/home/<USER>/workspace/apps/workflow/urls.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/workflow/urls.py"}, "/home/<USER>/workspace/apps/workflow/views.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/workflow/views.py"}, "/home/<USER>/workspace/apps/workflow/migrations/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/workflow/migrations/__init__.py"}, "/home/<USER>/workspace/apps/public/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/public/__init__.py"}, "/home/<USER>/workspace/apps/public/admin.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/public/admin.py"}, "/home/<USER>/workspace/apps/public/apps.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/public/apps.py"}, "/home/<USER>/workspace/apps/public/models.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/public/models.py"}, "/home/<USER>/workspace/apps/public/tests.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/public/tests.py"}, "/home/<USER>/workspace/apps/public/urls.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/public/urls.py"}, "/home/<USER>/workspace/apps/public/views.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/public/views.py"}, "/home/<USER>/workspace/apps/public/migrations/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/public/migrations/__init__.py"}, "/home/<USER>/workspace/apps/projects/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/projects/__init__.py"}, "/home/<USER>/workspace/apps/projects/admin.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/projects/admin.py"}, "/home/<USER>/workspace/apps/projects/apps.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/projects/apps.py"}, "/home/<USER>/workspace/apps/projects/models.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/projects/models.py"}, "/home/<USER>/workspace/apps/projects/tests.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/projects/tests.py"}, "/home/<USER>/workspace/apps/projects/urls.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/projects/urls.py"}, "/home/<USER>/workspace/apps/projects/views.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/projects/views.py"}, "/home/<USER>/workspace/apps/projects/migrations/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/projects/migrations/__init__.py"}, "/home/<USER>/workspace/apps/integrations/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/integrations/__init__.py"}, "/home/<USER>/workspace/apps/integrations/admin.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/integrations/admin.py"}, "/home/<USER>/workspace/apps/integrations/apps.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/integrations/apps.py"}, "/home/<USER>/workspace/apps/integrations/models.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/integrations/models.py"}, "/home/<USER>/workspace/apps/integrations/tests.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/integrations/tests.py"}, "/home/<USER>/workspace/apps/integrations/urls.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/integrations/urls.py"}, "/home/<USER>/workspace/apps/integrations/views.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/integrations/views.py"}, "/home/<USER>/workspace/apps/integrations/migrations/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/integrations/migrations/__init__.py"}, "/home/<USER>/workspace/apps/economic/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/economic/__init__.py"}, "/home/<USER>/workspace/apps/economic/admin.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/economic/admin.py"}, "/home/<USER>/workspace/apps/economic/apps.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/economic/apps.py"}, "/home/<USER>/workspace/apps/economic/models.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/economic/models.py"}, "/home/<USER>/workspace/apps/economic/tests.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/economic/tests.py"}, "/home/<USER>/workspace/apps/economic/urls.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/economic/urls.py"}, "/home/<USER>/workspace/apps/economic/views.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/economic/views.py"}, "/home/<USER>/workspace/apps/economic/migrations/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/economic/migrations/__init__.py"}, "/home/<USER>/workspace/apps/documents/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/documents/__init__.py"}, "/home/<USER>/workspace/apps/documents/admin.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/documents/admin.py"}, "/home/<USER>/workspace/apps/documents/apps.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/documents/apps.py"}, "/home/<USER>/workspace/apps/documents/models.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/documents/models.py"}, "/home/<USER>/workspace/apps/documents/tests.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/documents/tests.py"}, "/home/<USER>/workspace/apps/documents/urls.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/documents/urls.py"}, "/home/<USER>/workspace/apps/documents/views.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/documents/views.py"}, "/home/<USER>/workspace/apps/documents/migrations/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/documents/migrations/__init__.py"}, "/home/<USER>/workspace/apps/core/admin.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/core/admin.py"}, "/home/<USER>/workspace/apps/core/apps.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/core/apps.py"}, "/home/<USER>/workspace/apps/core/tests.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/core/tests.py"}, "/home/<USER>/workspace/apps/core/urls.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/core/urls.py"}, "/home/<USER>/workspace/apps/core/views.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/core/views.py"}, "/home/<USER>/workspace/apps/api/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/api/__init__.py"}, "/home/<USER>/workspace/apps/api/admin.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/api/admin.py"}, "/home/<USER>/workspace/apps/api/apps.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/api/apps.py"}, "/home/<USER>/workspace/apps/api/exceptions.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/api/exceptions.py"}, "/home/<USER>/workspace/apps/api/middleware.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/api/middleware.py"}, "/home/<USER>/workspace/apps/api/models.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/api/models.py"}, "/home/<USER>/workspace/apps/api/permissions.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/api/permissions.py"}, "/home/<USER>/workspace/apps/api/tests.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/api/tests.py"}, "/home/<USER>/workspace/apps/api/urls.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/api/urls.py"}, "/home/<USER>/workspace/apps/api/migrations/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/api/migrations/__init__.py"}, "/home/<USER>/workspace/.upm/store.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".upm/store.json"}, "/home/<USER>/workspace/.taskmaster/config.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".taskmaster/config.json"}, "/home/<USER>/workspace/.taskmaster/tasks/task_001.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".taskmaster/tasks/task_001.txt"}, "/home/<USER>/workspace/.taskmaster/tasks/task_002.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".taskmaster/tasks/task_002.txt"}, "/home/<USER>/workspace/.taskmaster/tasks/task_003.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".taskmaster/tasks/task_003.txt"}, "/home/<USER>/workspace/.taskmaster/tasks/task_004.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".taskmaster/tasks/task_004.txt"}, "/home/<USER>/workspace/.taskmaster/tasks/task_005.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".taskmaster/tasks/task_005.txt"}, "/home/<USER>/workspace/.taskmaster/tasks/task_006.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".taskmaster/tasks/task_006.txt"}, "/home/<USER>/workspace/.taskmaster/tasks/task_007.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".taskmaster/tasks/task_007.txt"}, "/home/<USER>/workspace/.taskmaster/tasks/task_008.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".taskmaster/tasks/task_008.txt"}, "/home/<USER>/workspace/.taskmaster/docs/exproject_prd.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".taskmaster/docs/exproject_prd.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/.obsolete": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/.obsolete"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/extensions.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/extensions.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/changelog.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/changelog.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.cs.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.cs.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.de.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.de.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.es.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.es.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.fr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.fr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.it.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.it.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.ja.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.ja.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.ko.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.ko.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.pl.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.pl.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.pt-br.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.pt-br.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.qps-ploc.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.qps-ploc.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.ru.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.ru.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.tr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.tr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.zh-cn.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.zh-cn.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.zh-tw.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/package.nls.zh-tw.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/telemetry.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/telemetry.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.cs.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.cs.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.de.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.de.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.es.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.es.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.fr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.fr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.it.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.it.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.ja.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.ja.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.ko.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.ko.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.pl.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.pl.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.pt-br.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.pt-br.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.qps-ploc.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.qps-ploc.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.ru.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.ru.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.tr.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.tr.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.zh-cn.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.zh-cn.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.zh-tw.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/l10n/bundle.l10n.zh-tw.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/copilotDebugCommand.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/copilotDebugCommand.js"}, "/home/<USER>/workspace/exproject/settings/base.py": {"rootPath": "/home/<USER>/workspace", "relPath": "exproject/settings/base.py"}, "/home/<USER>/workspace/exproject/settings/dev.py": {"rootPath": "/home/<USER>/workspace", "relPath": "exproject/settings/dev.py"}, "/home/<USER>/workspace/docs/DataModel.md": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/DataModel.md"}, "/home/<USER>/workspace/docs/exproject_plan.md": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/exproject_plan.md"}, "/home/<USER>/workspace/data/create_demo_data.py": {"rootPath": "/home/<USER>/workspace", "relPath": "data/create_demo_data.py"}, "/home/<USER>/workspace/data/create_working_schema.py": {"rootPath": "/home/<USER>/workspace", "relPath": "data/create_working_schema.py"}, "/home/<USER>/workspace/data/fix_swagger.py": {"rootPath": "/home/<USER>/workspace", "relPath": "data/fix_swagger.py"}, "/home/<USER>/workspace/apps/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/__init__.py"}, "/home/<USER>/workspace/apps/core/models.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/core/models.py"}, "/home/<USER>/workspace/apps/core/migrations/0001_initial.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/core/migrations/0001_initial.py"}, "/home/<USER>/workspace/apps/api/serializers.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/api/serializers.py"}, "/home/<USER>/workspace/apps/api/views.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/api/views.py"}, "/home/<USER>/workspace/.taskmaster/tasks/task_009.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".taskmaster/tasks/task_009.txt"}, "/home/<USER>/workspace/.taskmaster/tasks/task_010.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".taskmaster/tasks/task_010.txt"}, "/home/<USER>/workspace/.taskmaster/reports/task-complexity-report.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".taskmaster/reports/task-complexity-report.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/assets/vscode-chat-avatar-stable.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/assets/vscode-chat-avatar-stable.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.336.0/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.336.0/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.336.0/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.336.0/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.336.0/NOTICE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.336.0/NOTICE.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.336.0/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.336.0/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.336.0/syntaxes/ref.tmGrammar.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.336.0/syntaxes/ref.tmGrammar.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.336.0/dist/indexWorker.js.map": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.336.0/dist/indexWorker.js.map"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.336.0/dist/language-server.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.336.0/dist/language-server.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.336.0/dist/suggestionsPanelWebview.js.map": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.336.0/dist/suggestionsPanelWebview.js.map"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/activitybar.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/activitybar.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/panel-icon-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/panel-icon-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/panel-icon-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/panel-icon-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/bg-next-edit-applied-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/bg-next-edit-applied-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/bg-next-edit-gray-hook.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/bg-next-edit-gray-hook.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/bg-next-edit-gray-line.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/bg-next-edit-gray-line.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/bg-next-edit-inactive-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/bg-next-edit-inactive-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/bg-next-edit-inactive-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/bg-next-edit-inactive-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/bg-next-edit-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/bg-next-edit-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/left-dark-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/left-dark-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/left-dark-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/left-dark-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/left-light-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/left-light-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/left-light-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/left-light-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-addition-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-addition-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-addition-inbetween-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-addition-inbetween-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-addition-inbetween-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-addition-inbetween-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-addition-inbetween-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-addition-inbetween-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-addition-inbetween-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-addition-inbetween-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-addition-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-addition-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-addition-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-addition-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-addition-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-addition-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-applied-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-applied-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-applied-inbetween-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-applied-inbetween-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-applied-inbetween-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-applied-inbetween-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-applied-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-applied-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-available-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-available-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-available-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-available-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-change-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-change-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-change-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-change-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-change-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-change-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-update-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-update-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-update-loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-update-loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-update-loading-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-update-loading-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/right-dark-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/right-dark-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/right-dark-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/right-dark-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/right-light-disabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/right-light-disabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/right-light-enabled.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/right-light-enabled.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/a.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/a.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/alt.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/alt.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/b.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/b.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/backspace.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/backspace.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/c.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/c.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/command.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/command.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/control.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/control.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/ctrl.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/ctrl.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/d.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/d.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/delete.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/delete.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/e.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/e.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/escape.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/escape.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/f.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/f.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/g.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/g.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/h.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/h.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/i.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/i.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/j.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/j.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/k.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/k.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/l.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/l.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/m.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/m.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/meta.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/meta.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/n.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/n.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/o.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/o.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/option.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/option.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/p.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/p.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/q.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/q.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/r.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/r.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/return.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/return.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/s.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/s.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/semicolon.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/semicolon.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/shift.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/shift.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/t.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/t.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/tab.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/tab.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/u.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/u.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/v.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/v.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/w.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/w.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/win.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/win.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/x.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/x.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/y.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/y.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/z.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/light/z.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/a.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/a.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/alt.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/alt.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/b.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/b.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/backspace.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/backspace.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/c.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/c.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/command.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/command.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/control.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/control.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/ctrl.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/ctrl.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/d.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/d.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/delete.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/delete.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/e.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/e.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/escape.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/escape.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/f.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/f.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/g.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/g.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/h.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/h.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/i.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/i.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/j.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/j.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/k.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/k.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/l.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/l.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/m.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/m.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/meta.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/meta.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/n.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/n.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/o.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/o.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/option.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/option.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/p.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/p.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/q.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/q.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/diffWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/diffWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/tfidfWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/tfidfWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/tikTokenizerWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/tikTokenizerWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/assets/debug-icon.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/assets/debug-icon.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.336.0/dist/indexWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.336.0/dist/indexWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.336.0/dist/suggestionsPanelWebview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.336.0/dist/suggestionsPanelWebview.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-1.336.0/assets/status/documentation.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-1.336.0/assets/status/documentation.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/CHANGELOG.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/CHANGELOG.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/bg-next-edit-applied-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/bg-next-edit-applied-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/bg-next-edit-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/bg-next-edit-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-change-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-change-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-deletion-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-deletion-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-loading-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-loading-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-rejected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-rejected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-unavailable-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-unavailable-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-unavailable-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-unavailable-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-update-complete-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-update-complete-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-update-complete-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-update-complete-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-update-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-update-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-update-disabled-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-update-disabled-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-update-disabled-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-update-disabled-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/r.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/r.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/return.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/return.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/s.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/s.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/semicolon.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/semicolon.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/shift.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/shift.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/t.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/t.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/tab.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/tab.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/u.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/u.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/v.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/v.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/w.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/w.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/win.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/win.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/x.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/x.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/y.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/y.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/z.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/keyboard/dark/z.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/autofix.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/autofix.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/diff-view.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/diff-view.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/history.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/history.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/main-panel.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/main-panel.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/memories.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/memories.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/next-edit-suggestions.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/next-edit-suggestions.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/preference.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/preference.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/remote-agent-diff.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/remote-agent-diff.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/remote-agent-home.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/remote-agent-home.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/rules.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/rules.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/settings.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/settings.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/AugmentMessage-DL2hk-JX.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/AugmentMessage-DL2hk-JX.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/BaseButton-B2NZuaj3.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/BaseButton-B2NZuaj3.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/BaseButton-BqzdgpkK.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/BaseButton-BqzdgpkK.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ButtonAugment-CNK8zC8i.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ButtonAugment-CNK8zC8i.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ButtonAugment-DhtPLzGu.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ButtonAugment-DhtPLzGu.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/CalloutAugment-BFrX0piu.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/CalloutAugment-BFrX0piu.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/CalloutAugment-Dvw-pMXw.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/CalloutAugment-Dvw-pMXw.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/CardAugment-BAo8Ti0V.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/CardAugment-BAo8Ti0V.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/CardAugment-RumqAz-v.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/CardAugment-RumqAz-v.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/CollapseButtonAugment-B1-rUMk8.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/CollapseButtonAugment-B1-rUMk8.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/CollapseButtonAugment-D3vAw6HE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/CollapseButtonAugment-D3vAw6HE.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/Content-BiWRcmeV.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/Content-BiWRcmeV.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/Content-LuLOeTld.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/Content-LuLOeTld.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/CopyButton-C581jDHd.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/CopyButton-C581jDHd.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/CopyButton-CugjC8Pf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/CopyButton-CugjC8Pf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/Drawer-CBKSV_8p.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/Drawer-CBKSV_8p.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/Drawer-DwFbLE28.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/Drawer-DwFbLE28.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/Filespan-D-BqE8vd.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/Filespan-D-BqE8vd.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/Filespan-tclW2Ian.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/Filespan-tclW2Ian.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/IconButtonAugment-BjDqXmYl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/IconButtonAugment-BjDqXmYl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/IconButtonAugment-CA6XnfI-.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/IconButtonAugment-CA6XnfI-.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/IconFilePath-B4JAagx1.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/IconFilePath-B4JAagx1.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/IconFilePath-CiKel2Kp.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/IconFilePath-CiKel2Kp.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/Keybindings-BFFBoxX3.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/Keybindings-BFFBoxX3.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/LanguageIcon-D78BqCXT.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/LanguageIcon-D78BqCXT.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/LanguageIcon-FVMxq7uD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/LanguageIcon-FVMxq7uD.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/MarkdownEditor-ChD76zyi.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/MarkdownEditor-ChD76zyi.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/MaterialIcon-8-Z76Y2_.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/MaterialIcon-8-Z76Y2_.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/MessageList-BcJeGKHK.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/MessageList-BcJeGKHK.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/MessageList-DRTeF5X0.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/MessageList-DRTeF5X0.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/NextEditSuggestions-CwVP_7hG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/NextEditSuggestions-CwVP_7hG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/OpenFileButton-BO1gXf_-.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/OpenFileButton-BO1gXf_-.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/OpenFileButton-DgvbNVLn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/OpenFileButton-DgvbNVLn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/RemoteAgentRetry-BpNKi_iC.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/RemoteAgentRetry-BpNKi_iC.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/RemoteAgentSetup-CE-yp3a6.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/RemoteAgentSetup-CE-yp3a6.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/RulesDropdown-Df3D6er-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/RulesDropdown-Df3D6er-.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/elixir-BRjLKONM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/elixir-BRjLKONM.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/elixir-nOQiPlLZ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/elixir-nOQiPlLZ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ellipsis-Cm0UKVWz.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ellipsis-Cm0UKVWz.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/erDiagram-6RL3IURR-CLe-9k0o.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/erDiagram-6RL3IURR-CLe-9k0o.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/exclamation-triangle-BbVpV4C-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/exclamation-triangle-BbVpV4C-.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/expand-CURYX9ur.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/expand-CURYX9ur.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/file-paths-BcSg4gks.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/file-paths-BcSg4gks.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/file-reader-BZAZY_XQ.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/file-reader-BZAZY_XQ.css"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-global-state/fuzzyFsFoldersIndex.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-global-state/fuzzyFsFoldersIndex.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/log.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/log.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/index-BxQII05L.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/index-BxQII05L.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chunk-7U56Z5CX-RS-bkm5I.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chunk-7U56Z5CX-RS-bkm5I.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/diff-view-Brb4enjj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/diff-view-Brb4enjj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/fsharp-fd1GTHhf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/fsharp-fd1GTHhf.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/media/preview-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/media/preview-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/kotlin-CUUhw8ZM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/kotlin-CUUhw8ZM.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/lexon-Canl7DCW.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/lexon-Canl7DCW.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/bat-CtWuqYvB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/bat-CtWuqYvB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ordinal-_rw2EY4v.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ordinal-_rw2EY4v.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/images/code.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/images/code.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/augment-logo-DdgjewTP.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/augment-logo-DdgjewTP.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/github-7gPAsyj4.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/github-7gPAsyj4.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/html-Dv6uDOCE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/html-Dv6uDOCE.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/c4Diagram-6F5ED5ID-COd1ejrS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/c4Diagram-6F5ED5ID-COd1ejrS.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/Keybindings-C3J8hU1V.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/Keybindings-C3J8hU1V.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/dockerfile-DLk6rpji.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/dockerfile-DLk6rpji.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cypher-D84EuPTj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cypher-D84EuPTj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/htmlMode-BuVpxTb-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/htmlMode-BuVpxTb-.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/julia-ClS8lr_N.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/julia-ClS8lr_N.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/_basePickBy-DQ72Vnej.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/_basePickBy-DQ72Vnej.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pascaligo-5jv8CcQD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pascaligo-5jv8CcQD.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/TextTooltipAugment-CXnRMJBa.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/TextTooltipAugment-CXnRMJBa.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/apex-DFVco9Dq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/apex-DFVco9Dq.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/css-DQU6DXDx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/css-DQU6DXDx.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/init-g68aIKmP.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/init-g68aIKmP.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/media/main.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/media/main.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/index-DhtTPDph.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/index-DhtTPDph.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/_baseUniq-8WCPFFgF.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/_baseUniq-8WCPFFgF.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/next-edit-suggestions-UoxOVKUP.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/next-edit-suggestions-UoxOVKUP.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/media/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/media/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/main-panel-YmaBgMfd.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/main-panel-YmaBgMfd.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/csharp-1bC6NAu3.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/csharp-1bC6NAu3.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/php-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/php-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/bicep-BZbtZWRn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/bicep-BZbtZWRn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/less-CW-yd8b8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/less-CW-yd8b8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/diff-view-BoyKRB4C.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/diff-view-BoyKRB4C.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/arrow-up-right-from-square-Df_FYENN.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/arrow-up-right-from-square-Df_FYENN.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/php-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/php-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/gitGraphDiagram-NRZ2UAAF-BCecANDn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/gitGraphDiagram-NRZ2UAAF-BCecANDn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ini-COn9E3gi.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ini-COn9E3gi.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/csharp-BoL64M5l.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/csharp-BoL64M5l.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/liquid-CPIgs5dT.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/liquid-CPIgs5dT.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/markdown-9NNSJ0ww.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/markdown-9NNSJ0ww.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/NextEditSuggestions-C1kwmzU5.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/NextEditSuggestions-C1kwmzU5.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/dist/media/refresh-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/dist/media/refresh-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/assets/vscode-chat-avatar-insiders.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/assets/vscode-chat-avatar-insiders.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/bat-DPkNLes8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/bat-DPkNLes8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cssMode-BBxcXVId.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cssMode-BBxcXVId.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/bicep-C6yweCii.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/bicep-C6yweCii.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/StatusIndicator-BAEKlH2H.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/StatusIndicator-BAEKlH2H.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/architectureDiagram-UYN6MBPD-BjSJWx3j.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/architectureDiagram-UYN6MBPD-BjSJWx3j.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/dockerfile-CuMHdPl5.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/dockerfile-CuMHdPl5.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/hcl-CVzGlmMO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/hcl-CVzGlmMO.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/worker2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/dist/worker2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cytoscape.esm-B0yNE0-9.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cytoscape.esm-B0yNE0-9.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/notebook-out/katex.min.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/notebook-out/katex.min.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/RemoteAgentSetup-Chf1mksH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/RemoteAgentSetup-Chf1mksH.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/dart-D8lhlL1r.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/dart-D8lhlL1r.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/azcli-1IWB1ccx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/azcli-1IWB1ccx.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/history-BaUY2atN.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/history-BaUY2atN.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/liquid-DOEm5dbE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/liquid-DOEm5dbE.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/next-edit-types-904A5ehg.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/next-edit-types-904A5ehg.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cpp-B6k-yq-r.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cpp-B6k-yq-r.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/syntaxes/md-math-block.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/syntaxes/md-math-block.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chat-types-B-te1sXh.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chat-types-B-te1sXh.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pascal-BhNW15KB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pascal-BhNW15KB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/perl-DlYyT36c.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/perl-DlYyT36c.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/infoDiagram-A4XQUW5V-BFbjxDm0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/infoDiagram-A4XQUW5V-BFbjxDm0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/autofix-Dr7nxBLG.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/autofix-Dr7nxBLG.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/TextAreaAugment-DEYj-_0J.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/TextAreaAugment-DEYj-_0J.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cameligo-hfF0gFWA.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cameligo-hfF0gFWA.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/syntaxes/searchResult.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/syntaxes/searchResult.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/arc-4sw1e_bU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/arc-4sw1e_bU.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/apex-BE2Kqs_0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/apex-BE2Kqs_0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/VSCodeCodicon-DVaocTud.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/VSCodeCodicon-DVaocTud.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/blockDiagram-ZHA2E4KO-C9puqthI.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/blockDiagram-ZHA2E4KO-C9puqthI.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/java-DBwYS35M.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/java-DBwYS35M.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-deletion-selected-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-deletion-selected-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/StatusIndicator-D-yOSWp9.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/StatusIndicator-D-yOSWp9.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/MaterialIcon-BO_oU5T3.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/MaterialIcon-BO_oU5T3.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/index-DUiNNixO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/index-DUiNNixO.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ecl-DrG4DZS2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ecl-DrG4DZS2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cameligo-CGrWLZr3.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cameligo-CGrWLZr3.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pascal-DVjYFmSU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pascal-DVjYFmSU.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/cpu-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/cpu-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/index-DL-lqibn.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/index-DL-lqibn.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/media/codicon.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/media/codicon.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/TextAreaAugment-J75lFxU7.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/TextAreaAugment-J75lFxU7.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chunk-ASOPGD6M-CbQ5ZwRk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chunk-ASOPGD6M-CbQ5ZwRk.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/media/preview-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/media/preview-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/preview-styles/index.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/preview-styles/index.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/html-gnlaprsJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/html-gnlaprsJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/history-D5UbiMsA.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/history-D5UbiMsA.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/references-view/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/references-view/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/azcli-CBeeoD2V.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/azcli-CBeeoD2V.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/references-view/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/references-view/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/abap-BrlRCFwh.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/abap-BrlRCFwh.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/TextTooltipAugment-DTMpOwfF.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/TextTooltipAugment-DTMpOwfF.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ecl-BO6FnfXk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ecl-BO6FnfXk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/css-BkD51DMU.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/css-BkD51DMU.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/references-view/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/references-view/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/await_block-H61A9-v_.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/await_block-H61A9-v_.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-rejected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-rejected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/msdax-DBX3bZkL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/msdax-DBX3bZkL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/isObjectLike-BWVRxMGM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/isObjectLike-BWVRxMGM.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/design-system-init-BCZOObrS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/design-system-init-BCZOObrS.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/media/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/media/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chunk-TMUBEWPD-CEvn5tnI.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chunk-TMUBEWPD-CEvn5tnI.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/less-GGFNNJHn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/less-GGFNNJHn.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ini-BvajGCUy.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ini-BvajGCUy.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/heap-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/heap-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-deletion-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-deletion-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/diff-utils-C7XQLqYW.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/diff-utils-C7XQLqYW.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.web.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/extension.web.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/julia-DQXNmw_w.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/julia-DQXNmw_w.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/index-McRKs1sU.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/index-McRKs1sU.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/linear-BxeF_g5s.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/linear-BxeF_g5s.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/SpinnerAugment-Cx9dt_ox.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/SpinnerAugment-Cx9dt_ox.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/objective-c-BdAIHrxl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/objective-c-BdAIHrxl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/channel-JB0y-Inh.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/channel-JB0y-Inh.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/javascript-BrEubtUq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/javascript-BrEubtUq.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ganttDiagram-NTVNEXSI-BQEfstCi.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ganttDiagram-NTVNEXSI-BQEfstCi.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/VirtualizedMessageList-DhWSVHYH.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/VirtualizedMessageList-DhWSVHYH.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/github.copilot-chat-0.28.1/assets/bing.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/github.copilot-chat-0.28.1/assets/bing.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/handlebars-BJSeNQ27.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/handlebars-BJSeNQ27.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/m3-B3V054Zg.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/m3-B3V054Zg.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/markdown-B811l8j2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/markdown-B811l8j2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/javascript-BM_VUh7x.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/javascript-BM_VUh7x.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/media/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/media/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chunk-5HRBRIJM-C0moy_bE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chunk-5HRBRIJM-C0moy_bE.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/open-in-new-window-C_TwPNdv.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/open-in-new-window-C_TwPNdv.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/RemoteAgentRetry-ByFtlC0q.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/RemoteAgentRetry-ByFtlC0q.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/journeyDiagram-G5WM74LC-LNnvdniI.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/journeyDiagram-G5WM74LC-LNnvdniI.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/dart-CQal6Qht.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/dart-CQal6Qht.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/lua-BdjVVLHC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/lua-BdjVVLHC.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/java-SYsfObOQ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/java-SYsfObOQ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/check-BrrMO4vE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/check-BrrMO4vE.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/connect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/connect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/classDiagram-LNE6IOMH-DYn43beh.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/classDiagram-LNE6IOMH-DYn43beh.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/jsonMode-B6_b0_sN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/jsonMode-B6_b0_sN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/csp-C46ZqvIl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/csp-C46ZqvIl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chevron-down-DYf4hfS2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chevron-down-DYf4hfS2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/csp-ZI2qu8Le.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/csp-ZI2qu8Le.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/dagre-4EVJKHTY-HCbGuJiz.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/dagre-4EVJKHTY-HCbGuJiz.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pen-to-square-CZwCjcp0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pen-to-square-CZwCjcp0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/globals-D0QH3NT1.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/globals-D0QH3NT1.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/coffee-D3gVwdtb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/coffee-D3gVwdtb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/lexon-DwtVlf1I.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/lexon-DwtVlf1I.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chunk-KFBOBJHC-Ddw42s64.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chunk-KFBOBJHC-Ddw42s64.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/VirtualizedMessageList-DjO9gppt.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/VirtualizedMessageList-DjO9gppt.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chat-flags-model-GjgruWjX.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chat-flags-model-GjgruWjX.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/autofix-state-d-ymFdyn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/autofix-state-d-ymFdyn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/abap-CRCWOmpq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/abap-CRCWOmpq.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/VSCodeCodicon-B3px2_jp.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/VSCodeCodicon-B3px2_jp.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/jsonMode-Dek7wPyh.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/jsonMode-Dek7wPyh.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/SpinnerAugment-DnPofOlT.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/SpinnerAugment-DnPofOlT.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/diagram-QW4FP2JN-BawX4bkL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/diagram-QW4FP2JN-BawX4bkL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/clojure-D9WOWImG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/clojure-D9WOWImG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-deletion-selected-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/media/next-edit/nextedit-deletion-selected-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chunk-T2TOU4HS-jxUIqsMl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/chunk-T2TOU4HS-jxUIqsMl.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/php-language-features/dist/phpMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/php-language-features/dist/phpMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/graphql-LQdxqEYJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/graphql-LQdxqEYJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pgsql-cWj3SLw2.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pgsql-cWj3SLw2.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/autofix-B_2xrCkN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/autofix-B_2xrCkN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pascaligo-LOm9cWIk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pascaligo-LOm9cWIk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/clojure-BhAVYYK7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/clojure-BhAVYYK7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/objective-c-DCIC4Ga8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/objective-c-DCIC4Ga8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/arrow-up-right-from-square-D2UwhhNo.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/arrow-up-right-from-square-D2UwhhNo.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/coffee-B7EJu28W.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/coffee-B7EJu28W.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cypher-DQ3GyGCv.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cypher-DQ3GyGCv.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/design-system-init-y6tm-B4G.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/design-system-init-y6tm-B4G.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/clone-BO7hYRVH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/clone-BO7hYRVH.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/go-O9LJTZXk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/go-O9LJTZXk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/m3-Bu4mmWhs.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/m3-Bu4mmWhs.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cpp-DghbrAFl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cpp-DghbrAFl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/mdx-DuAILtAS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/mdx-DuAILtAS.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/design-system-init-CrDNmo5Z.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/design-system-init-CrDNmo5Z.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/diff-utils-DTcQ2vsq.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/diff-utils-DTcQ2vsq.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/stop.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/stop.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/memories-CXFK5KTm.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/memories-CXFK5KTm.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cssMode-Cf0wo1J6.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/cssMode-Cf0wo1J6.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/dist/media/refresh-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/search-result/dist/media/refresh-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/MarkdownEditor-zNvUkrOp.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/MarkdownEditor-zNvUkrOp.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/perl-UpK8AUhB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/perl-UpK8AUhB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/classDiagram-v2-MQ7JQ4JX-DYn43beh.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/classDiagram-v2-MQ7JQ4JX-DYn43beh.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/magnifying-glass-Fv6Gz5Ea.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/magnifying-glass-Fv6Gz5Ea.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/php-120yhfDK.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/php-120yhfDK.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/php-C92L-r_Y.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/php-C92L-r_Y.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pieDiagram-YF2LJOPJ-CnWPF43t.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pieDiagram-YF2LJOPJ-CnWPF43t.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pla-B-trYkKT.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pla-B-trYkKT.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pla-CjnFlu4u.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pla-CjnFlu4u.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/postiats-CQpG440k.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/postiats-CQpG440k.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/postiats-ToQhlN1R.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/postiats-ToQhlN1R.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/powerquery-BLkMU_zt.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/powerquery-BLkMU_zt.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/powershell-Bu_VLpJB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/powershell-Bu_VLpJB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/powershell-Cz-ePiwW.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/powershell-Cz-ePiwW.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/preference-BfSimiWU.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/preference-BfSimiWU.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/preference-DJ5bmhVd.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/preference-DJ5bmhVd.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/preload-helper-Dv6uf1Os.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/preload-helper-Dv6uf1Os.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/protobuf-BQ74DTcm.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/protobuf-BQ74DTcm.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/protobuf-CZXszgil.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/protobuf-CZXszgil.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pug-8ix3pnNZ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pug-8ix3pnNZ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pug-kFxLfcjb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pug-kFxLfcjb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/python-CZ67Wo4I.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/python-CZ67Wo4I.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/python-DY2G-JB8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/python-DY2G-JB8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/qsharp-YKUDF0Oj.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/qsharp-YKUDF0Oj.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/qsharp-q7JyzKFN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/qsharp-q7JyzKFN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/quadrantDiagram-OS5C2QUG-CnIugEw0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/quadrantDiagram-OS5C2QUG-CnIugEw0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/r-BIFz-_sK.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/r-BIFz-_sK.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/r-DShZCeRJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/r-DShZCeRJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/razor-BxlDHIuM.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/razor-BxlDHIuM.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/razor-iCuOooJL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/razor-iCuOooJL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/redis-CHOsPHWR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/redis-CHOsPHWR.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/redis-DJMpkPfA.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/redis-DJMpkPfA.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/redshift-6xAzNskS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/redshift-6xAzNskS.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/redshift-CBifECDb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/redshift-CBifECDb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/remote-agent-diff-C9S5rKqy.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/remote-agent-diff-C9S5rKqy.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/remote-agent-home-FEmWbk3M.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/remote-agent-home-FEmWbk3M.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/requirementDiagram-MIRIMTAZ-Bgwf2JUA.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/requirementDiagram-MIRIMTAZ-Bgwf2JUA.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/restructuredtext-CghPJEOS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/restructuredtext-CghPJEOS.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ruby-CYWGW-b1.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ruby-CYWGW-b1.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/rust-APfvjYow.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/rust-APfvjYow.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/rust-DMDD0SHb.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/rust-DMDD0SHb.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sankeyDiagram-Y46BX6SQ-BCHTwFGe.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sankeyDiagram-Y46BX6SQ-BCHTwFGe.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sb-BYAiYHFx.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sb-BYAiYHFx.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sb-Ddgo-Lel.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sb-Ddgo-Lel.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/scala-Bqvq8jcR.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/scala-Bqvq8jcR.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/scheme-Dhb-2j9p.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/scheme-Dhb-2j9p.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/scss-CTwUZ5N7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/scss-CTwUZ5N7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/scss-DuQSCaUL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/scss-DuQSCaUL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/utils-DJhaageo.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/utils-DJhaageo.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/file-reader-BdhEMA38.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/file-reader-BdhEMA38.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/flow9-Cac8vKd7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/flow9-Cac8vKd7.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/flow9-DFOiqFq1.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/flow9-DFOiqFq1.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/flowDiagram-7ASYPVHJ-mxubPaVS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/flowDiagram-7ASYPVHJ-mxubPaVS.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/freemarker2-CG76HvIH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/freemarker2-CG76HvIH.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/freemarker2-DoNuTueB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/freemarker2-DoNuTueB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/fsharp-BpBzFqoi.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/fsharp-BpBzFqoi.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/gitGraph-YCYPL57B-BkI2ZAJY.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/gitGraph-YCYPL57B-BkI2ZAJY.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/go-CHYgS3dC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/go-CHYgS3dC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/graph-Cmv4QMDC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/graph-Cmv4QMDC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/graphql-csByOneL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/graphql-csByOneL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/handlebars-CNQw3EXp.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/handlebars-CNQw3EXp.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/hcl-DxDQ3s82.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/hcl-DxDQ3s82.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/htmlMode-CIRhgpF_.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/htmlMode-CIRhgpF_.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/index-B6hSl5-9.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/index-B6hSl5-9.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/index-CGnj6T3o.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/index-CGnj6T3o.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/kanban-definition-QRCXZQQD-DwS1qoCw.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/kanban-definition-QRCXZQQD-DwS1qoCw.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/katex-BAVf198l.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/katex-BAVf198l.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/keypress-DD1aQVr0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/keypress-DD1aQVr0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/kotlin-qQ0MG-9I.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/kotlin-qQ0MG-9I.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/layout--Pr7JG77.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/layout--Pr7JG77.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/lodash-Drc0SN5U.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/lodash-Drc0SN5U.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/lua-D28Ae8-K.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/lua-D28Ae8-K.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/mcp-logo-DslCzNpc.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/mcp-logo-DslCzNpc.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/mdx-BaH_mmJD.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/mdx-BaH_mmJD.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/memories-mBEt2e4E.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/memories-mBEt2e4E.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/mindmap-definition-GWI6TPTV-C_2mJ5hS.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/mindmap-definition-GWI6TPTV-C_2mJ5hS.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/mips-CdjsipkG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/mips-CdjsipkG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/mips-Cu7FWeYr.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/mips-Cu7FWeYr.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/msdax-CYqgjx_P.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/msdax-CYqgjx_P.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/mysql-BHd6q0vd.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/mysql-BHd6q0vd.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/mysql-CMGNIvT0.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/mysql-CMGNIvT0.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/next-edit-suggestions-B7MC_6oB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/next-edit-suggestions-B7MC_6oB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/next-edit-suggestions-DRIPpJpq.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/next-edit-suggestions-DRIPpJpq.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pgsql-Dy0bjov7.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/pgsql-Dy0bjov7.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/dist/npmMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/dist/npmMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/dist/npmMain.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/npm/dist/npmMain.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/ci.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/ci.yml"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/tsconfig.browser.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/tsconfig.browser.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/425.heapsnapshotWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/425.heapsnapshotWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/848.extension.web.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/848.extension.web.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/dist/notebookSerializerWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/dist/notebookSerializerWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/grunt/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/grunt/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/media/index.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/media/index.html"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-added.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-added.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-untracked.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-untracked.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/main.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/restart.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/restart.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/restart.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/restart.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/resume.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/resume.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/service-worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/service-worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/stop-profiling.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/stop-profiling.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/stop.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/stop.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/syntaxes/md-math-fence.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/syntaxes/md-math-fence.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/syntaxes/md-math.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/syntaxes/md-math.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/powerquery-DdJtto1Z.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/powerquery-DdJtto1Z.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/scala-Bzjcj0lf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/scala-Bzjcj0lf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/scheme-Ecrf_Zyn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/scheme-Ecrf_Zyn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sequenceDiagram-G6AWOVSC-BJbaEsN6.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sequenceDiagram-G6AWOVSC-BJbaEsN6.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/settings-CFl314iJ.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/settings-CFl314iJ.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/shell-CNhb_Zkf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/shell-CNhb_Zkf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/shell-CsDZo4DB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/shell-CsDZo4DB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sophia-dWwzI90F.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sophia-dWwzI90F.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sparql-CouE6pZG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sparql-CouE6pZG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sparql-KEyrF7De.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sparql-KEyrF7De.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sql-BV61QDTH.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sql-BV61QDTH.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sql-BdTr02Mf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sql-BdTr02Mf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/st-BZ7aq21L.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/st-BZ7aq21L.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/st-C7iG7M4S.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/st-C7iG7M4S.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/stateDiagram-MAYHULR4-D-KkBMme.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/stateDiagram-MAYHULR4-D-KkBMme.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/stateDiagram-v2-4JROLMXI-Cb6iWWeE.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/stateDiagram-v2-4JROLMXI-Cb6iWWeE.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/swift-D7IUmUK8.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/swift-D7IUmUK8.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/swift-DqwpnxQL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/swift-DqwpnxQL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/systemverilog-CeZ7LPTL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/systemverilog-CeZ7LPTL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/systemverilog-DgMryOEJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/systemverilog-DgMryOEJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/tcl-Bl2hYPt-.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/tcl-Bl2hYPt-.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/tcl-PloMZuKG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/tcl-PloMZuKG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/terminal-BjJSzToG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/terminal-BjJSzToG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/timeline-definition-U7ZMHBDA-Cz5sWWoO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/timeline-definition-U7ZMHBDA-Cz5sWWoO.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/toggleHighContrast-D4zjdeIP.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/toggleHighContrast-D4zjdeIP.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/tsMode-BXDgwTrJ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/tsMode-BXDgwTrJ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/tsMode-DM5zRHFn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/tsMode-DM5zRHFn.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/twig-BfRIq3la.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/twig-BfRIq3la.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/twig-h6VuAx0U.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/twig-h6VuAx0U.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/types-8LwCBeyq.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/types-8LwCBeyq.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/types-CGlLNakm.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/types-CGlLNakm.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/types-DDm27S8B.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/types-DDm27S8B.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/typescript-DT13XRSV.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/typescript-DT13XRSV.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/typescript-DTP-A_Zf.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/typescript-DTP-A_Zf.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/typespec-5IKh-a8s.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/typespec-5IKh-a8s.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/typespec-DKGjpBXL.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/typespec-DKGjpBXL.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/vb-BwAE3J76.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/vb-BwAE3J76.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/vb-CS586MRk.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/vb-CS586MRk.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/wgsl-DCafy-vX.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/wgsl-DCafy-vX.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/wgsl-Du36xR5C.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/wgsl-Du36xR5C.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/xml-C-C41Cin.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/xml-C-C41Cin.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/xml-_u1XISHN.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/xml-_u1XISHN.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/xychartDiagram-6QU3TZC5-DlcWFMn3.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/xychartDiagram-6QU3TZC5-DlcWFMn3.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/yaml-CRGTkk5g.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/yaml-CRGTkk5g.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/yaml-Cr3uXDXT.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/yaml-Cr3uXDXT.js"}, "/home/<USER>/workspace/.config/.vscode-server/data/machineid": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/machineid"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/Augment-Memories": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/Augment-Memories"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/lru.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/lru.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/log.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/log.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/pid.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-848b80aeb52026648a8ff9f7c45a9b0a80641e2e/pid.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/pid.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/pid.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/LICENSE": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/LICENSE"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/product.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/product.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/bootstrap-fork.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/bootstrap-fork.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/folder-CEjIF7oG.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/folder-CEjIF7oG.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/folder-ol95BxhL.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/folder-ol95BxhL.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/folder-opened-CX_GXeEO.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/folder-opened-CX_GXeEO.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/base/node/cpuUsage.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/base/node/cpuUsage.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/base/node/ps.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/base/node/ps.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/schemas/jsconfig.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/schemas/jsconfig.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/schemas/tsconfig.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/schemas/tsconfig.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/resources/walkthroughs/create-a-js-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/resources/walkthroughs/create-a-js-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/resources/walkthroughs/debug-and-run.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/resources/walkthroughs/debug-and-run.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/resources/walkthroughs/install-node-js.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/resources/walkthroughs/install-node-js.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/resources/walkthroughs/learn-more.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/resources/walkthroughs/learn-more.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/dist/extension.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/typescript-language-features/dist/extension.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/tunnel-forwarding/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/tunnel-forwarding/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/tunnel-forwarding/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/tunnel-forwarding/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/terminal-suggest/.gitignore": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/terminal-suggest/.gitignore"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/terminal-suggest/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/terminal-suggest/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/terminal-suggest/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/terminal-suggest/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/terminal-suggest/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/terminal-suggest/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/terminal-suggest/dist/fig/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/terminal-suggest/dist/fig/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/simple-browser/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshot-client.bundle.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshot-client.bundle.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshotWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.vscode-js-profile-table/out/heapsnapshotWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/SECURITY.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/SECURITY.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/ci.yml": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/ci.yml"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/eslint.config.mjs": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/eslint.config.mjs"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/out/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug-companion/out/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/ThirdPartyNotices.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/ThirdPartyNotices.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/telemetry.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/telemetry.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/diagnosticTool.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/diagnosticTool.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/dist/extension.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/dist/extension.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/extension.webpack.config.cjs": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/extension.webpack.config.cjs"}, "/home/<USER>/workspace/.cache/pip/http-v2/d/9/8/7/1/d987112d26893b629f2e6af6e48ef04b7ba5ce0d5658b23cb2dd3f03.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/d/9/8/7/1/d987112d26893b629f2e6af6e48ef04b7ba5ce0d5658b23cb2dd3f03.body"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/watchdog.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/watchdog.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/vendor/acorn-loose.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/vendor/acorn-loose.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/vendor/acorn.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/vendor/acorn.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/ui/basic-wat.configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/targets/node/terminateProcess.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/targets/node/terminateProcess.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/logo.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/logo.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/configure.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/configure.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/connect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/connect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/disconnect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/disconnect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/node.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/node.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/open-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/open-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/page.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/page.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/pause.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/pause.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/resume.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/resume.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/service-worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/service-worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/worker.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/light/worker.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/configure.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/configure.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/disconnect.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/disconnect.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/node.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/node.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/open-file.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/open-file.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/page.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/page.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/pause.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/resources/dark/pause.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/audioPreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/audioPreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/audioPreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/audioPreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/imagePreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/imagePreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/imagePreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/imagePreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/loading-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/loading-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/loading-hc.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/loading-hc.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/loading.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/loading.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/videoPreview.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/videoPreview.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/videoPreview.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/media/videoPreview.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/notebook-out/katex.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/notebook-out/katex.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/notebook-out/cellAttachmentRenderer.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/notebook-out/cellAttachmentRenderer.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/remote-agent-diff-CgXpsGxF.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/remote-agent-diff-CgXpsGxF.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/remote-agent-home-BLGpqH61.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/remote-agent-home-BLGpqH61.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/resize-observer-DdAtcrRr.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/resize-observer-DdAtcrRr.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/restructuredtext-CQoPj0uC.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/restructuredtext-CQoPj0uC.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ruby-1H8dtvFl.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/ruby-1H8dtvFl.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/rules-C3YCcjLZ.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/rules-C3YCcjLZ.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/rules-ZbQWyCz-.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/rules-ZbQWyCz-.css"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/settings-Qb48izFy.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/settings-Qb48izFy.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/solidity-C4mwTkrB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/solidity-C4mwTkrB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/solidity-CME5AdoB.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/solidity-CME5AdoB.js"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sophia-RYC1BQQz.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/augment.vscode-augment-0.482.1/common-webviews/assets/sophia-RYC1BQQz.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/server-cli.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/server-cli.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-bash.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-env.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-login.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-profile.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration-rc.zsh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.fish": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.fish"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/workbench/contrib/terminal/common/scripts/shellIntegration.ps1"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/bootloader.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/bootloader.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/diagnosticTool.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/diagnosticTool.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/hash.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/hash.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/renameWorker.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ms-vscode.js-debug/src/renameWorker.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/dist/node/htmlServerMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/dist/node/htmlServerMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/client/dist/node/htmlClientMain.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/client/dist/node/htmlClientMain.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/gulp/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/gulp/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/gulp/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/gulp/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/gulp/dist/main.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/gulp/dist/main.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/media/auth.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/media/auth.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/markdown.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/markdown.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/some-markdown.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/some-markdown.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/docs/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/a.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/a.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/b.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/b.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/x.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/testWorkspace/.github/PULL_REQUEST_TEMPLATE/x.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/dist/430.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/dist/430.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/dist/555.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/dist/555.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/dist/698.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/dist/698.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/syntaxes/git-commit.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/syntaxes/git-commit.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/syntaxes/ignore.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/syntaxes/ignore.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/languages/ignore.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/languages/ignore.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/emojis.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/emojis.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-added.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-added.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-conflict.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-conflict.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-copied.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-copied.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-deleted.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-deleted.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-ignored.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-ignored.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-modified.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-modified.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-renamed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-renamed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-type-changed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-type-changed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-untracked.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/light/status-untracked.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-conflict.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-conflict.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-copied.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-copied.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-deleted.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-deleted.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-ignored.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-ignored.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-modified.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-modified.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-renamed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-renamed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-type-changed.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/resources/icons/dark/status-type-changed.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/askpass-empty.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/askpass-empty.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/askpass-main.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/askpass-main.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/askpass.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/askpass.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/git-editor-empty.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/git-editor-empty.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/git-editor-main.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/git-editor-main.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/git-editor.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/git-editor.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/ssh-askpass-empty.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/ssh-askpass-empty.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/ssh-askpass.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git/dist/ssh-askpass.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/extension-editing/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/extension-editing/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/extension-editing/dist/extensionEditingMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/extension-editing/dist/extensionEditingMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/emmet/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/emmet/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/emmet/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/emmet/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-auto-launch/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-auto-launch/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/server/dist/node/85.cssServerMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/server/dist/node/85.cssServerMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/server/dist/node/cssServerMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/server/dist/node/cssServerMain.js"}, "/home/<USER>/workspace/.cache/pip/http-v2/f/9/4/c/3/f94c38c3af610b2c03ee2a75463a0063f728a99412a4f6bfcefd7deb.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/f/9/4/c/3/f94c38c3af610b2c03ee2a75463a0063f728a99412a4f6bfcefd7deb.body"}, "/home/<USER>/workspace/.cache/pip/http-v2/e/e/3/f/2/ee3f2c1df32affec2adcc8a0609df02bcc0e6ae2f6007e360e3fe65d.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/e/e/3/f/2/ee3f2c1df32affec2adcc8a0609df02bcc0e6ae2f6007e360e3fe65d.body"}, "/home/<USER>/workspace/.cache/pip/http-v2/e/6/0/6/4/e6064a4f2a07f540b8009833121fd5eec9c0809f0d77295a6f7dc56a.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/e/6/0/6/4/e6064a4f2a07f540b8009833121fd5eec9c0809f0d77295a6f7dc56a.body"}, "/home/<USER>/workspace/.cache/pip/http-v2/e/3/0/4/6/e30463c88e9687072803051497bc912d248f72c94a37ddb28e024717.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/e/3/0/4/6/e30463c88e9687072803051497bc912d248f72c94a37ddb28e024717.body"}, "/home/<USER>/workspace/.cache/pip/http-v2/c/8/0/9/2/c80928cd334a6c21c8fabc7167bdcba88df8963ea4c11125c9a94df2.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/c/8/0/9/2/c80928cd334a6c21c8fabc7167bdcba88df8963ea4c11125c9a94df2.body"}, "/home/<USER>/workspace/.cache/pip/http-v2/b/a/8/d/1/ba8d19ea4a36af94f8a63194e609fb8693550cb8e04c7a3181a3de50.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/b/a/8/d/1/ba8d19ea4a36af94f8a63194e609fb8693550cb8e04c7a3181a3de50.body"}, "/home/<USER>/workspace/.cache/pip/http-v2/b/6/b/0/d/b6b0d71a0f7ec818cbac7aff4113923ad78ba89a1332d230f649e7a4.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/b/6/b/0/d/b6b0d71a0f7ec818cbac7aff4113923ad78ba89a1332d230f649e7a4.body"}, "/home/<USER>/workspace/.cache/pip/http-v2/9/d/5/e/c/9d5eccdaccef0d4545dbc823b4556021d1cc43c66214cc0573dd264d.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/9/d/5/e/c/9d5eccdaccef0d4545dbc823b4556021d1cc43c66214cc0573dd264d.body"}, "/home/<USER>/workspace/.cache/pip/http-v2/8/7/6/0/8/8760844e70423bf026b16e279bdee0bc6d3308da1d985cb14a8320a2.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/8/7/6/0/8/8760844e70423bf026b16e279bdee0bc6d3308da1d985cb14a8320a2.body"}, "/home/<USER>/workspace/.cache/pip/http-v2/5/7/6/5/6/57656d98096f5da9b4d8c8018b9eb91cc19d886f4e084963a7542c48.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/5/7/6/5/6/57656d98096f5da9b4d8c8018b9eb91cc19d886f4e084963a7542c48.body"}, "/home/<USER>/workspace/.cache/pip/http-v2/5/2/5/d/b/525db3f628e0e1a3244ec730abff666831fcaeea205c7300a2f484a5.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/5/2/5/d/b/525db3f628e0e1a3244ec730abff666831fcaeea205c7300a2f484a5.body"}, "/home/<USER>/workspace/.cache/pip/http-v2/3/f/3/3/6/3f3362379986e7b25995b2376ce9d878ec30a1115e3367921160a6e8.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/3/f/3/3/6/3f3362379986e7b25995b2376ce9d878ec30a1115e3367921160a6e8.body"}, "/home/<USER>/workspace/.cache/pip/http-v2/2/0/d/3/c/20d3cba6c36dbf3268e2bd51a5f219a599875f54365308fff0a489d4.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/2/0/d/3/c/20d3cba6c36dbf3268e2bd51a5f219a599875f54365308fff0a489d4.body"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/ce248d0eb8d4f662cc202e5206f37824b70c818932a8dbfb169463d063d70aa1/mtime-cache.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/media/auth.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/media/auth.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/dist/extension.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/microsoft-authentication/dist/extension.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/merge-conflict/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/merge-conflict/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/merge-conflict/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/merge-conflict/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/syntaxes/md-math-inline.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/syntaxes/md-math-inline.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-math/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/notebook-out/index.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/notebook-out/index.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/media/highlight.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/media/highlight.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/media/markdown.css": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/media/markdown.css"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/media/pre.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/media/pre.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/media/preview-dark.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/media/preview-dark.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/media/preview-light.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/media/preview-light.svg"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/dist/extension.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/dist/extension.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/dist/serverWorkerMain.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/markdown-language-features/dist/serverWorkerMain.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/server/.npmrc": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/server/.npmrc"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/server/dist/node/774.jsonServerMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/server/dist/node/774.jsonServerMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/terminal/node/ptyHostMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/files/node/watcher/watcherMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/out/vs/platform/files/node/watcher/watcherMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/dist/ipynbMain.node.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/ipynb/dist/ipynbMain.node.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/lib/jquery.d.ts": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/lib/jquery.d.ts"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/dist/node/421.htmlServerMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/dist/node/421.htmlServerMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/dist/node/490.htmlServerMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/html-language-features/server/dist/node/490.htmlServerMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/gulp/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/gulp/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/grunt/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/grunt/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/grunt/dist/main.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/grunt/dist/main.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github-authentication/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/dist/extension.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/github/dist/extension.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/syntaxes/git-rebase.tmLanguage.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/syntaxes/git-rebase.tmLanguage.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/languages/git-commit.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/languages/git-commit.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/languages/git-rebase.language-configuration.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/languages/git-rebase.language-configuration.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/git-base/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/emmet/dist/node/emmetNodeMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/emmet/dist/node/emmetNodeMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-server-ready/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-server-ready/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-server-ready/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-server-ready/dist/extension.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-auto-launch/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/debug-auto-launch/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/schemas/package.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/schemas/package.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/configuration-editing/schemas/attachContainer.schema.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/configuration-editing/schemas/attachContainer.schema.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/configuration-editing/dist/configurationEditingMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/configuration-editing/dist/configurationEditingMain.js"}, "/home/<USER>/workspace/.cache/replit/nix/dotreplitenv.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/nix/dotreplitenv.json"}, "/home/<USER>/workspace/.cache/replit/modules/replit-rtld-loader.res": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/replit/modules/replit-rtld-loader.res"}, "/home/<USER>/workspace/.cache/pip/http-v2/1/f/a/d/b/1fadb1649b903be8585c9fd8a5d5d89c78ca202efaca120c9ecab174.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/1/f/a/d/b/1fadb1649b903be8585c9fd8a5d5d89c78ca202efaca120c9ecab174.body"}, "/home/<USER>/workspace/.cache/pip/http-v2/1/7/f/1/b/17f1b9e31844b273de4c8bc85c4987db2341cdbd34d49173a8b7e6c6.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/1/7/f/1/b/17f1b9e31844b273de4c8bc85c4987db2341cdbd34d49173a8b7e6c6.body"}, "/home/<USER>/workspace/.cache/pip/http-v2/1/2/3/4/d/1234d55b4107c6742c376e547f58eaa1f2ef11789b541db7ffb05e3e.body": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/pip/http-v2/1/2/3/4/d/1234d55b4107c6742c376e547f58eaa1f2ef11789b541db7ffb05e3e.body"}, "/home/<USER>/workspace/.cache/Microsoft/DeveloperTools/deviceid": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/Microsoft/DeveloperTools/deviceid"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/manifest/manifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/manifest/manifest"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/merge-conflict/dist/mergeConflictMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/merge-conflict/dist/mergeConflictMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/merge-conflict/dist/mergeConflictMain.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/merge-conflict/dist/mergeConflictMain.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/media-preview/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/server/dist/node/875.jsonServerMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/server/dist/node/875.jsonServerMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/server/dist/node/jsonServerMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/server/dist/node/jsonServerMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/client/dist/node/jsonClientMain.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/json-language-features/client/dist/node/jsonClientMain.js.LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/jake/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/jake/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/jake/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/jake/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/jake/dist/main.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/jake/dist/main.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/client/dist/node/cssClientMain.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/css-language-features/client/dist/node/cssClientMain.js"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/configuration-editing/package.nls.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/extensions/configuration-editing/package.nls.json"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/bin/code-server": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/bin/code-server"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/bin/remote-cli/code": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/bin/remote-cli/code"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/bin/helpers/browser.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/bin/helpers/browser.sh"}, "/home/<USER>/workspace/.config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/bin/helpers/check-requirements.sh": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/cli/servers/Stable-18e3a1ec544e6907be1e944a94c496e302073435/server/bin/helpers/check-requirements.sh"}, "/home/<USER>/workspace/.claude/settings.local.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".claude/settings.local.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/anthropic.claude-code-1.0.11/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/anthropic.claude-code-1.0.11/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/anthropic.claude-code-1.0.11/README.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/anthropic.claude-code-1.0.11/README.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/anthropic.claude-code-1.0.11/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/anthropic.claude-code-1.0.11/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/anthropic.claude-code-1.0.11/resources/claude-logo.svg": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/anthropic.claude-code-1.0.11/resources/claude-logo.svg"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/anthropic.claude-code-1.0.11/dist/extension.js": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/anthropic.claude-code-1.0.11/dist/extension.js"}, "/home/<USER>/workspace/.cache/claude-cli-nodejs/-home-runner-workspace/mcp-logs-ide/2025-06-20T18-10-12-001Z.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".cache/claude-cli-nodejs/-home-runner-workspace/mcp-logs-ide/2025-06-20T18-10-12-001Z.txt"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-global-state/recentlyOpenedFiles.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-global-state/recentlyOpenedFiles.json"}, "/home/<USER>/workspace/data_utils/create_demo_data.py": {"rootPath": "/home/<USER>/workspace", "relPath": "data_utils/create_demo_data.py"}, "/home/<USER>/workspace/data_utils/create_working_schema.py": {"rootPath": "/home/<USER>/workspace", "relPath": "data_utils/create_working_schema.py"}, "/home/<USER>/workspace/data_utils/fix_swagger.py": {"rootPath": "/home/<USER>/workspace", "relPath": "data_utils/fix_swagger.py"}, "/home/<USER>/workspace/docs/ExProject PRD.pdf": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/ExProject PRD.pdf"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-global-state/requestIdSelectionMetadata.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-global-state/requestIdSelectionMetadata.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/24af9750-277d-4aa1-a232-2da893491f7a": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/24af9750-277d-4aa1-a232-2da893491f7a"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/736adb33-c433-47c6-93d0-8798cd71cb21": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/736adb33-c433-47c6-93d0-8798cd71cb21"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/a94e7e55-9e93-40ee-a32c-770ba586d31b": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/a94e7e55-9e93-40ee-a32c-770ba586d31b"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/d9dd4d80-f7a4-4773-bea7-008118783082": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/d9dd4d80-f7a4-4773-bea7-008118783082"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/dbcfbea9-9e5f-4333-a962-18e04986950f": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/dbcfbea9-9e5f-4333-a962-18e04986950f"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-4e67e35/pyC2.py": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-4e67e35/pyC2.py"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-4e67e35/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-4e67e35/entries.json"}, "/home/<USER>/workspace/exproject/settings/__pycache__/base.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "exproject/settings/__pycache__/base.cpython-311.pyc"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-3000788f-c7b2-4996-90b0-44c1875ce3d2.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/agent-edits/shards/agent-edit-shard-storage-shard-3000788f-c7b2-4996-90b0-44c1875ce3d2.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/checkpoint-documents/3000788f-c7b2-4996-90b0-44c1875ce3d2/document-_home_runner_workspace_exproject_settings_base.py-0-b7acdd53-c85c-454c-8972-30f9b31efbaf.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/checkpoint-documents/3000788f-c7b2-4996-90b0-44c1875ce3d2/document-_home_runner_workspace_exproject_settings_base.py-0-b7acdd53-c85c-454c-8972-30f9b31efbaf.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/checkpoint-documents/3000788f-c7b2-4996-90b0-44c1875ce3d2/document-_home_runner_workspace_exproject_settings_base.py-1750448365493-5666e8b9-abfb-4d21-827f-0e5fbbebe040.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/checkpoint-documents/3000788f-c7b2-4996-90b0-44c1875ce3d2/document-_home_runner_workspace_exproject_settings_base.py-1750448365493-5666e8b9-abfb-4d21-827f-0e5fbbebe040.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/checkpoint-documents/3000788f-c7b2-4996-90b0-44c1875ce3d2/document-_home_runner_workspace_exproject_settings_base.py-1750448365920-13a83d4c-c0fa-4eec-b41a-30cef055b0fb.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/checkpoint-documents/3000788f-c7b2-4996-90b0-44c1875ce3d2/document-_home_runner_workspace_exproject_settings_base.py-1750448365920-13a83d4c-c0fa-4eec-b41a-30cef055b0fb.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/checkpoint-documents/3000788f-c7b2-4996-90b0-44c1875ce3d2/document-_home_runner_workspace_exproject_settings_base.py-1750448365920-734beb9f-b180-42cc-a318-9e3264161bd4.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/checkpoint-documents/3000788f-c7b2-4996-90b0-44c1875ce3d2/document-_home_runner_workspace_exproject_settings_base.py-1750448365920-734beb9f-b180-42cc-a318-9e3264161bd4.json"}, "/home/<USER>/workspace/apps/ui/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/__init__.py"}, "/home/<USER>/workspace/apps/ui/apps.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/apps.py"}, "/home/<USER>/workspace/apps/ui/models.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/models.py"}, "/home/<USER>/workspace/apps/ui/design_tokens.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/design_tokens.py"}, "/home/<USER>/workspace/apps/ui/templatetags/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/templatetags/__init__.py"}, "/home/<USER>/workspace/apps/ui/templatetags/ui_components.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/templatetags/ui_components.py"}, "/home/<USER>/workspace/templates/ui/components/button.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/button.html"}, "/home/<USER>/workspace/templates/ui/components/card.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/card.html"}, "/home/<USER>/workspace/templates/ui/components/input.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/input.html"}, "/home/<USER>/workspace/templates/ui/components/badge.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/badge.html"}, "/home/<USER>/workspace/templates/ui/components/table.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/table.html"}, "/home/<USER>/workspace/templates/ui/base_modern.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/base_modern.html"}, "/home/<USER>/workspace/templates/ui/layouts/dashboard_modern.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/layouts/dashboard_modern.html"}, "/home/<USER>/workspace/apps/ui/services.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/services.py"}, "/home/<USER>/workspace/apps/ui/__pycache__/__init__.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/__pycache__/__init__.cpython-311.pyc"}, "/home/<USER>/workspace/apps/ui/__pycache__/apps.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/__pycache__/apps.cpython-311.pyc"}, "/home/<USER>/workspace/apps/ui/__pycache__/models.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/__pycache__/models.cpython-311.pyc"}, "/home/<USER>/workspace/apps/ui/templatetags/__pycache__/__init__.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/templatetags/__pycache__/__init__.cpython-311.pyc"}, "/home/<USER>/workspace/apps/ui/templatetags/__pycache__/ui_components.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/templatetags/__pycache__/ui_components.cpython-311.pyc"}, "/home/<USER>/workspace/apps/ui/migrations/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/migrations/__init__.py"}, "/home/<USER>/workspace/apps/ui/migrations/0001_initial.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/migrations/0001_initial.py"}, "/home/<USER>/workspace/apps/ui/migrations/__pycache__/__init__.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/migrations/__pycache__/__init__.cpython-311.pyc"}, "/home/<USER>/workspace/apps/ui/migrations/__pycache__/0001_initial.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/migrations/__pycache__/0001_initial.cpython-311.pyc"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-4e67e35/dNAG.py": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-4e67e35/dNAG.py"}, "/home/<USER>/workspace/apps/ui/admin.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/admin.py"}, "/home/<USER>/workspace/apps/ui/views.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/views.py"}, "/home/<USER>/workspace/apps/ui/urls.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/urls.py"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-156c013e/8cQz.py": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-156c013e/8cQz.py"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/-156c013e/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/-156c013e/entries.json"}, "/home/<USER>/workspace/apps/ui/__pycache__/admin.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/__pycache__/admin.cpython-311.pyc"}, "/home/<USER>/workspace/exproject/__pycache__/urls.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "exproject/__pycache__/urls.cpython-311.pyc"}, "/home/<USER>/workspace/apps/ui/__pycache__/design_tokens.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/__pycache__/design_tokens.cpython-311.pyc"}, "/home/<USER>/workspace/apps/ui/__pycache__/services.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/__pycache__/services.cpython-311.pyc"}, "/home/<USER>/workspace/apps/ui/__pycache__/urls.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/__pycache__/urls.cpython-311.pyc"}, "/home/<USER>/workspace/apps/ui/__pycache__/views.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/__pycache__/views.cpython-311.pyc"}, "/home/<USER>/workspace/templates/ui/design_system_docs.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/design_system_docs.html"}, "/home/<USER>/workspace/apps/ui/management/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/management/__init__.py"}, "/home/<USER>/workspace/apps/ui/management/commands/__init__.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/management/commands/__init__.py"}, "/home/<USER>/workspace/apps/ui/management/commands/init_design_system.py": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/management/commands/init_design_system.py"}, "/home/<USER>/workspace/apps/ui/management/__pycache__/__init__.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/management/__pycache__/__init__.cpython-311.pyc"}, "/home/<USER>/workspace/apps/ui/management/commands/__pycache__/__init__.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/management/commands/__pycache__/__init__.cpython-311.pyc"}, "/home/<USER>/workspace/apps/ui/management/commands/__pycache__/init_design_system.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/management/commands/__pycache__/init_design_system.cpython-311.pyc"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/0419a5d3-501e-4c42-b47e-fdd12455a796": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/0419a5d3-501e-4c42-b47e-fdd12455a796"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/5b7a7cec-bc2c-4e44-923e-d54176d9f0b5": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/5b7a7cec-bc2c-4e44-923e-d54176d9f0b5"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/a09ae36a-829b-4227-8d63-b50e1e3fa587": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/a09ae36a-829b-4227-8d63-b50e1e3fa587"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/f8193ce0-28b0-46c2-bbb2-bb0a145fb996": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/task-storage/tasks/f8193ce0-28b0-46c2-bbb2-bb0a145fb996"}, "/home/<USER>/workspace/templates/ui/components/alert.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/alert.html"}, "/home/<USER>/workspace/templates/core/dashboard_comparison.html": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/dashboard_comparison.html"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/17d856d8/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/17d856d8/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/17d856d8/S0Z5.py": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/17d856d8/S0Z5.py"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/636072a4/ZO8J.py": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/636072a4/ZO8J.py"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/636072a4/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/636072a4/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/17d856d8/9ky9.py": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/17d856d8/9ky9.py"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/636072a4/kJ9u.py": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/636072a4/kJ9u.py"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/3fbf7cca/dTiV.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/3fbf7cca/dTiV.html"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/3fbf7cca/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/3fbf7cca/entries.json"}, "/home/<USER>/workspace/apps/core/__pycache__/urls.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/core/__pycache__/urls.cpython-311.pyc"}, "/home/<USER>/workspace/apps/core/__pycache__/views.cpython-311.pyc": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/core/__pycache__/views.cpython-311.pyc"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/27b66d1711d911d62ca2edeee69602655aa1cb806785135ab476c24407a1e1e9.png": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/workspaceStorage/49b7aa64c464490995d065ddf511f204/Augment.vscode-augment/augment-user-assets/27b66d1711d911d62ca2edeee69602655aa1cb806785135ab476c24407a1e1e9.png"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5b0f544c/s0JI.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5b0f544c/s0JI.html"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5b0f544c/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5b0f544c/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5b0f544c/ghZk.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5b0f544c/ghZk.html"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5b0f544c/lPh2.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5b0f544c/lPh2.html"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5b0f544c/6c9V.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5b0f544c/6c9V.html"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/5b0f544c/g1Zx.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/5b0f544c/g1Zx.html"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/61ebc573/entries.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/61ebc573/entries.json"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/History/61ebc573/icvR.html": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/History/61ebc573/icvR.html"}, "/home/<USER>/workspace/.config/.vscode-server/data/CachedExtensionVSIXs/stagewise.stagewise-vscode-extension-0.6.0": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/CachedExtensionVSIXs/stagewise.stagewise-vscode-extension-0.6.0"}, "/home/<USER>/workspace/.config/.vscode-server/data/CachedExtensionVSIXs/stagewise.stagewise-vscode-extension-0.6.0.sigzip": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/CachedExtensionVSIXs/stagewise.stagewise-vscode-extension-0.6.0.sigzip"}, "/home/<USER>/workspace/.config/.vscode-server/data/User/globalStorage/stagewise.stagewise-vscode-extension/stagewise.hasSeenGettingStarted": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/data/User/globalStorage/stagewise.stagewise-vscode-extension/stagewise.hasSeenGettingStarted"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0/.vsixmanifest": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0/.vsixmanifest"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0/LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0/LICENSE.txt"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0/TELEMETRY.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0/TELEMETRY.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0/changelog.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0/changelog.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0/package.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0/package.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0/readme.md": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0/readme.md"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0/telemetry.json": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0/telemetry.json"}, "/home/<USER>/workspace/.config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0/out/extension.js.LICENSE.txt": {"rootPath": "/home/<USER>/workspace", "relPath": ".config/.vscode-server/extensions/stagewise.stagewise-vscode-extension-0.6.0/out/extension.js.LICENSE.txt"}}