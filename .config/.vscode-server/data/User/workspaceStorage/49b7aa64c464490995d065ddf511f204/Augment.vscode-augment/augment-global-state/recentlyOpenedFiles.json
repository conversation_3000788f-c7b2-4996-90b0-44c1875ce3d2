[["/home/<USER>/workspace/schema.yml", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "schema.yml"}}], ["/home/<USER>/workspace/apps/projects/apps.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/projects/apps.py"}}], ["/home/<USER>/workspace/apps/projects/models.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/projects/models.py"}}], ["/home/<USER>/workspace/apps/documents/apps.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/documents/apps.py"}}], ["/home/<USER>/workspace/apps/documents/models.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/documents/models.py"}}], ["/home/<USER>/workspace/docs/exproject_plan.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/exproject_plan.md"}}], ["/home/<USER>/workspace/apps/integrations/models.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/integrations/models.py"}}], ["/home/<USER>/workspace/exproject/urls.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "exproject/urls.py"}}], ["/home/<USER>/workspace/apps/ui/templatetags/ui_components.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/templatetags/ui_components.py"}}], ["/home/<USER>/workspace/apps/ui/admin.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/admin.py"}}], ["/home/<USER>/workspace/apps/ui/views.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/views.py"}}], ["/home/<USER>/workspace/templates/ui/components/avatar.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/avatar.html"}}], ["/home/<USER>/workspace/templates/ui/components/input.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/input.html"}}], ["/home/<USER>/workspace/templates/core/dashboard_comparison.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/dashboard_comparison.html"}}], ["/home/<USER>/workspace/templates/base.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/base.html"}}], ["/home/<USER>/workspace/templates/ui/components_test.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components_test.html"}}], ["/home/<USER>/workspace/templates/ui/base_modern.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/base_modern.html"}}], ["/home/<USER>/workspace/apps/core/models.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/core/models.py"}}], ["/home/<USER>/workspace/templates/core/dashboard.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/dashboard.html"}}], ["/home/<USER>/workspace/templates/public/home.html", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/public/home.html"}}]]