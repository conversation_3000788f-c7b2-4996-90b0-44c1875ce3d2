[["/home/<USER>/workspace/schema.yml", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "schema.yml"}}], ["/home/<USER>/workspace/apps/projects/apps.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/projects/apps.py"}}], ["/home/<USER>/workspace/apps/projects/models.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/projects/models.py"}}], ["/home/<USER>/workspace/apps/documents/apps.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/documents/apps.py"}}], ["/home/<USER>/workspace/apps/documents/models.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/documents/models.py"}}], ["/home/<USER>/workspace/apps/core/models.py", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/core/models.py"}}], ["/home/<USER>/workspace/docs/exproject_plan.md", {"value": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/exproject_plan.md"}}]]