{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/projects/delete.html"}, "originalCode": "{% extends 'base.html' %}\n\n{% block title %}Elimina {{ project.name }} - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <div class=\"mb-6\">\n        <a href=\"{% url 'projects:detail' project.id %}\" class=\"flex items-center text-blue-600 hover:text-blue-800\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\" />\n            </svg>\n            Torna ai dettagli del progetto\n        </a>\n    </div>\n    \n    <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n        <div class=\"px-6 py-4 border-b border-gray-200\">\n            <h1 class=\"text-xl font-semibold text-gray-900\">Elimina Progetto</h1>\n        </div>\n        \n        <div class=\"p-6\">\n            <div class=\"bg-red-50 border-l-4 border-red-500 p-4 mb-6\">\n                <div class=\"flex\">\n                    <div class=\"flex-shrink-0\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </div>\n                    <div class=\"ml-3\">\n                        <p class=\"text-sm text-red-700\">\n                            <strong>Attenzione!</strong> Stai per eliminare definitivamente il progetto <strong>\"{{ project.name }}\"</strong>.\n                        </p>\n                        <p class=\"text-sm text-red-700 mt-2\">\n                            Questa azione non può essere annullata. Tutti i dati associati al progetto, comprese le particelle, potrebbero essere persi.\n                        </p>\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"bg-gray-50 p-4 rounded-lg mb-6\">\n                <h2 class=\"text-lg font-medium text-gray-800 mb-3\">Dettagli Progetto</h2>\n                <p class=\"text-gray-600 mb-2\"><strong>Nome:</strong> {{ project.name }}</p>\n                <p class=\"text-gray-600 mb-2\"><strong>Descrizione:</strong> {{ project.description }}</p>\n                <p class=\"text-gray-600 mb-2\"><strong>Budget:</strong> € {{ project.budget|floatformat:2 }}</p>\n                <p class=\"text-gray-600 mb-2\"><strong>Stato:</strong> {{ project.get_status_display }}</p>\n                <p class=\"text-gray-600 mb-2\"><strong>Particelle associate:</strong> {{ project.parcels.count }}</p>\n                <p class=\"text-gray-600\"><strong>Data creazione:</strong> {{ project.created_at|date:\"d/m/Y\" }}</p>\n            </div>\n            \n            <form method=\"post\">\n                {% csrf_token %}\n                <div class=\"flex justify-end space-x-3\">\n                    <a href=\"{% url 'projects:detail' project.id %}\" class=\"btn btn-secondary\">Annulla</a>\n                    <button type=\"submit\" class=\"btn btn-danger\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1 inline\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                        </svg>\n                        Elimina Definitivamente\n                    </button>\n                </div>\n            </form>\n        </div>\n    </div>\n</div>\n{% endblock %} ", "modifiedCode": "{% extends 'base.html' %}\n\n{% block title %}Elimina {{ project.name }} - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <div class=\"mb-6\">\n        <a href=\"{% url 'projects:detail' project.id %}\" class=\"flex items-center text-blue-600 hover:text-blue-800\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\" />\n            </svg>\n            Torna ai dettagli del progetto\n        </a>\n    </div>\n    \n    <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n        <div class=\"px-6 py-4 border-b border-gray-200\">\n            <h1 class=\"text-xl font-semibold text-gray-900\">Elimina Progetto</h1>\n        </div>\n        \n        <div class=\"p-6\">\n            <div class=\"bg-red-50 border-l-4 border-red-500 p-4 mb-6\">\n                <div class=\"flex\">\n                    <div class=\"flex-shrink-0\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </div>\n                    <div class=\"ml-3\">\n                        <p class=\"text-sm text-red-700\">\n                            <strong>Attenzione!</strong> Stai per eliminare definitivamente il progetto <strong>\"{{ project.name }}\"</strong>.\n                        </p>\n                        <p class=\"text-sm text-red-700 mt-2\">\n                            Questa azione non può essere annullata. Tutti i dati associati al progetto, comprese le particelle, potrebbero essere persi.\n                        </p>\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"bg-gray-50 p-4 rounded-lg mb-6\">\n                <h2 class=\"text-lg font-medium text-gray-800 mb-3\">Dettagli Progetto</h2>\n                <p class=\"text-gray-600 mb-2\"><strong>Nome:</strong> {{ project.name }}</p>\n                <p class=\"text-gray-600 mb-2\"><strong>Descrizione:</strong> {{ project.description }}</p>\n                <p class=\"text-gray-600 mb-2\"><strong>Budget:</strong> € {{ project.budget|floatformat:2 }}</p>\n                <p class=\"text-gray-600 mb-2\"><strong>Stato:</strong> {{ project.get_status_display }}</p>\n                <p class=\"text-gray-600 mb-2\"><strong>Particelle associate:</strong> {{ project.parcels.count }}</p>\n                <p class=\"text-gray-600\"><strong>Data creazione:</strong> {{ project.created_at|date:\"d/m/Y\" }}</p>\n            </div>\n            \n            <form method=\"post\">\n                {% csrf_token %}\n                <div class=\"flex justify-end space-x-3\">\n                    <a href=\"{% url 'projects:detail' project.id %}\" class=\"btn btn-secondary\">Annulla</a>\n                    <button type=\"submit\" class=\"btn btn-danger\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1 inline\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                        </svg>\n                        Elimina Definitivamente\n                    </button>\n                </div>\n            </form>\n        </div>\n    </div>\n</div>\n{% endblock %} "}