{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/button.html"}, "originalCode": "{# Componente <PERSON><PERSON> ispirato a shadcn/ui #}\n<button class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\n    {% if size == 'sm' %}h-9 rounded-md px-3{% elif size == 'lg' %}h-11 rounded-md px-8{% elif size == 'icon' %}h-10 w-10{% else %}h-10 px-4 py-2{% endif %}\n    {% if variant == 'default' %}bg-primary text-primary-foreground hover:bg-primary/90{% elif variant == 'secondary' %}bg-secondary text-secondary-foreground hover:bg-secondary/80{% elif variant == 'outline' %}border border-input bg-background hover:bg-accent hover:text-accent-foreground{% elif variant == 'ghost' %}hover:bg-accent hover:text-accent-foreground{% elif variant == 'destructive' %}bg-destructive text-destructive-foreground hover:bg-destructive/90{% else %}bg-primary text-primary-foreground hover:bg-primary/90{% endif %}\"\n    {% for key, value in attrs.items %}{{ key }}=\"{{ value }}\"{% endfor %}>\n    {{ text }}\n</button>\n", "modifiedCode": "{# Componente <PERSON><PERSON> ispirato a shadcn/ui #}\n<button class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\n    {% if size == 'sm' %}h-9 rounded-md px-3{% elif size == 'lg' %}h-11 rounded-md px-8{% elif size == 'icon' %}h-10 w-10{% else %}h-10 px-4 py-2{% endif %}\n    {% if variant == 'default' %}bg-primary text-primary-foreground hover:bg-primary/90{% elif variant == 'secondary' %}bg-secondary text-secondary-foreground hover:bg-secondary/80{% elif variant == 'outline' %}border border-input bg-background hover:bg-accent hover:text-accent-foreground{% elif variant == 'ghost' %}hover:bg-accent hover:text-accent-foreground{% elif variant == 'destructive' %}bg-destructive text-destructive-foreground hover:bg-destructive/90{% else %}bg-primary text-primary-foreground hover:bg-primary/90{% endif %}\"\n    {% for key, value in attrs.items %}{{ key }}=\"{{ value }}\"{% endfor %}>\n    {{ text }}\n</button>\n"}