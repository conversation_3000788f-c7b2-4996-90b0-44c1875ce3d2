{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/dashboard_comparison.html"}, "originalCode": "{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}Confronto Design - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <!-- Header -->\n    <div class=\"mb-8\">\n        <h1 class=\"text-3xl font-bold tracking-tight mb-2\">Confronto Design System</h1>\n        <p class=\"text-muted-foreground\">Confronto tra il vecchio design e il nuovo design system moderno</p>\n    </div>\n    \n    <!-- Toggle per confronto -->\n    <div class=\"mb-8\" x-data=\"{ showOld: false }\">\n        <div class=\"flex items-center space-x-4\">\n            <span class=\"text-sm font-medium\">Mostra design:</span>\n            <button @click=\"showOld = false\" \n                    :class=\"!showOld ? 'bg-primary text-primary-foreground' : 'bg-secondary text-secondary-foreground'\"\n                    class=\"px-3 py-1 rounded-md text-sm font-medium transition-colors\">\n                Nuovo (shadcn/ui)\n            </button>\n            <button @click=\"showOld = true\"\n                    :class=\"showOld ? 'bg-primary text-primary-foreground' : 'bg-secondary text-secondary-foreground'\" \n                    class=\"px-3 py-1 rounded-md text-sm font-medium transition-colors\">\n                Vecchio (TailwindCSS base)\n            </button>\n        </div>\n        \n        <!-- Nuovo Design -->\n        <div x-show=\"!showOld\" class=\"mt-8\">\n            <h2 class=\"text-2xl font-bold mb-6\">✨ Nuovo Design System (shadcn/ui inspired)</h2>\n            \n            <!-- Buttons -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Buttons</h3>\n                <div class=\"flex flex-wrap gap-2\">\n                    <button class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90\">Primary</button>\n                    <button class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 bg-secondary text-secondary-foreground hover:bg-secondary/80\">Secondary</button>\n                    <button class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground\">Outline</button>\n                    <button class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 hover:bg-accent hover:text-accent-foreground\">Ghost</button>\n                    <button class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 bg-destructive text-destructive-foreground hover:bg-destructive/90\">Destructive</button>\n                </div>\n            </div>\n            \n            <!-- Cards -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Cards</h3>\n                <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                    <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n                        <div class=\"flex flex-col space-y-1.5 p-6\">\n                            <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Progetti Attivi</h3>\n                            <p class=\"text-sm text-muted-foreground\">Panoramica progetti in corso</p>\n                        </div>\n                        <div class=\"p-6 pt-0\">\n                            <div class=\"text-2xl font-bold\">24</div>\n                            <p class=\"text-xs text-muted-foreground\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"inline h-3 w-3 mr-1\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941\" />\n                                </svg>\n                                +12% dal mese scorso\n                            </p>\n                        </div>\n                    </div>\n\n                    <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n                        <div class=\"flex flex-col space-y-1.5 p-6\">\n                            <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Documenti Generati</h3>\n                        </div>\n                        <div class=\"p-6 pt-0\">\n                            <div class=\"text-2xl font-bold\">156</div>\n                            <p class=\"text-xs text-muted-foreground\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"inline h-3 w-3 mr-1\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\" />\n                                </svg>\n                                +8% dal mese scorso\n                            </p>\n                        </div>\n                    </div>\n\n                    <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n                        <div class=\"flex flex-col space-y-1.5 p-6\">\n                            <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Valore Totale</h3>\n                        </div>\n                        <div class=\"p-6 pt-0\">\n                            <div class=\"text-2xl font-bold\">€2.4M</div>\n                            <p class=\"text-xs text-muted-foreground\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"inline h-3 w-3 mr-1\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M14.25 7.756a4.5 4.5 0 100 8.488M7.5 10.5h5.25m-5.25 3h5.25M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                                </svg>\n                                +15% dal mese scorso\n                            </p>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            \n            <!-- Badges -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Badges</h3>\n                <div class=\"flex flex-wrap gap-2\">\n                    <div class=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-primary text-primary-foreground hover:bg-primary/80\">Attivo</div>\n                    <div class=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-secondary text-secondary-foreground hover:bg-secondary/80\">In Attesa</div>\n                    <div class=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground border-input\">Completato</div>\n                    <div class=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-destructive text-destructive-foreground hover:bg-destructive/80\">Errore</div>\n                </div>\n            </div>\n            \n            <!-- Alerts -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Alerts</h3>\n                <div class=\"space-y-4\">\n                    <div class=\"relative w-full rounded-lg border p-4 bg-background text-foreground border\">\n                        <div>\n                            <h5 class=\"mb-1 font-medium leading-none tracking-tight\">Informazione</h5>\n                            <div class=\"text-sm\">Questo è un messaggio informativo</div>\n                        </div>\n                    </div>\n                    <div class=\"relative w-full rounded-lg border p-4 border-success/50 text-success\">\n                        <div>\n                            <h5 class=\"mb-1 font-medium leading-none tracking-tight\">Successo</h5>\n                            <div class=\"text-sm\">Operazione completata con successo</div>\n                        </div>\n                    </div>\n                    <div class=\"relative w-full rounded-lg border p-4 border-warning/50 text-warning\">\n                        <div>\n                            <h5 class=\"mb-1 font-medium leading-none tracking-tight\">Attenzione</h5>\n                            <div class=\"text-sm\">Attenzione: verifica i dati inseriti</div>\n                        </div>\n                    </div>\n                    <div class=\"relative w-full rounded-lg border p-4 border-destructive/50 text-destructive\">\n                        <div>\n                            <h5 class=\"mb-1 font-medium leading-none tracking-tight\">Errore</h5>\n                            <div class=\"text-sm\">Si è verificato un errore</div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <!-- Inputs -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Form Elements</h3>\n                <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl\">\n                    <div class=\"grid w-full max-w-sm items-center gap-1.5\">\n                        <label class=\"text-sm font-medium leading-none\">Nome Progetto</label>\n                        <input type=\"text\" placeholder=\"Inserisci il nome del progetto\" class=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\">\n                    </div>\n                    <div class=\"grid w-full max-w-sm items-center gap-1.5\">\n                        <label class=\"text-sm font-medium leading-none\">Email</label>\n                        <input type=\"email\" placeholder=\"<EMAIL>\" class=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\">\n                    </div>\n                    <div class=\"grid w-full max-w-sm items-center gap-1.5\">\n                        <label class=\"text-sm font-medium leading-none\">Budget</label>\n                        <input type=\"number\" placeholder=\"0.00\" class=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\">\n                        <p class=\"text-sm text-muted-foreground\">Inserisci il budget in euro</p>\n                    </div>\n                    <div class=\"grid w-full max-w-sm items-center gap-1.5\">\n                        <label class=\"text-sm font-medium leading-none\">Campo con Errore <span class=\"text-destructive\">*</span></label>\n                        <input type=\"text\" placeholder=\"Valore non valido\" class=\"flex h-10 w-full rounded-md border border-destructive bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\">\n                        <p class=\"text-sm text-destructive\">Questo campo è obbligatorio</p>\n                    </div>\n                </div>\n            </div>\n        </div>\n        \n        <!-- Vecchio Design -->\n        <div x-show=\"showOld\" class=\"mt-8\">\n            <h2 class=\"text-2xl font-bold mb-6\">🔧 Vecchio Design (TailwindCSS base)</h2>\n            \n            <!-- Old Buttons -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Buttons</h3>\n                <div class=\"flex flex-wrap gap-2\">\n                    <button class=\"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\">Primary</button>\n                    <button class=\"bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\">Secondary</button>\n                    <button class=\"border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-bold py-2 px-4 rounded\">Outline</button>\n                    <button class=\"text-blue-600 hover:bg-blue-100 font-bold py-2 px-4 rounded\">Ghost</button>\n                    <button class=\"bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded\">Destructive</button>\n                </div>\n            </div>\n            \n            <!-- Old Cards -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Cards</h3>\n                <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                    <div class=\"bg-white rounded-lg shadow p-6\">\n                        <h4 class=\"text-lg font-medium text-gray-900 mb-2\">Progetti Attivi</h4>\n                        <p class=\"text-gray-600 text-sm mb-4\">Panoramica progetti in corso</p>\n                        <div class=\"text-2xl font-bold text-blue-600\">24</div>\n                        <p class=\"text-xs text-gray-500\">+12% dal mese scorso</p>\n                    </div>\n                    \n                    <div class=\"bg-white rounded-lg shadow p-6\">\n                        <h4 class=\"text-lg font-medium text-gray-900 mb-2\">Documenti Generati</h4>\n                        <div class=\"text-2xl font-bold text-green-600\">156</div>\n                        <p class=\"text-xs text-gray-500\">+8% dal mese scorso</p>\n                    </div>\n                    \n                    <div class=\"bg-white rounded-lg shadow p-6\">\n                        <h4 class=\"text-lg font-medium text-gray-900 mb-2\">Valore Totale</h4>\n                        <div class=\"text-2xl font-bold text-purple-600\">€2.4M</div>\n                        <p class=\"text-xs text-gray-500\">+15% dal mese scorso</p>\n                    </div>\n                </div>\n            </div>\n            \n            <!-- Old Badges -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Badges</h3>\n                <div class=\"flex flex-wrap gap-2\">\n                    <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\">Attivo</span>\n                    <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\">In Attesa</span>\n                    <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">Completato</span>\n                    <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800\">Errore</span>\n                </div>\n            </div>\n            \n            <!-- Old Alerts -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Alerts</h3>\n                <div class=\"space-y-4\">\n                    <div class=\"bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded\">\n                        <strong>Informazione:</strong> Questo è un messaggio informativo\n                    </div>\n                    <div class=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\">\n                        <strong>Successo:</strong> Operazione completata con successo\n                    </div>\n                    <div class=\"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded\">\n                        <strong>Attenzione:</strong> Verifica i dati inseriti\n                    </div>\n                    <div class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n                        <strong>Errore:</strong> Si è verificato un errore\n                    </div>\n                </div>\n            </div>\n            \n            <!-- Old Inputs -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Form Elements</h3>\n                <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl\">\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-1\">Nome Progetto</label>\n                        <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" placeholder=\"Inserisci il nome del progetto\">\n                    </div>\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\n                        <input type=\"email\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" placeholder=\"<EMAIL>\">\n                    </div>\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-1\">Budget</label>\n                        <input type=\"number\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" placeholder=\"0.00\">\n                        <p class=\"text-xs text-gray-500 mt-1\">Inserisci il budget in euro</p>\n                    </div>\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-1\">Campo con Errore</label>\n                        <input type=\"text\" class=\"w-full px-3 py-2 border border-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\" placeholder=\"Valore non valido\">\n                        <p class=\"text-xs text-red-500 mt-1\">Questo campo è obbligatorio</p>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    \n    <!-- Differenze principali -->\n    <div class=\"mt-12 p-6 bg-muted rounded-lg\">\n        <h2 class=\"text-xl font-bold mb-4\">🎯 Principali Differenze</h2>\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n                <h3 class=\"font-semibold text-green-600 mb-2\">✅ Nuovo Design System</h3>\n                <ul class=\"text-sm space-y-1\">\n                    <li>• Design tokens consistenti</li>\n                    <li>• Componenti riutilizzabili</li>\n                    <li>• Varianti predefinite</li>\n                    <li>• Dark mode ready</li>\n                    <li>• Accessibilità migliorata</li>\n                    <li>• Animazioni fluide</li>\n                    <li>• White label support</li>\n                </ul>\n            </div>\n            <div>\n                <h3 class=\"font-semibold text-orange-600 mb-2\">⚠️ Vecchio Design</h3>\n                <ul class=\"text-sm space-y-1\">\n                    <li>• Stili hardcoded</li>\n                    <li>• Inconsistenze visive</li>\n                    <li>• Difficile manutenzione</li>\n                    <li>• No standardizzazione</li>\n                    <li>• Accessibilità limitata</li>\n                    <li>• Personalizzazione complessa</li>\n                    <li>• No sistema di temi</li>\n                </ul>\n            </div>\n        </div>\n    </div>\n</div>\n{% endblock %}\n", "modifiedCode": "{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}Confronto Design - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <!-- Header -->\n    <div class=\"mb-8\">\n        <h1 class=\"text-3xl font-bold tracking-tight mb-2\">Confronto Design System</h1>\n        <p class=\"text-muted-foreground\">Confronto tra il vecchio design e il nuovo design system moderno</p>\n    </div>\n    \n    <!-- Toggle per confronto -->\n    <div class=\"mb-8\" x-data=\"{ showOld: false }\">\n        <div class=\"flex items-center space-x-4\">\n            <span class=\"text-sm font-medium\">Mostra design:</span>\n            <button @click=\"showOld = false\" \n                    :class=\"!showOld ? 'bg-primary text-primary-foreground' : 'bg-secondary text-secondary-foreground'\"\n                    class=\"px-3 py-1 rounded-md text-sm font-medium transition-colors\">\n                Nuovo (shadcn/ui)\n            </button>\n            <button @click=\"showOld = true\"\n                    :class=\"showOld ? 'bg-primary text-primary-foreground' : 'bg-secondary text-secondary-foreground'\" \n                    class=\"px-3 py-1 rounded-md text-sm font-medium transition-colors\">\n                Vecchio (TailwindCSS base)\n            </button>\n        </div>\n        \n        <!-- Nuovo Design -->\n        <div x-show=\"!showOld\" class=\"mt-8\">\n            <h2 class=\"text-2xl font-bold mb-6\">✨ Nuovo Design System (shadcn/ui inspired)</h2>\n            \n            <!-- Buttons -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Buttons</h3>\n                <div class=\"flex flex-wrap gap-2\">\n                    <button class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90\">Primary</button>\n                    <button class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 bg-secondary text-secondary-foreground hover:bg-secondary/80\">Secondary</button>\n                    <button class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground\">Outline</button>\n                    <button class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 hover:bg-accent hover:text-accent-foreground\">Ghost</button>\n                    <button class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 bg-destructive text-destructive-foreground hover:bg-destructive/90\">Destructive</button>\n                </div>\n            </div>\n            \n            <!-- Cards -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Cards</h3>\n                <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                    <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n                        <div class=\"flex flex-col space-y-1.5 p-6\">\n                            <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Progetti Attivi</h3>\n                            <p class=\"text-sm text-muted-foreground\">Panoramica progetti in corso</p>\n                        </div>\n                        <div class=\"p-6 pt-0\">\n                            <div class=\"text-2xl font-bold\">24</div>\n                            <p class=\"text-xs text-muted-foreground\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"inline h-3 w-3 mr-1\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941\" />\n                                </svg>\n                                +12% dal mese scorso\n                            </p>\n                        </div>\n                    </div>\n\n                    <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n                        <div class=\"flex flex-col space-y-1.5 p-6\">\n                            <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Documenti Generati</h3>\n                        </div>\n                        <div class=\"p-6 pt-0\">\n                            <div class=\"text-2xl font-bold\">156</div>\n                            <p class=\"text-xs text-muted-foreground\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"inline h-3 w-3 mr-1\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\" />\n                                </svg>\n                                +8% dal mese scorso\n                            </p>\n                        </div>\n                    </div>\n\n                    <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n                        <div class=\"flex flex-col space-y-1.5 p-6\">\n                            <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Valore Totale</h3>\n                        </div>\n                        <div class=\"p-6 pt-0\">\n                            <div class=\"text-2xl font-bold\">€2.4M</div>\n                            <p class=\"text-xs text-muted-foreground\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"inline h-3 w-3 mr-1\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M14.25 7.756a4.5 4.5 0 100 8.488M7.5 10.5h5.25m-5.25 3h5.25M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                                </svg>\n                                +15% dal mese scorso\n                            </p>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            \n            <!-- Badges -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Badges</h3>\n                <div class=\"flex flex-wrap gap-2\">\n                    <div class=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-primary text-primary-foreground hover:bg-primary/80\">Attivo</div>\n                    <div class=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-secondary text-secondary-foreground hover:bg-secondary/80\">In Attesa</div>\n                    <div class=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground border-input\">Completato</div>\n                    <div class=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-destructive text-destructive-foreground hover:bg-destructive/80\">Errore</div>\n                </div>\n            </div>\n            \n            <!-- Alerts -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Alerts</h3>\n                <div class=\"space-y-4\">\n                    <div class=\"relative w-full rounded-lg border p-4 bg-background text-foreground border\">\n                        <div>\n                            <h5 class=\"mb-1 font-medium leading-none tracking-tight\">Informazione</h5>\n                            <div class=\"text-sm\">Questo è un messaggio informativo</div>\n                        </div>\n                    </div>\n                    <div class=\"relative w-full rounded-lg border p-4 border-success/50 text-success\">\n                        <div>\n                            <h5 class=\"mb-1 font-medium leading-none tracking-tight\">Successo</h5>\n                            <div class=\"text-sm\">Operazione completata con successo</div>\n                        </div>\n                    </div>\n                    <div class=\"relative w-full rounded-lg border p-4 border-warning/50 text-warning\">\n                        <div>\n                            <h5 class=\"mb-1 font-medium leading-none tracking-tight\">Attenzione</h5>\n                            <div class=\"text-sm\">Attenzione: verifica i dati inseriti</div>\n                        </div>\n                    </div>\n                    <div class=\"relative w-full rounded-lg border p-4 border-destructive/50 text-destructive\">\n                        <div>\n                            <h5 class=\"mb-1 font-medium leading-none tracking-tight\">Errore</h5>\n                            <div class=\"text-sm\">Si è verificato un errore</div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n\n            <!-- Inputs -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Form Elements</h3>\n                <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl\">\n                    <div class=\"grid w-full max-w-sm items-center gap-1.5\">\n                        <label class=\"text-sm font-medium leading-none\">Nome Progetto</label>\n                        <input type=\"text\" placeholder=\"Inserisci il nome del progetto\" class=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\">\n                    </div>\n                    <div class=\"grid w-full max-w-sm items-center gap-1.5\">\n                        <label class=\"text-sm font-medium leading-none\">Email</label>\n                        <input type=\"email\" placeholder=\"<EMAIL>\" class=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\">\n                    </div>\n                    <div class=\"grid w-full max-w-sm items-center gap-1.5\">\n                        <label class=\"text-sm font-medium leading-none\">Budget</label>\n                        <input type=\"number\" placeholder=\"0.00\" class=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\">\n                        <p class=\"text-sm text-muted-foreground\">Inserisci il budget in euro</p>\n                    </div>\n                    <div class=\"grid w-full max-w-sm items-center gap-1.5\">\n                        <label class=\"text-sm font-medium leading-none\">Campo con Errore <span class=\"text-destructive\">*</span></label>\n                        <input type=\"text\" placeholder=\"Valore non valido\" class=\"flex h-10 w-full rounded-md border border-destructive bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\">\n                        <p class=\"text-sm text-destructive\">Questo campo è obbligatorio</p>\n                    </div>\n                </div>\n            </div>\n        </div>\n        \n        <!-- Vecchio Design -->\n        <div x-show=\"showOld\" class=\"mt-8\">\n            <h2 class=\"text-2xl font-bold mb-6\">🔧 Vecchio Design (TailwindCSS base)</h2>\n            \n            <!-- Old Buttons -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Buttons</h3>\n                <div class=\"flex flex-wrap gap-2\">\n                    <button class=\"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\">Primary</button>\n                    <button class=\"bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\">Secondary</button>\n                    <button class=\"border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-bold py-2 px-4 rounded\">Outline</button>\n                    <button class=\"text-blue-600 hover:bg-blue-100 font-bold py-2 px-4 rounded\">Ghost</button>\n                    <button class=\"bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded\">Destructive</button>\n                </div>\n            </div>\n            \n            <!-- Old Cards -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Cards</h3>\n                <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                    <div class=\"bg-white rounded-lg shadow p-6\">\n                        <h4 class=\"text-lg font-medium text-gray-900 mb-2\">Progetti Attivi</h4>\n                        <p class=\"text-gray-600 text-sm mb-4\">Panoramica progetti in corso</p>\n                        <div class=\"text-2xl font-bold text-blue-600\">24</div>\n                        <p class=\"text-xs text-gray-500\">+12% dal mese scorso</p>\n                    </div>\n                    \n                    <div class=\"bg-white rounded-lg shadow p-6\">\n                        <h4 class=\"text-lg font-medium text-gray-900 mb-2\">Documenti Generati</h4>\n                        <div class=\"text-2xl font-bold text-green-600\">156</div>\n                        <p class=\"text-xs text-gray-500\">+8% dal mese scorso</p>\n                    </div>\n                    \n                    <div class=\"bg-white rounded-lg shadow p-6\">\n                        <h4 class=\"text-lg font-medium text-gray-900 mb-2\">Valore Totale</h4>\n                        <div class=\"text-2xl font-bold text-purple-600\">€2.4M</div>\n                        <p class=\"text-xs text-gray-500\">+15% dal mese scorso</p>\n                    </div>\n                </div>\n            </div>\n            \n            <!-- Old Badges -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Badges</h3>\n                <div class=\"flex flex-wrap gap-2\">\n                    <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\">Attivo</span>\n                    <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\">In Attesa</span>\n                    <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">Completato</span>\n                    <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800\">Errore</span>\n                </div>\n            </div>\n            \n            <!-- Old Alerts -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Alerts</h3>\n                <div class=\"space-y-4\">\n                    <div class=\"bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded\">\n                        <strong>Informazione:</strong> Questo è un messaggio informativo\n                    </div>\n                    <div class=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\">\n                        <strong>Successo:</strong> Operazione completata con successo\n                    </div>\n                    <div class=\"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded\">\n                        <strong>Attenzione:</strong> Verifica i dati inseriti\n                    </div>\n                    <div class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n                        <strong>Errore:</strong> Si è verificato un errore\n                    </div>\n                </div>\n            </div>\n            \n            <!-- Old Inputs -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Form Elements</h3>\n                <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl\">\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-1\">Nome Progetto</label>\n                        <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" placeholder=\"Inserisci il nome del progetto\">\n                    </div>\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\n                        <input type=\"email\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" placeholder=\"<EMAIL>\">\n                    </div>\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-1\">Budget</label>\n                        <input type=\"number\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" placeholder=\"0.00\">\n                        <p class=\"text-xs text-gray-500 mt-1\">Inserisci il budget in euro</p>\n                    </div>\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-1\">Campo con Errore</label>\n                        <input type=\"text\" class=\"w-full px-3 py-2 border border-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\" placeholder=\"Valore non valido\">\n                        <p class=\"text-xs text-red-500 mt-1\">Questo campo è obbligatorio</p>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    \n    <!-- Differenze principali -->\n    <div class=\"mt-12 p-6 bg-muted rounded-lg\">\n        <h2 class=\"text-xl font-bold mb-4\">🎯 Principali Differenze</h2>\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n                <h3 class=\"font-semibold text-green-600 mb-2\">✅ Nuovo Design System</h3>\n                <ul class=\"text-sm space-y-1\">\n                    <li>• Design tokens consistenti</li>\n                    <li>• Componenti riutilizzabili</li>\n                    <li>• Varianti predefinite</li>\n                    <li>• Dark mode ready</li>\n                    <li>• Accessibilità migliorata</li>\n                    <li>• Animazioni fluide</li>\n                    <li>• White label support</li>\n                </ul>\n            </div>\n            <div>\n                <h3 class=\"font-semibold text-orange-600 mb-2\">⚠️ Vecchio Design</h3>\n                <ul class=\"text-sm space-y-1\">\n                    <li>• Stili hardcoded</li>\n                    <li>• Inconsistenze visive</li>\n                    <li>• Difficile manutenzione</li>\n                    <li>• No standardizzazione</li>\n                    <li>• Accessibilità limitata</li>\n                    <li>• Personalizzazione complessa</li>\n                    <li>• No sistema di temi</li>\n                </ul>\n            </div>\n        </div>\n    </div>\n</div>\n{% endblock %}\n"}