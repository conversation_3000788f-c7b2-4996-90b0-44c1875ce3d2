{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/management/commands/init_design_system.py"}, "modifiedCode": "from django.core.management.base import BaseCommand\nfrom django.contrib.auth.models import User\nfrom apps.ui.models import Theme, ComponentLibrary, LayoutConfiguration\nfrom apps.ui.design_tokens import DEFAULT_THEME_CONFIG, THEME_PRESETS\n\n\nclass Command(BaseCommand):\n    help = 'Inizializza il design system con temi e componenti di base'\n    \n    def add_arguments(self, parser):\n        parser.add_argument(\n            '--reset',\n            action='store_true',\n            help='Rimuove tutti i dati esistenti prima di inizializzare',\n        )\n    \n    def handle(self, *args, **options):\n        if options['reset']:\n            self.stdout.write('Rimozione dati esistenti...')\n            Theme.objects.all().delete()\n            ComponentLibrary.objects.all().delete()\n            LayoutConfiguration.objects.all().delete()\n        \n        # Crea tema di default\n        self.create_default_themes()\n        \n        # Crea componenti base\n        self.create_base_components()\n        \n        # Crea layout di base\n        self.create_base_layouts()\n        \n        self.stdout.write(\n            self.style.SUCCESS('Design system inizializzato con successo!')\n        )\n    \n    def create_default_themes(self):\n        \"\"\"Crea i temi predefiniti\"\"\"\n        self.stdout.write('Creazione temi predefiniti...')\n        \n        # Tema di default\n        default_theme, created = Theme.objects.get_or_create(\n            slug='exproject-default',\n            defaults={\n                'name': 'ExProject Default',\n                'is_default': True,\n                'is_active': True,\n                'colors': DEFAULT_THEME_CONFIG['colors'],\n                'typography': DEFAULT_THEME_CONFIG['typography'],\n                'spacing': DEFAULT_THEME_CONFIG['spacing'],\n                'borders': DEFAULT_THEME_CONFIG['borders'],\n                'shadows': DEFAULT_THEME_CONFIG['shadows'],\n                'brand_name': 'ExProject',\n                'brand_tagline': 'Sistema di gestione espropri',\n            }\n        )\n        \n        if created:\n            self.stdout.write(f'  ✓ Creato tema: {default_theme.name}')\n        \n        # Temi preset per diversi tipi di clienti\n        for preset_name, preset_config in THEME_PRESETS.items():\n            theme, created = Theme.objects.get_or_create(\n                slug=f'exproject-{preset_name}',\n                defaults={\n                    'name': f'ExProject {preset_config[\"name\"]}',\n                    'is_active': True,\n                    'colors': preset_config.get('colors', DEFAULT_THEME_CONFIG['colors']),\n                    'typography': DEFAULT_THEME_CONFIG['typography'],\n                    'spacing': DEFAULT_THEME_CONFIG['spacing'],\n                    'borders': DEFAULT_THEME_CONFIG['borders'],\n                    'shadows': DEFAULT_THEME_CONFIG['shadows'],\n                    'brand_name': 'ExProject',\n                    'brand_tagline': f'Sistema per {preset_config[\"name\"].lower()}',\n                }\n            )\n            \n            if created:\n                self.stdout.write(f'  ✓ Creato tema: {theme.name}')\n    \n    def create_base_components(self):\n        \"\"\"Crea i componenti base\"\"\"\n        self.stdout.write('Creazione componenti base...')\n        \n        components = [\n            {\n                'name': 'Button',\n                'slug': 'button',\n                'category': 'atoms',\n                'template_content': '<!-- Template button -->',\n                'variants': {\n                    'default': {'class': 'bg-primary text-primary-foreground hover:bg-primary/90'},\n                    'secondary': {'class': 'bg-secondary text-secondary-foreground hover:bg-secondary/80'},\n                    'outline': {'class': 'border border-input bg-background hover:bg-accent'},\n                    'ghost': {'class': 'hover:bg-accent hover:text-accent-foreground'},\n                    'destructive': {'class': 'bg-destructive text-destructive-foreground hover:bg-destructive/90'},\n                },\n                'description': 'Componente button con varianti multiple'\n            },\n            {\n                'name': 'Input',\n                'slug': 'input',\n                'category': 'atoms',\n                'template_content': '<!-- Template input -->',\n                'variants': {\n                    'default': {'class': 'border border-input bg-background'},\n                    'error': {'class': 'border-destructive'},\n                },\n                'description': 'Componente input con validazione'\n            },\n            {\n                'name': 'Card',\n                'slug': 'card',\n                'category': 'molecules',\n                'template_content': '<!-- Template card -->',\n                'variants': {\n                    'default': {'class': 'rounded-lg border bg-card shadow-sm'},\n                    'elevated': {'class': 'rounded-lg border bg-card shadow-lg'},\n                },\n                'description': 'Componente card per contenuti'\n            },\n            {\n                'name': 'Badge',\n                'slug': 'badge',\n                'category': 'atoms',\n                'template_content': '<!-- Template badge -->',\n                'variants': {\n                    'default': {'class': 'bg-primary text-primary-foreground'},\n                    'secondary': {'class': 'bg-secondary text-secondary-foreground'},\n                    'outline': {'class': 'border border-input'},\n                    'destructive': {'class': 'bg-destructive text-destructive-foreground'},\n                },\n                'description': 'Componente badge per status e labels'\n            },\n            {\n                'name': 'Alert',\n                'slug': 'alert',\n                'category': 'molecules',\n                'template_content': '<!-- Template alert -->',\n                'variants': {\n                    'default': {'class': 'bg-background text-foreground border'},\n                    'destructive': {'class': 'border-destructive/50 text-destructive'},\n                    'warning': {'class': 'border-warning/50 text-warning'},\n                    'success': {'class': 'border-success/50 text-success'},\n                },\n                'description': 'Componente alert per messaggi'\n            },\n            {\n                'name': 'Table',\n                'slug': 'table',\n                'category': 'organisms',\n                'template_content': '<!-- Template table -->',\n                'variants': {\n                    'default': {'class': 'w-full caption-bottom text-sm'},\n                    'striped': {'class': 'w-full caption-bottom text-sm [&_tr:nth-child(even)]:bg-muted/50'},\n                },\n                'description': 'Componente table responsive'\n            },\n        ]\n        \n        for comp_data in components:\n            component, created = ComponentLibrary.objects.get_or_create(\n                slug=comp_data['slug'],\n                defaults=comp_data\n            )\n            \n            if created:\n                self.stdout.write(f'  ✓ Creato componente: {component.name}')\n    \n    def create_base_layouts(self):\n        \"\"\"Crea i layout base\"\"\"\n        self.stdout.write('Creazione layout base...')\n        \n        layouts = [\n            {\n                'name': 'Dashboard Standard',\n                'slug': 'dashboard-standard',\n                'layout_type': 'dashboard',\n                'sidebar_enabled': True,\n                'sidebar_position': 'left',\n                'header_style': 'fixed',\n                'footer_enabled': True,\n                'container_max_width': '7xl',\n                'content_padding': '6',\n                'grid_columns': {'sm': 1, 'md': 2, 'lg': 3, 'xl': 4},\n                'components': ['header', 'sidebar', 'main', 'footer']\n            },\n            {\n                'name': 'Form Centrato',\n                'slug': 'form-centered',\n                'layout_type': 'form',\n                'sidebar_enabled': False,\n                'header_style': 'static',\n                'footer_enabled': True,\n                'container_max_width': 'md',\n                'content_padding': '8',\n                'grid_columns': {'sm': 1},\n                'components': ['header', 'main', 'footer']\n            },\n            {\n                'name': 'Lista con Filtri',\n                'slug': 'list-with-filters',\n                'layout_type': 'list',\n                'sidebar_enabled': True,\n                'sidebar_position': 'left',\n                'header_style': 'fixed',\n                'footer_enabled': True,\n                'container_max_width': '7xl',\n                'content_padding': '6',\n                'grid_columns': {'sm': 1, 'lg': 4},\n                'components': ['header', 'sidebar', 'main', 'footer']\n            },\n            {\n                'name': 'Dettaglio Completo',\n                'slug': 'detail-full',\n                'layout_type': 'detail',\n                'sidebar_enabled': False,\n                'header_style': 'fixed',\n                'footer_enabled': True,\n                'container_max_width': '6xl',\n                'content_padding': '6',\n                'grid_columns': {'sm': 1, 'lg': 2},\n                'components': ['header', 'main', 'footer']\n            },\n            {\n                'name': 'Report con Grafici',\n                'slug': 'report-charts',\n                'layout_type': 'report',\n                'sidebar_enabled': True,\n                'sidebar_position': 'right',\n                'header_style': 'fixed',\n                'footer_enabled': True,\n                'container_max_width': '7xl',\n                'content_padding': '6',\n                'grid_columns': {'sm': 1, 'md': 2, 'xl': 3},\n                'components': ['header', 'main', 'sidebar', 'footer']\n            },\n            {\n                'name': 'Pagina Pubblica',\n                'slug': 'public-page',\n                'layout_type': 'public',\n                'sidebar_enabled': False,\n                'header_style': 'static',\n                'footer_enabled': True,\n                'container_max_width': '6xl',\n                'content_padding': '8',\n                'grid_columns': {'sm': 1, 'md': 2},\n                'components': ['header', 'main', 'footer']\n            },\n        ]\n        \n        for layout_data in layouts:\n            layout, created = LayoutConfiguration.objects.get_or_create(\n                slug=layout_data['slug'],\n                defaults=layout_data\n            )\n            \n            if created:\n                self.stdout.write(f'  ✓ Creato layout: {layout.name}')\n"}