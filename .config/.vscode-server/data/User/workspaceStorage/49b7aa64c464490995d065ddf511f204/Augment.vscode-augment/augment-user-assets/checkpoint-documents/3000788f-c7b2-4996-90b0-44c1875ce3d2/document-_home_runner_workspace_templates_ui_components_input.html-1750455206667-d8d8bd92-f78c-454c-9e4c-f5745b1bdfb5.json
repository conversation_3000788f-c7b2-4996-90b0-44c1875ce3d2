{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/input.html"}, "originalCode": "{% load ui_components %}\n{# Componente Input ispirato a shadcn/ui #}\n<div class=\"grid w-full max-w-sm items-center gap-1.5\">\n    {% if label %}\n    <label for=\"{{ name }}\" class=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\">\n        {{ label }}\n        {% if attrs.required %}<span class=\"text-destructive\">*</span>{% endif %}\n    </label>\n    {% endif %}\n    \n    <input \n        type=\"{{ type }}\"\n        id=\"{{ name }}\"\n        name=\"{{ name }}\"\n        {% if placeholder %}placeholder=\"{{ placeholder }}\"{% endif %}\n        class=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\n        {% if attrs.error %}border-destructive{% endif %}\n        {% if attrs.class %}{{ attrs.class }}{% endif %}\"\n        {% for key, value in attrs.items %}{% if key not in 'class,error,required' %}{{ key }}=\"{{ value }}\"{% endfor %}\n    />\n    \n    {% if attrs.error %}\n    <p class=\"text-sm text-destructive\">{{ attrs.error }}</p>\n    {% endif %}\n    \n    {% if attrs.help_text %}\n    <p class=\"text-sm text-muted-foreground\">{{ attrs.help_text }}</p>\n    {% endif %}\n</div>\n", "modifiedCode": "{% load ui_components %}\n{# Componente Input ispirato a shadcn/ui #}\n<div class=\"grid w-full max-w-sm items-center gap-1.5\">\n    {% if label %}\n    <label for=\"{{ name }}\" class=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\">\n        {{ label }}\n        {% if attrs.required %}<span class=\"text-destructive\">*</span>{% endif %}\n    </label>\n    {% endif %}\n    \n    <input \n        type=\"{{ type }}\"\n        id=\"{{ name }}\"\n        name=\"{{ name }}\"\n        {% if placeholder %}placeholder=\"{{ placeholder }}\"{% endif %}\n        class=\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\n        {% if attrs.error %}border-destructive{% endif %}\n        {% if attrs.class %}{{ attrs.class }}{% endif %}\"\n        {% for key, value in attrs.items %}{% if key not in 'class,error,required,help_text' %}{{ key }}=\"{{ value }}\"{% endif %}{% endfor %}\n    />\n    \n    {% if attrs.error %}\n    <p class=\"text-sm text-destructive\">{{ attrs.error }}</p>\n    {% endif %}\n    \n    {% if attrs.help_text %}\n    <p class=\"text-sm text-muted-foreground\">{{ attrs.help_text }}</p>\n    {% endif %}\n</div>\n"}