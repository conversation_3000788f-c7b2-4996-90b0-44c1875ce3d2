{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/boilerplates/dashboard.html"}, "modifiedCode": "{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}Dashboard{% endblock %}\n\n{% block content %}\n<div class=\"flex-1 space-y-4 p-8 pt-6\">\n    <!-- Header Section -->\n    <div class=\"flex items-center justify-between\">\n        <h2 class=\"text-3xl font-bold tracking-tight\">{{ page_title|default:\"Dashboard\" }}</h2>\n        <div class=\"flex items-center space-x-2\">\n            {% if create_url %}\n                {% ui_button \"New Item\" variant=\"default\" onclick=\"location.href='{{ create_url }}'\" %}\n            {% endif %}\n            {% if export_enabled %}\n                {% ui_button \"Export\" variant=\"outline\" %}\n            {% endif %}\n        </div>\n    </div>\n    \n    <!-- Stats Grid -->\n    {% if stats %}\n    <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n        {% for stat in stats %}\n            {% ui_card \n                title=stat.title\n                content=\"<div class='text-2xl font-bold text-{{ stat.color|default:'foreground' }}'>{{ stat.value }}</div>\"\n            %}\n        {% endfor %}\n    </div>\n    {% endif %}\n    \n    <!-- Main Content Grid -->\n    <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-7\">\n        <!-- Primary Content -->\n        <div class=\"col-span-4\">\n            {% ui_card \n                title=main_section.title\n                content=main_section.content\n            %}\n        </div>\n        \n        <!-- Sidebar -->\n        <div class=\"col-span-3\">\n            {% if sidebar_sections %}\n                {% for section in sidebar_sections %}\n                    {% ui_card \n                        title=section.title\n                        content=section.content\n                    %}\n                {% endfor %}\n            {% endif %}\n        </div>\n    </div>\n    \n    <!-- Alerts/Messages -->\n    {% if messages %}\n        <div class=\"space-y-2\">\n            {% for message in messages %}\n                {% ui_alert message.message variant=message.tags %}\n            {% endfor %}\n        </div>\n    {% endif %}\n</div>\n{% endblock %}\n"}