{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/dashboard_comparison.html"}, "modifiedCode": "{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}Confronto Design - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <!-- Header -->\n    <div class=\"mb-8\">\n        <h1 class=\"text-3xl font-bold tracking-tight mb-2\">Confronto Design System</h1>\n        <p class=\"text-muted-foreground\">Confronto tra il vecchio design e il nuovo design system moderno</p>\n    </div>\n    \n    <!-- Toggle per confronto -->\n    <div class=\"mb-8\" x-data=\"{ showOld: false }\">\n        <div class=\"flex items-center space-x-4\">\n            <span class=\"text-sm font-medium\">Mostra design:</span>\n            <button @click=\"showOld = false\" \n                    :class=\"!showOld ? 'bg-primary text-primary-foreground' : 'bg-secondary text-secondary-foreground'\"\n                    class=\"px-3 py-1 rounded-md text-sm font-medium transition-colors\">\n                Nuovo (shadcn/ui)\n            </button>\n            <button @click=\"showOld = true\"\n                    :class=\"showOld ? 'bg-primary text-primary-foreground' : 'bg-secondary text-secondary-foreground'\" \n                    class=\"px-3 py-1 rounded-md text-sm font-medium transition-colors\">\n                Vecchio (TailwindCSS base)\n            </button>\n        </div>\n        \n        <!-- Nuovo Design -->\n        <div x-show=\"!showOld\" class=\"mt-8\">\n            <h2 class=\"text-2xl font-bold mb-6\">✨ Nuovo Design System (shadcn/ui inspired)</h2>\n            \n            <!-- Buttons -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Buttons</h3>\n                <div class=\"flex flex-wrap gap-2\">\n                    {% ui_button \"Primary\" variant=\"default\" %}\n                    {% ui_button \"Secondary\" variant=\"secondary\" %}\n                    {% ui_button \"Outline\" variant=\"outline\" %}\n                    {% ui_button \"Ghost\" variant=\"ghost\" %}\n                    {% ui_button \"Destructive\" variant=\"destructive\" %}\n                    {% ui_button \"Con Icona\" variant=\"default\" icon_left=\"plus\" %}\n                    {% ui_button \"Loading\" variant=\"default\" loading=True %}\n                </div>\n            </div>\n            \n            <!-- Cards -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Cards</h3>\n                <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                    {% ui_card title=\"Progetti Attivi\" description=\"Panoramica progetti in corso\" %}\n                        {% block card_content %}\n                        <div class=\"text-2xl font-bold\">24</div>\n                        <p class=\"text-xs text-muted-foreground\">\n                            {% ui_icon 'trending-up' size=\"3\" class=\"inline mr-1\" %}\n                            +12% dal mese scorso\n                        </p>\n                        {% endblock %}\n                    {% endui_card %}\n                    \n                    {% ui_card title=\"Documenti Generati\" %}\n                        {% block card_content %}\n                        <div class=\"text-2xl font-bold\">156</div>\n                        <p class=\"text-xs text-muted-foreground\">\n                            {% ui_icon 'document' size=\"3\" class=\"inline mr-1\" %}\n                            +8% dal mese scorso\n                        </p>\n                        {% endblock %}\n                    {% endui_card %}\n                    \n                    {% ui_card title=\"Valore Totale\" %}\n                        {% block card_content %}\n                        <div class=\"text-2xl font-bold\">€2.4M</div>\n                        <p class=\"text-xs text-muted-foreground\">\n                            {% ui_icon 'euro' size=\"3\" class=\"inline mr-1\" %}\n                            +15% dal mese scorso\n                        </p>\n                        {% endblock %}\n                    {% endui_card %}\n                </div>\n            </div>\n            \n            <!-- Badges -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Badges</h3>\n                <div class=\"flex flex-wrap gap-2\">\n                    {% ui_badge \"Attivo\" variant=\"default\" %}\n                    {% ui_badge \"In Attesa\" variant=\"secondary\" %}\n                    {% ui_badge \"Completato\" variant=\"outline\" %}\n                    {% ui_badge \"Errore\" variant=\"destructive\" %}\n                    {% ui_badge \"Con Icona\" variant=\"default\" icon=\"check\" %}\n                </div>\n            </div>\n            \n            <!-- Alerts -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Alerts</h3>\n                <div class=\"space-y-4\">\n                    {% ui_alert \"Questo è un messaggio informativo\" variant=\"default\" title=\"Informazione\" %}\n                    {% ui_alert \"Operazione completata con successo\" variant=\"success\" title=\"Successo\" %}\n                    {% ui_alert \"Attenzione: verifica i dati inseriti\" variant=\"warning\" title=\"Attenzione\" %}\n                    {% ui_alert \"Si è verificato un errore\" variant=\"destructive\" title=\"Errore\" %}\n                </div>\n            </div>\n            \n            <!-- Inputs -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Form Elements</h3>\n                <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl\">\n                    {% ui_input \"name\" label=\"Nome Progetto\" placeholder=\"Inserisci il nome del progetto\" %}\n                    {% ui_input \"email\" label=\"Email\" placeholder=\"<EMAIL>\" type=\"email\" %}\n                    {% ui_input \"budget\" label=\"Budget\" placeholder=\"0.00\" type=\"number\" help_text=\"Inserisci il budget in euro\" %}\n                    {% ui_input \"error_field\" label=\"Campo con Errore\" placeholder=\"Valore non valido\" error=\"Questo campo è obbligatorio\" %}\n                </div>\n            </div>\n        </div>\n        \n        <!-- Vecchio Design -->\n        <div x-show=\"showOld\" class=\"mt-8\">\n            <h2 class=\"text-2xl font-bold mb-6\">🔧 Vecchio Design (TailwindCSS base)</h2>\n            \n            <!-- Old Buttons -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Buttons</h3>\n                <div class=\"flex flex-wrap gap-2\">\n                    <button class=\"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\">Primary</button>\n                    <button class=\"bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded\">Secondary</button>\n                    <button class=\"border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-bold py-2 px-4 rounded\">Outline</button>\n                    <button class=\"text-blue-600 hover:bg-blue-100 font-bold py-2 px-4 rounded\">Ghost</button>\n                    <button class=\"bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded\">Destructive</button>\n                </div>\n            </div>\n            \n            <!-- Old Cards -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Cards</h3>\n                <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                    <div class=\"bg-white rounded-lg shadow p-6\">\n                        <h4 class=\"text-lg font-medium text-gray-900 mb-2\">Progetti Attivi</h4>\n                        <p class=\"text-gray-600 text-sm mb-4\">Panoramica progetti in corso</p>\n                        <div class=\"text-2xl font-bold text-blue-600\">24</div>\n                        <p class=\"text-xs text-gray-500\">+12% dal mese scorso</p>\n                    </div>\n                    \n                    <div class=\"bg-white rounded-lg shadow p-6\">\n                        <h4 class=\"text-lg font-medium text-gray-900 mb-2\">Documenti Generati</h4>\n                        <div class=\"text-2xl font-bold text-green-600\">156</div>\n                        <p class=\"text-xs text-gray-500\">+8% dal mese scorso</p>\n                    </div>\n                    \n                    <div class=\"bg-white rounded-lg shadow p-6\">\n                        <h4 class=\"text-lg font-medium text-gray-900 mb-2\">Valore Totale</h4>\n                        <div class=\"text-2xl font-bold text-purple-600\">€2.4M</div>\n                        <p class=\"text-xs text-gray-500\">+15% dal mese scorso</p>\n                    </div>\n                </div>\n            </div>\n            \n            <!-- Old Badges -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Badges</h3>\n                <div class=\"flex flex-wrap gap-2\">\n                    <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\">Attivo</span>\n                    <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\">In Attesa</span>\n                    <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800\">Completato</span>\n                    <span class=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800\">Errore</span>\n                </div>\n            </div>\n            \n            <!-- Old Alerts -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Alerts</h3>\n                <div class=\"space-y-4\">\n                    <div class=\"bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded\">\n                        <strong>Informazione:</strong> Questo è un messaggio informativo\n                    </div>\n                    <div class=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\">\n                        <strong>Successo:</strong> Operazione completata con successo\n                    </div>\n                    <div class=\"bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded\">\n                        <strong>Attenzione:</strong> Verifica i dati inseriti\n                    </div>\n                    <div class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n                        <strong>Errore:</strong> Si è verificato un errore\n                    </div>\n                </div>\n            </div>\n            \n            <!-- Old Inputs -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-4\">Form Elements</h3>\n                <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl\">\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-1\">Nome Progetto</label>\n                        <input type=\"text\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" placeholder=\"Inserisci il nome del progetto\">\n                    </div>\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-1\">Email</label>\n                        <input type=\"email\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" placeholder=\"<EMAIL>\">\n                    </div>\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-1\">Budget</label>\n                        <input type=\"number\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\" placeholder=\"0.00\">\n                        <p class=\"text-xs text-gray-500 mt-1\">Inserisci il budget in euro</p>\n                    </div>\n                    <div>\n                        <label class=\"block text-sm font-medium text-gray-700 mb-1\">Campo con Errore</label>\n                        <input type=\"text\" class=\"w-full px-3 py-2 border border-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500\" placeholder=\"Valore non valido\">\n                        <p class=\"text-xs text-red-500 mt-1\">Questo campo è obbligatorio</p>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    \n    <!-- Differenze principali -->\n    <div class=\"mt-12 p-6 bg-muted rounded-lg\">\n        <h2 class=\"text-xl font-bold mb-4\">🎯 Principali Differenze</h2>\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n                <h3 class=\"font-semibold text-green-600 mb-2\">✅ Nuovo Design System</h3>\n                <ul class=\"text-sm space-y-1\">\n                    <li>• Design tokens consistenti</li>\n                    <li>• Componenti riutilizzabili</li>\n                    <li>• Varianti predefinite</li>\n                    <li>• Dark mode ready</li>\n                    <li>• Accessibilità migliorata</li>\n                    <li>• Animazioni fluide</li>\n                    <li>• White label support</li>\n                </ul>\n            </div>\n            <div>\n                <h3 class=\"font-semibold text-orange-600 mb-2\">⚠️ Vecchio Design</h3>\n                <ul class=\"text-sm space-y-1\">\n                    <li>• Stili hardcoded</li>\n                    <li>• Inconsistenze visive</li>\n                    <li>• Difficile manutenzione</li>\n                    <li>• No standardizzazione</li>\n                    <li>• Accessibilità limitata</li>\n                    <li>• Personalizzazione complessa</li>\n                    <li>• No sistema di temi</li>\n                </ul>\n            </div>\n        </div>\n    </div>\n</div>\n{% endblock %}\n"}