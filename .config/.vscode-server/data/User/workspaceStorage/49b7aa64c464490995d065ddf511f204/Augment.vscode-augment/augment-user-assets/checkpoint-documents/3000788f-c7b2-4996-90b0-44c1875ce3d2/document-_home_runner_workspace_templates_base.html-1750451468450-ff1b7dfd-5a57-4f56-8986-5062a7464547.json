{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/base.html"}, "originalCode": "<!DOCTYPE html>\n<html lang=\"it\">\n<head>\n    {% load static %}\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{% block title %}ExProject{% endblock %}</title>\n    \n    <!-- Favicon e PWA -->\n    <link rel=\"icon\" href=\"{% static 'img/favicon.ico' %}\" type=\"image/x-icon\">\n    <meta name=\"theme-color\" content=\"#2563eb\">\n    \n    <!-- HTMX e Alpine.js per interattività -->\n    <script src=\"https://cdn.jsdelivr.net/npm/htmx.org@1.9.10\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js\" defer></script>\n    \n    <!-- TailwindCSS -->\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script>\n        tailwind.config = {\n            theme: {\n                extend: {\n                    colors: {\n                        primary: '#2563eb',\n                        'primary-dark': '#1d4ed8',\n                        'primary-light': '#3b82f6'\n                    }\n                }\n            }\n        }\n    </script>\n    \n    <link rel=\"stylesheet\" href=\"{% static 'base.css' %}\">\n    {% block extra_head %}{% endblock %}\n</head>\n<body class=\"bg-gray-50 min-h-screen flex flex-col\"\n      hx-headers='{\"X-CSRFToken\": \"{{ csrf_token }}\"}'\n      x-data=\"{}\">\n    \n    <!-- Overlay per mobile sidebar -->\n    <div id=\"sidebar-overlay\" \n         class=\"fixed inset-0 bg-black opacity-50 z-20 hidden md:hidden\" \n         @click=\"document.getElementById('mobile-menu').classList.add('hidden');\n                this.classList.add('hidden');\">\n    </div>\n    \n    {% block header %}\n    <header class=\"bg-blue-600 text-white shadow-lg sticky top-0 z-30\">\n        <div class=\"container mx-auto px-4 py-3\">\n            <div class=\"flex justify-between items-center\">\n                <div class=\"flex items-center\">\n                    <!-- Hamburger menu per mobile -->\n                    <button class=\"mr-2 md:hidden text-white\" \n                            @click=\"document.getElementById('mobile-menu').classList.toggle('hidden');\n                                   document.getElementById('sidebar-overlay').classList.toggle('hidden');\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16M4 18h16\" />\n                        </svg>\n                    </button>\n                    \n                    <h1 class=\"text-2xl font-bold flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-9 w-9 mr-2\" viewBox=\"0 0 80 80\" fill=\"none\">\n                            <!-- Logo per ExProject - Sistema gestione espropri -->\n                            <!-- Sfondo circolare -->\n                            <circle cx=\"40\" cy=\"40\" r=\"36\" fill=\"white\" />\n                            \n                            <!-- Edificio stilizzato -->\n                            <path d=\"M40 12L60 28V64H20V28L40 12Z\" fill=\"#1d4ed8\" />\n                            <path d=\"M40 12L60 28V64H40V12Z\" fill=\"#2563eb\" />\n                            \n                            <!-- Dettagli dell'edificio -->\n                            <rect x=\"26\" y=\"36\" width=\"8\" height=\"12\" rx=\"1\" fill=\"white\" />\n                            <rect x=\"46\" y=\"36\" width=\"8\" height=\"12\" rx=\"1\" fill=\"white\" />\n                            <rect x=\"36\" y=\"48\" width=\"8\" height=\"16\" rx=\"1\" fill=\"white\" />\n                            \n                            <!-- Mappa / Griglia catastale -->\n                            <path d=\"M20 52L32 40L38 46L48 36L60 48\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" />\n                            \n                            <!-- Contorno -->\n                            <circle cx=\"40\" cy=\"40\" r=\"36\" stroke=\"#1d4ed8\" stroke-width=\"2\" />\n                        </svg>\n                        <a href=\"{% url 'public:home' %}\" class=\"hover:text-blue-100\">ExProject</a>\n                    </h1>\n                    <p class=\"text-blue-100 text-sm ml-2 hidden sm:block\">Sistema Gestione Espropri</p>\n                </div>\n                \n                <!-- Barra di ricerca -->\n                {% if user.is_authenticated %}\n                <div class=\"hidden md:block flex-1 max-w-md mx-4\">\n                    <form class=\"relative\" method=\"get\" action=\"{% url 'core:search' %}\">\n                        <input type=\"text\" name=\"q\" placeholder=\"Cerca progetto, particella, documento...\" \n                               class=\"w-full py-1 pl-3 pr-10 rounded-full text-sm bg-blue-700 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white\">\n                        <button type=\"submit\" class=\"absolute right-0 top-0 mt-1 mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 text-blue-200\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                            </svg>\n                        </button>\n                    </form>\n                </div>\n                {% endif %}\n                \n                <div class=\"flex items-center space-x-2\">\n                    {% if user.is_authenticated %}\n                        <!-- Notifiche -->\n                        <div class=\"relative\" x-data=\"{ open: false }\">\n                            <button class=\"p-1 rounded-full hover:bg-blue-700 relative notifications-button\" \n                                    @click=\"open = !open\"\n                                    hx-get=\"{% url 'core:notifications' %}\"\n                                    hx-trigger=\"click\"\n                                    hx-target=\"#notifications-dropdown\"\n                                    hx-swap=\"innerHTML\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\" />\n                                </svg>\n                                <span class=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-blue-600\"></span>\n                            </button>\n                            \n                            <div id=\"notifications-dropdown\" \n                                 class=\"absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg py-1 z-50\"\n                                 x-show=\"open\"\n                                 @click.away=\"open = false\"\n                                 x-transition:enter=\"transition ease-out duration-100\"\n                                 x-transition:enter-start=\"transform opacity-0 scale-95\"\n                                 x-transition:enter-end=\"transform opacity-100 scale-100\"\n                                 style=\"display: none;\">\n                                <!-- Contenuto caricato via HTMX -->\n                            </div>\n                        </div>\n                        \n                        <!-- Menu utente -->\n                        <div class=\"relative ml-3\" x-data=\"{ open: false }\">\n                            <button type=\"button\" \n                                    class=\"flex text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-white user-menu-button\"\n                                    @click=\"open = !open\">\n                                <span class=\"sr-only\">Menu utente</span>\n                                <div class=\"h-8 w-8 rounded-full bg-blue-700 flex items-center justify-center\">\n                                    <span class=\"text-white font-medium\">{{ user.username|slice:\":1\" }}</span>\n                                </div>\n                            </button>\n                            \n                            <div class=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 user-menu-dropdown\"\n                                 x-show=\"open\"\n                                 @click.away=\"open = false\"\n                                 x-transition:enter=\"transition ease-out duration-100\"\n                                 x-transition:enter-start=\"transform opacity-0 scale-95\"\n                                 x-transition:enter-end=\"transform opacity-100 scale-100\"\n                                 style=\"display: none;\">\n                                <div class=\"block px-4 py-2 text-xs text-gray-500\">\n                                    {{ user.get_full_name|default:user.username }}\n                                </div>\n                                <a href=\"{% url 'core:dashboard' %}\" class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">Dashboard</a>\n                                <a href=\"{% url 'core:profile' %}\" class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">Profilo</a>\n                                <a href=\"{% url 'core:settings' %}\" class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">Impostazioni</a>\n                                <div class=\"border-t border-gray-100\"></div>\n                                <a href=\"{% url 'core:logout' %}\" class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">Logout</a>\n                            </div>\n                        </div>\n                    {% else %}\n                        <a href=\"{% url 'core:login' %}\" class=\"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm\">Area Riservata</a>\n                    {% endif %}\n                </div>\n            </div>\n            \n            <!-- Navbar integrata nell'header -->\n            {% if user.is_authenticated %}\n                {% if request.resolver_match.namespace == 'core' or request.resolver_match.namespace == 'projects' or request.resolver_match.namespace == 'workflow' or request.resolver_match.namespace == 'documents' or request.resolver_match.namespace == 'economic' or request.resolver_match.namespace == 'integrations' %}\n                <!-- Navbar amministrativa -->\n                <div class=\"border-t border-blue-500 mt-3 pt-2\">\n                    <ul class=\"flex space-x-6 py-2 overflow-x-auto scrollbar-hide\">\n                        <li><a href=\"{% url 'core:dashboard' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'dashboard' %}font-bold{% endif %}\">Dashboard</a></li>\n                        <li><a href=\"{% url 'projects:list' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.namespace == 'projects' %}font-bold{% endif %}\">Progetti</a></li>\n                        <li><a href=\"{% url 'workflow:list' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.namespace == 'workflow' %}font-bold{% endif %}\">Workflow</a></li>\n                        <li><a href=\"{% url 'documents:list' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.namespace == 'documents' %}font-bold{% endif %}\">Documenti</a></li>\n                        <li><a href=\"{% url 'economic:list' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.namespace == 'economic' %}font-bold{% endif %}\">Economico</a></li>\n                        <li><a href=\"{% url 'integrations:list' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.namespace == 'integrations' %}font-bold{% endif %}\">Integrazioni</a></li>\n                        <li><a href=\"{% url 'public:home' %}\" class=\"hover:text-blue-100 py-2\">Sito Pubblico</a></li>\n                    </ul>\n                </div>\n                {% else %}\n                <!-- Navbar pubblica -->\n                <div class=\"border-t border-blue-500 mt-3 pt-2\">\n                    <ul class=\"flex space-x-6 py-2 overflow-x-auto scrollbar-hide\">\n                        <li><a href=\"{% url 'public:home' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'home' %}font-bold{% endif %}\">Home</a></li>\n                        <li><a href=\"{% url 'public:about' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'about' %}font-bold{% endif %}\">Chi Siamo</a></li>\n                        <li><a href=\"{% url 'public:procedures' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'procedures' %}font-bold{% endif %}\">Procedure</a></li>\n                        <li><a href=\"{% url 'public:news' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'news' %}font-bold{% endif %}\">News</a></li>\n                        <li><a href=\"{% url 'public:contacts' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'contacts' %}font-bold{% endif %}\">Contatti</a></li>\n                        <li><a href=\"{% url 'core:dashboard' %}\" class=\"hover:text-blue-100 py-2 bg-blue-700 px-3 rounded\">Dashboard</a></li>\n                    </ul>\n                </div>\n                {% endif %}\n            {% else %}\n            <!-- Navbar pubblica per utenti non autenticati -->\n            <div class=\"border-t border-blue-500 mt-3 pt-2\">\n                <ul class=\"flex space-x-6 py-2 overflow-x-auto scrollbar-hide\">\n                    <li><a href=\"{% url 'public:home' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'home' %}font-bold{% endif %}\">Home</a></li>\n                    <li><a href=\"{% url 'public:about' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'about' %}font-bold{% endif %}\">Chi Siamo</a></li>\n                    <li><a href=\"{% url 'public:procedures' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'procedures' %}font-bold{% endif %}\">Procedure</a></li>\n                    <li><a href=\"{% url 'public:news' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'news' %}font-bold{% endif %}\">News</a></li>\n                    <li><a href=\"{% url 'public:contacts' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'contacts' %}font-bold{% endif %}\">Contatti</a></li>\n                </ul>\n            </div>\n            {% endif %}\n        </div>\n    </header>\n    {% endblock %}\n\n    <!-- Mobile sidebar menu -->\n    <div id=\"mobile-menu\" class=\"fixed inset-y-0 left-0 w-64 bg-white shadow-lg z-30 transform transition-transform duration-300 ease-in-out hidden md:hidden\">\n        <div class=\"flex items-center justify-between p-4 border-b\">\n            <h2 class=\"font-semibold text-lg\">Menu</h2>\n            <button @click=\"document.getElementById('mobile-menu').classList.add('hidden');\n                              document.getElementById('sidebar-overlay').classList.add('hidden');\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n            </button>\n        </div>\n        \n        <nav class=\"mt-2 px-4\">\n            {% block mobile_nav %}\n            <ul class=\"space-y-1\">\n                {% block nav_mobile_items %}\n                {% if user.is_authenticated %}\n                    {% if request.resolver_match.namespace == 'core' or request.resolver_match.namespace == 'projects' or request.resolver_match.namespace == 'workflow' or request.resolver_match.namespace == 'documents' or request.resolver_match.namespace == 'economic' or request.resolver_match.namespace == 'integrations' %}\n                    <!-- Menu mobile amministrativo -->\n                    <li><a href=\"{% url 'core:dashboard' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'dashboard' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Dashboard</a></li>\n                    <li><a href=\"{% url 'projects:list' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.namespace == 'projects' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Progetti</a></li>\n                    <li><a href=\"{% url 'workflow:list' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.namespace == 'workflow' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Workflow</a></li>\n                    <li><a href=\"{% url 'documents:list' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.namespace == 'documents' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Documenti</a></li>\n                    <li><a href=\"{% url 'economic:list' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.namespace == 'economic' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Economico</a></li>\n                    <li><a href=\"{% url 'integrations:list' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.namespace == 'integrations' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Integrazioni</a></li>\n                    <li class=\"border-t my-2 pt-2\">\n                        <a href=\"{% url 'public:home' %}\" class=\"block py-2 px-4 rounded bg-gray-100 text-gray-700 hover:bg-gray-200\">Sito Pubblico</a>\n                    </li>\n                    {% else %}\n                    <!-- Menu mobile pubblico per utenti autenticati -->\n                    <li><a href=\"{% url 'public:home' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'home' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Home</a></li>\n                    <li><a href=\"{% url 'public:about' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'about' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Chi Siamo</a></li>\n                    <li><a href=\"{% url 'public:procedures' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'procedures' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Procedure</a></li>\n                    <li><a href=\"{% url 'public:news' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'news' %}text-blue-600 font-medium bg-blue-50{% endif %}\">News</a></li>\n                    <li><a href=\"{% url 'public:contacts' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'contacts' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Contatti</a></li>\n                    <li class=\"border-t my-2 pt-2\">\n                        <a href=\"{% url 'core:dashboard' %}\" class=\"block py-2 px-4 rounded bg-blue-100 text-blue-700 hover:bg-blue-200\">Dashboard</a>\n                    </li>\n                    {% endif %}\n                {% else %}\n                <!-- Menu mobile per utenti non autenticati -->\n                <li><a href=\"{% url 'public:home' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'home' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Home</a></li>\n                <li><a href=\"{% url 'public:about' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'about' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Chi Siamo</a></li>\n                <li><a href=\"{% url 'public:procedures' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'procedures' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Procedure</a></li>\n                <li><a href=\"{% url 'public:news' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'news' %}text-blue-600 font-medium bg-blue-50{% endif %}\">News</a></li>\n                <li><a href=\"{% url 'public:contacts' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'contacts' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Contatti</a></li>\n                {% endif %}\n                {% endblock %}\n            </ul>\n            {% endblock %}\n        </nav>\n    </div>\n\n    {% block navigation %}\n    <!-- Navigation is now integrated in the header -->\n    {% endblock %}\n\n    {% block messages %}\n    <div id=\"messages-container\" class=\"container mx-auto px-4 py-2\">\n    {% if messages %}\n        {% for message in messages %}\n            <div class=\"alert alert-{{ message.tags }} bg-{% if message.tags == 'error' %}red{% elif message.tags == 'warning' %}yellow{% else %}green{% endif %}-100 border border-{% if message.tags == 'error' %}red{% elif message.tags == 'warning' %}yellow{% else %}green{% endif %}-400 text-{% if message.tags == 'error' %}red{% elif message.tags == 'warning' %}yellow{% else %}green{% endif %}-700 px-4 py-3 rounded mb-2 flex justify-between items-center fade-in\"\n                 hx-swap-oob=\"true\">\n                <span>{{ message }}</span>\n                <button type=\"button\" class=\"text-gray-500 hover:text-gray-700\" onclick=\"this.parentElement.remove()\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                    </svg>\n                </button>\n            </div>\n        {% endfor %}\n    {% endif %}\n    </div>\n    {% endblock %}\n\n    <main class=\"flex-1\">\n        {% block main %}\n        <div class=\"container mx-auto px-4 py-6\">\n            {% block content %}{% endblock %}\n        </div>\n        {% endblock %}\n    </main>\n\n    {% block footer %}\n    <footer class=\"bg-gray-800 text-white mt-auto\">\n        <div class=\"container mx-auto px-4 py-6\">\n            <div class=\"md:flex md:justify-between\">\n                <div class=\"mb-4 md:mb-0\">\n                    <h2 class=\"text-lg font-semibold mb-2\">ExProject</h2>\n                    <p class=\"text-gray-400 text-sm\">Sistema Gestione Espropri secondo DPR 327/2001</p>\n                </div>\n                <div>\n                    <h3 class=\"text-sm font-semibold mb-2 text-gray-300\">Collegamenti Rapidi</h3>\n                    <ul class=\"text-sm text-gray-400 space-y-1\">\n                        <li><a href=\"{% url 'public:about' %}\" class=\"hover:text-white\">Chi Siamo</a></li>\n                        <li><a href=\"{% url 'public:procedures' %}\" class=\"hover:text-white\">Procedure</a></li>\n                        <li><a href=\"{% url 'public:contacts' %}\" class=\"hover:text-white\">Contatti</a></li>\n                    </ul>\n                </div>\n                <div class=\"mt-4 md:mt-0\">\n                    <h3 class=\"text-sm font-semibold mb-2 text-gray-300\">Informazioni Legali</h3>\n                    <ul class=\"text-sm text-gray-400 space-y-1\">\n                        <li><a href=\"#\" class=\"hover:text-white\">Privacy Policy</a></li>\n                        <li><a href=\"#\" class=\"hover:text-white\">Termini di Servizio</a></li>\n                        <li><a href=\"#\" class=\"hover:text-white\">Cookie Policy</a></li>\n                    </ul>\n                </div>\n            </div>\n            <div class=\"mt-8 border-t border-gray-700 pt-4 text-center text-gray-400 text-sm\">\n                <p>&copy; 2025 ExProject - Tutti i diritti riservati</p>\n                <p class=\"mt-2\">Sviluppato con Django, HTMX e TailwindCSS</p>\n            </div>\n        </div>\n    </footer>\n    {% endblock %}\n\n    <!-- Script di supporto per HTMX -->\n    <script>\n        // Handler per mostrare messaggi temporanei\n        document.addEventListener('htmx:afterSwap', function(event) {\n            if (event.detail.target.id === 'messages-container') {\n                setTimeout(function() {\n                    const messages = document.querySelectorAll('.alert');\n                    messages.forEach(msg => {\n                        msg.classList.add('opacity-0');\n                        setTimeout(() => msg.remove(), 300);\n                    });\n                }, 5000);\n            }\n        });\n        \n        // Indicate loading state\n        document.addEventListener('htmx:beforeRequest', function(event) {\n            const target = event.detail.target;\n            if (target.tagName === 'BUTTON' || target.tagName === 'A') {\n                const spinner = document.createElement('span');\n                spinner.classList.add('htmx-indicator');\n                spinner.innerHTML = `\n                    <svg class=\"animate-spin -ml-1 mr-2 h-4 w-4 inline\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n                        <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                    </svg>\n                `;\n                target.appendChild(spinner);\n            }\n        });\n        \n        document.addEventListener('htmx:afterRequest', function(event) {\n            const target = event.detail.target;\n            const indicators = target.querySelectorAll('.htmx-indicator');\n            indicators.forEach(i => i.remove());\n        });\n    </script>\n\n    {% block extra_js %}{% endblock %}\n</body>\n</html>", "modifiedCode": "<!DOCTYPE html>\n<html lang=\"it\">\n<head>\n    {% load static %}\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{% block title %}ExProject{% endblock %}</title>\n    \n    <!-- Favicon e PWA -->\n    <link rel=\"icon\" href=\"{% static 'img/favicon.ico' %}\" type=\"image/x-icon\">\n    <meta name=\"theme-color\" content=\"#2563eb\">\n    \n    <!-- HTMX e Alpine.js per interattività -->\n    <script src=\"https://cdn.jsdelivr.net/npm/htmx.org@1.9.10\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js\" defer></script>\n    \n    <!-- TailwindCSS -->\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script>\n        tailwind.config = {\n            theme: {\n                extend: {\n                    colors: {\n                        primary: '#2563eb',\n                        'primary-dark': '#1d4ed8',\n                        'primary-light': '#3b82f6'\n                    }\n                }\n            }\n        }\n    </script>\n    \n    <link rel=\"stylesheet\" href=\"{% static 'base.css' %}\">\n    {% block extra_head %}{% endblock %}\n</head>\n<body class=\"bg-gray-50 min-h-screen flex flex-col\"\n      hx-headers='{\"X-CSRFToken\": \"{{ csrf_token }}\"}'\n      x-data=\"{}\">\n    \n    <!-- Overlay per mobile sidebar -->\n    <div id=\"sidebar-overlay\" \n         class=\"fixed inset-0 bg-black opacity-50 z-20 hidden md:hidden\" \n         @click=\"document.getElementById('mobile-menu').classList.add('hidden');\n                this.classList.add('hidden');\">\n    </div>\n    \n    {% block header %}\n    <header class=\"bg-blue-600 text-white shadow-lg sticky top-0 z-30\">\n        <div class=\"container mx-auto px-4 py-3\">\n            <div class=\"flex justify-between items-center\">\n                <div class=\"flex items-center\">\n                    <!-- Hamburger menu per mobile -->\n                    <button class=\"mr-2 md:hidden text-white\" \n                            @click=\"document.getElementById('mobile-menu').classList.toggle('hidden');\n                                   document.getElementById('sidebar-overlay').classList.toggle('hidden');\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M4 6h16M4 12h16M4 18h16\" />\n                        </svg>\n                    </button>\n                    \n                    <h1 class=\"text-2xl font-bold flex items-center\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-9 w-9 mr-2\" viewBox=\"0 0 80 80\" fill=\"none\">\n                            <!-- Logo per ExProject - Sistema gestione espropri -->\n                            <!-- Sfondo circolare -->\n                            <circle cx=\"40\" cy=\"40\" r=\"36\" fill=\"white\" />\n                            \n                            <!-- Edificio stilizzato -->\n                            <path d=\"M40 12L60 28V64H20V28L40 12Z\" fill=\"#1d4ed8\" />\n                            <path d=\"M40 12L60 28V64H40V12Z\" fill=\"#2563eb\" />\n                            \n                            <!-- Dettagli dell'edificio -->\n                            <rect x=\"26\" y=\"36\" width=\"8\" height=\"12\" rx=\"1\" fill=\"white\" />\n                            <rect x=\"46\" y=\"36\" width=\"8\" height=\"12\" rx=\"1\" fill=\"white\" />\n                            <rect x=\"36\" y=\"48\" width=\"8\" height=\"16\" rx=\"1\" fill=\"white\" />\n                            \n                            <!-- Mappa / Griglia catastale -->\n                            <path d=\"M20 52L32 40L38 46L48 36L60 48\" stroke=\"white\" stroke-width=\"2\" stroke-linecap=\"round\" />\n                            \n                            <!-- Contorno -->\n                            <circle cx=\"40\" cy=\"40\" r=\"36\" stroke=\"#1d4ed8\" stroke-width=\"2\" />\n                        </svg>\n                        <a href=\"{% url 'public:home' %}\" class=\"hover:text-blue-100\">ExProject</a>\n                    </h1>\n                    <p class=\"text-blue-100 text-sm ml-2 hidden sm:block\">Sistema Gestione Espropri</p>\n                </div>\n                \n                <!-- Barra di ricerca -->\n                {% if user.is_authenticated %}\n                <div class=\"hidden md:block flex-1 max-w-md mx-4\">\n                    <form class=\"relative\" method=\"get\" action=\"{% url 'core:search' %}\">\n                        <input type=\"text\" name=\"q\" placeholder=\"Cerca progetto, particella, documento...\" \n                               class=\"w-full py-1 pl-3 pr-10 rounded-full text-sm bg-blue-700 text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-white\">\n                        <button type=\"submit\" class=\"absolute right-0 top-0 mt-1 mr-3\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 text-blue-200\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                            </svg>\n                        </button>\n                    </form>\n                </div>\n                {% endif %}\n                \n                <div class=\"flex items-center space-x-2\">\n                    {% if user.is_authenticated %}\n                        <!-- Notifiche -->\n                        <div class=\"relative\" x-data=\"{ open: false }\">\n                            <button class=\"p-1 rounded-full hover:bg-blue-700 relative notifications-button\" \n                                    @click=\"open = !open\"\n                                    hx-get=\"{% url 'core:notifications' %}\"\n                                    hx-trigger=\"click\"\n                                    hx-target=\"#notifications-dropdown\"\n                                    hx-swap=\"innerHTML\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\" />\n                                </svg>\n                                <span class=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-blue-600\"></span>\n                            </button>\n                            \n                            <div id=\"notifications-dropdown\" \n                                 class=\"absolute right-0 mt-2 w-80 bg-white rounded-md shadow-lg py-1 z-50\"\n                                 x-show=\"open\"\n                                 @click.away=\"open = false\"\n                                 x-transition:enter=\"transition ease-out duration-100\"\n                                 x-transition:enter-start=\"transform opacity-0 scale-95\"\n                                 x-transition:enter-end=\"transform opacity-100 scale-100\"\n                                 style=\"display: none;\">\n                                <!-- Contenuto caricato via HTMX -->\n                            </div>\n                        </div>\n                        \n                        <!-- Menu utente -->\n                        <div class=\"relative ml-3\" x-data=\"{ open: false }\">\n                            <button type=\"button\" \n                                    class=\"flex text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-white user-menu-button\"\n                                    @click=\"open = !open\">\n                                <span class=\"sr-only\">Menu utente</span>\n                                <div class=\"h-8 w-8 rounded-full bg-blue-700 flex items-center justify-center\">\n                                    <span class=\"text-white font-medium\">{{ user.username|slice:\":1\" }}</span>\n                                </div>\n                            </button>\n                            \n                            <div class=\"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 user-menu-dropdown\"\n                                 x-show=\"open\"\n                                 @click.away=\"open = false\"\n                                 x-transition:enter=\"transition ease-out duration-100\"\n                                 x-transition:enter-start=\"transform opacity-0 scale-95\"\n                                 x-transition:enter-end=\"transform opacity-100 scale-100\"\n                                 style=\"display: none;\">\n                                <div class=\"block px-4 py-2 text-xs text-gray-500\">\n                                    {{ user.get_full_name|default:user.username }}\n                                </div>\n                                <a href=\"{% url 'core:dashboard' %}\" class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">Dashboard</a>\n                                <a href=\"{% url 'core:profile' %}\" class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">Profilo</a>\n                                <a href=\"{% url 'core:settings' %}\" class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">Impostazioni</a>\n                                <div class=\"border-t border-gray-100\"></div>\n                                <a href=\"{% url 'core:logout' %}\" class=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">Logout</a>\n                            </div>\n                        </div>\n                    {% else %}\n                        <a href=\"{% url 'core:login' %}\" class=\"bg-blue-700 hover:bg-blue-800 px-3 py-1 rounded text-sm\">Area Riservata</a>\n                    {% endif %}\n                </div>\n            </div>\n            \n            <!-- Navbar integrata nell'header -->\n            {% if user.is_authenticated %}\n                {% if request.resolver_match.namespace == 'core' or request.resolver_match.namespace == 'projects' or request.resolver_match.namespace == 'workflow' or request.resolver_match.namespace == 'documents' or request.resolver_match.namespace == 'economic' or request.resolver_match.namespace == 'integrations' %}\n                <!-- Navbar amministrativa -->\n                <div class=\"border-t border-blue-500 mt-3 pt-2\">\n                    <ul class=\"flex space-x-6 py-2 overflow-x-auto scrollbar-hide\">\n                        <li><a href=\"{% url 'core:dashboard' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'dashboard' %}font-bold{% endif %}\">Dashboard</a></li>\n                        <li><a href=\"{% url 'projects:list' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.namespace == 'projects' %}font-bold{% endif %}\">Progetti</a></li>\n                        <li><a href=\"{% url 'workflow:list' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.namespace == 'workflow' %}font-bold{% endif %}\">Workflow</a></li>\n                        <li><a href=\"{% url 'documents:list' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.namespace == 'documents' %}font-bold{% endif %}\">Documenti</a></li>\n                        <li><a href=\"{% url 'economic:list' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.namespace == 'economic' %}font-bold{% endif %}\">Economico</a></li>\n                        <li><a href=\"{% url 'integrations:list' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.namespace == 'integrations' %}font-bold{% endif %}\">Integrazioni</a></li>\n                        <li><a href=\"{% url 'ui:design_system_docs' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.namespace == 'ui' %}font-bold{% endif %}\">Design System</a></li>\n                        <li><a href=\"{% url 'public:home' %}\" class=\"hover:text-blue-100 py-2\">Sito Pubblico</a></li>\n                    </ul>\n                </div>\n                {% else %}\n                <!-- Navbar pubblica -->\n                <div class=\"border-t border-blue-500 mt-3 pt-2\">\n                    <ul class=\"flex space-x-6 py-2 overflow-x-auto scrollbar-hide\">\n                        <li><a href=\"{% url 'public:home' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'home' %}font-bold{% endif %}\">Home</a></li>\n                        <li><a href=\"{% url 'public:about' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'about' %}font-bold{% endif %}\">Chi Siamo</a></li>\n                        <li><a href=\"{% url 'public:procedures' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'procedures' %}font-bold{% endif %}\">Procedure</a></li>\n                        <li><a href=\"{% url 'public:news' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'news' %}font-bold{% endif %}\">News</a></li>\n                        <li><a href=\"{% url 'public:contacts' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'contacts' %}font-bold{% endif %}\">Contatti</a></li>\n                        <li><a href=\"{% url 'core:dashboard' %}\" class=\"hover:text-blue-100 py-2 bg-blue-700 px-3 rounded\">Dashboard</a></li>\n                    </ul>\n                </div>\n                {% endif %}\n            {% else %}\n            <!-- Navbar pubblica per utenti non autenticati -->\n            <div class=\"border-t border-blue-500 mt-3 pt-2\">\n                <ul class=\"flex space-x-6 py-2 overflow-x-auto scrollbar-hide\">\n                    <li><a href=\"{% url 'public:home' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'home' %}font-bold{% endif %}\">Home</a></li>\n                    <li><a href=\"{% url 'public:about' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'about' %}font-bold{% endif %}\">Chi Siamo</a></li>\n                    <li><a href=\"{% url 'public:procedures' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'procedures' %}font-bold{% endif %}\">Procedure</a></li>\n                    <li><a href=\"{% url 'public:news' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'news' %}font-bold{% endif %}\">News</a></li>\n                    <li><a href=\"{% url 'public:contacts' %}\" class=\"hover:text-blue-100 py-2 {% if request.resolver_match.url_name == 'contacts' %}font-bold{% endif %}\">Contatti</a></li>\n                </ul>\n            </div>\n            {% endif %}\n        </div>\n    </header>\n    {% endblock %}\n\n    <!-- Mobile sidebar menu -->\n    <div id=\"mobile-menu\" class=\"fixed inset-y-0 left-0 w-64 bg-white shadow-lg z-30 transform transition-transform duration-300 ease-in-out hidden md:hidden\">\n        <div class=\"flex items-center justify-between p-4 border-b\">\n            <h2 class=\"font-semibold text-lg\">Menu</h2>\n            <button @click=\"document.getElementById('mobile-menu').classList.add('hidden');\n                              document.getElementById('sidebar-overlay').classList.add('hidden');\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n            </button>\n        </div>\n        \n        <nav class=\"mt-2 px-4\">\n            {% block mobile_nav %}\n            <ul class=\"space-y-1\">\n                {% block nav_mobile_items %}\n                {% if user.is_authenticated %}\n                    {% if request.resolver_match.namespace == 'core' or request.resolver_match.namespace == 'projects' or request.resolver_match.namespace == 'workflow' or request.resolver_match.namespace == 'documents' or request.resolver_match.namespace == 'economic' or request.resolver_match.namespace == 'integrations' %}\n                    <!-- Menu mobile amministrativo -->\n                    <li><a href=\"{% url 'core:dashboard' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'dashboard' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Dashboard</a></li>\n                    <li><a href=\"{% url 'projects:list' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.namespace == 'projects' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Progetti</a></li>\n                    <li><a href=\"{% url 'workflow:list' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.namespace == 'workflow' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Workflow</a></li>\n                    <li><a href=\"{% url 'documents:list' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.namespace == 'documents' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Documenti</a></li>\n                    <li><a href=\"{% url 'economic:list' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.namespace == 'economic' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Economico</a></li>\n                    <li><a href=\"{% url 'integrations:list' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.namespace == 'integrations' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Integrazioni</a></li>\n                    <li class=\"border-t my-2 pt-2\">\n                        <a href=\"{% url 'public:home' %}\" class=\"block py-2 px-4 rounded bg-gray-100 text-gray-700 hover:bg-gray-200\">Sito Pubblico</a>\n                    </li>\n                    {% else %}\n                    <!-- Menu mobile pubblico per utenti autenticati -->\n                    <li><a href=\"{% url 'public:home' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'home' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Home</a></li>\n                    <li><a href=\"{% url 'public:about' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'about' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Chi Siamo</a></li>\n                    <li><a href=\"{% url 'public:procedures' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'procedures' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Procedure</a></li>\n                    <li><a href=\"{% url 'public:news' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'news' %}text-blue-600 font-medium bg-blue-50{% endif %}\">News</a></li>\n                    <li><a href=\"{% url 'public:contacts' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'contacts' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Contatti</a></li>\n                    <li class=\"border-t my-2 pt-2\">\n                        <a href=\"{% url 'core:dashboard' %}\" class=\"block py-2 px-4 rounded bg-blue-100 text-blue-700 hover:bg-blue-200\">Dashboard</a>\n                    </li>\n                    {% endif %}\n                {% else %}\n                <!-- Menu mobile per utenti non autenticati -->\n                <li><a href=\"{% url 'public:home' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'home' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Home</a></li>\n                <li><a href=\"{% url 'public:about' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'about' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Chi Siamo</a></li>\n                <li><a href=\"{% url 'public:procedures' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'procedures' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Procedure</a></li>\n                <li><a href=\"{% url 'public:news' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'news' %}text-blue-600 font-medium bg-blue-50{% endif %}\">News</a></li>\n                <li><a href=\"{% url 'public:contacts' %}\" class=\"block py-2 px-4 rounded hover:bg-blue-50 {% if request.resolver_match.url_name == 'contacts' %}text-blue-600 font-medium bg-blue-50{% endif %}\">Contatti</a></li>\n                {% endif %}\n                {% endblock %}\n            </ul>\n            {% endblock %}\n        </nav>\n    </div>\n\n    {% block navigation %}\n    <!-- Navigation is now integrated in the header -->\n    {% endblock %}\n\n    {% block messages %}\n    <div id=\"messages-container\" class=\"container mx-auto px-4 py-2\">\n    {% if messages %}\n        {% for message in messages %}\n            <div class=\"alert alert-{{ message.tags }} bg-{% if message.tags == 'error' %}red{% elif message.tags == 'warning' %}yellow{% else %}green{% endif %}-100 border border-{% if message.tags == 'error' %}red{% elif message.tags == 'warning' %}yellow{% else %}green{% endif %}-400 text-{% if message.tags == 'error' %}red{% elif message.tags == 'warning' %}yellow{% else %}green{% endif %}-700 px-4 py-3 rounded mb-2 flex justify-between items-center fade-in\"\n                 hx-swap-oob=\"true\">\n                <span>{{ message }}</span>\n                <button type=\"button\" class=\"text-gray-500 hover:text-gray-700\" onclick=\"this.parentElement.remove()\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M6 18L18 6M6 6l12 12\" />\n                    </svg>\n                </button>\n            </div>\n        {% endfor %}\n    {% endif %}\n    </div>\n    {% endblock %}\n\n    <main class=\"flex-1\">\n        {% block main %}\n        <div class=\"container mx-auto px-4 py-6\">\n            {% block content %}{% endblock %}\n        </div>\n        {% endblock %}\n    </main>\n\n    {% block footer %}\n    <footer class=\"bg-gray-800 text-white mt-auto\">\n        <div class=\"container mx-auto px-4 py-6\">\n            <div class=\"md:flex md:justify-between\">\n                <div class=\"mb-4 md:mb-0\">\n                    <h2 class=\"text-lg font-semibold mb-2\">ExProject</h2>\n                    <p class=\"text-gray-400 text-sm\">Sistema Gestione Espropri secondo DPR 327/2001</p>\n                </div>\n                <div>\n                    <h3 class=\"text-sm font-semibold mb-2 text-gray-300\">Collegamenti Rapidi</h3>\n                    <ul class=\"text-sm text-gray-400 space-y-1\">\n                        <li><a href=\"{% url 'public:about' %}\" class=\"hover:text-white\">Chi Siamo</a></li>\n                        <li><a href=\"{% url 'public:procedures' %}\" class=\"hover:text-white\">Procedure</a></li>\n                        <li><a href=\"{% url 'public:contacts' %}\" class=\"hover:text-white\">Contatti</a></li>\n                    </ul>\n                </div>\n                <div class=\"mt-4 md:mt-0\">\n                    <h3 class=\"text-sm font-semibold mb-2 text-gray-300\">Informazioni Legali</h3>\n                    <ul class=\"text-sm text-gray-400 space-y-1\">\n                        <li><a href=\"#\" class=\"hover:text-white\">Privacy Policy</a></li>\n                        <li><a href=\"#\" class=\"hover:text-white\">Termini di Servizio</a></li>\n                        <li><a href=\"#\" class=\"hover:text-white\">Cookie Policy</a></li>\n                    </ul>\n                </div>\n            </div>\n            <div class=\"mt-8 border-t border-gray-700 pt-4 text-center text-gray-400 text-sm\">\n                <p>&copy; 2025 ExProject - Tutti i diritti riservati</p>\n                <p class=\"mt-2\">Sviluppato con Django, HTMX e TailwindCSS</p>\n            </div>\n        </div>\n    </footer>\n    {% endblock %}\n\n    <!-- Script di supporto per HTMX -->\n    <script>\n        // Handler per mostrare messaggi temporanei\n        document.addEventListener('htmx:afterSwap', function(event) {\n            if (event.detail.target.id === 'messages-container') {\n                setTimeout(function() {\n                    const messages = document.querySelectorAll('.alert');\n                    messages.forEach(msg => {\n                        msg.classList.add('opacity-0');\n                        setTimeout(() => msg.remove(), 300);\n                    });\n                }, 5000);\n            }\n        });\n        \n        // Indicate loading state\n        document.addEventListener('htmx:beforeRequest', function(event) {\n            const target = event.detail.target;\n            if (target.tagName === 'BUTTON' || target.tagName === 'A') {\n                const spinner = document.createElement('span');\n                spinner.classList.add('htmx-indicator');\n                spinner.innerHTML = `\n                    <svg class=\"animate-spin -ml-1 mr-2 h-4 w-4 inline\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                        <circle class=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" stroke-width=\"4\"></circle>\n                        <path class=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                    </svg>\n                `;\n                target.appendChild(spinner);\n            }\n        });\n        \n        document.addEventListener('htmx:afterRequest', function(event) {\n            const target = event.detail.target;\n            const indicators = target.querySelectorAll('.htmx-indicator');\n            indicators.forEach(i => i.remove());\n        });\n    </script>\n\n    {% block extra_js %}{% endblock %}\n</body>\n</html>"}