{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/select.html"}, "modifiedCode": "{# Componente Select dropdown #}\n<div class=\"grid w-full max-w-sm items-center gap-1.5\">\n    {% if label %}\n    <label for=\"{{ name }}\" class=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\">{{ label }}</label>\n    {% endif %}\n    <select \n        id=\"{{ name }}\" \n        name=\"{{ name }}\"\n        class=\"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\"\n        {% for key, value in attrs.items %}{{ key }}=\"{{ value }}\"{% endfor %}>\n        {% for option in options %}\n        <option value=\"{{ option.value }}\">{{ option.label }}</option>\n        {% endfor %}\n    </select>\n</div>\n"}