{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/alert.html"}, "modifiedCode": "{% load ui_components %}\n{# Componente Alert ispirato a shadcn/ui #}\n<div class=\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\n    {% ui_variant_classes 'alert' variant %}\n    {% if attrs.class %}{{ attrs.class }}{% endif %}\"\n    {% for key, value in attrs.items %}{% if key != 'class' %}{{ key }}=\"{{ value }}\"{% endif %}{% endfor %}>\n    \n    {% if variant == 'destructive' %}\n        {% ui_icon 'x-circle' size=\"4\" %}\n    {% elif variant == 'warning' %}\n        {% ui_icon 'exclamation-triangle' size=\"4\" %}\n    {% elif variant == 'success' %}\n        {% ui_icon 'check-circle' size=\"4\" %}\n    {% else %}\n        {% ui_icon 'info' size=\"4\" %}\n    {% endif %}\n    \n    <div>\n        {% if title %}\n        <h5 class=\"mb-1 font-medium leading-none tracking-tight\">{{ title }}</h5>\n        {% endif %}\n        <div class=\"text-sm [&_p]:leading-relaxed\">{{ message }}</div>\n    </div>\n    \n    {% if attrs.dismissible %}\n    <button class=\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none\"\n            onclick=\"this.parentElement.remove()\">\n        {% ui_icon 'x' size=\"4\" %}\n        <span class=\"sr-only\">Chiudi</span>\n    </button>\n    {% endif %}\n</div>\n"}