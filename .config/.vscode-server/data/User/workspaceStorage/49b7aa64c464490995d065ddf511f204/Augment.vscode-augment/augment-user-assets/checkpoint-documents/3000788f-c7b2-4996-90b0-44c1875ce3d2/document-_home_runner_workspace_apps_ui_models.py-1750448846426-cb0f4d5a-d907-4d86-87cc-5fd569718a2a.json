{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/models.py"}, "modifiedCode": "from django.db import models\nfrom django.contrib.auth.models import User\nimport json\n\n\nclass Theme(models.Model):\n    \"\"\"Configurazione tema per white label\"\"\"\n    name = models.CharField(max_length=100, unique=True)\n    slug = models.SlugField(max_length=100, unique=True)\n    is_active = models.BooleanField(default=False)\n    is_default = models.BooleanField(default=False)\n    \n    # Design tokens\n    colors = models.JSONField(default=dict, help_text=\"Palette colori del tema\")\n    typography = models.JSONField(default=dict, help_text=\"Configurazione tipografia\")\n    spacing = models.JSONField(default=dict, help_text=\"Sistema di spacing\")\n    borders = models.JSONField(default=dict, help_text=\"Configurazione bordi e radius\")\n    shadows = models.JSONField(default=dict, help_text=\"Sistema di ombre\")\n    \n    # Branding\n    logo_url = models.URLField(blank=True, null=True)\n    favicon_url = models.URLField(blank=True, null=True)\n    brand_name = models.CharField(max_length=100, blank=True)\n    brand_tagline = models.CharField(max_length=200, blank=True)\n    \n    # Configurazioni avanzate\n    custom_css = models.TextField(blank=True, help_text=\"CSS personalizzato\")\n    component_overrides = models.JSONField(default=dict, help_text=\"Override componenti\")\n    \n    created_at = models.DateTimeField(auto_now_add=True)\n    updated_at = models.DateTimeField(auto_now=True)\n    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)\n    \n    class Meta:\n        verbose_name = \"Tema\"\n        verbose_name_plural = \"Temi\"\n        ordering = ['name']\n    \n    def __str__(self):\n        return self.name\n    \n    def save(self, *args, **kwargs):\n        if self.is_default:\n            # Assicura che ci sia solo un tema default\n            Theme.objects.filter(is_default=True).update(is_default=False)\n        super().save(*args, **kwargs)\n    \n    @classmethod\n    def get_active_theme(cls):\n        \"\"\"Restituisce il tema attivo o quello di default\"\"\"\n        return cls.objects.filter(is_active=True).first() or cls.objects.filter(is_default=True).first()\n\n\nclass ComponentLibrary(models.Model):\n    \"\"\"Libreria di componenti UI riutilizzabili\"\"\"\n    name = models.CharField(max_length=100)\n    slug = models.SlugField(max_length=100, unique=True)\n    category = models.CharField(max_length=50, choices=[\n        ('atoms', 'Atoms'),\n        ('molecules', 'Molecules'), \n        ('organisms', 'Organisms'),\n        ('templates', 'Templates'),\n    ])\n    \n    # Template del componente\n    template_content = models.TextField(help_text=\"Template HTML del componente\")\n    css_classes = models.TextField(blank=True, help_text=\"Classi CSS specifiche\")\n    javascript = models.TextField(blank=True, help_text=\"JavaScript del componente\")\n    \n    # Configurazione\n    props_schema = models.JSONField(default=dict, help_text=\"Schema delle proprietà\")\n    variants = models.JSONField(default=dict, help_text=\"Varianti del componente\")\n    examples = models.JSONField(default=list, help_text=\"Esempi di utilizzo\")\n    \n    # Metadati\n    description = models.TextField(blank=True)\n    documentation = models.TextField(blank=True)\n    is_active = models.BooleanField(default=True)\n    version = models.CharField(max_length=20, default=\"1.0.0\")\n    \n    created_at = models.DateTimeField(auto_now_add=True)\n    updated_at = models.DateTimeField(auto_now=True)\n    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)\n    \n    class Meta:\n        verbose_name = \"Componente\"\n        verbose_name_plural = \"Componenti\"\n        ordering = ['category', 'name']\n    \n    def __str__(self):\n        return f\"{self.get_category_display()} - {self.name}\"\n\n\nclass LayoutConfiguration(models.Model):\n    \"\"\"Configurazioni layout per diverse sezioni\"\"\"\n    name = models.CharField(max_length=100)\n    slug = models.SlugField(max_length=100, unique=True)\n    layout_type = models.CharField(max_length=50, choices=[\n        ('dashboard', 'Dashboard'),\n        ('list', 'Lista/Tabella'),\n        ('detail', 'Dettaglio'),\n        ('form', 'Form'),\n        ('report', 'Report'),\n        ('public', 'Pagina Pubblica'),\n    ])\n    \n    # Configurazione layout\n    sidebar_enabled = models.BooleanField(default=True)\n    sidebar_position = models.CharField(max_length=10, choices=[('left', 'Sinistra'), ('right', 'Destra')], default='left')\n    header_style = models.CharField(max_length=20, choices=[('fixed', 'Fisso'), ('static', 'Statico')], default='fixed')\n    footer_enabled = models.BooleanField(default=True)\n    \n    # Grid e spacing\n    container_max_width = models.CharField(max_length=20, default='7xl')\n    content_padding = models.CharField(max_length=20, default='6')\n    grid_columns = models.JSONField(default=dict, help_text=\"Configurazione colonne responsive\")\n    \n    # Componenti inclusi\n    components = models.JSONField(default=list, help_text=\"Lista componenti da includere\")\n    \n    is_active = models.BooleanField(default=True)\n    created_at = models.DateTimeField(auto_now_add=True)\n    updated_at = models.DateTimeField(auto_now=True)\n    \n    class Meta:\n        verbose_name = \"Layout\"\n        verbose_name_plural = \"Layout\"\n        ordering = ['layout_type', 'name']\n    \n    def __str__(self):\n        return f\"{self.get_layout_type_display()} - {self.name}\"\n"}