{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/public/news.html"}, "originalCode": "{% extends \"base.html\" %}\n{% load static %}\n\n{% block title %}{{ title }}{% endblock %}\n\n{% block content %}\n<div class=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n    <!-- Header -->\n    <div class=\"text-center mb-12\">\n        <h1 class=\"text-4xl font-bold text-gray-900 mb-4\">{{ title }}</h1>\n        <p class=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Resta aggiornato sulle ultime novità di ExProject e sul mondo degli espropri\n        </p>\n    </div>\n    \n    <!-- News Grid -->\n    <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n        {% for item in news_items %}\n        <article class=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div class=\"aspect-w-16 aspect-h-9\">\n                <img src=\"{% static item.image %}\" alt=\"{{ item.title }}\" \n                     class=\"w-full h-48 object-cover\">\n            </div>\n            <div class=\"p-6\">\n                <div class=\"text-sm text-gray-500 mb-2\">\n                    {{ item.date|date:\"d F Y\" }}\n                </div>\n                <h2 class=\"text-xl font-bold text-gray-900 mb-3\">\n                    {{ item.title }}\n                </h2>\n                <p class=\"text-gray-600 mb-4\">\n                    {{ item.summary }}\n                </p>\n                <a href=\"#\" class=\"text-blue-600 hover:text-blue-800 font-medium\">\n                    Leggi di più →\n                </a>\n            </div>\n        </article>\n        {% endfor %}\n    </div>\n    \n    <!-- Newsletter -->\n    <div class=\"mt-16 bg-blue-50 rounded-lg p-8\">\n        <div class=\"max-w-2xl mx-auto text-center\">\n            <h2 class=\"text-2xl font-bold text-gray-900 mb-4\">\n                Iscriviti alla Newsletter\n            </h2>\n            <p class=\"text-gray-600 mb-6\">\n                Ricevi aggiornamenti mensili su novità, aggiornamenti normativi e best practices\n            </p>\n            <form class=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <input type=\"email\" placeholder=\"La tua email\" \n                       class=\"flex-1 max-w-md px-4 py-2 rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500\">\n                <button type=\"submit\" class=\"btn btn-primary whitespace-nowrap\">\n                    Iscriviti\n                </button>\n            </form>\n        </div>\n    </div>\n</div>\n{% endblock %}", "modifiedCode": "{% extends \"base.html\" %}\n{% load static %}\n\n{% block title %}{{ title }}{% endblock %}\n\n{% block content %}\n<div class=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n    <!-- Header -->\n    <div class=\"text-center mb-12\">\n        <h1 class=\"text-4xl font-bold text-gray-900 mb-4\">{{ title }}</h1>\n        <p class=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Resta aggiornato sulle ultime novità di ExProject e sul mondo degli espropri\n        </p>\n    </div>\n    \n    <!-- News Grid -->\n    <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n        {% for item in news_items %}\n        <article class=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div class=\"aspect-w-16 aspect-h-9\">\n                <img src=\"{% static item.image %}\" alt=\"{{ item.title }}\" \n                     class=\"w-full h-48 object-cover\">\n            </div>\n            <div class=\"p-6\">\n                <div class=\"text-sm text-gray-500 mb-2\">\n                    {{ item.date|date:\"d F Y\" }}\n                </div>\n                <h2 class=\"text-xl font-bold text-gray-900 mb-3\">\n                    {{ item.title }}\n                </h2>\n                <p class=\"text-gray-600 mb-4\">\n                    {{ item.summary }}\n                </p>\n                <a href=\"#\" class=\"text-blue-600 hover:text-blue-800 font-medium\">\n                    Leggi di più →\n                </a>\n            </div>\n        </article>\n        {% endfor %}\n    </div>\n    \n    <!-- Newsletter -->\n    <div class=\"mt-16 bg-blue-50 rounded-lg p-8\">\n        <div class=\"max-w-2xl mx-auto text-center\">\n            <h2 class=\"text-2xl font-bold text-gray-900 mb-4\">\n                Iscriviti alla Newsletter\n            </h2>\n            <p class=\"text-gray-600 mb-6\">\n                Ricevi aggiornamenti mensili su novità, aggiornamenti normativi e best practices\n            </p>\n            <form class=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                <input type=\"email\" placeholder=\"La tua email\" \n                       class=\"flex-1 max-w-md px-4 py-2 rounded-md border-gray-300 focus:border-blue-500 focus:ring-blue-500\">\n                <button type=\"submit\" class=\"btn btn-primary whitespace-nowrap\">\n                    Iscriviti\n                </button>\n            </form>\n        </div>\n    </div>\n</div>\n{% endblock %}"}