{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/boilerplates/list.html"}, "modifiedCode": "{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}{{ page_title }}{% endblock %}\n\n{% block content %}\n<div class=\"flex-1 space-y-4 p-8 pt-6\">\n    <!-- Header with Actions -->\n    <div class=\"flex items-center justify-between\">\n        <h2 class=\"text-3xl font-bold tracking-tight\">{{ page_title }}</h2>\n        <div class=\"flex items-center space-x-2\">\n            {% if create_url %}\n                {% ui_button \"Add New\" variant=\"default\" onclick=\"location.href='{{ create_url }}'\" %}\n            {% endif %}\n            {% if export_enabled %}\n                {% ui_button \"Export\" variant=\"outline\" %}\n            {% endif %}\n            {% if bulk_actions %}\n                {% ui_button \"Bulk Actions\" variant=\"secondary\" %}\n            {% endif %}\n        </div>\n    </div>\n    \n    <!-- Filters and Search -->\n    {% if filters_enabled %}\n    <div class=\"flex flex-col space-y-4 md:flex-row md:items-center md:space-y-0 md:space-x-4\">\n        <!-- Search -->\n        <div class=\"flex-1\">\n            <form method=\"get\" class=\"flex space-x-2\">\n                {% ui_input name=\"search\" placeholder=\"Search...\" value=request.GET.search %}\n                {% ui_button \"Search\" type=\"submit\" variant=\"secondary\" %}\n            </form>\n        </div>\n        \n        <!-- Filters -->\n        <div class=\"flex space-x-2\">\n            {% if status_filter %}\n                {% ui_select name=\"status\" options=status_options value=request.GET.status %}\n            {% endif %}\n            {% if date_filter %}\n                {% ui_input name=\"date_from\" type=\"date\" placeholder=\"From\" value=request.GET.date_from %}\n                {% ui_input name=\"date_to\" type=\"date\" placeholder=\"To\" value=request.GET.date_to %}\n            {% endif %}\n            {% ui_button \"Filter\" variant=\"outline\" %}\n            {% if request.GET %}\n                {% ui_button \"Clear\" variant=\"ghost\" onclick=\"location.href='{{ request.path }}'\" %}\n            {% endif %}\n        </div>\n    </div>\n    {% endif %}\n    \n    <!-- Data Table -->\n    {% ui_card \n        content=\"\n        <div class='overflow-x-auto'>\n            <table class='w-full'>\n                <thead>\n                    <tr class='border-b'>\n                        {% if bulk_select %}\n                            <th class='text-left p-4'>\n                                <input type='checkbox' id='select-all' class='rounded border-input'>\n                            </th>\n                        {% endif %}\n                        {% for header in table_headers %}\n                            <th class='text-left p-4 font-medium'>{{ header.title }}</th>\n                        {% endfor %}\n                        {% if row_actions %}\n                            <th class='text-right p-4 font-medium'>Actions</th>\n                        {% endif %}\n                    </tr>\n                </thead>\n                <tbody>\n                    {% for item in object_list %}\n                        <tr class='border-b hover:bg-muted/50'>\n                            {% if bulk_select %}\n                                <td class='p-4'>\n                                    <input type='checkbox' name='selected_items' value='{{ item.pk }}' class='rounded border-input'>\n                                </td>\n                            {% endif %}\n                            {% for field in table_fields %}\n                                <td class='p-4'>{{ item|lookup:field }}</td>\n                            {% endfor %}\n                            {% if row_actions %}\n                                <td class='p-4 text-right'>\n                                    <div class='flex justify-end space-x-2'>\n                                        <a href='{{ item.get_absolute_url }}' class='text-blue-600 hover:text-blue-800'>View</a>\n                                        {% if item.can_edit %}\n                                            <a href='{{ item.get_edit_url }}' class='text-green-600 hover:text-green-800'>Edit</a>\n                                        {% endif %}\n                                        {% if item.can_delete %}\n                                            <a href='{{ item.get_delete_url }}' class='text-red-600 hover:text-red-800'>Delete</a>\n                                        {% endif %}\n                                    </div>\n                                </td>\n                            {% endif %}\n                        </tr>\n                    {% empty %}\n                        <tr>\n                            <td colspan='{{ table_headers|length|add:2 }}' class='p-8 text-center text-muted-foreground'>\n                                No items found.\n                                {% if create_url %}\n                                    <a href='{{ create_url }}' class='text-blue-600 hover:text-blue-800 ml-2'>Create the first one</a>\n                                {% endif %}\n                            </td>\n                        </tr>\n                    {% endfor %}\n                </tbody>\n            </table>\n        </div>\n        \"\n    %}\n    \n    <!-- Pagination -->\n    {% if is_paginated %}\n    <div class=\"flex items-center justify-between\">\n        <p class=\"text-sm text-muted-foreground\">\n            Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ paginator.count }} results\n        </p>\n        <div class=\"flex items-center space-x-2\">\n            {% if page_obj.has_previous %}\n                {% ui_button \"Previous\" variant=\"outline\" onclick=\"location.href='?page={{ page_obj.previous_page_number }}'\" %}\n            {% endif %}\n            \n            <span class=\"text-sm text-muted-foreground\">\n                Page {{ page_obj.number }} of {{ paginator.num_pages }}\n            </span>\n            \n            {% if page_obj.has_next %}\n                {% ui_button \"Next\" variant=\"outline\" onclick=\"location.href='?page={{ page_obj.next_page_number }}'\" %}\n            {% endif %}\n        </div>\n    </div>\n    {% endif %}\n    \n    <!-- Messages -->\n    {% if messages %}\n        <div class=\"space-y-2\">\n            {% for message in messages %}\n                {% ui_alert message.message variant=message.tags %}\n            {% endfor %}\n        </div>\n    {% endif %}\n</div>\n{% endblock %}\n"}