{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/search.html"}, "originalCode": "{% extends \"base.html\" %}\n\n{% block title %}Ricerca - {{ query }}{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <h1 class=\"text-2xl font-bold mb-6\">Risultati della ricerca per \"{{ query }}\"</h1>\n    \n    {% if query %}\n        <!-- Progetti -->\n        {% if results.projects %}\n            <div class=\"mb-8\">\n                <h2 class=\"text-xl font-semibold mb-4\"><PERSON><PERSON><PERSON></h2>\n                <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n                    {% for project in results.projects %}\n                        <div class=\"card p-4\">\n                            <h3 class=\"font-semibold mb-2\">{{ project.name }}</h3>\n                            <p class=\"text-sm text-gray-600 mb-2\">{{ project.description|truncatewords:30 }}</p>\n                            <div class=\"flex justify-between items-center\">\n                                <span class=\"project-status-{{ project.status }}\">{{ project.get_status_display }}</span>\n                                <a href=\"{% url 'core:project_detail' project.id %}\" class=\"text-blue-600 hover:text-blue-800 text-sm\">Dettagli →</a>\n                            </div>\n                        </div>\n                    {% endfor %}\n                </div>\n            </div>\n        {% endif %}\n        \n        <!-- Particelle -->\n        {% if results.parcels %}\n            <div class=\"mb-8\">\n                <h2 class=\"text-xl font-semibold mb-4\">Particelle</h2>\n                <div class=\"overflow-x-auto\">\n                    <table class=\"data-table\">\n                        <thead>\n                            <tr>\n                                <th>ID Catastale</th>\n                                <th>Progetto</th>\n                                <th>Stato</th>\n                                <th>Azioni</th>\n                            </tr>\n                        </thead>\n                        <tbody>\n                            {% for parcel in results.parcels %}\n                                <tr>\n                                    <td>{{ parcel.cadastral_id }}</td>\n                                    <td>{{ parcel.project.name }}</td>\n                                    <td><span class=\"parcel-badge bg-blue-100 text-blue-800\">{{ parcel.get_state_display }}</span></td>\n                                    <td>\n                                        <a href=\"{% url 'core:parcel_detail' parcel.id %}\" class=\"text-blue-600 hover:text-blue-800\">Dettagli →</a>\n                                    </td>\n                                </tr>\n                            {% endfor %}\n                        </tbody>\n                    </table>\n                </div>\n            </div>\n        {% endif %}\n        \n        <!-- Documenti -->\n        {% if results.documents %}\n            <div class=\"mb-8\">\n                <h2 class=\"text-xl font-semibold mb-4\">Documenti</h2>\n                <div class=\"overflow-x-auto\">\n                    <table class=\"data-table\">\n                        <thead>\n                            <tr>\n                                <th>Documento</th>\n                                <th>Particella</th>\n                                <th>Stato</th>\n                                <th>Azioni</th>\n                            </tr>\n                        </thead>\n                        <tbody>\n                            {% for doc in results.documents %}\n                                <tr>\n                                    <td>{{ doc.template.name }}</td>\n                                    <td>{{ doc.parcel.cadastral_id }}</td>\n                                    <td><span class=\"project-status-{{ doc.status }}\">{{ doc.get_status_display }}</span></td>\n                                    <td>\n                                        <a href=\"{% url 'core:document_detail' doc.id %}\" class=\"text-blue-600 hover:text-blue-800\">Dettagli →</a>\n                                    </td>\n                                </tr>\n                            {% endfor %}\n                        </tbody>\n                    </table>\n                </div>\n            </div>\n        {% endif %}\n        \n        {% if not results.projects and not results.parcels and not results.documents %}\n            <div class=\"text-center py-8\">\n                <p class=\"text-gray-600\">Nessun risultato trovato per \"{{ query }}\"</p>\n            </div>\n        {% endif %}\n    {% else %}\n        <div class=\"text-center py-8\">\n            <p class=\"text-gray-600\">Inserisci un termine di ricerca</p>\n        </div>\n    {% endif %}\n</div>\n{% endblock %} ", "modifiedCode": "{% extends \"base.html\" %}\n\n{% block title %}Ricerca - {{ query }}{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <h1 class=\"text-2xl font-bold mb-6\">Risultati della ricerca per \"{{ query }}\"</h1>\n    \n    {% if query %}\n        <!-- Progetti -->\n        {% if results.projects %}\n            <div class=\"mb-8\">\n                <h2 class=\"text-xl font-semibold mb-4\"><PERSON><PERSON><PERSON></h2>\n                <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-3\">\n                    {% for project in results.projects %}\n                        <div class=\"card p-4\">\n                            <h3 class=\"font-semibold mb-2\">{{ project.name }}</h3>\n                            <p class=\"text-sm text-gray-600 mb-2\">{{ project.description|truncatewords:30 }}</p>\n                            <div class=\"flex justify-between items-center\">\n                                <span class=\"project-status-{{ project.status }}\">{{ project.get_status_display }}</span>\n                                <a href=\"{% url 'core:project_detail' project.id %}\" class=\"text-blue-600 hover:text-blue-800 text-sm\">Dettagli →</a>\n                            </div>\n                        </div>\n                    {% endfor %}\n                </div>\n            </div>\n        {% endif %}\n        \n        <!-- Particelle -->\n        {% if results.parcels %}\n            <div class=\"mb-8\">\n                <h2 class=\"text-xl font-semibold mb-4\">Particelle</h2>\n                <div class=\"overflow-x-auto\">\n                    <table class=\"data-table\">\n                        <thead>\n                            <tr>\n                                <th>ID Catastale</th>\n                                <th>Progetto</th>\n                                <th>Stato</th>\n                                <th>Azioni</th>\n                            </tr>\n                        </thead>\n                        <tbody>\n                            {% for parcel in results.parcels %}\n                                <tr>\n                                    <td>{{ parcel.cadastral_id }}</td>\n                                    <td>{{ parcel.project.name }}</td>\n                                    <td><span class=\"parcel-badge bg-blue-100 text-blue-800\">{{ parcel.get_state_display }}</span></td>\n                                    <td>\n                                        <a href=\"{% url 'core:parcel_detail' parcel.id %}\" class=\"text-blue-600 hover:text-blue-800\">Dettagli →</a>\n                                    </td>\n                                </tr>\n                            {% endfor %}\n                        </tbody>\n                    </table>\n                </div>\n            </div>\n        {% endif %}\n        \n        <!-- Documenti -->\n        {% if results.documents %}\n            <div class=\"mb-8\">\n                <h2 class=\"text-xl font-semibold mb-4\">Documenti</h2>\n                <div class=\"overflow-x-auto\">\n                    <table class=\"data-table\">\n                        <thead>\n                            <tr>\n                                <th>Documento</th>\n                                <th>Particella</th>\n                                <th>Stato</th>\n                                <th>Azioni</th>\n                            </tr>\n                        </thead>\n                        <tbody>\n                            {% for doc in results.documents %}\n                                <tr>\n                                    <td>{{ doc.template.name }}</td>\n                                    <td>{{ doc.parcel.cadastral_id }}</td>\n                                    <td><span class=\"project-status-{{ doc.status }}\">{{ doc.get_status_display }}</span></td>\n                                    <td>\n                                        <a href=\"{% url 'core:document_detail' doc.id %}\" class=\"text-blue-600 hover:text-blue-800\">Dettagli →</a>\n                                    </td>\n                                </tr>\n                            {% endfor %}\n                        </tbody>\n                    </table>\n                </div>\n            </div>\n        {% endif %}\n        \n        {% if not results.projects and not results.parcels and not results.documents %}\n            <div class=\"text-center py-8\">\n                <p class=\"text-gray-600\">Nessun risultato trovato per \"{{ query }}\"</p>\n            </div>\n        {% endif %}\n    {% else %}\n        <div class=\"text-center py-8\">\n            <p class=\"text-gray-600\">Inserisci un termine di ricerca</p>\n        </div>\n    {% endif %}\n</div>\n{% endblock %} "}