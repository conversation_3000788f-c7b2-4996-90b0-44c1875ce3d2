{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/core/views.py"}, "originalCode": "from django.shortcuts import render, redirect\nfrom django.contrib.auth import authenticate, login, logout\nfrom django.contrib.auth.decorators import login_required\nfrom django.contrib import messages\nfrom django.db import models\nfrom django.http import HttpResponse\nimport logging\nfrom django.utils import timezone\nimport random\n\n# Configurazione del logger\nlogger = logging.getLogger(__name__)\n\n@login_required\ndef dashboard(request):\n    \"\"\"Vista principale della dashboard\"\"\"\n    try:\n        # Recuperiamo progetti recenti\n        from .models import Project, Activity\n        \n        # Recuperiamo progetti (con gestione eccezioni)\n        try:\n            recent_projects = Project.objects.all().order_by('-updated_at')[:5]\n        except Exception as e:\n            logger.error(f\"Errore nel recupero dei progetti: {str(e)}\")\n            recent_projects = []\n        \n        # Recuperiamo attività recenti (con gestione eccezioni)\n        try:\n            recent_activities = Activity.objects.all().order_by('-created_at')[:10]\n            \n            # Se non ci sono attività, creiamo alcuni esempi\n            if not recent_activities.exists():\n                _create_sample_activities(request.user)\n                recent_activities = Activity.objects.all().order_by('-created_at')[:10]\n        except Exception as e:\n            logger.error(f\"Errore nel recupero delle attività: {str(e)}\")\n            recent_activities = []\n        \n        # Registriamo un'attività di visualizzazione dashboard\n        try:\n            Activity.objects.create(\n                user=request.user,\n                activity_type='view',\n                description=f\"Visualizzazione dashboard\",\n                object_name='Dashboard'\n            )\n        except Exception as e:\n            logger.error(f\"Errore nella creazione dell'attività: {str(e)}\")\n        \n        return render(request, 'core/dashboard.html', {\n            'recent_projects': recent_projects,\n            'recent_activities': recent_activities\n        })\n    except Exception as e:\n        logger.error(f\"Errore generale nella dashboard: {str(e)}\")\n        messages.error(request, \"Si è verificato un errore nel caricamento della dashboard\")\n        return render(request, 'core/dashboard.html', {\n            'recent_projects': [],\n            'recent_activities': []\n        })\n\ndef _create_sample_activities(user):\n    \"\"\"Crea alcune attività di esempio\"\"\"\n    from .models import Activity\n    \n    sample_descriptions = [\n        \"Login al sistema\",\n        \"Visualizzazione progetto 'Raddoppio linea ferroviaria'\",\n        \"Creazione nuovo documento per particella 25/B\",\n        \"Modifica stato particella 12/A\",\n        \"Calcolo indennità per particella 18/C\",\n        \"Download documento decreto di esproprio\",\n        \"Visualizzazione mappa GIS\",\n        \"Consultazione dati catastali\"\n    ]\n    \n    sample_types = ['login', 'view', 'create', 'update', 'other', 'download']\n    \n    # Crea 8 attività di esempio con date distribuite negli ultimi 3 giorni\n    for i, desc in enumerate(sample_descriptions):\n        # Crea data casuale negli ultimi 3 giorni\n        days_ago = random.randint(0, 2)\n        hours_ago = random.randint(0, 23)\n        minutes_ago = random.randint(0, 59)\n        \n        activity_time = timezone.now() - timezone.timedelta(\n            days=days_ago, \n            hours=hours_ago,\n            minutes=minutes_ago\n        )\n        \n        Activity.objects.create(\n            user=user,\n            activity_type=random.choice(sample_types),\n            description=desc,\n            object_name=f\"Esempio {i+1}\",\n            created_at=activity_time\n        )\n\n@login_required\ndef activities(request):\n    \"\"\"Vista per recuperare le attività recenti (utilizzata con HTMX)\"\"\"\n    try:\n        from .models import Activity\n        \n        # Recuperiamo le attività recenti (globali o filtrate per utente)\n        recent_activities = Activity.objects.all().order_by('-created_at')[:10]\n        \n        # Se non ci sono attività, mostriamo un messaggio vuoto\n        if not recent_activities.exists():\n            return HttpResponse(\n                '<div class=\"text-center py-8\">'\n                '<svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-10 w-10 text-gray-400 mx-auto mb-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">'\n                '<path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />'\n                '</svg>'\n                '<p class=\"text-gray-500\">Nessuna attività recente</p>'\n                '</div>'\n            )\n        \n        return render(request, 'core/activities.html', {\n            'recent_activities': recent_activities\n        })\n    except Exception as e:\n        logger.error(f\"Errore nella vista activities: {str(e)}\")\n        return HttpResponse(\n            '<div class=\"text-center py-8\">'\n            '<p class=\"text-red-500\">Errore nel caricamento delle attività</p>'\n            '</div>'\n        )\n\ndef login_view(request):\n    if request.method == 'POST':\n        try:\n            username = request.POST.get('username')\n            password = request.POST.get('password')\n            \n            if not username or not password:\n                messages.error(request, 'Username e password sono obbligatori')\n                return render(request, 'core/login.html')\n            \n            user = authenticate(request, username=username, password=password)\n            if user:\n                if user.is_active:\n                    login(request, user)\n                    \n                    # Registriamo l'attività di login\n                    try:\n                        from .models import Activity\n                        Activity.objects.create(\n                            user=user,\n                            activity_type='login',\n                            description=f\"Login effettuato da {user.username}\",\n                            object_name=user.username\n                        )\n                    except Exception as e:\n                        logger.error(f\"Errore nella registrazione attività login: {str(e)}\")\n                    \n                    return redirect('core:dashboard')\n                else:\n                    messages.error(request, 'Account disattivato')\n            else:\n                messages.error(request, 'Username o password non corretti')\n                \n        except Exception as e:\n            logger.error(f\"Errore durante il login: {str(e)}\")\n            messages.error(request, 'Si è verificato un errore durante il login. Riprova.')\n    \n    return render(request, 'core/login.html')\n\ndef logout_view(request):\n    # Registriamo l'attività di logout\n    if request.user.is_authenticated:\n        from .models import Activity\n        Activity.objects.create(\n            user=request.user,\n            activity_type='logout',\n            description=f\"Logout effettuato da {request.user.username}\",\n            object_name=request.user.username\n        )\n    \n    logout(request)\n    return redirect('public:home')\n\n@login_required\ndef search(request):\n    query = request.GET.get('q', '').strip()\n    results = {\n        'projects': [],\n        'parcels': [],\n        'documents': []\n    }\n    \n    if query:\n        from .models import Project, Parcel, Document\n        \n        # Ricerca progetti\n        results['projects'] = Project.objects.filter(\n            models.Q(name__icontains=query) |\n            models.Q(description__icontains=query)\n        )[:5]\n        \n        # Ricerca particelle\n        results['parcels'] = Parcel.objects.filter(\n            models.Q(cadastral_id__icontains=query) |\n            models.Q(project__name__icontains=query)\n        ).select_related('project')[:5]\n        \n        # Ricerca documenti\n        results['documents'] = Document.objects.filter(\n            models.Q(parcel__cadastral_id__icontains=query) |\n            models.Q(template__name__icontains=query) |\n            models.Q(content__icontains=query)\n        ).select_related('parcel', 'template')[:5]\n    \n    return render(request, 'core/search.html', {\n        'query': query,\n        'results': results\n    })\n\n@login_required\ndef notifications(request):\n    \"\"\"View per ottenere le notifiche dell'utente\"\"\"\n    notifications = request.user.notifications.filter(is_read=False).order_by('-created_at')[:5]\n    \n    if not notifications:\n        return HttpResponse('<div class=\"p-4 text-center text-gray-500\">Nessuna notifica</div>')\n    \n    return render(request, 'core/notifications.html', {\n        'notifications': notifications\n    })\n\n@login_required\ndef mark_notification_read(request, notification_id):\n    \"\"\"View per segnare una notifica come letta\"\"\"\n    try:\n        notification = request.user.notifications.get(id=notification_id)\n        notification.is_read = True\n        notification.save()\n        return HttpResponse(status=204)\n    except Notification.DoesNotExist:\n        return HttpResponse(status=404)\n\n@login_required\ndef profile(request):\n    \"\"\"View per la gestione del profilo utente\"\"\"\n    if request.method == 'POST':\n        # Gestione aggiornamento profilo\n        user = request.user\n        user.first_name = request.POST.get('first_name', user.first_name)\n        user.last_name = request.POST.get('last_name', user.last_name)\n        user.email = request.POST.get('email', user.email)\n        \n        # Gestione cambio password\n        current_password = request.POST.get('current_password')\n        new_password = request.POST.get('new_password')\n        if current_password and new_password:\n            if user.check_password(current_password):\n                user.set_password(new_password)\n                messages.success(request, 'Password aggiornata con successo')\n            else:\n                messages.error(request, 'Password attuale non corretta')\n                return redirect('core:profile')\n        \n        user.save()\n        messages.success(request, 'Profilo aggiornato con successo')\n        return redirect('core:profile')\n    \n    return render(request, 'core/profile.html')\n\n@login_required\ndef settings(request):\n    \"\"\"View per le impostazioni dell'utente\"\"\"\n    if request.method == 'POST':\n        # Gestione preferenze utente\n        user = request.user\n        # Qui possiamo aggiungere la gestione delle preferenze utente\n        # quando implementeremo il modello UserPreferences\n        messages.success(request, 'Impostazioni aggiornate con successo')\n        return redirect('core:settings')\n    \n    return render(request, 'core/settings.html')\n\ndef design_comparison(request):\n    \"\"\"View per confrontare il vecchio e nuovo design system\"\"\"\n    return render(request, 'core/dashboard_comparison.html')\n\n@login_required\ndef dashboard_modern(request):\n    \"\"\"Dashboard moderna con il nuovo design system\"\"\"\n    from .models import Project, Activity\n\n    # Statistiche\n    stats = {\n        'active_projects': Project.objects.filter(status='active').count(),\n        'pending_workflows': 15,  # Placeholder\n        'documents_generated': 42,  # Placeholder\n        'total_value': 2450000,  # Placeholder\n    }\n\n    # Progetti recenti\n    recent_projects = Project.objects.all().order_by('-created_at')[:5]\n\n    # Attività recenti\n    try:\n        recent_activities = Activity.objects.all().order_by('-created_at')[:5]\n        if not recent_activities.exists():\n            _create_sample_activities(request.user)\n            recent_activities = Activity.objects.all().order_by('-created_at')[:5]\n    except Exception as e:\n        logger.error(f\"Errore nel recupero delle attività: {str(e)}\")\n        recent_activities = []\n\n    # Prepara dati per la tabella\n    table_headers = ['Nome', 'Stato', 'Data', 'Azioni']\n    table_rows = []\n    for project in recent_projects:\n        table_rows.append([\n            project.name,\n            project.get_status_display(),\n            project.created_at.strftime('%d/%m/%Y'),\n            f'<a href=\"/projects/{project.id}/\" class=\"text-primary hover:underline\">Visualizza</a>'\n        ])\n\n    return render(request, 'ui/layouts/dashboard_modern.html', {\n        'stats': stats,\n        'recent_projects': recent_projects,\n        'recent_activities': recent_activities,\n        'table_headers': table_headers,\n        'table_rows': table_rows,\n    })\n", "modifiedCode": "from django.shortcuts import render, redirect\nfrom django.contrib.auth import authenticate, login, logout\nfrom django.contrib.auth.decorators import login_required\nfrom django.contrib import messages\nfrom django.db import models\nfrom django.http import HttpResponse\nimport logging\nfrom django.utils import timezone\nimport random\n\n# Configurazione del logger\nlogger = logging.getLogger(__name__)\n\n@login_required\ndef dashboard(request):\n    \"\"\"Vista principale della dashboard\"\"\"\n    try:\n        # Recuperiamo progetti recenti\n        from .models import Project, Activity\n        \n        # Recuperiamo progetti (con gestione eccezioni)\n        try:\n            recent_projects = Project.objects.all().order_by('-updated_at')[:5]\n        except Exception as e:\n            logger.error(f\"Errore nel recupero dei progetti: {str(e)}\")\n            recent_projects = []\n        \n        # Recuperiamo attività recenti (con gestione eccezioni)\n        try:\n            recent_activities = Activity.objects.all().order_by('-created_at')[:10]\n            \n            # Se non ci sono attività, creiamo alcuni esempi\n            if not recent_activities.exists():\n                _create_sample_activities(request.user)\n                recent_activities = Activity.objects.all().order_by('-created_at')[:10]\n        except Exception as e:\n            logger.error(f\"Errore nel recupero delle attività: {str(e)}\")\n            recent_activities = []\n        \n        # Registriamo un'attività di visualizzazione dashboard\n        try:\n            Activity.objects.create(\n                user=request.user,\n                activity_type='view',\n                description=f\"Visualizzazione dashboard\",\n                object_name='Dashboard'\n            )\n        except Exception as e:\n            logger.error(f\"Errore nella creazione dell'attività: {str(e)}\")\n        \n        return render(request, 'core/dashboard.html', {\n            'recent_projects': recent_projects,\n            'recent_activities': recent_activities\n        })\n    except Exception as e:\n        logger.error(f\"Errore generale nella dashboard: {str(e)}\")\n        messages.error(request, \"Si è verificato un errore nel caricamento della dashboard\")\n        return render(request, 'core/dashboard.html', {\n            'recent_projects': [],\n            'recent_activities': []\n        })\n\ndef _create_sample_activities(user):\n    \"\"\"Crea alcune attività di esempio\"\"\"\n    from .models import Activity\n    \n    sample_descriptions = [\n        \"Login al sistema\",\n        \"Visualizzazione progetto 'Raddoppio linea ferroviaria'\",\n        \"Creazione nuovo documento per particella 25/B\",\n        \"Modifica stato particella 12/A\",\n        \"Calcolo indennità per particella 18/C\",\n        \"Download documento decreto di esproprio\",\n        \"Visualizzazione mappa GIS\",\n        \"Consultazione dati catastali\"\n    ]\n    \n    sample_types = ['login', 'view', 'create', 'update', 'other', 'download']\n    \n    # Crea 8 attività di esempio con date distribuite negli ultimi 3 giorni\n    for i, desc in enumerate(sample_descriptions):\n        # Crea data casuale negli ultimi 3 giorni\n        days_ago = random.randint(0, 2)\n        hours_ago = random.randint(0, 23)\n        minutes_ago = random.randint(0, 59)\n        \n        activity_time = timezone.now() - timezone.timedelta(\n            days=days_ago, \n            hours=hours_ago,\n            minutes=minutes_ago\n        )\n        \n        Activity.objects.create(\n            user=user,\n            activity_type=random.choice(sample_types),\n            description=desc,\n            object_name=f\"Esempio {i+1}\",\n            created_at=activity_time\n        )\n\n@login_required\ndef activities(request):\n    \"\"\"Vista per recuperare le attività recenti (utilizzata con HTMX)\"\"\"\n    try:\n        from .models import Activity\n        \n        # Recuperiamo le attività recenti (globali o filtrate per utente)\n        recent_activities = Activity.objects.all().order_by('-created_at')[:10]\n        \n        # Se non ci sono attività, mostriamo un messaggio vuoto\n        if not recent_activities.exists():\n            return HttpResponse(\n                '<div class=\"text-center py-8\">'\n                '<svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-10 w-10 text-gray-400 mx-auto mb-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">'\n                '<path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />'\n                '</svg>'\n                '<p class=\"text-gray-500\">Nessuna attività recente</p>'\n                '</div>'\n            )\n        \n        return render(request, 'core/activities.html', {\n            'recent_activities': recent_activities\n        })\n    except Exception as e:\n        logger.error(f\"Errore nella vista activities: {str(e)}\")\n        return HttpResponse(\n            '<div class=\"text-center py-8\">'\n            '<p class=\"text-red-500\">Errore nel caricamento delle attività</p>'\n            '</div>'\n        )\n\ndef login_view(request):\n    if request.method == 'POST':\n        try:\n            username = request.POST.get('username')\n            password = request.POST.get('password')\n            \n            if not username or not password:\n                messages.error(request, 'Username e password sono obbligatori')\n                return render(request, 'core/login.html')\n            \n            user = authenticate(request, username=username, password=password)\n            if user:\n                if user.is_active:\n                    login(request, user)\n                    \n                    # Registriamo l'attività di login\n                    try:\n                        from .models import Activity\n                        Activity.objects.create(\n                            user=user,\n                            activity_type='login',\n                            description=f\"Login effettuato da {user.username}\",\n                            object_name=user.username\n                        )\n                    except Exception as e:\n                        logger.error(f\"Errore nella registrazione attività login: {str(e)}\")\n                    \n                    return redirect('core:dashboard')\n                else:\n                    messages.error(request, 'Account disattivato')\n            else:\n                messages.error(request, 'Username o password non corretti')\n                \n        except Exception as e:\n            logger.error(f\"Errore durante il login: {str(e)}\")\n            messages.error(request, 'Si è verificato un errore durante il login. Riprova.')\n    \n    return render(request, 'core/login.html')\n\ndef logout_view(request):\n    # Registriamo l'attività di logout\n    if request.user.is_authenticated:\n        from .models import Activity\n        Activity.objects.create(\n            user=request.user,\n            activity_type='logout',\n            description=f\"Logout effettuato da {request.user.username}\",\n            object_name=request.user.username\n        )\n    \n    logout(request)\n    return redirect('public:home')\n\n@login_required\ndef search(request):\n    query = request.GET.get('q', '').strip()\n    results = {\n        'projects': [],\n        'parcels': [],\n        'documents': []\n    }\n    \n    if query:\n        from .models import Project, Parcel, Document\n        \n        # Ricerca progetti\n        results['projects'] = Project.objects.filter(\n            models.Q(name__icontains=query) |\n            models.Q(description__icontains=query)\n        )[:5]\n        \n        # Ricerca particelle\n        results['parcels'] = Parcel.objects.filter(\n            models.Q(cadastral_id__icontains=query) |\n            models.Q(project__name__icontains=query)\n        ).select_related('project')[:5]\n        \n        # Ricerca documenti\n        results['documents'] = Document.objects.filter(\n            models.Q(parcel__cadastral_id__icontains=query) |\n            models.Q(template__name__icontains=query) |\n            models.Q(content__icontains=query)\n        ).select_related('parcel', 'template')[:5]\n    \n    return render(request, 'core/search.html', {\n        'query': query,\n        'results': results\n    })\n\n@login_required\ndef notifications(request):\n    \"\"\"View per ottenere le notifiche dell'utente\"\"\"\n    notifications = request.user.notifications.filter(is_read=False).order_by('-created_at')[:5]\n    \n    if not notifications:\n        return HttpResponse('<div class=\"p-4 text-center text-gray-500\">Nessuna notifica</div>')\n    \n    return render(request, 'core/notifications.html', {\n        'notifications': notifications\n    })\n\n@login_required\ndef mark_notification_read(request, notification_id):\n    \"\"\"View per segnare una notifica come letta\"\"\"\n    try:\n        notification = request.user.notifications.get(id=notification_id)\n        notification.is_read = True\n        notification.save()\n        return HttpResponse(status=204)\n    except Notification.DoesNotExist:\n        return HttpResponse(status=404)\n\n@login_required\ndef profile(request):\n    \"\"\"View per la gestione del profilo utente\"\"\"\n    if request.method == 'POST':\n        # Gestione aggiornamento profilo\n        user = request.user\n        user.first_name = request.POST.get('first_name', user.first_name)\n        user.last_name = request.POST.get('last_name', user.last_name)\n        user.email = request.POST.get('email', user.email)\n        \n        # Gestione cambio password\n        current_password = request.POST.get('current_password')\n        new_password = request.POST.get('new_password')\n        if current_password and new_password:\n            if user.check_password(current_password):\n                user.set_password(new_password)\n                messages.success(request, 'Password aggiornata con successo')\n            else:\n                messages.error(request, 'Password attuale non corretta')\n                return redirect('core:profile')\n        \n        user.save()\n        messages.success(request, 'Profilo aggiornato con successo')\n        return redirect('core:profile')\n    \n    return render(request, 'core/profile.html')\n\n@login_required\ndef settings(request):\n    \"\"\"View per le impostazioni dell'utente\"\"\"\n    if request.method == 'POST':\n        # Gestione preferenze utente\n        user = request.user\n        # Qui possiamo aggiungere la gestione delle preferenze utente\n        # quando implementeremo il modello UserPreferences\n        messages.success(request, 'Impostazioni aggiornate con successo')\n        return redirect('core:settings')\n    \n    return render(request, 'core/settings.html')\n\ndef design_comparison(request):\n    \"\"\"View per confrontare il vecchio e nuovo design system\"\"\"\n    return render(request, 'core/dashboard_comparison.html')\n\n@login_required\ndef dashboard_modern(request):\n    \"\"\"Dashboard moderna con il nuovo design system\"\"\"\n    from .models import Project, Activity\n\n    # Statistiche\n    stats = {\n        'active_projects': Project.objects.filter(status='active').count(),\n        'pending_workflows': 15,  # Placeholder\n        'documents_generated': 42,  # Placeholder\n        'total_value': 2450000,  # Placeholder\n    }\n\n    # Progetti recenti\n    recent_projects = Project.objects.all().order_by('-created_at')[:5]\n\n    # Attività recenti\n    try:\n        recent_activities = Activity.objects.all().order_by('-created_at')[:5]\n        if not recent_activities.exists():\n            _create_sample_activities(request.user)\n            recent_activities = Activity.objects.all().order_by('-created_at')[:5]\n    except Exception as e:\n        logger.error(f\"Errore nel recupero delle attività: {str(e)}\")\n        recent_activities = []\n\n    # Prepara dati per la tabella\n    table_headers = ['Nome', 'Stato', 'Data', 'Azioni']\n    table_rows = []\n    for project in recent_projects:\n        table_rows.append([\n            project.name,\n            project.get_status_display(),\n            project.created_at.strftime('%d/%m/%Y'),\n            f'<a href=\"/projects/{project.id}/\" class=\"text-primary hover:underline\">Visualizza</a>'\n        ])\n\n    return render(request, 'ui/layouts/dashboard_modern.html', {\n        'stats': stats,\n        'recent_projects': recent_projects,\n        'recent_activities': recent_activities,\n        'table_headers': table_headers,\n        'table_rows': table_rows,\n    })\n"}