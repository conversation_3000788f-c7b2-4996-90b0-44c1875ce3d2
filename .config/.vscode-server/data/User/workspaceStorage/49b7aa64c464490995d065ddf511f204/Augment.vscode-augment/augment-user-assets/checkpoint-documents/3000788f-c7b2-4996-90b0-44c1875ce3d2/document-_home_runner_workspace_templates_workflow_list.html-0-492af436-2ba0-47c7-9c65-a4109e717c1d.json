{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/workflow/list.html"}, "originalCode": "{% extends 'base.html' %}\n\n{% block title %}Workflow - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <h1 class=\"text-2xl font-bold text-gray-900 mb-6\">Gestione Workflow</h1>\n    \n    {% if messages %}\n    <div class=\"mb-6\">\n        {% for message in messages %}\n        <div class=\"p-4 mb-4 rounded {% if message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-red-100 text-red-700{% endif %}\">\n            {{ message }}\n        </div>\n        {% endfor %}\n    </div>\n    {% endif %}\n    \n    <div class=\"bg-white shadow rounded-lg p-6\">\n        <p class=\"text-gray-600 mb-4\">Questa sezione consente di gestire i workflow di progetto e particelle.</p>\n        \n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-6\">\n            <div class=\"bg-blue-50 rounded-lg p-6\">\n                <h3 class=\"text-lg font-medium text-blue-800 mb-3\">Workflow di Progetto</h3>\n                <p class=\"text-blue-600 mb-4\">Gestisci le fasi dei progetti di esproprio.</p>\n                <a href=\"#\" class=\"btn btn-primary inline-block\">Gestisci</a>\n            </div>\n            \n            <div class=\"bg-green-50 rounded-lg p-6\">\n                <h3 class=\"text-lg font-medium text-green-800 mb-3\">Workflow di Particella</h3>\n                <p class=\"text-green-600 mb-4\">Gestisci i passaggi di stato per le particelle.</p>\n                <a href=\"#\" class=\"btn btn-primary inline-block\">Gestisci</a>\n            </div>\n            \n            <div class=\"bg-purple-50 rounded-lg p-6\">\n                <h3 class=\"text-lg font-medium text-purple-800 mb-3\">Notifiche Workflow</h3>\n                <p class=\"text-purple-600 mb-4\">Configura le notifiche per i cambi di stato.</p>\n                <a href=\"#\" class=\"btn btn-primary inline-block\">Configura</a>\n            </div>\n        </div>\n    </div>\n</div>\n{% endblock %} ", "modifiedCode": "{% extends 'base.html' %}\n\n{% block title %}Workflow - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <h1 class=\"text-2xl font-bold text-gray-900 mb-6\">Gestione Workflow</h1>\n    \n    {% if messages %}\n    <div class=\"mb-6\">\n        {% for message in messages %}\n        <div class=\"p-4 mb-4 rounded {% if message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-red-100 text-red-700{% endif %}\">\n            {{ message }}\n        </div>\n        {% endfor %}\n    </div>\n    {% endif %}\n    \n    <div class=\"bg-white shadow rounded-lg p-6\">\n        <p class=\"text-gray-600 mb-4\">Questa sezione consente di gestire i workflow di progetto e particelle.</p>\n        \n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-6\">\n            <div class=\"bg-blue-50 rounded-lg p-6\">\n                <h3 class=\"text-lg font-medium text-blue-800 mb-3\">Workflow di Progetto</h3>\n                <p class=\"text-blue-600 mb-4\">Gestisci le fasi dei progetti di esproprio.</p>\n                <a href=\"#\" class=\"btn btn-primary inline-block\">Gestisci</a>\n            </div>\n            \n            <div class=\"bg-green-50 rounded-lg p-6\">\n                <h3 class=\"text-lg font-medium text-green-800 mb-3\">Workflow di Particella</h3>\n                <p class=\"text-green-600 mb-4\">Gestisci i passaggi di stato per le particelle.</p>\n                <a href=\"#\" class=\"btn btn-primary inline-block\">Gestisci</a>\n            </div>\n            \n            <div class=\"bg-purple-50 rounded-lg p-6\">\n                <h3 class=\"text-lg font-medium text-purple-800 mb-3\">Notifiche Workflow</h3>\n                <p class=\"text-purple-600 mb-4\">Configura le notifiche per i cambi di stato.</p>\n                <a href=\"#\" class=\"btn btn-primary inline-block\">Configura</a>\n            </div>\n        </div>\n    </div>\n</div>\n{% endblock %} "}