{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/badge.html"}, "originalCode": "{# Componente Badge ispirato a shadcn/ui #}\n<div class=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\n    {% if variant == 'secondary' %}bg-secondary text-secondary-foreground hover:bg-secondary/80{% elif variant == 'outline' %}text-foreground border-input{% elif variant == 'destructive' %}bg-destructive text-destructive-foreground hover:bg-destructive/80{% else %}bg-primary text-primary-foreground hover:bg-primary/80{% endif %}\n    {% if attrs.class %}{{ attrs.class }}{% endif %}\"\n    {% for key, value in attrs.items %}{% if key != 'class' %}{{ key }}=\"{{ value }}\"{% endif %}{% endfor %}>\n\n    {% if attrs.icon %}\n    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-3 w-3 mr-1\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m4.5 12.75 6 6 9-13.5\" />\n    </svg>\n    {% endif %}\n\n    {{ text }}\n\n    {% if attrs.removable %}\n    <button class=\"ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2\"\n            onclick=\"this.parentElement.remove()\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-3 w-3\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M6 18 18 6M6 6l12 12\" />\n        </svg>\n    </button>\n    {% endif %}\n</div>\n", "modifiedCode": "{# Componente Badge ispirato a shadcn/ui #}\n<div class=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\n    {% if variant == 'secondary' %}bg-secondary text-secondary-foreground hover:bg-secondary/80{% elif variant == 'outline' %}text-foreground border-input{% elif variant == 'destructive' %}bg-destructive text-destructive-foreground hover:bg-destructive/80{% else %}bg-primary text-primary-foreground hover:bg-primary/80{% endif %}\n    {% if attrs.class %}{{ attrs.class }}{% endif %}\"\n    {% for key, value in attrs.items %}{% if key != 'class' %}{{ key }}=\"{{ value }}\"{% endif %}{% endfor %}>\n\n    {% if attrs.icon %}\n    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-3 w-3 mr-1\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m4.5 12.75 6 6 9-13.5\" />\n    </svg>\n    {% endif %}\n\n    {{ text }}\n\n    {% if attrs.removable %}\n    <button class=\"ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2\"\n            onclick=\"this.parentElement.remove()\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-3 w-3\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M6 18 18 6M6 6l12 12\" />\n        </svg>\n    </button>\n    {% endif %}\n</div>\n"}