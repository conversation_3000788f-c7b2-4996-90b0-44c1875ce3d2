{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/design_tokens.py"}, "modifiedCode": "\"\"\"\nDesign Tokens per ExProject UI System\nIspirato a shadcn/ui con supporto per dark mode e white label\n\"\"\"\n\n# Palette colori base (shadcn/ui inspired)\nDEFAULT_COLORS = {\n    # Grigi neutri\n    \"background\": \"hsl(0 0% 100%)\",\n    \"foreground\": \"hsl(222.2 84% 4.9%)\",\n    \"card\": \"hsl(0 0% 100%)\",\n    \"card-foreground\": \"hsl(222.2 84% 4.9%)\",\n    \"popover\": \"hsl(0 0% 100%)\",\n    \"popover-foreground\": \"hsl(222.2 84% 4.9%)\",\n    \n    # Colori primari\n    \"primary\": \"hsl(221.2 83.2% 53.3%)\",\n    \"primary-foreground\": \"hsl(210 40% 98%)\",\n    \n    # Colori secondari\n    \"secondary\": \"hsl(210 40% 96%)\",\n    \"secondary-foreground\": \"hsl(222.2 84% 4.9%)\",\n    \n    # Colori muted\n    \"muted\": \"hsl(210 40% 96%)\",\n    \"muted-foreground\": \"hsl(215.4 16.3% 46.9%)\",\n    \n    # Colori accent\n    \"accent\": \"hsl(210 40% 96%)\",\n    \"accent-foreground\": \"hsl(222.2 84% 4.9%)\",\n    \n    # Colori destructive\n    \"destructive\": \"hsl(0 84.2% 60.2%)\",\n    \"destructive-foreground\": \"hsl(210 40% 98%)\",\n    \n    # Bordi e input\n    \"border\": \"hsl(214.3 31.8% 91.4%)\",\n    \"input\": \"hsl(214.3 31.8% 91.4%)\",\n    \"ring\": \"hsl(221.2 83.2% 53.3%)\",\n    \n    # Colori di stato\n    \"success\": \"hsl(142.1 76.2% 36.3%)\",\n    \"success-foreground\": \"hsl(355.7 100% 97.3%)\",\n    \"warning\": \"hsl(32.1 94.6% 43.7%)\",\n    \"warning-foreground\": \"hsl(355.7 100% 97.3%)\",\n    \"info\": \"hsl(221.2 83.2% 53.3%)\",\n    \"info-foreground\": \"hsl(210 40% 98%)\",\n}\n\n# Dark mode colors\nDARK_COLORS = {\n    \"background\": \"hsl(222.2 84% 4.9%)\",\n    \"foreground\": \"hsl(210 40% 98%)\",\n    \"card\": \"hsl(222.2 84% 4.9%)\",\n    \"card-foreground\": \"hsl(210 40% 98%)\",\n    \"popover\": \"hsl(222.2 84% 4.9%)\",\n    \"popover-foreground\": \"hsl(210 40% 98%)\",\n    \n    \"primary\": \"hsl(217.2 91.2% 59.8%)\",\n    \"primary-foreground\": \"hsl(222.2 84% 4.9%)\",\n    \n    \"secondary\": \"hsl(217.2 32.6% 17.5%)\",\n    \"secondary-foreground\": \"hsl(210 40% 98%)\",\n    \n    \"muted\": \"hsl(217.2 32.6% 17.5%)\",\n    \"muted-foreground\": \"hsl(215 20.2% 65.1%)\",\n    \n    \"accent\": \"hsl(217.2 32.6% 17.5%)\",\n    \"accent-foreground\": \"hsl(210 40% 98%)\",\n    \n    \"destructive\": \"hsl(0 62.8% 30.6%)\",\n    \"destructive-foreground\": \"hsl(210 40% 98%)\",\n    \n    \"border\": \"hsl(217.2 32.6% 17.5%)\",\n    \"input\": \"hsl(217.2 32.6% 17.5%)\",\n    \"ring\": \"hsl(224.3 76.3% 94.1%)\",\n    \n    \"success\": \"hsl(142.1 70.6% 45.3%)\",\n    \"success-foreground\": \"hsl(144.9 80.4% 10%)\",\n    \"warning\": \"hsl(32.1 94.6% 43.7%)\",\n    \"warning-foreground\": \"hsl(20.5 90.2% 48.2%)\",\n    \"info\": \"hsl(217.2 91.2% 59.8%)\",\n    \"info-foreground\": \"hsl(222.2 84% 4.9%)\",\n}\n\n# Tipografia\nDEFAULT_TYPOGRAPHY = {\n    \"font-family\": {\n        \"sans\": [\"Inter\", \"ui-sans-serif\", \"system-ui\", \"sans-serif\"],\n        \"mono\": [\"JetBrains Mono\", \"ui-monospace\", \"monospace\"],\n    },\n    \"font-size\": {\n        \"xs\": \"0.75rem\",\n        \"sm\": \"0.875rem\", \n        \"base\": \"1rem\",\n        \"lg\": \"1.125rem\",\n        \"xl\": \"1.25rem\",\n        \"2xl\": \"1.5rem\",\n        \"3xl\": \"1.875rem\",\n        \"4xl\": \"2.25rem\",\n        \"5xl\": \"3rem\",\n    },\n    \"font-weight\": {\n        \"normal\": \"400\",\n        \"medium\": \"500\",\n        \"semibold\": \"600\",\n        \"bold\": \"700\",\n    },\n    \"line-height\": {\n        \"tight\": \"1.25\",\n        \"normal\": \"1.5\",\n        \"relaxed\": \"1.75\",\n    },\n}\n\n# Spacing system\nDEFAULT_SPACING = {\n    \"0\": \"0px\",\n    \"1\": \"0.25rem\",\n    \"2\": \"0.5rem\", \n    \"3\": \"0.75rem\",\n    \"4\": \"1rem\",\n    \"5\": \"1.25rem\",\n    \"6\": \"1.5rem\",\n    \"8\": \"2rem\",\n    \"10\": \"2.5rem\",\n    \"12\": \"3rem\",\n    \"16\": \"4rem\",\n    \"20\": \"5rem\",\n    \"24\": \"6rem\",\n}\n\n# Border radius\nDEFAULT_BORDERS = {\n    \"radius\": {\n        \"none\": \"0px\",\n        \"sm\": \"0.125rem\",\n        \"md\": \"0.375rem\", \n        \"lg\": \"0.5rem\",\n        \"xl\": \"0.75rem\",\n        \"2xl\": \"1rem\",\n        \"full\": \"9999px\",\n    },\n    \"width\": {\n        \"0\": \"0px\",\n        \"1\": \"1px\",\n        \"2\": \"2px\",\n        \"4\": \"4px\",\n        \"8\": \"8px\",\n    }\n}\n\n# Shadows\nDEFAULT_SHADOWS = {\n    \"sm\": \"0 1px 2px 0 rgb(0 0 0 / 0.05)\",\n    \"md\": \"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)\",\n    \"lg\": \"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)\",\n    \"xl\": \"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)\",\n    \"2xl\": \"0 25px 50px -12px rgb(0 0 0 / 0.25)\",\n    \"inner\": \"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)\",\n    \"none\": \"0 0 #0000\",\n}\n\n# Animazioni e transizioni\nDEFAULT_ANIMATIONS = {\n    \"duration\": {\n        \"75\": \"75ms\",\n        \"100\": \"100ms\",\n        \"150\": \"150ms\",\n        \"200\": \"200ms\",\n        \"300\": \"300ms\",\n        \"500\": \"500ms\",\n        \"700\": \"700ms\",\n        \"1000\": \"1000ms\",\n    },\n    \"timing\": {\n        \"linear\": \"linear\",\n        \"in\": \"cubic-bezier(0.4, 0, 1, 1)\",\n        \"out\": \"cubic-bezier(0, 0, 0.2, 1)\",\n        \"in-out\": \"cubic-bezier(0.4, 0, 0.2, 1)\",\n    }\n}\n\n# Configurazione default completa\nDEFAULT_THEME_CONFIG = {\n    \"name\": \"ExProject Default\",\n    \"slug\": \"exproject-default\",\n    \"colors\": DEFAULT_COLORS,\n    \"dark_colors\": DARK_COLORS,\n    \"typography\": DEFAULT_TYPOGRAPHY,\n    \"spacing\": DEFAULT_SPACING,\n    \"borders\": DEFAULT_BORDERS,\n    \"shadows\": DEFAULT_SHADOWS,\n    \"animations\": DEFAULT_ANIMATIONS,\n}\n\n# Temi predefiniti per diversi clienti\nTHEME_PRESETS = {\n    \"municipality\": {\n        \"name\": \"Tema Comunale\",\n        \"colors\": {\n            **DEFAULT_COLORS,\n            \"primary\": \"hsl(142.1 76.2% 36.3%)\",  # Verde istituzionale\n            \"primary-foreground\": \"hsl(355.7 100% 97.3%)\",\n        }\n    },\n    \"province\": {\n        \"name\": \"Tema Provinciale\", \n        \"colors\": {\n            **DEFAULT_COLORS,\n            \"primary\": \"hsl(262.1 83.3% 57.8%)\",  # Viola istituzionale\n            \"primary-foreground\": \"hsl(210 40% 98%)\",\n        }\n    },\n    \"region\": {\n        \"name\": \"Tema Regionale\",\n        \"colors\": {\n            **DEFAULT_COLORS,\n            \"primary\": \"hsl(0 84.2% 60.2%)\",  # Rosso istituzionale\n            \"primary-foreground\": \"hsl(210 40% 98%)\",\n        }\n    }\n}\n\n\ndef get_css_variables(theme_config):\n    \"\"\"Genera CSS custom properties dal tema\"\"\"\n    css_vars = []\n    \n    # Colori\n    for name, value in theme_config.get(\"colors\", {}).items():\n        css_vars.append(f\"  --{name}: {value};\")\n    \n    # Dark mode colors\n    dark_colors = theme_config.get(\"dark_colors\", {})\n    if dark_colors:\n        css_vars.append(\"\\n@media (prefers-color-scheme: dark) {\")\n        css_vars.append(\"  :root {\")\n        for name, value in dark_colors.items():\n            css_vars.append(f\"    --{name}: {value};\")\n        css_vars.append(\"  }\")\n        css_vars.append(\"}\")\n    \n    return \"\\n\".join([\":root {\"] + css_vars + [\"}\"])\n\n\ndef get_tailwind_config(theme_config):\n    \"\"\"Genera configurazione Tailwind dal tema\"\"\"\n    return {\n        \"theme\": {\n            \"extend\": {\n                \"colors\": {\n                    name.replace(\"-\", \"\"): f\"hsl(var(--{name}))\"\n                    for name in theme_config.get(\"colors\", {}).keys()\n                },\n                \"fontFamily\": theme_config.get(\"typography\", {}).get(\"font-family\", {}),\n                \"fontSize\": theme_config.get(\"typography\", {}).get(\"font-size\", {}),\n                \"spacing\": theme_config.get(\"spacing\", {}),\n                \"borderRadius\": theme_config.get(\"borders\", {}).get(\"radius\", {}),\n                \"boxShadow\": theme_config.get(\"shadows\", {}),\n            }\n        }\n    }\n"}