{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/public/contacts.html"}, "originalCode": "{% extends \"base.html\" %}\n\n{% block title %}{{ title }}{% endblock %}\n\n{% block content %}\n<div class=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n    <!-- Header -->\n    <div class=\"text-center mb-12\">\n        <h1 class=\"text-4xl font-bold text-gray-900 mb-4\">{{ title }}</h1>\n        <p class=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Siamo qui per aiutarti. Contattaci per qualsiasi informazione o supporto\n        </p>\n    </div>\n    \n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n        <!-- Informazioni di Contatto -->\n        <div class=\"bg-white shadow rounded-lg p-8\">\n            <h2 class=\"text-2xl font-bold text-gray-900 mb-6\">Contattaci</h2>\n            \n            <div class=\"space-y-6\">\n                <div class=\"flex items-start\">\n                    <div class=\"flex-shrink-0\">\n                        <svg class=\"h-6 w-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" \n                                  d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"/>\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" \n                                  d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"/>\n                        </svg>\n                    </div>\n                    <div class=\"ml-4\">\n                        <h3 class=\"text-lg font-medium text-gray-900\">Indirizzo</h3>\n                        <p class=\"mt-1 text-gray-600\">{{ contact_info.address }}</p>\n                    </div>\n                </div>\n                \n                <div class=\"flex items-start\">\n                    <div class=\"flex-shrink-0\">\n                        <svg class=\"h-6 w-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" \n                                  d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"/>\n                        </svg>\n                    </div>\n                    <div class=\"ml-4\">\n                        <h3 class=\"text-lg font-medium text-gray-900\">Telefono</h3>\n                        <p class=\"mt-1 text-gray-600\">{{ contact_info.phone }}</p>\n                    </div>\n                </div>\n                \n                <div class=\"flex items-start\">\n                    <div class=\"flex-shrink-0\">\n                        <svg class=\"h-6 w-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" \n                                  d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"/>\n                        </svg>\n                    </div>\n                    <div class=\"ml-4\">\n                        <h3 class=\"text-lg font-medium text-gray-900\">Email</h3>\n                        <p class=\"mt-1 text-gray-600\">{{ contact_info.email }}</p>\n                    </div>\n                </div>\n                \n                <div class=\"flex items-start\">\n                    <div class=\"flex-shrink-0\">\n                        <svg class=\"h-6 w-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" \n                                  d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n                        </svg>\n                    </div>\n                    <div class=\"ml-4\">\n                        <h3 class=\"text-lg font-medium text-gray-900\">Orari</h3>\n                        <p class=\"mt-1 text-gray-600\">{{ contact_info.hours }}</p>\n                    </div>\n                </div>\n            </div>\n            \n            <!-- Social Media -->\n            <div class=\"mt-8 pt-8 border-t border-gray-200\">\n                <h3 class=\"text-lg font-medium text-gray-900 mb-4\">Seguici</h3>\n                <div class=\"flex space-x-6\">\n                    <a href=\"{{ contact_info.social.linkedin }}\" class=\"text-gray-400 hover:text-gray-500\">\n                        <span class=\"sr-only\">LinkedIn</span>\n                        <svg class=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path d=\"M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z\"/>\n                        </svg>\n                    </a>\n                    <a href=\"{{ contact_info.social.twitter }}\" class=\"text-gray-400 hover:text-gray-500\">\n                        <span class=\"sr-only\">Twitter</span>\n                        <svg class=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\n                        </svg>\n                    </a>\n                    <a href=\"{{ contact_info.social.facebook }}\" class=\"text-gray-400 hover:text-gray-500\">\n                        <span class=\"sr-only\">Facebook</span>\n                        <svg class=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path fill-rule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clip-rule=\"evenodd\"/>\n                        </svg>\n                    </a>\n                </div>\n            </div>\n        </div>\n        \n        <!-- Form di Contatto -->\n        <div class=\"bg-white shadow rounded-lg p-8\">\n            <h2 class=\"text-2xl font-bold text-gray-900 mb-6\">Invia un Messaggio</h2>\n            \n            <form class=\"space-y-6\">\n                <div>\n                    <label for=\"name\" class=\"block text-sm font-medium text-gray-700\">Nome</label>\n                    <input type=\"text\" name=\"name\" id=\"name\" required\n                           class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n                </div>\n                \n                <div>\n                    <label for=\"email\" class=\"block text-sm font-medium text-gray-700\">Email</label>\n                    <input type=\"email\" name=\"email\" id=\"email\" required\n                           class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n                </div>\n                \n                <div>\n                    <label for=\"subject\" class=\"block text-sm font-medium text-gray-700\">Oggetto</label>\n                    <input type=\"text\" name=\"subject\" id=\"subject\" required\n                           class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n                </div>\n                \n                <div>\n                    <label for=\"message\" class=\"block text-sm font-medium text-gray-700\">Messaggio</label>\n                    <textarea name=\"message\" id=\"message\" rows=\"4\" required\n                              class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\"></textarea>\n                </div>\n                \n                <div>\n                    <button type=\"submit\" class=\"btn btn-primary w-full\">\n                        Invia Messaggio\n                    </button>\n                </div>\n            </form>\n        </div>\n    </div>\n</div>\n{% endblock %}", "modifiedCode": "{% extends \"base.html\" %}\n\n{% block title %}{{ title }}{% endblock %}\n\n{% block content %}\n<div class=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n    <!-- Header -->\n    <div class=\"text-center mb-12\">\n        <h1 class=\"text-4xl font-bold text-gray-900 mb-4\">{{ title }}</h1>\n        <p class=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Siamo qui per aiutarti. Contattaci per qualsiasi informazione o supporto\n        </p>\n    </div>\n    \n    <div class=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n        <!-- Informazioni di Contatto -->\n        <div class=\"bg-white shadow rounded-lg p-8\">\n            <h2 class=\"text-2xl font-bold text-gray-900 mb-6\">Contattaci</h2>\n            \n            <div class=\"space-y-6\">\n                <div class=\"flex items-start\">\n                    <div class=\"flex-shrink-0\">\n                        <svg class=\"h-6 w-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" \n                                  d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"/>\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" \n                                  d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"/>\n                        </svg>\n                    </div>\n                    <div class=\"ml-4\">\n                        <h3 class=\"text-lg font-medium text-gray-900\">Indirizzo</h3>\n                        <p class=\"mt-1 text-gray-600\">{{ contact_info.address }}</p>\n                    </div>\n                </div>\n                \n                <div class=\"flex items-start\">\n                    <div class=\"flex-shrink-0\">\n                        <svg class=\"h-6 w-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" \n                                  d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"/>\n                        </svg>\n                    </div>\n                    <div class=\"ml-4\">\n                        <h3 class=\"text-lg font-medium text-gray-900\">Telefono</h3>\n                        <p class=\"mt-1 text-gray-600\">{{ contact_info.phone }}</p>\n                    </div>\n                </div>\n                \n                <div class=\"flex items-start\">\n                    <div class=\"flex-shrink-0\">\n                        <svg class=\"h-6 w-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" \n                                  d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"/>\n                        </svg>\n                    </div>\n                    <div class=\"ml-4\">\n                        <h3 class=\"text-lg font-medium text-gray-900\">Email</h3>\n                        <p class=\"mt-1 text-gray-600\">{{ contact_info.email }}</p>\n                    </div>\n                </div>\n                \n                <div class=\"flex items-start\">\n                    <div class=\"flex-shrink-0\">\n                        <svg class=\"h-6 w-6 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" \n                                  d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"/>\n                        </svg>\n                    </div>\n                    <div class=\"ml-4\">\n                        <h3 class=\"text-lg font-medium text-gray-900\">Orari</h3>\n                        <p class=\"mt-1 text-gray-600\">{{ contact_info.hours }}</p>\n                    </div>\n                </div>\n            </div>\n            \n            <!-- Social Media -->\n            <div class=\"mt-8 pt-8 border-t border-gray-200\">\n                <h3 class=\"text-lg font-medium text-gray-900 mb-4\">Seguici</h3>\n                <div class=\"flex space-x-6\">\n                    <a href=\"{{ contact_info.social.linkedin }}\" class=\"text-gray-400 hover:text-gray-500\">\n                        <span class=\"sr-only\">LinkedIn</span>\n                        <svg class=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path d=\"M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z\"/>\n                        </svg>\n                    </a>\n                    <a href=\"{{ contact_info.social.twitter }}\" class=\"text-gray-400 hover:text-gray-500\">\n                        <span class=\"sr-only\">Twitter</span>\n                        <svg class=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\"/>\n                        </svg>\n                    </a>\n                    <a href=\"{{ contact_info.social.facebook }}\" class=\"text-gray-400 hover:text-gray-500\">\n                        <span class=\"sr-only\">Facebook</span>\n                        <svg class=\"h-6 w-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path fill-rule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clip-rule=\"evenodd\"/>\n                        </svg>\n                    </a>\n                </div>\n            </div>\n        </div>\n        \n        <!-- Form di Contatto -->\n        <div class=\"bg-white shadow rounded-lg p-8\">\n            <h2 class=\"text-2xl font-bold text-gray-900 mb-6\">Invia un Messaggio</h2>\n            \n            <form class=\"space-y-6\">\n                <div>\n                    <label for=\"name\" class=\"block text-sm font-medium text-gray-700\">Nome</label>\n                    <input type=\"text\" name=\"name\" id=\"name\" required\n                           class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n                </div>\n                \n                <div>\n                    <label for=\"email\" class=\"block text-sm font-medium text-gray-700\">Email</label>\n                    <input type=\"email\" name=\"email\" id=\"email\" required\n                           class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n                </div>\n                \n                <div>\n                    <label for=\"subject\" class=\"block text-sm font-medium text-gray-700\">Oggetto</label>\n                    <input type=\"text\" name=\"subject\" id=\"subject\" required\n                           class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n                </div>\n                \n                <div>\n                    <label for=\"message\" class=\"block text-sm font-medium text-gray-700\">Messaggio</label>\n                    <textarea name=\"message\" id=\"message\" rows=\"4\" required\n                              class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\"></textarea>\n                </div>\n                \n                <div>\n                    <button type=\"submit\" class=\"btn btn-primary w-full\">\n                        Invia Messaggio\n                    </button>\n                </div>\n            </form>\n        </div>\n    </div>\n</div>\n{% endblock %}"}