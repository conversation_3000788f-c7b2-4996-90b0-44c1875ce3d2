{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/boilerplates/form.html"}, "modifiedCode": "{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}{{ form_title }}{% endblock %}\n\n{% block content %}\n<div class=\"flex-1 space-y-4 p-8 pt-6\">\n    <!-- Header -->\n    <div class=\"flex items-center justify-between\">\n        <h2 class=\"text-3xl font-bold tracking-tight\">{{ form_title }}</h2>\n        <div class=\"flex items-center space-x-2\">\n            {% ui_button \"Cancel\" variant=\"outline\" onclick=\"history.back()\" %}\n        </div>\n    </div>\n    \n    <!-- Form Card -->\n    <div class=\"max-w-2xl\">\n        {% ui_card \n            title=\"Details\"\n            content=\"\n            <form method='post' enctype='multipart/form-data' class='space-y-6'>\n                {% csrf_token %}\n                \n                <!-- Form Fields -->\n                {% for field in form %}\n                    <div class='space-y-2'>\n                        <label for='{{ field.id_for_label }}' class='text-sm font-medium'>\n                            {{ field.label }}\n                            {% if field.field.required %}<span class='text-red-500'>*</span>{% endif %}\n                        </label>\n                        {{ field }}\n                        {% if field.errors %}\n                            <div class='text-sm text-red-600'>\n                                {% for error in field.errors %}\n                                    <p>{{ error }}</p>\n                                {% endfor %}\n                            </div>\n                        {% endif %}\n                        {% if field.help_text %}\n                            <p class='text-sm text-muted-foreground'>{{ field.help_text }}</p>\n                        {% endif %}\n                    </div>\n                {% endfor %}\n                \n                <!-- Form Actions -->\n                <div class='flex justify-end space-x-2 pt-6 border-t'>\n                    {% ui_button 'Cancel' variant='outline' onclick='history.back()' %}\n                    {% ui_button 'Save' type='submit' variant='default' %}\n                </div>\n            </form>\n            \"\n        %}\n    </div>\n    \n    <!-- Messages -->\n    {% if messages %}\n        <div class=\"space-y-2\">\n            {% for message in messages %}\n                {% ui_alert message.message variant=message.tags %}\n            {% endfor %}\n        </div>\n    {% endif %}\n</div>\n{% endblock %}\n"}