{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/base_modern.html"}, "originalCode": "{% load static %}\n{% load ui_components %}\n<!DOCTYPE html>\n<html lang=\"it\" class=\"h-full\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{% block title %}ExProject{% endblock %}</title>\n    \n    <!-- Favicon e PWA -->\n    <link rel=\"icon\" href=\"{% static 'img/favicon.ico' %}\" type=\"image/x-icon\">\n    <meta name=\"theme-color\" content=\"hsl(221.2 83.2% 53.3%)\">\n    \n    <!-- Fonts -->\n    <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n    <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap\" rel=\"stylesheet\">\n    \n    <!-- Design System CSS -->\n    {% ui_theme_css %}\n    \n    <!-- TailwindCSS con configurazione custom -->\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script>\n        tailwind.config = {\n            darkMode: 'class',\n            theme: {\n                extend: {\n                    colors: {\n                        border: \"hsl(var(--border))\",\n                        input: \"hsl(var(--input))\",\n                        ring: \"hsl(var(--ring))\",\n                        background: \"hsl(var(--background))\",\n                        foreground: \"hsl(var(--foreground))\",\n                        primary: {\n                            DEFAULT: \"hsl(var(--primary))\",\n                            foreground: \"hsl(var(--primary-foreground))\",\n                        },\n                        secondary: {\n                            DEFAULT: \"hsl(var(--secondary))\",\n                            foreground: \"hsl(var(--secondary-foreground))\",\n                        },\n                        destructive: {\n                            DEFAULT: \"hsl(var(--destructive))\",\n                            foreground: \"hsl(var(--destructive-foreground))\",\n                        },\n                        muted: {\n                            DEFAULT: \"hsl(var(--muted))\",\n                            foreground: \"hsl(var(--muted-foreground))\",\n                        },\n                        accent: {\n                            DEFAULT: \"hsl(var(--accent))\",\n                            foreground: \"hsl(var(--accent-foreground))\",\n                        },\n                        popover: {\n                            DEFAULT: \"hsl(var(--popover))\",\n                            foreground: \"hsl(var(--popover-foreground))\",\n                        },\n                        card: {\n                            DEFAULT: \"hsl(var(--card))\",\n                            foreground: \"hsl(var(--card-foreground))\",\n                        },\n                    },\n                    fontFamily: {\n                        sans: ['Inter', 'ui-sans-serif', 'system-ui'],\n                        mono: ['JetBrains Mono', 'ui-monospace'],\n                    },\n                    borderRadius: {\n                        lg: \"var(--radius)\",\n                        md: \"calc(var(--radius) - 2px)\",\n                        sm: \"calc(var(--radius) - 4px)\",\n                    },\n                    keyframes: {\n                        \"accordion-down\": {\n                            from: { height: 0 },\n                            to: { height: \"var(--radix-accordion-content-height)\" },\n                        },\n                        \"accordion-up\": {\n                            from: { height: \"var(--radix-accordion-content-height)\" },\n                            to: { height: 0 },\n                        },\n                    },\n                    animation: {\n                        \"accordion-down\": \"accordion-down 0.2s ease-out\",\n                        \"accordion-up\": \"accordion-up 0.2s ease-out\",\n                    },\n                }\n            }\n        }\n    </script>\n    \n    <!-- HTMX e Alpine.js -->\n    <script src=\"https://cdn.jsdelivr.net/npm/htmx.org@1.9.10\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js\" defer></script>\n    \n    {% block extra_head %}{% endblock %}\n</head>\n<body class=\"min-h-screen bg-background font-sans antialiased\"\n      hx-headers='{\"X-CSRFToken\": \"{{ csrf_token }}\"}'\n      x-data=\"{ sidebarOpen: false, darkMode: false }\"\n      x-init=\"darkMode = localStorage.getItem('darkMode') === 'true'\"\n      :class=\"{ 'dark': darkMode }\">\n    \n    <!-- Header moderno -->\n    {% block header %}\n    <header class=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n        <div class=\"container flex h-14 items-center\">\n            <!-- Mobile menu button -->\n            <button @click=\"sidebarOpen = !sidebarOpen\"\n                    class=\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10 md:hidden\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5\" />\n                </svg>\n            </button>\n            \n            <!-- Logo -->\n            <div class=\"mr-4 hidden md:flex\">\n                <a href=\"{% url 'public:home' %}\" class=\"mr-6 flex items-center space-x-2\">\n                    <div class=\"h-6 w-6 rounded bg-primary\"></div>\n                    <span class=\"hidden font-bold sm:inline-block\">ExProject</span>\n                </a>\n            </div>\n            \n            <!-- Navigation -->\n            {% if user.is_authenticated %}\n            <nav class=\"flex items-center space-x-6 text-sm font-medium hidden md:flex\">\n                <a href=\"{% url 'core:dashboard' %}\" \n                   class=\"transition-colors hover:text-foreground/80 {% if request.resolver_match.url_name == 'dashboard' %}text-foreground{% else %}text-foreground/60{% endif %}\">\n                    Dashboard\n                </a>\n                <a href=\"{% url 'projects:list' %}\" \n                   class=\"transition-colors hover:text-foreground/80 {% if request.resolver_match.namespace == 'projects' %}text-foreground{% else %}text-foreground/60{% endif %}\">\n                    Progetti\n                </a>\n                <a href=\"{% url 'workflow:list' %}\" \n                   class=\"transition-colors hover:text-foreground/80 {% if request.resolver_match.namespace == 'workflow' %}text-foreground{% else %}text-foreground/60{% endif %}\">\n                    Workflow\n                </a>\n                <a href=\"{% url 'documents:list' %}\" \n                   class=\"transition-colors hover:text-foreground/80 {% if request.resolver_match.namespace == 'documents' %}text-foreground{% else %}text-foreground/60{% endif %}\">\n                    Documenti\n                </a>\n            </nav>\n            {% endif %}\n            \n            <div class=\"flex flex-1 items-center justify-between space-x-2 md:justify-end\">\n                <!-- Search -->\n                {% if user.is_authenticated %}\n                <div class=\"w-full flex-1 md:w-auto md:flex-none\">\n                    <form method=\"get\" action=\"{% url 'core:search' %}\" class=\"relative\">\n                        <input type=\"text\" name=\"q\" placeholder=\"Cerca...\" \n                               class=\"inline-flex items-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2 relative w-full justify-start text-sm text-muted-foreground sm:pr-12 md:w-40 lg:w-64\">\n                        <button type=\"submit\" class=\"absolute right-0 top-0 h-9 w-9 inline-flex items-center justify-center\">\n                            {% ui_icon 'search' size=\"4\" %}\n                        </button>\n                    </form>\n                </div>\n                {% endif %}\n                \n                <!-- Theme toggle -->\n                <button @click=\"darkMode = !darkMode; localStorage.setItem('darkMode', darkMode)\" \n                        class=\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n                    <span x-show=\"!darkMode\">{% ui_icon 'moon' size=\"4\" %}</span>\n                    <span x-show=\"darkMode\">{% ui_icon 'sun' size=\"4\" %}</span>\n                </button>\n                \n                <!-- User menu -->\n                {% if user.is_authenticated %}\n                <div class=\"relative\" x-data=\"{ open: false }\">\n                    <button @click=\"open = !open\" \n                            class=\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n                        <div class=\"h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground text-sm font-medium\">\n                            {{ user.username|slice:\":1\"|upper }}\n                        </div>\n                    </button>\n                    \n                    <div x-show=\"open\" @click.away=\"open = false\" \n                         x-transition:enter=\"transition ease-out duration-100\"\n                         x-transition:enter-start=\"transform opacity-0 scale-95\"\n                         x-transition:enter-end=\"transform opacity-100 scale-100\"\n                         x-transition:leave=\"transition ease-in duration-75\"\n                         x-transition:leave-start=\"transform opacity-100 scale-100\"\n                         x-transition:leave-end=\"transform opacity-0 scale-95\"\n                         class=\"absolute right-0 mt-2 w-56 rounded-md border bg-popover p-1 text-popover-foreground shadow-md outline-none z-50\">\n                        <div class=\"px-2 py-1.5 text-sm font-semibold\">{{ user.get_full_name|default:user.username }}</div>\n                        <div class=\"h-px bg-border my-1\"></div>\n                        <a href=\"{% url 'core:profile' %}\" class=\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground\">\n                            {% ui_icon 'user' size=\"4\" class=\"mr-2\" %}\n                            Profilo\n                        </a>\n                        <a href=\"{% url 'core:settings' %}\" class=\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground\">\n                            {% ui_icon 'settings' size=\"4\" class=\"mr-2\" %}\n                            Impostazioni\n                        </a>\n                        <div class=\"h-px bg-border my-1\"></div>\n                        <a href=\"{% url 'core:logout' %}\" class=\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground\">\n                            {% ui_icon 'logout' size=\"4\" class=\"mr-2\" %}\n                            Logout\n                        </a>\n                    </div>\n                </div>\n                {% else %}\n                {% ui_button \"Accedi\" variant=\"default\" size=\"sm\" href=\"/login/\" %}\n                {% endif %}\n            </div>\n        </div>\n    </header>\n    {% endblock %}\n    \n    <!-- Mobile sidebar overlay -->\n    <div x-show=\"sidebarOpen\" @click=\"sidebarOpen = false\" \n         class=\"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm md:hidden\"\n         x-transition:enter=\"transition-all ease-in-out duration-300\"\n         x-transition:enter-start=\"opacity-0\"\n         x-transition:enter-end=\"opacity-100\"\n         x-transition:leave=\"transition-all ease-in-out duration-300\"\n         x-transition:leave-start=\"opacity-100\"\n         x-transition:leave-end=\"opacity-0\"></div>\n    \n    <!-- Main content -->\n    <div class=\"flex-1\">\n        {% block messages %}\n        {% if messages %}\n        <div class=\"container mx-auto px-4 py-2\">\n            {% for message in messages %}\n            {% ui_alert message.message variant=message.tags title=\"Notifica\" %}\n            {% endfor %}\n        </div>\n        {% endif %}\n        {% endblock %}\n        \n        {% block main %}\n        <main class=\"container mx-auto px-4 py-6\">\n            {% block content %}{% endblock %}\n        </main>\n        {% endblock %}\n    </div>\n    \n    <!-- Footer -->\n    {% block footer %}\n    <footer class=\"border-t bg-background\">\n        <div class=\"container flex flex-col items-center justify-between gap-4 py-10 md:h-24 md:flex-row md:py-0\">\n            <div class=\"flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0\">\n                <p class=\"text-center text-sm leading-loose text-muted-foreground md:text-left\">\n                    © 2025 ExProject. Tutti i diritti riservati.\n                </p>\n            </div>\n        </div>\n    </footer>\n    {% endblock %}\n    \n    {% block extra_js %}{% endblock %}\n</body>\n</html>\n", "modifiedCode": "{% load static %}\n{% load ui_components %}\n<!DOCTYPE html>\n<html lang=\"it\" class=\"h-full\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{% block title %}ExProject{% endblock %}</title>\n    \n    <!-- Favicon e PWA -->\n    <link rel=\"icon\" href=\"{% static 'img/favicon.ico' %}\" type=\"image/x-icon\">\n    <meta name=\"theme-color\" content=\"hsl(221.2 83.2% 53.3%)\">\n    \n    <!-- Fonts -->\n    <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n    <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap\" rel=\"stylesheet\">\n    \n    <!-- Design System CSS -->\n    {% ui_theme_css %}\n    \n    <!-- TailwindCSS con configurazione custom -->\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script>\n        tailwind.config = {\n            darkMode: 'class',\n            theme: {\n                extend: {\n                    colors: {\n                        border: \"hsl(var(--border))\",\n                        input: \"hsl(var(--input))\",\n                        ring: \"hsl(var(--ring))\",\n                        background: \"hsl(var(--background))\",\n                        foreground: \"hsl(var(--foreground))\",\n                        primary: {\n                            DEFAULT: \"hsl(var(--primary))\",\n                            foreground: \"hsl(var(--primary-foreground))\",\n                        },\n                        secondary: {\n                            DEFAULT: \"hsl(var(--secondary))\",\n                            foreground: \"hsl(var(--secondary-foreground))\",\n                        },\n                        destructive: {\n                            DEFAULT: \"hsl(var(--destructive))\",\n                            foreground: \"hsl(var(--destructive-foreground))\",\n                        },\n                        muted: {\n                            DEFAULT: \"hsl(var(--muted))\",\n                            foreground: \"hsl(var(--muted-foreground))\",\n                        },\n                        accent: {\n                            DEFAULT: \"hsl(var(--accent))\",\n                            foreground: \"hsl(var(--accent-foreground))\",\n                        },\n                        popover: {\n                            DEFAULT: \"hsl(var(--popover))\",\n                            foreground: \"hsl(var(--popover-foreground))\",\n                        },\n                        card: {\n                            DEFAULT: \"hsl(var(--card))\",\n                            foreground: \"hsl(var(--card-foreground))\",\n                        },\n                    },\n                    fontFamily: {\n                        sans: ['Inter', 'ui-sans-serif', 'system-ui'],\n                        mono: ['JetBrains Mono', 'ui-monospace'],\n                    },\n                    borderRadius: {\n                        lg: \"var(--radius)\",\n                        md: \"calc(var(--radius) - 2px)\",\n                        sm: \"calc(var(--radius) - 4px)\",\n                    },\n                    keyframes: {\n                        \"accordion-down\": {\n                            from: { height: 0 },\n                            to: { height: \"var(--radix-accordion-content-height)\" },\n                        },\n                        \"accordion-up\": {\n                            from: { height: \"var(--radix-accordion-content-height)\" },\n                            to: { height: 0 },\n                        },\n                    },\n                    animation: {\n                        \"accordion-down\": \"accordion-down 0.2s ease-out\",\n                        \"accordion-up\": \"accordion-up 0.2s ease-out\",\n                    },\n                }\n            }\n        }\n    </script>\n    \n    <!-- HTMX e Alpine.js -->\n    <script src=\"https://cdn.jsdelivr.net/npm/htmx.org@1.9.10\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js\" defer></script>\n    \n    {% block extra_head %}{% endblock %}\n</head>\n<body class=\"min-h-screen bg-background font-sans antialiased\"\n      hx-headers='{\"X-CSRFToken\": \"{{ csrf_token }}\"}'\n      x-data=\"{ sidebarOpen: false, darkMode: false }\"\n      x-init=\"darkMode = localStorage.getItem('darkMode') === 'true'\"\n      :class=\"{ 'dark': darkMode }\">\n    \n    <!-- Header moderno -->\n    {% block header %}\n    <header class=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n        <div class=\"container flex h-14 items-center\">\n            <!-- Mobile menu button -->\n            <button @click=\"sidebarOpen = !sidebarOpen\"\n                    class=\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10 md:hidden\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5\" />\n                </svg>\n            </button>\n            \n            <!-- Logo -->\n            <div class=\"mr-4 hidden md:flex\">\n                <a href=\"{% url 'public:home' %}\" class=\"mr-6 flex items-center space-x-2\">\n                    <div class=\"h-6 w-6 rounded bg-primary\"></div>\n                    <span class=\"hidden font-bold sm:inline-block\">ExProject</span>\n                </a>\n            </div>\n            \n            <!-- Navigation -->\n            {% if user.is_authenticated %}\n            <nav class=\"flex items-center space-x-6 text-sm font-medium hidden md:flex\">\n                <a href=\"{% url 'core:dashboard' %}\" \n                   class=\"transition-colors hover:text-foreground/80 {% if request.resolver_match.url_name == 'dashboard' %}text-foreground{% else %}text-foreground/60{% endif %}\">\n                    Dashboard\n                </a>\n                <a href=\"{% url 'projects:list' %}\" \n                   class=\"transition-colors hover:text-foreground/80 {% if request.resolver_match.namespace == 'projects' %}text-foreground{% else %}text-foreground/60{% endif %}\">\n                    Progetti\n                </a>\n                <a href=\"{% url 'workflow:list' %}\" \n                   class=\"transition-colors hover:text-foreground/80 {% if request.resolver_match.namespace == 'workflow' %}text-foreground{% else %}text-foreground/60{% endif %}\">\n                    Workflow\n                </a>\n                <a href=\"{% url 'documents:list' %}\" \n                   class=\"transition-colors hover:text-foreground/80 {% if request.resolver_match.namespace == 'documents' %}text-foreground{% else %}text-foreground/60{% endif %}\">\n                    Documenti\n                </a>\n            </nav>\n            {% endif %}\n            \n            <div class=\"flex flex-1 items-center justify-between space-x-2 md:justify-end\">\n                <!-- Search -->\n                {% if user.is_authenticated %}\n                <div class=\"w-full flex-1 md:w-auto md:flex-none\">\n                    <form method=\"get\" action=\"{% url 'core:search' %}\" class=\"relative\">\n                        <input type=\"text\" name=\"q\" placeholder=\"Cerca...\" \n                               class=\"inline-flex items-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2 relative w-full justify-start text-sm text-muted-foreground sm:pr-12 md:w-40 lg:w-64\">\n                        <button type=\"submit\" class=\"absolute right-0 top-0 h-9 w-9 inline-flex items-center justify-center\">\n                            {% ui_icon 'search' size=\"4\" %}\n                        </button>\n                    </form>\n                </div>\n                {% endif %}\n                \n                <!-- Theme toggle -->\n                <button @click=\"darkMode = !darkMode; localStorage.setItem('darkMode', darkMode)\" \n                        class=\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n                    <span x-show=\"!darkMode\">{% ui_icon 'moon' size=\"4\" %}</span>\n                    <span x-show=\"darkMode\">{% ui_icon 'sun' size=\"4\" %}</span>\n                </button>\n                \n                <!-- User menu -->\n                {% if user.is_authenticated %}\n                <div class=\"relative\" x-data=\"{ open: false }\">\n                    <button @click=\"open = !open\" \n                            class=\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n                        <div class=\"h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground text-sm font-medium\">\n                            {{ user.username|slice:\":1\"|upper }}\n                        </div>\n                    </button>\n                    \n                    <div x-show=\"open\" @click.away=\"open = false\" \n                         x-transition:enter=\"transition ease-out duration-100\"\n                         x-transition:enter-start=\"transform opacity-0 scale-95\"\n                         x-transition:enter-end=\"transform opacity-100 scale-100\"\n                         x-transition:leave=\"transition ease-in duration-75\"\n                         x-transition:leave-start=\"transform opacity-100 scale-100\"\n                         x-transition:leave-end=\"transform opacity-0 scale-95\"\n                         class=\"absolute right-0 mt-2 w-56 rounded-md border bg-popover p-1 text-popover-foreground shadow-md outline-none z-50\">\n                        <div class=\"px-2 py-1.5 text-sm font-semibold\">{{ user.get_full_name|default:user.username }}</div>\n                        <div class=\"h-px bg-border my-1\"></div>\n                        <a href=\"{% url 'core:profile' %}\" class=\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground\">\n                            {% ui_icon 'user' size=\"4\" class=\"mr-2\" %}\n                            Profilo\n                        </a>\n                        <a href=\"{% url 'core:settings' %}\" class=\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground\">\n                            {% ui_icon 'settings' size=\"4\" class=\"mr-2\" %}\n                            Impostazioni\n                        </a>\n                        <div class=\"h-px bg-border my-1\"></div>\n                        <a href=\"{% url 'core:logout' %}\" class=\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground\">\n                            {% ui_icon 'logout' size=\"4\" class=\"mr-2\" %}\n                            Logout\n                        </a>\n                    </div>\n                </div>\n                {% else %}\n                {% ui_button \"Accedi\" variant=\"default\" size=\"sm\" href=\"/login/\" %}\n                {% endif %}\n            </div>\n        </div>\n    </header>\n    {% endblock %}\n    \n    <!-- Mobile sidebar overlay -->\n    <div x-show=\"sidebarOpen\" @click=\"sidebarOpen = false\" \n         class=\"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm md:hidden\"\n         x-transition:enter=\"transition-all ease-in-out duration-300\"\n         x-transition:enter-start=\"opacity-0\"\n         x-transition:enter-end=\"opacity-100\"\n         x-transition:leave=\"transition-all ease-in-out duration-300\"\n         x-transition:leave-start=\"opacity-100\"\n         x-transition:leave-end=\"opacity-0\"></div>\n    \n    <!-- Main content -->\n    <div class=\"flex-1\">\n        {% block messages %}\n        {% if messages %}\n        <div class=\"container mx-auto px-4 py-2\">\n            {% for message in messages %}\n            {% ui_alert message.message variant=message.tags title=\"Notifica\" %}\n            {% endfor %}\n        </div>\n        {% endif %}\n        {% endblock %}\n        \n        {% block main %}\n        <main class=\"container mx-auto px-4 py-6\">\n            {% block content %}{% endblock %}\n        </main>\n        {% endblock %}\n    </div>\n    \n    <!-- Footer -->\n    {% block footer %}\n    <footer class=\"border-t bg-background\">\n        <div class=\"container flex flex-col items-center justify-between gap-4 py-10 md:h-24 md:flex-row md:py-0\">\n            <div class=\"flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0\">\n                <p class=\"text-center text-sm leading-loose text-muted-foreground md:text-left\">\n                    © 2025 ExProject. Tutti i diritti riservati.\n                </p>\n            </div>\n        </div>\n    </footer>\n    {% endblock %}\n    \n    {% block extra_js %}{% endblock %}\n</body>\n</html>\n"}