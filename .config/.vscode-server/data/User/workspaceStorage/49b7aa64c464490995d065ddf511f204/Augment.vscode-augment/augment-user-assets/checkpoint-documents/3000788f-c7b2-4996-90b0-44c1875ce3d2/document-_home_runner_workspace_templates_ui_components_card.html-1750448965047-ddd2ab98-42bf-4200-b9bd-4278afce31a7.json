{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/card.html"}, "modifiedCode": "{% load ui_components %}\n{# Componente Card ispirato a shadcn/ui #}\n<div class=\"rounded-lg border bg-card text-card-foreground shadow-sm {% if attrs.class %}{{ attrs.class }}{% endif %}\"\n     {% for key, value in attrs.items %}{% if key != 'class' %}{{ key }}=\"{{ value }}\"{% endif %}{% endfor %}>\n    \n    {% if title or description %}\n    <div class=\"flex flex-col space-y-1.5 p-6\">\n        {% if title %}\n        <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">{{ title }}</h3>\n        {% endif %}\n        \n        {% if description %}\n        <p class=\"text-sm text-muted-foreground\">{{ description }}</p>\n        {% endif %}\n    </div>\n    {% endif %}\n    \n    <div class=\"p-6 pt-0\">\n        {% block card_content %}\n        <!-- Contenuto della card -->\n        {% endblock %}\n    </div>\n    \n    {% block card_footer %}\n    <!-- Footer opzionale -->\n    {% endblock %}\n</div>\n"}