{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/card.html"}, "originalCode": "{# Componente Card ispirato a shadcn/ui #}\n<div class=\"rounded-lg border bg-card text-card-foreground shadow-sm {% if attrs.class %}{{ attrs.class }}{% endif %}\"\n     {% for key, value in attrs.items %}{% if key != 'class' %}{{ key }}=\"{{ value }}\"{% endif %}{% endfor %}>\n\n    {% if title or description %}\n    <div class=\"flex flex-col space-y-1.5 p-6\">\n        {% if title %}\n        <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">{{ title }}</h3>\n        {% endif %}\n\n        {% if description %}\n        <p class=\"text-sm text-muted-foreground\">{{ description }}</p>\n        {% endif %}\n    </div>\n    {% endif %}\n\n    {% if attrs.content %}\n    <div class=\"p-6 {% if title or description %}pt-0{% endif %}\">\n        {{ attrs.content|safe }}\n    </div>\n    {% endif %}\n\n    {% if attrs.footer %}\n    <div class=\"flex items-center p-6 pt-0\">\n        {{ attrs.footer|safe }}\n    </div>\n    {% endif %}\n</div>\n", "modifiedCode": "{# Componente Card ispirato a shadcn/ui #}\n<div class=\"rounded-lg border bg-card text-card-foreground shadow-sm {% if attrs.class %}{{ attrs.class }}{% endif %}\"\n     {% for key, value in attrs.items %}{% if key != 'class' %}{{ key }}=\"{{ value }}\"{% endif %}{% endfor %}>\n\n    {% if title or description %}\n    <div class=\"flex flex-col space-y-1.5 p-6\">\n        {% if title %}\n        <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">{{ title }}</h3>\n        {% endif %}\n\n        {% if description %}\n        <p class=\"text-sm text-muted-foreground\">{{ description }}</p>\n        {% endif %}\n    </div>\n    {% endif %}\n\n    {% if attrs.content %}\n    <div class=\"p-6 {% if title or description %}pt-0{% endif %}\">\n        {{ attrs.content|safe }}\n    </div>\n    {% endif %}\n\n    {% if attrs.footer %}\n    <div class=\"flex items-center p-6 pt-0\">\n        {{ attrs.footer|safe }}\n    </div>\n    {% endif %}\n</div>\n"}