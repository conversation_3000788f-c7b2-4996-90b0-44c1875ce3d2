{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/activities.html"}, "originalCode": "{% extends 'base.html' %}\n\n{% block title %}Attività Recenti - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"bg-white rounded-lg shadow p-6\">\n    <h2 class=\"text-xl font-semibold mb-4\">Attività Recenti</h2>\n    <div class=\"space-y-4\">\n        {% if recent_activities %}\n            {% for activity in recent_activities %}\n                <div class=\"mb-4 border-b pb-4\">\n                    <p class=\"font-medium\">{{ activity.description }}</p>\n                    <p class=\"text-xs text-gray-500\">{{ activity.created_at|date:\"d/m/Y H:i\" }}</p>\n                </div>\n            {% endfor %}\n        {% else %}\n            <div class=\"text-center py-8\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-10 w-10 text-gray-400 mx-auto mb-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                <p class=\"text-gray-500\">Nessuna attività recente</p>\n            </div>\n        {% endif %}\n    </div>\n</div>\n{% endblock %}", "modifiedCode": "{% extends 'base.html' %}\n\n{% block title %}Attività Recenti - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"bg-white rounded-lg shadow p-6\">\n    <h2 class=\"text-xl font-semibold mb-4\">Attività Recenti</h2>\n    <div class=\"space-y-4\">\n        {% if recent_activities %}\n            {% for activity in recent_activities %}\n                <div class=\"mb-4 border-b pb-4\">\n                    <p class=\"font-medium\">{{ activity.description }}</p>\n                    <p class=\"text-xs text-gray-500\">{{ activity.created_at|date:\"d/m/Y H:i\" }}</p>\n                </div>\n            {% endfor %}\n        {% else %}\n            <div class=\"text-center py-8\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-10 w-10 text-gray-400 mx-auto mb-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                <p class=\"text-gray-500\">Nessuna attività recente</p>\n            </div>\n        {% endif %}\n    </div>\n</div>\n{% endblock %}"}