{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/design_system_docs.html"}, "originalCode": "{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}Design System - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <div class=\"max-w-4xl mx-auto\">\n        <!-- Header -->\n        <div class=\"mb-8\">\n            <h1 class=\"text-4xl font-bold tracking-tight mb-2\">Design System</h1>\n            <p class=\"text-xl text-muted-foreground\">\n                Componenti riutilizzabili e design tokens per ExProject\n            </p>\n        </div>\n        \n        <!-- Navigation -->\n        <div class=\"mb-8\">\n            <nav class=\"flex space-x-4 border-b\">\n                <a href=\"#colors\" class=\"px-3 py-2 text-sm font-medium border-b-2 border-primary text-primary\">Colori</a>\n                <a href=\"#typography\" class=\"px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground\">Tipografia</a>\n                <a href=\"#components\" class=\"px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground\">Componenti</a>\n                <a href=\"#layouts\" class=\"px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground\">Layout</a>\n            </nav>\n        </div>\n        \n        <!-- Sezione Colori -->\n        <section id=\"colors\" class=\"mb-12\">\n            <h2 class=\"text-2xl font-bold mb-4\">Palette Colori</h2>\n            \n            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6\">\n                <!-- Colori primari -->\n                <div class=\"space-y-2\">\n                    <h3 class=\"font-semibold\">Primari</h3>\n                    <div class=\"space-y-1\">\n                        <div class=\"flex items-center space-x-2\">\n                            <div class=\"w-8 h-8 rounded bg-primary border\"></div>\n                            <span class=\"text-sm\">Primary</span>\n                        </div>\n                        <div class=\"flex items-center space-x-2\">\n                            <div class=\"w-8 h-8 rounded bg-primary-foreground border\"></div>\n                            <span class=\"text-sm\">Primary Foreground</span>\n                        </div>\n                    </div>\n                </div>\n                \n                <!-- Colori secondari -->\n                <div class=\"space-y-2\">\n                    <h3 class=\"font-semibold\">Secondari</h3>\n                    <div class=\"space-y-1\">\n                        <div class=\"flex items-center space-x-2\">\n                            <div class=\"w-8 h-8 rounded bg-secondary border\"></div>\n                            <span class=\"text-sm\">Secondary</span>\n                        </div>\n                        <div class=\"flex items-center space-x-2\">\n                            <div class=\"w-8 h-8 rounded bg-secondary-foreground border\"></div>\n                            <span class=\"text-sm\">Secondary Foreground</span>\n                        </div>\n                    </div>\n                </div>\n                \n                <!-- Colori di stato -->\n                <div class=\"space-y-2\">\n                    <h3 class=\"font-semibold\">Stati</h3>\n                    <div class=\"space-y-1\">\n                        <div class=\"flex items-center space-x-2\">\n                            <div class=\"w-8 h-8 rounded bg-destructive border\"></div>\n                            <span class=\"text-sm\">Destructive</span>\n                        </div>\n                        <div class=\"flex items-center space-x-2\">\n                            <div class=\"w-8 h-8 rounded bg-success border\"></div>\n                            <span class=\"text-sm\">Success</span>\n                        </div>\n                        <div class=\"flex items-center space-x-2\">\n                            <div class=\"w-8 h-8 rounded bg-warning border\"></div>\n                            <span class=\"text-sm\">Warning</span>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </section>\n        \n        <!-- Sezione Tipografia -->\n        <section id=\"typography\" class=\"mb-12\">\n            <h2 class=\"text-2xl font-bold mb-4\">Tipografia</h2>\n            \n            <div class=\"space-y-4\">\n                <div>\n                    <h1 class=\"text-4xl font-bold\">Heading 1</h1>\n                    <code class=\"text-sm text-muted-foreground\">text-4xl font-bold</code>\n                </div>\n                <div>\n                    <h2 class=\"text-3xl font-bold\">Heading 2</h2>\n                    <code class=\"text-sm text-muted-foreground\">text-3xl font-bold</code>\n                </div>\n                <div>\n                    <h3 class=\"text-2xl font-bold\">Heading 3</h3>\n                    <code class=\"text-sm text-muted-foreground\">text-2xl font-bold</code>\n                </div>\n                <div>\n                    <p class=\"text-base\">Paragrafo normale con testo di esempio per mostrare la tipografia di base.</p>\n                    <code class=\"text-sm text-muted-foreground\">text-base</code>\n                </div>\n                <div>\n                    <p class=\"text-sm text-muted-foreground\">Testo secondario più piccolo e meno prominente.</p>\n                    <code class=\"text-sm text-muted-foreground\">text-sm text-muted-foreground</code>\n                </div>\n            </div>\n        </section>\n        \n        <!-- Sezione Componenti -->\n        <section id=\"components\" class=\"mb-12\">\n            <h2 class=\"text-2xl font-bold mb-4\">Componenti</h2>\n            \n            <!-- Buttons -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-3\">Buttons</h3>\n                <div class=\"flex flex-wrap gap-2 mb-4\">\n                    {% for example in component_examples.buttons %}\n                    {% ui_button example.text variant=example.variant %}\n                    {% endfor %}\n                </div>\n                <details class=\"mb-4\">\n                    <summary class=\"cursor-pointer text-sm font-medium\">Mostra codice</summary>\n                    <pre class=\"mt-2 p-4 bg-muted rounded text-sm overflow-x-auto\"><code>{% templatetag openblock %} ui_button \"Primary Button\" variant=\"default\" {% templatetag closeblock %}\n{% templatetag openblock %} ui_button \"Secondary Button\" variant=\"secondary\" {% templatetag closeblock %}\n{% templatetag openblock %} ui_button \"Outline Button\" variant=\"outline\" {% templatetag closeblock %}</code></pre>\n                </details>\n            </div>\n            \n            <!-- Badges -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-3\">Badges</h3>\n                <div class=\"flex flex-wrap gap-2 mb-4\">\n                    {% for example in component_examples.badges %}\n                    {% ui_badge example.text variant=example.variant %}\n                    {% endfor %}\n                </div>\n                <details class=\"mb-4\">\n                    <summary class=\"cursor-pointer text-sm font-medium\">Mostra codice</summary>\n                    <pre class=\"mt-2 p-4 bg-muted rounded text-sm overflow-x-auto\"><code>{% templatetag openblock %} ui_badge \"Default\" variant=\"default\" {% templatetag closeblock %}\n{% templatetag openblock %} ui_badge \"Secondary\" variant=\"secondary\" {% templatetag closeblock %}</code></pre>\n                </details>\n            </div>\n            \n            <!-- Inputs -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-3\">Inputs</h3>\n                <div class=\"space-y-4 max-w-md mb-4\">\n                    {% ui_input \"email\" label=\"Email\" placeholder=\"Inserisci la tua email\" type=\"email\" %}\n                    {% ui_input \"password\" label=\"Password\" placeholder=\"Inserisci la password\" type=\"password\" %}\n                    {% ui_input \"search\" label=\"Ricerca\" placeholder=\"Cerca...\" %}\n                </div>\n                <details class=\"mb-4\">\n                    <summary class=\"cursor-pointer text-sm font-medium\">Mostra codice</summary>\n                    <pre class=\"mt-2 p-4 bg-muted rounded text-sm overflow-x-auto\"><code>{% templatetag openblock %} ui_input \"email\" label=\"Email\" placeholder=\"Inserisci la tua email\" type=\"email\" {% templatetag closeblock %}</code></pre>\n                </details>\n            </div>\n            \n            <!-- Cards -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-3\">Cards</h3>\n                <div class=\"max-w-md mb-4\">\n                    {% ui_card title=\"Titolo Card\" description=\"Descrizione della card con testo di esempio.\" %}\n                        {% block card_content %}\n                        <p class=\"text-sm\">Contenuto della card con informazioni aggiuntive.</p>\n                        {% endblock %}\n                    {% endui_card %}\n                </div>\n                <details class=\"mb-4\">\n                    <summary class=\"cursor-pointer text-sm font-medium\">Mostra codice</summary>\n                    <pre class=\"mt-2 p-4 bg-muted rounded text-sm overflow-x-auto\"><code>{% templatetag openblock %} ui_card title=\"Titolo Card\" description=\"Descrizione della card\" {% templatetag closeblock %}\n    {% templatetag openblock %} block card_content {% templatetag closeblock %}\n    <p>Contenuto della card</p>\n    {% templatetag openblock %} endblock {% templatetag closeblock %}\n{% templatetag openblock %} endui_card {% templatetag closeblock %}</code></pre>\n                </details>\n            </div>\n            \n            <!-- Alerts -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-3\">Alerts</h3>\n                <div class=\"space-y-2 mb-4\">\n                    {% for example in component_examples.alerts %}\n                    {% ui_alert example.message variant=example.variant %}\n                    {% endfor %}\n                </div>\n                <details class=\"mb-4\">\n                    <summary class=\"cursor-pointer text-sm font-medium\">Mostra codice</summary>\n                    <pre class=\"mt-2 p-4 bg-muted rounded text-sm overflow-x-auto\"><code>{% templatetag openblock %} ui_alert \"Messaggio di default\" variant=\"default\" {% templatetag closeblock %}\n{% templatetag openblock %} ui_alert \"Messaggio di errore\" variant=\"destructive\" {% templatetag closeblock %}</code></pre>\n                </details>\n            </div>\n        </section>\n        \n        <!-- Sezione Layout -->\n        <section id=\"layouts\" class=\"mb-12\">\n            <h2 class=\"text-2xl font-bold mb-4\">Layout</h2>\n            \n            <div class=\"space-y-6\">\n                <div>\n                    <h3 class=\"text-lg font-semibold mb-2\">Dashboard Layout</h3>\n                    <p class=\"text-muted-foreground mb-4\">Layout principale per dashboard con sidebar e header fisso.</p>\n                    <div class=\"border rounded-lg p-4 bg-muted/50\">\n                        <div class=\"text-sm font-mono\">templates/ui/layouts/dashboard_modern.html</div>\n                    </div>\n                </div>\n                \n                <div>\n                    <h3 class=\"text-lg font-semibold mb-2\">Form Layout</h3>\n                    <p class=\"text-muted-foreground mb-4\">Layout ottimizzato per form con validazione e feedback.</p>\n                    <div class=\"border rounded-lg p-4 bg-muted/50\">\n                        <div class=\"text-sm font-mono\">templates/ui/layouts/form.html</div>\n                    </div>\n                </div>\n            </div>\n        </section>\n        \n        <!-- Footer -->\n        <div class=\"border-t pt-8 mt-12\">\n            <p class=\"text-sm text-muted-foreground\">\n                Design System basato su shadcn/ui e Tailwind CSS. \n                <a href=\"{% url 'ui:component_library' %}\" class=\"text-primary hover:underline\">\n                    Visualizza libreria componenti completa\n                </a>\n            </p>\n        </div>\n    </div>\n</div>\n\n<script>\n// Smooth scroll per navigation\ndocument.querySelectorAll('nav a[href^=\"#\"]').forEach(anchor => {\n    anchor.addEventListener('click', function (e) {\n        e.preventDefault();\n        const target = document.querySelector(this.getAttribute('href'));\n        if (target) {\n            target.scrollIntoView({\n                behavior: 'smooth',\n                block: 'start'\n            });\n        }\n    });\n});\n\n// Highlight active section in navigation\nwindow.addEventListener('scroll', () => {\n    const sections = document.querySelectorAll('section[id]');\n    const navLinks = document.querySelectorAll('nav a[href^=\"#\"]');\n    \n    let current = '';\n    sections.forEach(section => {\n        const sectionTop = section.offsetTop;\n        const sectionHeight = section.clientHeight;\n        if (scrollY >= sectionTop - 200) {\n            current = section.getAttribute('id');\n        }\n    });\n    \n    navLinks.forEach(link => {\n        link.classList.remove('border-primary', 'text-primary');\n        link.classList.add('text-muted-foreground');\n        if (link.getAttribute('href') === `#${current}`) {\n            link.classList.add('border-primary', 'text-primary');\n            link.classList.remove('text-muted-foreground');\n        }\n    });\n});\n</script>\n{% endblock %}\n", "modifiedCode": "{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}Design System - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <div class=\"max-w-4xl mx-auto\">\n        <!-- Header -->\n        <div class=\"mb-8\">\n            <h1 class=\"text-4xl font-bold tracking-tight mb-2\">Design System</h1>\n            <p class=\"text-xl text-muted-foreground\">\n                Componenti riutilizzabili e design tokens per ExProject\n            </p>\n        </div>\n        \n        <!-- Navigation -->\n        <div class=\"mb-8\">\n            <nav class=\"flex space-x-4 border-b\">\n                <a href=\"#colors\" class=\"px-3 py-2 text-sm font-medium border-b-2 border-primary text-primary\">Colori</a>\n                <a href=\"#typography\" class=\"px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground\">Tipografia</a>\n                <a href=\"#components\" class=\"px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground\">Componenti</a>\n                <a href=\"#layouts\" class=\"px-3 py-2 text-sm font-medium text-muted-foreground hover:text-foreground\">Layout</a>\n            </nav>\n        </div>\n        \n        <!-- Sezione Colori -->\n        <section id=\"colors\" class=\"mb-12\">\n            <h2 class=\"text-2xl font-bold mb-4\">Palette Colori</h2>\n            \n            <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6\">\n                <!-- Colori primari -->\n                <div class=\"space-y-2\">\n                    <h3 class=\"font-semibold\">Primari</h3>\n                    <div class=\"space-y-1\">\n                        <div class=\"flex items-center space-x-2\">\n                            <div class=\"w-8 h-8 rounded bg-primary border\"></div>\n                            <span class=\"text-sm\">Primary</span>\n                        </div>\n                        <div class=\"flex items-center space-x-2\">\n                            <div class=\"w-8 h-8 rounded bg-primary-foreground border\"></div>\n                            <span class=\"text-sm\">Primary Foreground</span>\n                        </div>\n                    </div>\n                </div>\n                \n                <!-- Colori secondari -->\n                <div class=\"space-y-2\">\n                    <h3 class=\"font-semibold\">Secondari</h3>\n                    <div class=\"space-y-1\">\n                        <div class=\"flex items-center space-x-2\">\n                            <div class=\"w-8 h-8 rounded bg-secondary border\"></div>\n                            <span class=\"text-sm\">Secondary</span>\n                        </div>\n                        <div class=\"flex items-center space-x-2\">\n                            <div class=\"w-8 h-8 rounded bg-secondary-foreground border\"></div>\n                            <span class=\"text-sm\">Secondary Foreground</span>\n                        </div>\n                    </div>\n                </div>\n                \n                <!-- Colori di stato -->\n                <div class=\"space-y-2\">\n                    <h3 class=\"font-semibold\">Stati</h3>\n                    <div class=\"space-y-1\">\n                        <div class=\"flex items-center space-x-2\">\n                            <div class=\"w-8 h-8 rounded bg-destructive border\"></div>\n                            <span class=\"text-sm\">Destructive</span>\n                        </div>\n                        <div class=\"flex items-center space-x-2\">\n                            <div class=\"w-8 h-8 rounded bg-success border\"></div>\n                            <span class=\"text-sm\">Success</span>\n                        </div>\n                        <div class=\"flex items-center space-x-2\">\n                            <div class=\"w-8 h-8 rounded bg-warning border\"></div>\n                            <span class=\"text-sm\">Warning</span>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </section>\n        \n        <!-- Sezione Tipografia -->\n        <section id=\"typography\" class=\"mb-12\">\n            <h2 class=\"text-2xl font-bold mb-4\">Tipografia</h2>\n            \n            <div class=\"space-y-4\">\n                <div>\n                    <h1 class=\"text-4xl font-bold\">Heading 1</h1>\n                    <code class=\"text-sm text-muted-foreground\">text-4xl font-bold</code>\n                </div>\n                <div>\n                    <h2 class=\"text-3xl font-bold\">Heading 2</h2>\n                    <code class=\"text-sm text-muted-foreground\">text-3xl font-bold</code>\n                </div>\n                <div>\n                    <h3 class=\"text-2xl font-bold\">Heading 3</h3>\n                    <code class=\"text-sm text-muted-foreground\">text-2xl font-bold</code>\n                </div>\n                <div>\n                    <p class=\"text-base\">Paragrafo normale con testo di esempio per mostrare la tipografia di base.</p>\n                    <code class=\"text-sm text-muted-foreground\">text-base</code>\n                </div>\n                <div>\n                    <p class=\"text-sm text-muted-foreground\">Testo secondario più piccolo e meno prominente.</p>\n                    <code class=\"text-sm text-muted-foreground\">text-sm text-muted-foreground</code>\n                </div>\n            </div>\n        </section>\n        \n        <!-- Sezione Componenti -->\n        <section id=\"components\" class=\"mb-12\">\n            <h2 class=\"text-2xl font-bold mb-4\">Componenti</h2>\n            \n            <!-- Buttons -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-3\">Buttons</h3>\n                <div class=\"flex flex-wrap gap-2 mb-4\">\n                    {% for example in component_examples.buttons %}\n                    {% ui_button example.text variant=example.variant %}\n                    {% endfor %}\n                </div>\n                <details class=\"mb-4\">\n                    <summary class=\"cursor-pointer text-sm font-medium\">Mostra codice</summary>\n                    <pre class=\"mt-2 p-4 bg-muted rounded text-sm overflow-x-auto\"><code>{% templatetag openblock %} ui_button \"Primary Button\" variant=\"default\" {% templatetag closeblock %}\n{% templatetag openblock %} ui_button \"Secondary Button\" variant=\"secondary\" {% templatetag closeblock %}\n{% templatetag openblock %} ui_button \"Outline Button\" variant=\"outline\" {% templatetag closeblock %}</code></pre>\n                </details>\n            </div>\n            \n            <!-- Badges -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-3\">Badges</h3>\n                <div class=\"flex flex-wrap gap-2 mb-4\">\n                    {% for example in component_examples.badges %}\n                    {% ui_badge example.text variant=example.variant %}\n                    {% endfor %}\n                </div>\n                <details class=\"mb-4\">\n                    <summary class=\"cursor-pointer text-sm font-medium\">Mostra codice</summary>\n                    <pre class=\"mt-2 p-4 bg-muted rounded text-sm overflow-x-auto\"><code>{% templatetag openblock %} ui_badge \"Default\" variant=\"default\" {% templatetag closeblock %}\n{% templatetag openblock %} ui_badge \"Secondary\" variant=\"secondary\" {% templatetag closeblock %}</code></pre>\n                </details>\n            </div>\n            \n            <!-- Inputs -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-3\">Inputs</h3>\n                <div class=\"space-y-4 max-w-md mb-4\">\n                    {% ui_input \"email\" label=\"Email\" placeholder=\"Inserisci la tua email\" type=\"email\" %}\n                    {% ui_input \"password\" label=\"Password\" placeholder=\"Inserisci la password\" type=\"password\" %}\n                    {% ui_input \"search\" label=\"Ricerca\" placeholder=\"Cerca...\" %}\n                </div>\n                <details class=\"mb-4\">\n                    <summary class=\"cursor-pointer text-sm font-medium\">Mostra codice</summary>\n                    <pre class=\"mt-2 p-4 bg-muted rounded text-sm overflow-x-auto\"><code>{% templatetag openblock %} ui_input \"email\" label=\"Email\" placeholder=\"Inserisci la tua email\" type=\"email\" {% templatetag closeblock %}</code></pre>\n                </details>\n            </div>\n            \n            <!-- Cards -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-3\">Cards</h3>\n                <div class=\"max-w-md mb-4\">\n                    <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n                        <div class=\"flex flex-col space-y-1.5 p-6\">\n                            <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Titolo Card</h3>\n                            <p class=\"text-sm text-muted-foreground\">Descrizione della card con testo di esempio.</p>\n                        </div>\n                        <div class=\"p-6 pt-0\">\n                            <p class=\"text-sm\">Contenuto della card con informazioni aggiuntive.</p>\n                        </div>\n                    </div>\n                </div>\n                <details class=\"mb-4\">\n                    <summary class=\"cursor-pointer text-sm font-medium\">Mostra codice</summary>\n                    <pre class=\"mt-2 p-4 bg-muted rounded text-sm overflow-x-auto\"><code>&lt;div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\"&gt;\n    &lt;div class=\"flex flex-col space-y-1.5 p-6\"&gt;\n        &lt;h3 class=\"text-2xl font-semibold leading-none tracking-tight\"&gt;Titolo Card&lt;/h3&gt;\n        &lt;p class=\"text-sm text-muted-foreground\"&gt;Descrizione della card&lt;/p&gt;\n    &lt;/div&gt;\n    &lt;div class=\"p-6 pt-0\"&gt;\n        &lt;p&gt;Contenuto della card&lt;/p&gt;\n    &lt;/div&gt;\n&lt;/div&gt;</code></pre>\n                </details>\n            </div>\n            \n            <!-- Alerts -->\n            <div class=\"mb-8\">\n                <h3 class=\"text-lg font-semibold mb-3\">Alerts</h3>\n                <div class=\"space-y-2 mb-4\">\n                    {% for example in component_examples.alerts %}\n                    {% ui_alert example.message variant=example.variant %}\n                    {% endfor %}\n                </div>\n                <details class=\"mb-4\">\n                    <summary class=\"cursor-pointer text-sm font-medium\">Mostra codice</summary>\n                    <pre class=\"mt-2 p-4 bg-muted rounded text-sm overflow-x-auto\"><code>{% templatetag openblock %} ui_alert \"Messaggio di default\" variant=\"default\" {% templatetag closeblock %}\n{% templatetag openblock %} ui_alert \"Messaggio di errore\" variant=\"destructive\" {% templatetag closeblock %}</code></pre>\n                </details>\n            </div>\n        </section>\n        \n        <!-- Sezione Layout -->\n        <section id=\"layouts\" class=\"mb-12\">\n            <h2 class=\"text-2xl font-bold mb-4\">Layout</h2>\n            \n            <div class=\"space-y-6\">\n                <div>\n                    <h3 class=\"text-lg font-semibold mb-2\">Dashboard Layout</h3>\n                    <p class=\"text-muted-foreground mb-4\">Layout principale per dashboard con sidebar e header fisso.</p>\n                    <div class=\"border rounded-lg p-4 bg-muted/50\">\n                        <div class=\"text-sm font-mono\">templates/ui/layouts/dashboard_modern.html</div>\n                    </div>\n                </div>\n                \n                <div>\n                    <h3 class=\"text-lg font-semibold mb-2\">Form Layout</h3>\n                    <p class=\"text-muted-foreground mb-4\">Layout ottimizzato per form con validazione e feedback.</p>\n                    <div class=\"border rounded-lg p-4 bg-muted/50\">\n                        <div class=\"text-sm font-mono\">templates/ui/layouts/form.html</div>\n                    </div>\n                </div>\n            </div>\n        </section>\n        \n        <!-- Footer -->\n        <div class=\"border-t pt-8 mt-12\">\n            <p class=\"text-sm text-muted-foreground\">\n                Design System basato su shadcn/ui e Tailwind CSS. \n                <a href=\"{% url 'ui:component_library' %}\" class=\"text-primary hover:underline\">\n                    Visualizza libreria componenti completa\n                </a>\n            </p>\n        </div>\n    </div>\n</div>\n\n<script>\n// Smooth scroll per navigation\ndocument.querySelectorAll('nav a[href^=\"#\"]').forEach(anchor => {\n    anchor.addEventListener('click', function (e) {\n        e.preventDefault();\n        const target = document.querySelector(this.getAttribute('href'));\n        if (target) {\n            target.scrollIntoView({\n                behavior: 'smooth',\n                block: 'start'\n            });\n        }\n    });\n});\n\n// Highlight active section in navigation\nwindow.addEventListener('scroll', () => {\n    const sections = document.querySelectorAll('section[id]');\n    const navLinks = document.querySelectorAll('nav a[href^=\"#\"]');\n    \n    let current = '';\n    sections.forEach(section => {\n        const sectionTop = section.offsetTop;\n        const sectionHeight = section.clientHeight;\n        if (scrollY >= sectionTop - 200) {\n            current = section.getAttribute('id');\n        }\n    });\n    \n    navLinks.forEach(link => {\n        link.classList.remove('border-primary', 'text-primary');\n        link.classList.add('text-muted-foreground');\n        if (link.getAttribute('href') === `#${current}`) {\n            link.classList.add('border-primary', 'text-primary');\n            link.classList.remove('text-muted-foreground');\n        }\n    });\n});\n</script>\n{% endblock %}\n"}