{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/public/home.html"}, "originalCode": "{% extends 'base.html' %}\n{% load static %}\n\n{% block title %}ExProject - Sistema Gestione Espropri{% endblock %}\n\n{% block extra_head %}\n<style>\n    .hero-section {\n        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);\n        position: relative;\n        overflow: hidden;\n    }\n    \n    .hero-pattern {\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background-image: url(\"data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E\");\n    }\n    \n    .feature-card {\n        transition: all 0.3s ease;\n        border-top: 4px solid transparent;\n    }\n    \n    .feature-card:hover {\n        transform: translateY(-5px);\n        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n    }\n    \n    .feature-card.blue:hover {\n        border-top-color: #2563eb;\n    }\n    \n    .feature-card.green:hover {\n        border-top-color: #10b981;\n    }\n    \n    .feature-card.purple:hover {\n        border-top-color: #8b5cf6;\n    }\n    \n    .feature-icon {\n        transition: all 0.3s ease;\n    }\n    \n    .feature-card:hover .feature-icon {\n        transform: scale(1.1);\n    }\n    \n    .cta-button {\n        transition: all 0.2s ease;\n        position: relative;\n        overflow: hidden;\n    }\n    \n    .cta-button:after {\n        content: \"\";\n        position: absolute;\n        top: -50%;\n        left: -60%;\n        width: 200%;\n        height: 200%;\n        background: rgba(255, 255, 255, 0.2);\n        transform: rotate(30deg);\n        transition: all 0.4s ease;\n        opacity: 0;\n    }\n    \n    .cta-button:hover:after {\n        opacity: 1;\n        left: 100%;\n    }\n    \n    .stats-card {\n        transition: all 0.3s ease;\n    }\n    \n    .stats-card:hover {\n        transform: translateY(-5px);\n    }\n    \n    @keyframes float {\n        0% { transform: translateY(0px); }\n        50% { transform: translateY(-15px); }\n        100% { transform: translateY(0px); }\n    }\n    \n    .floating-image {\n        animation: float 6s ease-in-out infinite;\n    }\n</style>\n{% endblock %}\n\n{% block content %}\n<!-- Hero Section -->\n<div class=\"hero-section py-16 md:py-24\">\n    <div class=\"hero-pattern\"></div>\n    <div class=\"container mx-auto px-4\">\n        <div class=\"flex flex-col md:flex-row items-center\">\n            <div class=\"md:w-1/2 text-white z-10 mb-10 md:mb-0\">\n                <h1 class=\"text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight\">\n                    Digitalizza il processo espropriativo\n                </h1>\n                <p class=\"text-xl md:text-2xl mb-8 text-blue-100\">\n                    Semplifica, automatizza e gestisci con efficienza l'intero ciclo di vita degli espropri secondo il DPR 327/2001\n                </p>\n                <div class=\"flex flex-col sm:flex-row gap-4\">\n                    <a href=\"{% url 'core:login' %}\" class=\"cta-button bg-white text-blue-700 hover:bg-blue-50 font-bold py-3 px-8 rounded-lg shadow-lg inline-flex items-center justify-center\">\n                        <span>Accedi</span>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </a>\n                    <a href=\"{% url 'public:procedures' %}\" class=\"cta-button bg-blue-700 hover:bg-blue-800 text-white font-bold py-3 px-8 rounded-lg shadow-lg border border-blue-500 inline-flex items-center justify-center\">\n                        <span>Scopri di più</span>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </a>\n                </div>\n            </div>\n            <div class=\"md:w-1/2 z-10 pl-10 hidden md:block\">\n                <div class=\"floating-image w-full h-auto rounded-lg shadow-2xl bg-white p-6\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1200 800\" class=\"w-full\">\n                        <!-- Background with grid pattern -->\n                        <rect width=\"1200\" height=\"800\" fill=\"#f8fafc\" />\n                        <path d=\"M0 0h1200v800H0z\" fill=\"url(#grid)\" />\n                        \n                        <!-- Header bar -->\n                        <rect x=\"0\" y=\"0\" width=\"1200\" height=\"70\" fill=\"#2563eb\" />\n                        <rect x=\"20\" y=\"20\" width=\"180\" height=\"30\" rx=\"5\" fill=\"#3b82f6\" />\n                        <rect x=\"900\" y=\"20\" width=\"120\" height=\"30\" rx=\"15\" fill=\"#3b82f6\" />\n                        <circle cx=\"1150\" cy=\"35\" r=\"20\" fill=\"#3b82f6\" />\n                        \n                        <!-- Left sidebar -->\n                        <rect x=\"0\" y=\"70\" width=\"220\" height=\"730\" fill=\"#f1f5f9\" />\n                        <rect x=\"20\" y=\"100\" width=\"180\" height=\"40\" rx=\"5\" fill=\"#dbeafe\" />\n                        <rect x=\"20\" y=\"160\" width=\"180\" height=\"30\" rx=\"5\" fill=\"white\" />\n                        <rect x=\"20\" y=\"210\" width=\"180\" height=\"30\" rx=\"5\" fill=\"white\" />\n                        <rect x=\"20\" y=\"260\" width=\"180\" height=\"30\" rx=\"5\" fill=\"white\" />\n                        <rect x=\"20\" y=\"310\" width=\"180\" height=\"30\" rx=\"5\" fill=\"white\" />\n                        \n                        <!-- Main content area -->\n                        <rect x=\"240\" y=\"90\" width=\"940\" height=\"160\" rx=\"8\" fill=\"white\" />\n                        <rect x=\"260\" y=\"110\" width=\"200\" height=\"30\" rx=\"4\" fill=\"#e2e8f0\" />\n                        <rect x=\"260\" y=\"160\" width=\"600\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"260\" y=\"185\" width=\"400\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"260\" y=\"210\" width=\"200\" height=\"20\" rx=\"4\" fill=\"#2563eb\" />\n                        \n                        <!-- Stats cards -->\n                        <rect x=\"240\" y=\"270\" width=\"220\" height=\"120\" rx=\"8\" fill=\"white\" />\n                        <rect x=\"260\" y=\"290\" width=\"100\" height=\"30\" rx=\"4\" fill=\"#e2e8f0\" />\n                        <rect x=\"260\" y=\"330\" width=\"180\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"260\" y=\"355\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#2563eb\" />\n                        \n                        <rect x=\"480\" y=\"270\" width=\"220\" height=\"120\" rx=\"8\" fill=\"white\" />\n                        <rect x=\"500\" y=\"290\" width=\"100\" height=\"30\" rx=\"4\" fill=\"#e2e8f0\" />\n                        <rect x=\"500\" y=\"330\" width=\"180\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"500\" y=\"355\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#10b981\" />\n                        \n                        <rect x=\"720\" y=\"270\" width=\"220\" height=\"120\" rx=\"8\" fill=\"white\" />\n                        <rect x=\"740\" y=\"290\" width=\"100\" height=\"30\" rx=\"4\" fill=\"#e2e8f0\" />\n                        <rect x=\"740\" y=\"330\" width=\"180\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"740\" y=\"355\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#ef4444\" />\n                        \n                        <rect x=\"960\" y=\"270\" width=\"220\" height=\"120\" rx=\"8\" fill=\"white\" />\n                        <rect x=\"980\" y=\"290\" width=\"100\" height=\"30\" rx=\"4\" fill=\"#e2e8f0\" />\n                        <rect x=\"980\" y=\"330\" width=\"180\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"980\" y=\"355\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#f59e0b\" />\n                        \n                        <!-- Table -->\n                        <rect x=\"240\" y=\"410\" width=\"940\" height=\"350\" rx=\"8\" fill=\"white\" />\n                        <rect x=\"260\" y=\"430\" width=\"250\" height=\"30\" rx=\"4\" fill=\"#e2e8f0\" />\n                        <line x1=\"240\" y1=\"480\" x2=\"1180\" y2=\"480\" stroke=\"#e2e8f0\" stroke-width=\"2\" />\n                        \n                        <rect x=\"260\" y=\"500\" width=\"150\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"500\" y=\"500\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"700\" y=\"500\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"900\" y=\"500\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"1050\" y=\"500\" width=\"70\" height=\"20\" rx=\"10\" fill=\"#bfdbfe\" />\n                        \n                        <line x1=\"240\" y1=\"530\" x2=\"1180\" y2=\"530\" stroke=\"#f8fafc\" stroke-width=\"2\" />\n                        \n                        <rect x=\"260\" y=\"550\" width=\"150\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"500\" y=\"550\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"700\" y=\"550\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"900\" y=\"550\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"1050\" y=\"550\" width=\"70\" height=\"20\" rx=\"10\" fill=\"#dcfce7\" />\n                        \n                        <line x1=\"240\" y1=\"580\" x2=\"1180\" y2=\"580\" stroke=\"#f8fafc\" stroke-width=\"2\" />\n                        \n                        <rect x=\"260\" y=\"600\" width=\"150\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"500\" y=\"600\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"700\" y=\"600\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"900\" y=\"600\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"1050\" y=\"600\" width=\"70\" height=\"20\" rx=\"10\" fill=\"#fee2e2\" />\n                        \n                        <line x1=\"240\" y1=\"630\" x2=\"1180\" y2=\"630\" stroke=\"#f8fafc\" stroke-width=\"2\" />\n                        \n                        <rect x=\"260\" y=\"650\" width=\"150\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"500\" y=\"650\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"700\" y=\"650\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"900\" y=\"650\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"1050\" y=\"650\" width=\"70\" height=\"20\" rx=\"10\" fill=\"#bfdbfe\" />\n                        \n                        <line x1=\"240\" y1=\"680\" x2=\"1180\" y2=\"680\" stroke=\"#f8fafc\" stroke-width=\"2\" />\n                        \n                        <rect x=\"260\" y=\"700\" width=\"150\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"500\" y=\"700\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"700\" y=\"700\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"900\" y=\"700\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"1050\" y=\"700\" width=\"70\" height=\"20\" rx=\"10\" fill=\"#dcfce7\" />\n                        \n                        <!-- Grid pattern definition -->\n                        <defs>\n                            <pattern id=\"grid\" width=\"40\" height=\"40\" patternUnits=\"userSpaceOnUse\">\n                                <path d=\"M0 0h40v40H0z\" fill=\"none\" />\n                                <path d=\"M0 0h40v1H0z\" fill=\"#f1f5f9\" />\n                                <path d=\"M0 0h1v40H0z\" fill=\"#f1f5f9\" />\n                            </pattern>\n                        </defs>\n                    </svg>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>\n\n<!-- Statistiche -->\n<div class=\"bg-gray-50 py-10\">\n    <div class=\"container mx-auto px-4\">\n        <div class=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\">\n            <div class=\"stats-card bg-white rounded-lg shadow p-6\">\n                <p class=\"text-3xl md:text-4xl font-bold text-blue-600 mb-2\">12+</p>\n                <p class=\"text-gray-600\">Anni di esperienza</p>\n            </div>\n            <div class=\"stats-card bg-white rounded-lg shadow p-6\">\n                <p class=\"text-3xl md:text-4xl font-bold text-blue-600 mb-2\">98%</p>\n                <p class=\"text-gray-600\">Clienti soddisfatti</p>\n            </div>\n            <div class=\"stats-card bg-white rounded-lg shadow p-6\">\n                <p class=\"text-3xl md:text-4xl font-bold text-blue-600 mb-2\">500+</p>\n                <p class=\"text-gray-600\">Progetti gestiti</p>\n            </div>\n            <div class=\"stats-card bg-white rounded-lg shadow p-6\">\n                <p class=\"text-3xl md:text-4xl font-bold text-blue-600 mb-2\">20K+</p>\n                <p class=\"text-gray-600\">Particelle trattate</p>\n            </div>\n        </div>\n    </div>\n</div>\n\n<!-- Caratteristiche principali -->\n<div class=\"py-16\">\n    <div class=\"container mx-auto px-4\">\n        <div class=\"text-center mb-16\">\n            <h2 class=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">Un sistema completo e integrato</h2>\n            <p class=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n                ExProject offre tutti gli strumenti necessari per gestire l'intero processo espropriativo, dalla pianificazione al pagamento delle indennità.\n            </p>\n        </div>\n        \n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div class=\"feature-card blue bg-white rounded-lg shadow p-8\">\n                <div class=\"feature-icon bg-blue-100 w-16 h-16 flex items-center justify-center rounded-full mb-6 mx-auto\">\n                    <svg class=\"w-8 h-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-center mb-4\">Gestione Completa</h3>\n                <p class=\"text-gray-600 text-center mb-6\">\n                    Gestisci tutte le fasi del procedimento espropriativo in un unico sistema integrato, dalla dichiarazione di pubblica utilità fino alla conclusione.\n                </p>\n                <div class=\"text-center\">\n                    <a href=\"{% url 'public:procedures' %}\" class=\"text-blue-600 hover:text-blue-800 font-medium inline-flex items-center\">\n                        <span>Scopri di più</span>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </a>\n                </div>\n            </div>\n            \n            <div class=\"feature-card green bg-white rounded-lg shadow p-8\">\n                <div class=\"feature-icon bg-green-100 w-16 h-16 flex items-center justify-center rounded-full mb-6 mx-auto\">\n                    <svg class=\"w-8 h-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\"></path>\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-center mb-4\">Workflow Automatizzato</h3>\n                <p class=\"text-gray-600 text-center mb-6\">\n                    Automazione delle fasi procedurali con controlli, notifiche e scadenze integrate secondo la normativa vigente.\n                </p>\n                <div class=\"text-center\">\n                    <a href=\"{% url 'public:about' %}\" class=\"text-green-600 hover:text-green-800 font-medium inline-flex items-center\">\n                        <span>Maggiori info</span>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </a>\n                </div>\n            </div>\n            \n            <div class=\"feature-card purple bg-white rounded-lg shadow p-8\">\n                <div class=\"feature-icon bg-purple-100 w-16 h-16 flex items-center justify-center rounded-full mb-6 mx-auto\">\n                    <svg class=\"w-8 h-8 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\"></path>\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-center mb-4\">Intelligenza Artificiale</h3>\n                <p class=\"text-gray-600 text-center mb-6\">\n                    Assistenza intelligente per analisi documentale, supporto decisionale e previsione di costi e tempistiche.\n                </p>\n                <div class=\"text-center\">\n                    <a href=\"{% url 'public:about' %}\" class=\"text-purple-600 hover:text-purple-800 font-medium inline-flex items-center\">\n                        <span>Esplora</span>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </a>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>\n\n<!-- CTA Section -->\n<div class=\"bg-blue-600 py-16\">\n    <div class=\"container mx-auto px-4 text-center\">\n        <h2 class=\"text-3xl md:text-4xl font-bold text-white mb-6\">Pronto a semplificare la gestione degli espropri?</h2>\n        <p class=\"text-xl text-blue-100 mb-8 max-w-3xl mx-auto\">\n            Unisciti alle centinaia di enti e professionisti che utilizzano ExProject per gestire in modo efficiente i procedimenti espropriativi.\n        </p>\n        <div class=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <a href=\"{% url 'core:login' %}\" class=\"cta-button bg-white text-blue-700 hover:bg-blue-50 font-bold py-4 px-8 rounded-lg shadow-lg inline-flex items-center justify-center\">\n                <span>Inizia ora</span>\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fill-rule=\"evenodd\" d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                </svg>\n            </a>\n            <a href=\"{% url 'public:contacts' %}\" class=\"cta-button bg-transparent text-white hover:bg-blue-700 font-bold py-4 px-8 rounded-lg shadow-lg border border-white inline-flex items-center justify-center\">\n                <span>Contattaci</span>\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path d=\"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\" />\n                    <path d=\"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\" />\n                </svg>\n            </a>\n        </div>\n    </div>\n</div>\n{% endblock %}", "modifiedCode": "{% extends 'base.html' %}\n{% load static %}\n\n{% block title %}ExProject - Sistema Gestione Espropri{% endblock %}\n\n{% block extra_head %}\n<style>\n    .hero-section {\n        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);\n        position: relative;\n        overflow: hidden;\n    }\n    \n    .hero-pattern {\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background-image: url(\"data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E\");\n    }\n    \n    .feature-card {\n        transition: all 0.3s ease;\n        border-top: 4px solid transparent;\n    }\n    \n    .feature-card:hover {\n        transform: translateY(-5px);\n        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n    }\n    \n    .feature-card.blue:hover {\n        border-top-color: #2563eb;\n    }\n    \n    .feature-card.green:hover {\n        border-top-color: #10b981;\n    }\n    \n    .feature-card.purple:hover {\n        border-top-color: #8b5cf6;\n    }\n    \n    .feature-icon {\n        transition: all 0.3s ease;\n    }\n    \n    .feature-card:hover .feature-icon {\n        transform: scale(1.1);\n    }\n    \n    .cta-button {\n        transition: all 0.2s ease;\n        position: relative;\n        overflow: hidden;\n    }\n    \n    .cta-button:after {\n        content: \"\";\n        position: absolute;\n        top: -50%;\n        left: -60%;\n        width: 200%;\n        height: 200%;\n        background: rgba(255, 255, 255, 0.2);\n        transform: rotate(30deg);\n        transition: all 0.4s ease;\n        opacity: 0;\n    }\n    \n    .cta-button:hover:after {\n        opacity: 1;\n        left: 100%;\n    }\n    \n    .stats-card {\n        transition: all 0.3s ease;\n    }\n    \n    .stats-card:hover {\n        transform: translateY(-5px);\n    }\n    \n    @keyframes float {\n        0% { transform: translateY(0px); }\n        50% { transform: translateY(-15px); }\n        100% { transform: translateY(0px); }\n    }\n    \n    .floating-image {\n        animation: float 6s ease-in-out infinite;\n    }\n</style>\n{% endblock %}\n\n{% block content %}\n<!-- Hero Section -->\n<div class=\"hero-section py-16 md:py-24\">\n    <div class=\"hero-pattern\"></div>\n    <div class=\"container mx-auto px-4\">\n        <div class=\"flex flex-col md:flex-row items-center\">\n            <div class=\"md:w-1/2 text-white z-10 mb-10 md:mb-0\">\n                <h1 class=\"text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight\">\n                    Digitalizza il processo espropriativo\n                </h1>\n                <p class=\"text-xl md:text-2xl mb-8 text-blue-100\">\n                    Semplifica, automatizza e gestisci con efficienza l'intero ciclo di vita degli espropri secondo il DPR 327/2001\n                </p>\n                <div class=\"flex flex-col sm:flex-row gap-4\">\n                    <a href=\"{% url 'core:login' %}\" class=\"cta-button bg-white text-blue-700 hover:bg-blue-50 font-bold py-3 px-8 rounded-lg shadow-lg inline-flex items-center justify-center\">\n                        <span>Accedi</span>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </a>\n                    <a href=\"{% url 'public:procedures' %}\" class=\"cta-button bg-blue-700 hover:bg-blue-800 text-white font-bold py-3 px-8 rounded-lg shadow-lg border border-blue-500 inline-flex items-center justify-center\">\n                        <span>Scopri di più</span>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </a>\n                </div>\n            </div>\n            <div class=\"md:w-1/2 z-10 pl-10 hidden md:block\">\n                <div class=\"floating-image w-full h-auto rounded-lg shadow-2xl bg-white p-6\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1200 800\" class=\"w-full\">\n                        <!-- Background with grid pattern -->\n                        <rect width=\"1200\" height=\"800\" fill=\"#f8fafc\" />\n                        <path d=\"M0 0h1200v800H0z\" fill=\"url(#grid)\" />\n                        \n                        <!-- Header bar -->\n                        <rect x=\"0\" y=\"0\" width=\"1200\" height=\"70\" fill=\"#2563eb\" />\n                        <rect x=\"20\" y=\"20\" width=\"180\" height=\"30\" rx=\"5\" fill=\"#3b82f6\" />\n                        <rect x=\"900\" y=\"20\" width=\"120\" height=\"30\" rx=\"15\" fill=\"#3b82f6\" />\n                        <circle cx=\"1150\" cy=\"35\" r=\"20\" fill=\"#3b82f6\" />\n                        \n                        <!-- Left sidebar -->\n                        <rect x=\"0\" y=\"70\" width=\"220\" height=\"730\" fill=\"#f1f5f9\" />\n                        <rect x=\"20\" y=\"100\" width=\"180\" height=\"40\" rx=\"5\" fill=\"#dbeafe\" />\n                        <rect x=\"20\" y=\"160\" width=\"180\" height=\"30\" rx=\"5\" fill=\"white\" />\n                        <rect x=\"20\" y=\"210\" width=\"180\" height=\"30\" rx=\"5\" fill=\"white\" />\n                        <rect x=\"20\" y=\"260\" width=\"180\" height=\"30\" rx=\"5\" fill=\"white\" />\n                        <rect x=\"20\" y=\"310\" width=\"180\" height=\"30\" rx=\"5\" fill=\"white\" />\n                        \n                        <!-- Main content area -->\n                        <rect x=\"240\" y=\"90\" width=\"940\" height=\"160\" rx=\"8\" fill=\"white\" />\n                        <rect x=\"260\" y=\"110\" width=\"200\" height=\"30\" rx=\"4\" fill=\"#e2e8f0\" />\n                        <rect x=\"260\" y=\"160\" width=\"600\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"260\" y=\"185\" width=\"400\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"260\" y=\"210\" width=\"200\" height=\"20\" rx=\"4\" fill=\"#2563eb\" />\n                        \n                        <!-- Stats cards -->\n                        <rect x=\"240\" y=\"270\" width=\"220\" height=\"120\" rx=\"8\" fill=\"white\" />\n                        <rect x=\"260\" y=\"290\" width=\"100\" height=\"30\" rx=\"4\" fill=\"#e2e8f0\" />\n                        <rect x=\"260\" y=\"330\" width=\"180\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"260\" y=\"355\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#2563eb\" />\n                        \n                        <rect x=\"480\" y=\"270\" width=\"220\" height=\"120\" rx=\"8\" fill=\"white\" />\n                        <rect x=\"500\" y=\"290\" width=\"100\" height=\"30\" rx=\"4\" fill=\"#e2e8f0\" />\n                        <rect x=\"500\" y=\"330\" width=\"180\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"500\" y=\"355\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#10b981\" />\n                        \n                        <rect x=\"720\" y=\"270\" width=\"220\" height=\"120\" rx=\"8\" fill=\"white\" />\n                        <rect x=\"740\" y=\"290\" width=\"100\" height=\"30\" rx=\"4\" fill=\"#e2e8f0\" />\n                        <rect x=\"740\" y=\"330\" width=\"180\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"740\" y=\"355\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#ef4444\" />\n                        \n                        <rect x=\"960\" y=\"270\" width=\"220\" height=\"120\" rx=\"8\" fill=\"white\" />\n                        <rect x=\"980\" y=\"290\" width=\"100\" height=\"30\" rx=\"4\" fill=\"#e2e8f0\" />\n                        <rect x=\"980\" y=\"330\" width=\"180\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"980\" y=\"355\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#f59e0b\" />\n                        \n                        <!-- Table -->\n                        <rect x=\"240\" y=\"410\" width=\"940\" height=\"350\" rx=\"8\" fill=\"white\" />\n                        <rect x=\"260\" y=\"430\" width=\"250\" height=\"30\" rx=\"4\" fill=\"#e2e8f0\" />\n                        <line x1=\"240\" y1=\"480\" x2=\"1180\" y2=\"480\" stroke=\"#e2e8f0\" stroke-width=\"2\" />\n                        \n                        <rect x=\"260\" y=\"500\" width=\"150\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"500\" y=\"500\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"700\" y=\"500\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"900\" y=\"500\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"1050\" y=\"500\" width=\"70\" height=\"20\" rx=\"10\" fill=\"#bfdbfe\" />\n                        \n                        <line x1=\"240\" y1=\"530\" x2=\"1180\" y2=\"530\" stroke=\"#f8fafc\" stroke-width=\"2\" />\n                        \n                        <rect x=\"260\" y=\"550\" width=\"150\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"500\" y=\"550\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"700\" y=\"550\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"900\" y=\"550\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"1050\" y=\"550\" width=\"70\" height=\"20\" rx=\"10\" fill=\"#dcfce7\" />\n                        \n                        <line x1=\"240\" y1=\"580\" x2=\"1180\" y2=\"580\" stroke=\"#f8fafc\" stroke-width=\"2\" />\n                        \n                        <rect x=\"260\" y=\"600\" width=\"150\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"500\" y=\"600\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"700\" y=\"600\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"900\" y=\"600\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"1050\" y=\"600\" width=\"70\" height=\"20\" rx=\"10\" fill=\"#fee2e2\" />\n                        \n                        <line x1=\"240\" y1=\"630\" x2=\"1180\" y2=\"630\" stroke=\"#f8fafc\" stroke-width=\"2\" />\n                        \n                        <rect x=\"260\" y=\"650\" width=\"150\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"500\" y=\"650\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"700\" y=\"650\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"900\" y=\"650\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"1050\" y=\"650\" width=\"70\" height=\"20\" rx=\"10\" fill=\"#bfdbfe\" />\n                        \n                        <line x1=\"240\" y1=\"680\" x2=\"1180\" y2=\"680\" stroke=\"#f8fafc\" stroke-width=\"2\" />\n                        \n                        <rect x=\"260\" y=\"700\" width=\"150\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"500\" y=\"700\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"700\" y=\"700\" width=\"80\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"900\" y=\"700\" width=\"100\" height=\"15\" rx=\"2\" fill=\"#e2e8f0\" />\n                        <rect x=\"1050\" y=\"700\" width=\"70\" height=\"20\" rx=\"10\" fill=\"#dcfce7\" />\n                        \n                        <!-- Grid pattern definition -->\n                        <defs>\n                            <pattern id=\"grid\" width=\"40\" height=\"40\" patternUnits=\"userSpaceOnUse\">\n                                <path d=\"M0 0h40v40H0z\" fill=\"none\" />\n                                <path d=\"M0 0h40v1H0z\" fill=\"#f1f5f9\" />\n                                <path d=\"M0 0h1v40H0z\" fill=\"#f1f5f9\" />\n                            </pattern>\n                        </defs>\n                    </svg>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>\n\n<!-- Statistiche -->\n<div class=\"bg-gray-50 py-10\">\n    <div class=\"container mx-auto px-4\">\n        <div class=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\">\n            <div class=\"stats-card bg-white rounded-lg shadow p-6\">\n                <p class=\"text-3xl md:text-4xl font-bold text-blue-600 mb-2\">12+</p>\n                <p class=\"text-gray-600\">Anni di esperienza</p>\n            </div>\n            <div class=\"stats-card bg-white rounded-lg shadow p-6\">\n                <p class=\"text-3xl md:text-4xl font-bold text-blue-600 mb-2\">98%</p>\n                <p class=\"text-gray-600\">Clienti soddisfatti</p>\n            </div>\n            <div class=\"stats-card bg-white rounded-lg shadow p-6\">\n                <p class=\"text-3xl md:text-4xl font-bold text-blue-600 mb-2\">500+</p>\n                <p class=\"text-gray-600\">Progetti gestiti</p>\n            </div>\n            <div class=\"stats-card bg-white rounded-lg shadow p-6\">\n                <p class=\"text-3xl md:text-4xl font-bold text-blue-600 mb-2\">20K+</p>\n                <p class=\"text-gray-600\">Particelle trattate</p>\n            </div>\n        </div>\n    </div>\n</div>\n\n<!-- Caratteristiche principali -->\n<div class=\"py-16\">\n    <div class=\"container mx-auto px-4\">\n        <div class=\"text-center mb-16\">\n            <h2 class=\"text-3xl md:text-4xl font-bold text-gray-900 mb-4\">Un sistema completo e integrato</h2>\n            <p class=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n                ExProject offre tutti gli strumenti necessari per gestire l'intero processo espropriativo, dalla pianificazione al pagamento delle indennità.\n            </p>\n        </div>\n        \n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <div class=\"feature-card blue bg-white rounded-lg shadow p-8\">\n                <div class=\"feature-icon bg-blue-100 w-16 h-16 flex items-center justify-center rounded-full mb-6 mx-auto\">\n                    <svg class=\"w-8 h-8 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"></path>\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-center mb-4\">Gestione Completa</h3>\n                <p class=\"text-gray-600 text-center mb-6\">\n                    Gestisci tutte le fasi del procedimento espropriativo in un unico sistema integrato, dalla dichiarazione di pubblica utilità fino alla conclusione.\n                </p>\n                <div class=\"text-center\">\n                    <a href=\"{% url 'public:procedures' %}\" class=\"text-blue-600 hover:text-blue-800 font-medium inline-flex items-center\">\n                        <span>Scopri di più</span>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </a>\n                </div>\n            </div>\n            \n            <div class=\"feature-card green bg-white rounded-lg shadow p-8\">\n                <div class=\"feature-icon bg-green-100 w-16 h-16 flex items-center justify-center rounded-full mb-6 mx-auto\">\n                    <svg class=\"w-8 h-8 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\"></path>\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-center mb-4\">Workflow Automatizzato</h3>\n                <p class=\"text-gray-600 text-center mb-6\">\n                    Automazione delle fasi procedurali con controlli, notifiche e scadenze integrate secondo la normativa vigente.\n                </p>\n                <div class=\"text-center\">\n                    <a href=\"{% url 'public:about' %}\" class=\"text-green-600 hover:text-green-800 font-medium inline-flex items-center\">\n                        <span>Maggiori info</span>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </a>\n                </div>\n            </div>\n            \n            <div class=\"feature-card purple bg-white rounded-lg shadow p-8\">\n                <div class=\"feature-icon bg-purple-100 w-16 h-16 flex items-center justify-center rounded-full mb-6 mx-auto\">\n                    <svg class=\"w-8 h-8 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\"></path>\n                    </svg>\n                </div>\n                <h3 class=\"text-xl font-bold text-center mb-4\">Intelligenza Artificiale</h3>\n                <p class=\"text-gray-600 text-center mb-6\">\n                    Assistenza intelligente per analisi documentale, supporto decisionale e previsione di costi e tempistiche.\n                </p>\n                <div class=\"text-center\">\n                    <a href=\"{% url 'public:about' %}\" class=\"text-purple-600 hover:text-purple-800 font-medium inline-flex items-center\">\n                        <span>Esplora</span>\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                        </svg>\n                    </a>\n                </div>\n            </div>\n        </div>\n    </div>\n</div>\n\n<!-- CTA Section -->\n<div class=\"bg-blue-600 py-16\">\n    <div class=\"container mx-auto px-4 text-center\">\n        <h2 class=\"text-3xl md:text-4xl font-bold text-white mb-6\">Pronto a semplificare la gestione degli espropri?</h2>\n        <p class=\"text-xl text-blue-100 mb-8 max-w-3xl mx-auto\">\n            Unisciti alle centinaia di enti e professionisti che utilizzano ExProject per gestire in modo efficiente i procedimenti espropriativi.\n        </p>\n        <div class=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <a href=\"{% url 'core:login' %}\" class=\"cta-button bg-white text-blue-700 hover:bg-blue-50 font-bold py-4 px-8 rounded-lg shadow-lg inline-flex items-center justify-center\">\n                <span>Inizia ora</span>\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fill-rule=\"evenodd\" d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\" clip-rule=\"evenodd\" />\n                </svg>\n            </a>\n            <a href=\"{% url 'public:contacts' %}\" class=\"cta-button bg-transparent text-white hover:bg-blue-700 font-bold py-4 px-8 rounded-lg shadow-lg border border-white inline-flex items-center justify-center\">\n                <span>Contattaci</span>\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 ml-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path d=\"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z\" />\n                    <path d=\"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z\" />\n                </svg>\n            </a>\n        </div>\n    </div>\n</div>\n{% endblock %}"}