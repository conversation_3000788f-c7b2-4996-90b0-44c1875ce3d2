{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/services.py"}, "modifiedCode": "\"\"\"\nServizi per la gestione del design system e configurazioni white label\n\"\"\"\nimport json\nimport os\nfrom django.conf import settings\nfrom django.core.cache import cache\nfrom django.template.loader import render_to_string\nfrom .models import Theme, ComponentLibrary, LayoutConfiguration\nfrom .design_tokens import DEFAULT_THEME_CONFIG, THEME_PRESETS, get_css_variables, get_tailwind_config\n\n\nclass ThemeService:\n    \"\"\"Servizio per la gestione dei temi\"\"\"\n    \n    CACHE_KEY_PREFIX = 'ui_theme_'\n    CACHE_TIMEOUT = 3600  # 1 ora\n    \n    @classmethod\n    def get_active_theme_config(cls):\n        \"\"\"Restituisce la configurazione del tema attivo\"\"\"\n        cache_key = f\"{cls.CACHE_KEY_PREFIX}active_config\"\n        config = cache.get(cache_key)\n        \n        if config is None:\n            theme = Theme.get_active_theme()\n            if theme:\n                config = {\n                    'name': theme.name,\n                    'slug': theme.slug,\n                    'colors': theme.colors,\n                    'typography': theme.typography,\n                    'spacing': theme.spacing,\n                    'borders': theme.borders,\n                    'shadows': theme.shadows,\n                    'branding': {\n                        'logo_url': theme.logo_url,\n                        'favicon_url': theme.favicon_url,\n                        'brand_name': theme.brand_name,\n                        'brand_tagline': theme.brand_tagline,\n                    },\n                    'custom_css': theme.custom_css,\n                    'component_overrides': theme.component_overrides,\n                }\n            else:\n                config = DEFAULT_THEME_CONFIG\n            \n            cache.set(cache_key, config, cls.CACHE_TIMEOUT)\n        \n        return config\n    \n    @classmethod\n    def generate_css(cls, theme_config=None):\n        \"\"\"Genera CSS custom properties dal tema\"\"\"\n        if theme_config is None:\n            theme_config = cls.get_active_theme_config()\n        \n        return get_css_variables(theme_config)\n    \n    @classmethod\n    def generate_tailwind_config(cls, theme_config=None):\n        \"\"\"Genera configurazione Tailwind dal tema\"\"\"\n        if theme_config is None:\n            theme_config = cls.get_active_theme_config()\n        \n        return get_tailwind_config(theme_config)\n    \n    @classmethod\n    def create_theme_from_preset(cls, preset_name, name=None, user=None):\n        \"\"\"Crea un tema da un preset predefinito\"\"\"\n        if preset_name not in THEME_PRESETS:\n            raise ValueError(f\"Preset '{preset_name}' non trovato\")\n        \n        preset = THEME_PRESETS[preset_name]\n        theme_name = name or preset['name']\n        \n        theme = Theme.objects.create(\n            name=theme_name,\n            slug=theme_name.lower().replace(' ', '-'),\n            colors=preset.get('colors', DEFAULT_THEME_CONFIG['colors']),\n            typography=preset.get('typography', DEFAULT_THEME_CONFIG['typography']),\n            spacing=preset.get('spacing', DEFAULT_THEME_CONFIG['spacing']),\n            borders=preset.get('borders', DEFAULT_THEME_CONFIG['borders']),\n            shadows=preset.get('shadows', DEFAULT_THEME_CONFIG['shadows']),\n            created_by=user,\n        )\n        \n        cls.clear_cache()\n        return theme\n    \n    @classmethod\n    def clear_cache(cls):\n        \"\"\"Pulisce la cache dei temi\"\"\"\n        cache.delete_pattern(f\"{cls.CACHE_KEY_PREFIX}*\")\n\n\nclass ComponentService:\n    \"\"\"Servizio per la gestione dei componenti\"\"\"\n    \n    @classmethod\n    def render_component(cls, component_slug, props=None, variant=None):\n        \"\"\"Renderizza un componente con le proprietà specificate\"\"\"\n        try:\n            component = ComponentLibrary.objects.get(slug=component_slug, is_active=True)\n        except ComponentLibrary.DoesNotExist:\n            return f\"<!-- Componente '{component_slug}' non trovato -->\"\n        \n        # Merge props con defaults\n        context = {}\n        if props:\n            context.update(props)\n        \n        # Applica variante se specificata\n        if variant and variant in component.variants:\n            variant_props = component.variants[variant]\n            context.update(variant_props)\n        \n        # Renderizza template\n        try:\n            return render_to_string(\n                f'ui/components/{component_slug}.html',\n                context\n            )\n        except Exception as e:\n            return f\"<!-- Errore rendering componente '{component_slug}': {str(e)} -->\"\n    \n    @classmethod\n    def get_component_variants(cls, component_slug):\n        \"\"\"Restituisce le varianti disponibili per un componente\"\"\"\n        try:\n            component = ComponentLibrary.objects.get(slug=component_slug, is_active=True)\n            return component.variants\n        except ComponentLibrary.DoesNotExist:\n            return {}\n    \n    @classmethod\n    def get_components_by_category(cls, category):\n        \"\"\"Restituisce tutti i componenti di una categoria\"\"\"\n        return ComponentLibrary.objects.filter(\n            category=category,\n            is_active=True\n        ).order_by('name')\n\n\nclass LayoutService:\n    \"\"\"Servizio per la gestione dei layout\"\"\"\n    \n    @classmethod\n    def get_layout_config(cls, layout_type, layout_name=None):\n        \"\"\"Restituisce la configurazione di un layout\"\"\"\n        query = LayoutConfiguration.objects.filter(\n            layout_type=layout_type,\n            is_active=True\n        )\n        \n        if layout_name:\n            query = query.filter(slug=layout_name)\n        \n        return query.first()\n    \n    @classmethod\n    def render_layout(cls, layout_type, context=None, layout_name=None):\n        \"\"\"Renderizza un layout con il contesto specificato\"\"\"\n        layout_config = cls.get_layout_config(layout_type, layout_name)\n        \n        if not layout_config:\n            return f\"<!-- Layout '{layout_type}' non trovato -->\"\n        \n        template_context = {\n            'layout_config': layout_config,\n            'sidebar_enabled': layout_config.sidebar_enabled,\n            'sidebar_position': layout_config.sidebar_position,\n            'header_style': layout_config.header_style,\n            'footer_enabled': layout_config.footer_enabled,\n            'container_max_width': layout_config.container_max_width,\n            'content_padding': layout_config.content_padding,\n            'grid_columns': layout_config.grid_columns,\n            'components': layout_config.components,\n        }\n        \n        if context:\n            template_context.update(context)\n        \n        try:\n            return render_to_string(\n                f'ui/layouts/{layout_type}.html',\n                template_context\n            )\n        except Exception as e:\n            return f\"<!-- Errore rendering layout '{layout_type}': {str(e)} -->\"\n\n\nclass WhiteLabelService:\n    \"\"\"Servizio per la configurazione white label\"\"\"\n    \n    @classmethod\n    def get_client_config(cls, client_slug=None):\n        \"\"\"Restituisce la configurazione per un cliente specifico\"\"\"\n        # Se non specificato, usa il tema attivo\n        if not client_slug:\n            return ThemeService.get_active_theme_config()\n        \n        # Cerca tema specifico per cliente\n        try:\n            theme = Theme.objects.get(slug=client_slug, is_active=True)\n            return {\n                'name': theme.name,\n                'slug': theme.slug,\n                'colors': theme.colors,\n                'typography': theme.typography,\n                'spacing': theme.spacing,\n                'borders': theme.borders,\n                'shadows': theme.shadows,\n                'branding': {\n                    'logo_url': theme.logo_url,\n                    'favicon_url': theme.favicon_url,\n                    'brand_name': theme.brand_name,\n                    'brand_tagline': theme.brand_tagline,\n                },\n                'custom_css': theme.custom_css,\n                'component_overrides': theme.component_overrides,\n            }\n        except Theme.DoesNotExist:\n            return ThemeService.get_active_theme_config()\n    \n    @classmethod\n    def generate_client_css(cls, client_slug=None):\n        \"\"\"Genera CSS personalizzato per un cliente\"\"\"\n        config = cls.get_client_config(client_slug)\n        css = ThemeService.generate_css(config)\n        \n        # Aggiungi CSS personalizzato se presente\n        if config.get('custom_css'):\n            css += f\"\\n\\n/* CSS Personalizzato */\\n{config['custom_css']}\"\n        \n        return css\n    \n    @classmethod\n    def export_theme_config(cls, theme_slug):\n        \"\"\"Esporta la configurazione di un tema in formato JSON\"\"\"\n        try:\n            theme = Theme.objects.get(slug=theme_slug)\n            config = {\n                'name': theme.name,\n                'slug': theme.slug,\n                'colors': theme.colors,\n                'typography': theme.typography,\n                'spacing': theme.spacing,\n                'borders': theme.borders,\n                'shadows': theme.shadows,\n                'branding': {\n                    'logo_url': theme.logo_url,\n                    'favicon_url': theme.favicon_url,\n                    'brand_name': theme.brand_name,\n                    'brand_tagline': theme.brand_tagline,\n                },\n                'custom_css': theme.custom_css,\n                'component_overrides': theme.component_overrides,\n                'version': '1.0.0',\n                'exported_at': theme.updated_at.isoformat(),\n            }\n            return json.dumps(config, indent=2, ensure_ascii=False)\n        except Theme.DoesNotExist:\n            return None\n    \n    @classmethod\n    def import_theme_config(cls, config_json, user=None):\n        \"\"\"Importa una configurazione tema da JSON\"\"\"\n        try:\n            config = json.loads(config_json)\n            \n            theme = Theme.objects.create(\n                name=config['name'],\n                slug=config['slug'],\n                colors=config.get('colors', {}),\n                typography=config.get('typography', {}),\n                spacing=config.get('spacing', {}),\n                borders=config.get('borders', {}),\n                shadows=config.get('shadows', {}),\n                logo_url=config.get('branding', {}).get('logo_url'),\n                favicon_url=config.get('branding', {}).get('favicon_url'),\n                brand_name=config.get('branding', {}).get('brand_name'),\n                brand_tagline=config.get('branding', {}).get('brand_tagline'),\n                custom_css=config.get('custom_css', ''),\n                component_overrides=config.get('component_overrides', {}),\n                created_by=user,\n            )\n            \n            ThemeService.clear_cache()\n            return theme\n        except (json.JSONDecodeError, KeyError) as e:\n            raise ValueError(f\"Configurazione non valida: {str(e)}\")\n\n\n# Utility functions per template tags\ndef get_component_classes(component_type, variant='default', size='default'):\n    \"\"\"Restituisce le classi CSS per un componente\"\"\"\n    # Implementazione delle classi base per ogni componente\n    base_classes = {\n        'button': 'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n        'input': 'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n        'card': 'rounded-lg border bg-card text-card-foreground shadow-sm',\n        'badge': 'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\n    }\n    \n    return base_classes.get(component_type, '')\n"}