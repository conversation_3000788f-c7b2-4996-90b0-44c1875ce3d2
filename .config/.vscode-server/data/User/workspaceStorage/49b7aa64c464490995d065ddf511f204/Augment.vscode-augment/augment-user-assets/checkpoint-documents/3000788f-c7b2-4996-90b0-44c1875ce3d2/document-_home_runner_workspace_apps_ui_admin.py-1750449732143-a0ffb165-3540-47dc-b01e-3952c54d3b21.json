{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/admin.py"}, "modifiedCode": "from django.contrib import admin\nfrom django.utils.html import format_html\nfrom .models import Theme, ComponentLibrary, LayoutConfiguration\n\n\****************(Theme)\nclass ThemeAdmin(admin.ModelAdmin):\n    list_display = ['name', 'slug', 'is_active', 'is_default', 'created_at']\n    list_filter = ['is_active', 'is_default', 'created_at']\n    search_fields = ['name', 'slug', 'brand_name']\n    readonly_fields = ['created_at', 'updated_at']\n    \n    fieldsets = (\n        ('Informazioni Base', {\n            'fields': ('name', 'slug', 'is_active', 'is_default')\n        }),\n        ('Design Tokens', {\n            'fields': ('colors', 'typography', 'spacing', 'borders', 'shadows'),\n            'classes': ('collapse',)\n        }),\n        ('Branding', {\n            'fields': ('logo_url', 'favicon_url', 'brand_name', 'brand_tagline'),\n            'classes': ('collapse',)\n        }),\n        ('Personalizzazioni', {\n            'fields': ('custom_css', 'component_overrides'),\n            'classes': ('collapse',)\n        }),\n        ('Metadati', {\n            'fields': ('created_by', 'created_at', 'updated_at'),\n            'classes': ('collapse',)\n        }),\n    )\n    \n    def save_model(self, request, obj, form, change):\n        if not change:  # Nuovo oggetto\n            obj.created_by = request.user\n        super().save_model(request, obj, form, change)\n\n\****************(ComponentLibrary)\nclass ComponentLibraryAdmin(admin.ModelAdmin):\n    list_display = ['name', 'category', 'is_active', 'version', 'created_at']\n    list_filter = ['category', 'is_active', 'created_at']\n    search_fields = ['name', 'slug', 'description']\n    readonly_fields = ['created_at', 'updated_at']\n    \n    fieldsets = (\n        ('Informazioni Base', {\n            'fields': ('name', 'slug', 'category', 'is_active', 'version')\n        }),\n        ('Template', {\n            'fields': ('template_content', 'css_classes', 'javascript'),\n        }),\n        ('Configurazione', {\n            'fields': ('props_schema', 'variants', 'examples'),\n            'classes': ('collapse',)\n        }),\n        ('Documentazione', {\n            'fields': ('description', 'documentation'),\n            'classes': ('collapse',)\n        }),\n        ('Metadati', {\n            'fields': ('created_by', 'created_at', 'updated_at'),\n            'classes': ('collapse',)\n        }),\n    )\n    \n    def save_model(self, request, obj, form, change):\n        if not change:  # Nuovo oggetto\n            obj.created_by = request.user\n        super().save_model(request, obj, form, change)\n\n\****************(LayoutConfiguration)\nclass LayoutConfigurationAdmin(admin.ModelAdmin):\n    list_display = ['name', 'layout_type', 'is_active', 'created_at']\n    list_filter = ['layout_type', 'is_active', 'created_at']\n    search_fields = ['name', 'slug']\n    readonly_fields = ['created_at', 'updated_at']\n    \n    fieldsets = (\n        ('Informazioni Base', {\n            'fields': ('name', 'slug', 'layout_type', 'is_active')\n        }),\n        ('Configurazione Layout', {\n            'fields': ('sidebar_enabled', 'sidebar_position', 'header_style', 'footer_enabled'),\n        }),\n        ('Grid e Spacing', {\n            'fields': ('container_max_width', 'content_padding', 'grid_columns'),\n            'classes': ('collapse',)\n        }),\n        ('Componenti', {\n            'fields': ('components',),\n            'classes': ('collapse',)\n        }),\n        ('Metadati', {\n            'fields': ('created_at', 'updated_at'),\n            'classes': ('collapse',)\n        }),\n    )\n"}