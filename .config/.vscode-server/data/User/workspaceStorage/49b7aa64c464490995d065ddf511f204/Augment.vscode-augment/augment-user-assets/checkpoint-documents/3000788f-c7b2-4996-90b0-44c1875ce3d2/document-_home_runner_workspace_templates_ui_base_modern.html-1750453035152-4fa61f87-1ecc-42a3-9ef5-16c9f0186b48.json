{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/base_modern.html"}, "originalCode": "{% load static %}\n{% load ui_components %}\n<!DOCTYPE html>\n<html lang=\"it\" class=\"h-full\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{% block title %}ExProject{% endblock %}</title>\n    \n    <!-- Favicon e PWA -->\n    <link rel=\"icon\" href=\"{% static 'img/favicon.ico' %}\" type=\"image/x-icon\">\n    <meta name=\"theme-color\" content=\"hsl(221.2 83.2% 53.3%)\">\n    \n    <!-- Fonts -->\n    <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n    <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap\" rel=\"stylesheet\">\n    \n    <!-- Design System CSS -->\n    {% ui_theme_css %}\n    \n    <!-- TailwindCSS con configurazione custom -->\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script>\n        tailwind.config = {\n            darkMode: 'class',\n            theme: {\n                extend: {\n                    colors: {\n                        border: \"hsl(var(--border))\",\n                        input: \"hsl(var(--input))\",\n                        ring: \"hsl(var(--ring))\",\n                        background: \"hsl(var(--background))\",\n                        foreground: \"hsl(var(--foreground))\",\n                        primary: {\n                            DEFAULT: \"hsl(var(--primary))\",\n                            foreground: \"hsl(var(--primary-foreground))\",\n                        },\n                        secondary: {\n                            DEFAULT: \"hsl(var(--secondary))\",\n                            foreground: \"hsl(var(--secondary-foreground))\",\n                        },\n                        destructive: {\n                            DEFAULT: \"hsl(var(--destructive))\",\n                            foreground: \"hsl(var(--destructive-foreground))\",\n                        },\n                        muted: {\n                            DEFAULT: \"hsl(var(--muted))\",\n                            foreground: \"hsl(var(--muted-foreground))\",\n                        },\n                        accent: {\n                            DEFAULT: \"hsl(var(--accent))\",\n                            foreground: \"hsl(var(--accent-foreground))\",\n                        },\n                        popover: {\n                            DEFAULT: \"hsl(var(--popover))\",\n                            foreground: \"hsl(var(--popover-foreground))\",\n                        },\n                        card: {\n                            DEFAULT: \"hsl(var(--card))\",\n                            foreground: \"hsl(var(--card-foreground))\",\n                        },\n                    },\n                    fontFamily: {\n                        sans: ['Inter', 'ui-sans-serif', 'system-ui'],\n                        mono: ['JetBrains Mono', 'ui-monospace'],\n                    },\n                    borderRadius: {\n                        lg: \"var(--radius)\",\n                        md: \"calc(var(--radius) - 2px)\",\n                        sm: \"calc(var(--radius) - 4px)\",\n                    },\n                    keyframes: {\n                        \"accordion-down\": {\n                            from: { height: 0 },\n                            to: { height: \"var(--radix-accordion-content-height)\" },\n                        },\n                        \"accordion-up\": {\n                            from: { height: \"var(--radix-accordion-content-height)\" },\n                            to: { height: 0 },\n                        },\n                    },\n                    animation: {\n                        \"accordion-down\": \"accordion-down 0.2s ease-out\",\n                        \"accordion-up\": \"accordion-up 0.2s ease-out\",\n                    },\n                }\n            }\n        }\n    </script>\n    \n    <!-- HTMX e Alpine.js -->\n    <script src=\"https://cdn.jsdelivr.net/npm/htmx.org@1.9.10\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js\" defer></script>\n    \n    {% block extra_head %}{% endblock %}\n</head>\n<body class=\"min-h-screen bg-background font-sans antialiased\"\n      hx-headers='{\"X-CSRFToken\": \"{{ csrf_token }}\"}'\n      x-data=\"{ sidebarOpen: false, darkMode: false }\"\n      x-init=\"darkMode = localStorage.getItem('darkMode') === 'true'\"\n      :class=\"{ 'dark': darkMode }\">\n    \n    <!-- Header moderno -->\n    {% block header %}\n    <header class=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n        <div class=\"container flex h-14 items-center\">\n            <!-- Mobile menu button -->\n            <button @click=\"sidebarOpen = !sidebarOpen\"\n                    class=\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10 md:hidden\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5\" />\n                </svg>\n            </button>\n            \n            <!-- Logo -->\n            <div class=\"mr-4 hidden md:flex\">\n                <a href=\"{% url 'public:home' %}\" class=\"mr-6 flex items-center space-x-2\">\n                    <div class=\"h-6 w-6 rounded bg-primary\"></div>\n                    <span class=\"hidden font-bold sm:inline-block\">ExProject</span>\n                </a>\n            </div>\n            \n            <!-- Navigation -->\n            {% if user.is_authenticated %}\n            <nav class=\"flex items-center space-x-6 text-sm font-medium hidden md:flex\">\n                <a href=\"{% url 'core:dashboard' %}\" \n                   class=\"transition-colors hover:text-foreground/80 {% if request.resolver_match.url_name == 'dashboard' %}text-foreground{% else %}text-foreground/60{% endif %}\">\n                    Dashboard\n                </a>\n                <a href=\"{% url 'projects:list' %}\" \n                   class=\"transition-colors hover:text-foreground/80 {% if request.resolver_match.namespace == 'projects' %}text-foreground{% else %}text-foreground/60{% endif %}\">\n                    Progetti\n                </a>\n                <a href=\"{% url 'workflow:list' %}\" \n                   class=\"transition-colors hover:text-foreground/80 {% if request.resolver_match.namespace == 'workflow' %}text-foreground{% else %}text-foreground/60{% endif %}\">\n                    Workflow\n                </a>\n                <a href=\"{% url 'documents:list' %}\" \n                   class=\"transition-colors hover:text-foreground/80 {% if request.resolver_match.namespace == 'documents' %}text-foreground{% else %}text-foreground/60{% endif %}\">\n                    Documenti\n                </a>\n            </nav>\n            {% endif %}\n            \n            <div class=\"flex flex-1 items-center justify-between space-x-2 md:justify-end\">\n                <!-- Search -->\n                {% if user.is_authenticated %}\n                <div class=\"w-full flex-1 md:w-auto md:flex-none\">\n                    <form method=\"get\" action=\"{% url 'core:search' %}\" class=\"relative\">\n                        <input type=\"text\" name=\"q\" placeholder=\"Cerca...\" \n                               class=\"inline-flex items-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2 relative w-full justify-start text-sm text-muted-foreground sm:pr-12 md:w-40 lg:w-64\">\n                        <button type=\"submit\" class=\"absolute right-0 top-0 h-9 w-9 inline-flex items-center justify-center\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\" />\n                            </svg>\n                        </button>\n                    </form>\n                </div>\n                {% endif %}\n                \n                <!-- Theme toggle -->\n                <button @click=\"darkMode = !darkMode; localStorage.setItem('darkMode', darkMode)\"\n                        class=\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n                    <span x-show=\"!darkMode\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z\" />\n                        </svg>\n                    </span>\n                    <span x-show=\"darkMode\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z\" />\n                        </svg>\n                    </span>\n                </button>\n                \n                <!-- User menu -->\n                {% if user.is_authenticated %}\n                <div class=\"relative\" x-data=\"{ open: false }\">\n                    <button @click=\"open = !open\" \n                            class=\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n                        <div class=\"h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground text-sm font-medium\">\n                            {{ user.username|slice:\":1\"|upper }}\n                        </div>\n                    </button>\n                    \n                    <div x-show=\"open\" @click.away=\"open = false\" \n                         x-transition:enter=\"transition ease-out duration-100\"\n                         x-transition:enter-start=\"transform opacity-0 scale-95\"\n                         x-transition:enter-end=\"transform opacity-100 scale-100\"\n                         x-transition:leave=\"transition ease-in duration-75\"\n                         x-transition:leave-start=\"transform opacity-100 scale-100\"\n                         x-transition:leave-end=\"transform opacity-0 scale-95\"\n                         class=\"absolute right-0 mt-2 w-56 rounded-md border bg-popover p-1 text-popover-foreground shadow-md outline-none z-50\">\n                        <div class=\"px-2 py-1.5 text-sm font-semibold\">{{ user.get_full_name|default:user.username }}</div>\n                        <div class=\"h-px bg-border my-1\"></div>\n                        <a href=\"{% url 'core:profile' %}\" class=\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4 mr-2\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                            </svg>\n                            Profilo\n                        </a>\n                        <a href=\"{% url 'core:settings' %}\" class=\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4 mr-2\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a6.759 6.759 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z\" />\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\" />\n                            </svg>\n                            Impostazioni\n                        </a>\n                        <div class=\"h-px bg-border my-1\"></div>\n                        <a href=\"{% url 'core:logout' %}\" class=\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4 mr-2\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15M12 9l-3 3m0 0 3 3m-3-3h12.75\" />\n                            </svg>\n                            Logout\n                        </a>\n                    </div>\n                </div>\n                {% else %}\n                {% ui_button \"Accedi\" variant=\"default\" size=\"sm\" href=\"/login/\" %}\n                {% endif %}\n            </div>\n        </div>\n    </header>\n    {% endblock %}\n    \n    <!-- Mobile sidebar overlay -->\n    <div x-show=\"sidebarOpen\" @click=\"sidebarOpen = false\" \n         class=\"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm md:hidden\"\n         x-transition:enter=\"transition-all ease-in-out duration-300\"\n         x-transition:enter-start=\"opacity-0\"\n         x-transition:enter-end=\"opacity-100\"\n         x-transition:leave=\"transition-all ease-in-out duration-300\"\n         x-transition:leave-start=\"opacity-100\"\n         x-transition:leave-end=\"opacity-0\"></div>\n    \n    <!-- Main content -->\n    <div class=\"flex-1\">\n        {% block messages %}\n        {% if messages %}\n        <div class=\"container mx-auto px-4 py-2\">\n            {% for message in messages %}\n            {% ui_alert message.message variant=message.tags title=\"Notifica\" %}\n            {% endfor %}\n        </div>\n        {% endif %}\n        {% endblock %}\n        \n        {% block main %}\n        <main class=\"container mx-auto px-4 py-6\">\n            {% block content %}{% endblock %}\n        </main>\n        {% endblock %}\n    </div>\n    \n    <!-- Footer -->\n    {% block footer %}\n    <footer class=\"border-t bg-background\">\n        <div class=\"container flex flex-col items-center justify-between gap-4 py-10 md:h-24 md:flex-row md:py-0\">\n            <div class=\"flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0\">\n                <p class=\"text-center text-sm leading-loose text-muted-foreground md:text-left\">\n                    © 2025 ExProject. Tutti i diritti riservati.\n                </p>\n            </div>\n        </div>\n    </footer>\n    {% endblock %}\n    \n    {% block extra_js %}{% endblock %}\n</body>\n</html>\n", "modifiedCode": "{% load static %}\n{% load ui_components %}\n<!DOCTYPE html>\n<html lang=\"it\" class=\"h-full\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>{% block title %}ExProject{% endblock %}</title>\n    \n    <!-- Favicon e PWA -->\n    <link rel=\"icon\" href=\"{% static 'img/favicon.ico' %}\" type=\"image/x-icon\">\n    <meta name=\"theme-color\" content=\"hsl(221.2 83.2% 53.3%)\">\n    \n    <!-- Fonts -->\n    <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\">\n    <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossorigin>\n    <link href=\"https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap\" rel=\"stylesheet\">\n    \n    <!-- Design System CSS -->\n    {% ui_theme_css %}\n    \n    <!-- TailwindCSS con configurazione custom -->\n    <script src=\"https://cdn.tailwindcss.com\"></script>\n    <script>\n        tailwind.config = {\n            darkMode: 'class',\n            theme: {\n                extend: {\n                    colors: {\n                        border: \"hsl(var(--border))\",\n                        input: \"hsl(var(--input))\",\n                        ring: \"hsl(var(--ring))\",\n                        background: \"hsl(var(--background))\",\n                        foreground: \"hsl(var(--foreground))\",\n                        primary: {\n                            DEFAULT: \"hsl(var(--primary))\",\n                            foreground: \"hsl(var(--primary-foreground))\",\n                        },\n                        secondary: {\n                            DEFAULT: \"hsl(var(--secondary))\",\n                            foreground: \"hsl(var(--secondary-foreground))\",\n                        },\n                        destructive: {\n                            DEFAULT: \"hsl(var(--destructive))\",\n                            foreground: \"hsl(var(--destructive-foreground))\",\n                        },\n                        muted: {\n                            DEFAULT: \"hsl(var(--muted))\",\n                            foreground: \"hsl(var(--muted-foreground))\",\n                        },\n                        accent: {\n                            DEFAULT: \"hsl(var(--accent))\",\n                            foreground: \"hsl(var(--accent-foreground))\",\n                        },\n                        popover: {\n                            DEFAULT: \"hsl(var(--popover))\",\n                            foreground: \"hsl(var(--popover-foreground))\",\n                        },\n                        card: {\n                            DEFAULT: \"hsl(var(--card))\",\n                            foreground: \"hsl(var(--card-foreground))\",\n                        },\n                    },\n                    fontFamily: {\n                        sans: ['Inter', 'ui-sans-serif', 'system-ui'],\n                        mono: ['JetBrains Mono', 'ui-monospace'],\n                    },\n                    borderRadius: {\n                        lg: \"var(--radius)\",\n                        md: \"calc(var(--radius) - 2px)\",\n                        sm: \"calc(var(--radius) - 4px)\",\n                    },\n                    keyframes: {\n                        \"accordion-down\": {\n                            from: { height: 0 },\n                            to: { height: \"var(--radix-accordion-content-height)\" },\n                        },\n                        \"accordion-up\": {\n                            from: { height: \"var(--radix-accordion-content-height)\" },\n                            to: { height: 0 },\n                        },\n                    },\n                    animation: {\n                        \"accordion-down\": \"accordion-down 0.2s ease-out\",\n                        \"accordion-up\": \"accordion-up 0.2s ease-out\",\n                    },\n                }\n            }\n        }\n    </script>\n    \n    <!-- HTMX e Alpine.js -->\n    <script src=\"https://cdn.jsdelivr.net/npm/htmx.org@1.9.10\"></script>\n    <script src=\"https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js\" defer></script>\n    \n    {% block extra_head %}{% endblock %}\n</head>\n<body class=\"min-h-screen bg-background font-sans antialiased\"\n      hx-headers='{\"X-CSRFToken\": \"{{ csrf_token }}\"}'\n      x-data=\"{ sidebarOpen: false, darkMode: false }\"\n      x-init=\"darkMode = localStorage.getItem('darkMode') === 'true'\"\n      :class=\"{ 'dark': darkMode }\">\n    \n    <!-- Header moderno -->\n    {% block header %}\n    <header class=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n        <div class=\"container flex h-14 items-center\">\n            <!-- Mobile menu button -->\n            <button @click=\"sidebarOpen = !sidebarOpen\"\n                    class=\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10 md:hidden\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5\" />\n                </svg>\n            </button>\n            \n            <!-- Logo -->\n            <div class=\"mr-4 hidden md:flex\">\n                <a href=\"{% url 'public:home' %}\" class=\"mr-6 flex items-center space-x-2\">\n                    <div class=\"h-6 w-6 rounded bg-primary\"></div>\n                    <span class=\"hidden font-bold sm:inline-block\">ExProject</span>\n                </a>\n            </div>\n            \n            <!-- Navigation -->\n            {% if user.is_authenticated %}\n            <nav class=\"flex items-center space-x-6 text-sm font-medium hidden md:flex\">\n                <a href=\"{% url 'core:dashboard' %}\" \n                   class=\"transition-colors hover:text-foreground/80 {% if request.resolver_match.url_name == 'dashboard' %}text-foreground{% else %}text-foreground/60{% endif %}\">\n                    Dashboard\n                </a>\n                <a href=\"{% url 'projects:list' %}\" \n                   class=\"transition-colors hover:text-foreground/80 {% if request.resolver_match.namespace == 'projects' %}text-foreground{% else %}text-foreground/60{% endif %}\">\n                    Progetti\n                </a>\n                <a href=\"{% url 'workflow:list' %}\" \n                   class=\"transition-colors hover:text-foreground/80 {% if request.resolver_match.namespace == 'workflow' %}text-foreground{% else %}text-foreground/60{% endif %}\">\n                    Workflow\n                </a>\n                <a href=\"{% url 'documents:list' %}\" \n                   class=\"transition-colors hover:text-foreground/80 {% if request.resolver_match.namespace == 'documents' %}text-foreground{% else %}text-foreground/60{% endif %}\">\n                    Documenti\n                </a>\n            </nav>\n            {% endif %}\n            \n            <div class=\"flex flex-1 items-center justify-between space-x-2 md:justify-end\">\n                <!-- Search -->\n                {% if user.is_authenticated %}\n                <div class=\"w-full flex-1 md:w-auto md:flex-none\">\n                    <form method=\"get\" action=\"{% url 'core:search' %}\" class=\"relative\">\n                        <input type=\"text\" name=\"q\" placeholder=\"Cerca...\" \n                               class=\"inline-flex items-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2 relative w-full justify-start text-sm text-muted-foreground sm:pr-12 md:w-40 lg:w-64\">\n                        <button type=\"submit\" class=\"absolute right-0 top-0 h-9 w-9 inline-flex items-center justify-center\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\" />\n                            </svg>\n                        </button>\n                    </form>\n                </div>\n                {% endif %}\n                \n                <!-- Theme toggle -->\n                <button @click=\"darkMode = !darkMode; localStorage.setItem('darkMode', darkMode)\"\n                        class=\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n                    <span x-show=\"!darkMode\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M21.752 15.002A9.72 9.72 0 0 1 18 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 0 0 3 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 0 0 9.002-5.998Z\" />\n                        </svg>\n                    </span>\n                    <span x-show=\"darkMode\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n                            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12 3v2.25m6.364.386-1.591 1.591M21 12h-2.25m-.386 6.364-1.591-1.591M12 18.75V21m-4.773-4.227-1.591 1.591M5.25 12H3m4.227-4.773L5.636 5.636M15.75 12a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0Z\" />\n                        </svg>\n                    </span>\n                </button>\n                \n                <!-- User menu -->\n                {% if user.is_authenticated %}\n                <div class=\"relative\" x-data=\"{ open: false }\">\n                    <button @click=\"open = !open\" \n                            class=\"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10\">\n                        <div class=\"h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground text-sm font-medium\">\n                            {{ user.username|slice:\":1\"|upper }}\n                        </div>\n                    </button>\n                    \n                    <div x-show=\"open\" @click.away=\"open = false\" \n                         x-transition:enter=\"transition ease-out duration-100\"\n                         x-transition:enter-start=\"transform opacity-0 scale-95\"\n                         x-transition:enter-end=\"transform opacity-100 scale-100\"\n                         x-transition:leave=\"transition ease-in duration-75\"\n                         x-transition:leave-start=\"transform opacity-100 scale-100\"\n                         x-transition:leave-end=\"transform opacity-0 scale-95\"\n                         class=\"absolute right-0 mt-2 w-56 rounded-md border bg-popover p-1 text-popover-foreground shadow-md outline-none z-50\">\n                        <div class=\"px-2 py-1.5 text-sm font-semibold\">{{ user.get_full_name|default:user.username }}</div>\n                        <div class=\"h-px bg-border my-1\"></div>\n                        <a href=\"{% url 'core:profile' %}\" class=\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4 mr-2\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n                            </svg>\n                            Profilo\n                        </a>\n                        <a href=\"{% url 'core:settings' %}\" class=\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4 mr-2\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a6.759 6.759 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z\" />\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\" />\n                            </svg>\n                            Impostazioni\n                        </a>\n                        <div class=\"h-px bg-border my-1\"></div>\n                        <a href=\"{% url 'core:logout' %}\" class=\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4 mr-2\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15M12 9l-3 3m0 0 3 3m-3-3h12.75\" />\n                            </svg>\n                            Logout\n                        </a>\n                    </div>\n                </div>\n                {% else %}\n                <a href=\"{% url 'core:login' %}\" class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-9 px-3 bg-primary text-primary-foreground hover:bg-primary/90\">\n                    Accedi\n                </a>\n                {% endif %}\n            </div>\n        </div>\n    </header>\n    {% endblock %}\n    \n    <!-- Mobile sidebar overlay -->\n    <div x-show=\"sidebarOpen\" @click=\"sidebarOpen = false\" \n         class=\"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm md:hidden\"\n         x-transition:enter=\"transition-all ease-in-out duration-300\"\n         x-transition:enter-start=\"opacity-0\"\n         x-transition:enter-end=\"opacity-100\"\n         x-transition:leave=\"transition-all ease-in-out duration-300\"\n         x-transition:leave-start=\"opacity-100\"\n         x-transition:leave-end=\"opacity-0\"></div>\n    \n    <!-- Main content -->\n    <div class=\"flex-1\">\n        {% block messages %}\n        {% if messages %}\n        <div class=\"container mx-auto px-4 py-2\">\n            {% for message in messages %}\n            {% ui_alert message.message variant=message.tags title=\"Notifica\" %}\n            {% endfor %}\n        </div>\n        {% endif %}\n        {% endblock %}\n        \n        {% block main %}\n        <main class=\"container mx-auto px-4 py-6\">\n            {% block content %}{% endblock %}\n        </main>\n        {% endblock %}\n    </div>\n    \n    <!-- Footer -->\n    {% block footer %}\n    <footer class=\"border-t bg-background\">\n        <div class=\"container flex flex-col items-center justify-between gap-4 py-10 md:h-24 md:flex-row md:py-0\">\n            <div class=\"flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0\">\n                <p class=\"text-center text-sm leading-loose text-muted-foreground md:text-left\">\n                    © 2025 ExProject. Tutti i diritti riservati.\n                </p>\n            </div>\n        </div>\n    </footer>\n    {% endblock %}\n    \n    {% block extra_js %}{% endblock %}\n</body>\n</html>\n"}