{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/public/news.html"}, "modifiedCode": "{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}{{ title }} - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"flex-1 space-y-8 p-8 pt-6\">\n    <!-- Header -->\n    <div class=\"text-center\">\n        <h1 class=\"text-4xl font-bold tracking-tight mb-4\">{{ title }}</h1>\n        <p class=\"text-xl text-muted-foreground max-w-3xl mx-auto\">\n            Rimani aggiornato sulle ultime novità di ExProject e del settore espropri\n        </p>\n    </div>\n    \n    <!-- News Grid -->\n    <div class=\"max-w-6xl mx-auto\">\n        <div class=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n            {% for news_item in news_items %}\n                {% ui_card \n                    content=\"\n                    <article class='space-y-4'>\n                        <!-- News Image Placeholder -->\n                        <div class='w-full h-48 bg-muted rounded-lg flex items-center justify-center'>\n                            <svg class='w-12 h-12 text-muted-foreground' fill='none' stroke='currentColor' viewBox='0 0 24 24'>\n                                <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z'></path>\n                            </svg>\n                        </div>\n                        \n                        <!-- News Content -->\n                        <div class='space-y-2'>\n                            <div class='flex items-center space-x-2 text-sm text-muted-foreground'>\n                                <svg class='w-4 h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>\n                                    <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'></path>\n                                </svg>\n                                <time datetime='{{ news_item.date }}'>{{ news_item.date|date:'d M Y' }}</time>\n                            </div>\n                            \n                            <h2 class='text-xl font-semibold leading-tight'>{{ news_item.title }}</h2>\n                            \n                            <p class='text-muted-foreground'>{{ news_item.summary }}</p>\n                            \n                            <div class='pt-2'>\n                                <a href='#' class='inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-800'>\n                                    Leggi di più\n                                    <svg class='w-4 h-4 ml-1' fill='none' stroke='currentColor' viewBox='0 0 24 24'>\n                                        <path stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 5l7 7-7 7'></path>\n                                    </svg>\n                                </a>\n                            </div>\n                        </div>\n                    </article>\n                    \"\n                %}\n            {% endfor %}\n        </div>\n    </div>\n    \n    <!-- Newsletter Subscription -->\n    <div class=\"max-w-4xl mx-auto\">\n        {% ui_card \n            title=\"Rimani Aggiornato\"\n            content=\"\n            <div class='space-y-6'>\n                <p class='text-muted-foreground text-center'>\n                    Iscriviti alla nostra newsletter per ricevere aggiornamenti su nuove funzionalità, \n                    modifiche normative e best practice del settore.\n                </p>\n                \n                <form class='flex flex-col sm:flex-row gap-4 max-w-md mx-auto'>\n                    <div class='flex-1'>\n                        <input \n                            type='email' \n                            placeholder='La tua email' \n                            class='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'\n                            required\n                        >\n                    </div>\n                    <button \n                        type='submit'\n                        class='inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2'\n                    >\n                        Iscriviti\n                    </button>\n                </form>\n                \n                <p class='text-xs text-muted-foreground text-center'>\n                    Rispettiamo la tua privacy. Nessuno spam, solo contenuti di valore.\n                </p>\n            </div>\n            \"\n        %}\n    </div>\n    \n    <!-- Related Links -->\n    <div class=\"max-w-6xl mx-auto\">\n        <h2 class=\"text-2xl font-bold text-center mb-6\">Risorse Utili</h2>\n        <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n            {% ui_card \n                title=\"Documentazione\"\n                content=\"\n                <div class='space-y-2'>\n                    <p class='text-sm text-muted-foreground'>Guide complete per utilizzare ExProject</p>\n                    <a href='#' class='text-sm text-blue-600 hover:text-blue-800'>Vai alla documentazione →</a>\n                </div>\n                \"\n            %}\n            \n            {% ui_card \n                title=\"Video Tutorial\"\n                content=\"\n                <div class='space-y-2'>\n                    <p class='text-sm text-muted-foreground'>Tutorial video passo-passo</p>\n                    <a href='#' class='text-sm text-blue-600 hover:text-blue-800'>Guarda i video →</a>\n                </div>\n                \"\n            %}\n            \n            {% ui_card \n                title=\"FAQ\"\n                content=\"\n                <div class='space-y-2'>\n                    <p class='text-sm text-muted-foreground'>Risposte alle domande più frequenti</p>\n                    <a href='#' class='text-sm text-blue-600 hover:text-blue-800'>Leggi le FAQ →</a>\n                </div>\n                \"\n            %}\n            \n            {% ui_card \n                title=\"Supporto\"\n                content=\"\n                <div class='space-y-2'>\n                    <p class='text-sm text-muted-foreground'>Contatta il nostro team di supporto</p>\n                    <a href='{% url 'public:contacts' %}' class='text-sm text-blue-600 hover:text-blue-800'>Contattaci →</a>\n                </div>\n                \"\n            %}\n        </div>\n    </div>\n</div>\n{% endblock %}\n"}