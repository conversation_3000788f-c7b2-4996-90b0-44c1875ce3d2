{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/dialog.html"}, "modifiedCode": "{# Componente Dialog/Modal #}\n<div x-data=\"{ open: false }\">\n    <!-- Trigger -->\n    <button \n        @click=\"open = true\"\n        class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90\">\n        {{ trigger_text }}\n    </button>\n    \n    <!-- Modal -->\n    <div \n        x-show=\"open\"\n        x-transition:enter=\"transition ease-out duration-300\"\n        x-transition:enter-start=\"opacity-0\"\n        x-transition:enter-end=\"opacity-100\"\n        x-transition:leave=\"transition ease-in duration-200\"\n        x-transition:leave-start=\"opacity-100\"\n        x-transition:leave-end=\"opacity-0\"\n        class=\"fixed inset-0 z-50 bg-background/80 backdrop-blur-sm\"\n        @click=\"open = false\">\n        \n        <div \n            @click.stop\n            class=\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 rounded-lg\">\n            \n            <!-- Header -->\n            <div class=\"flex flex-col space-y-1.5 text-center sm:text-left\">\n                <h2 class=\"text-lg font-semibold leading-none tracking-tight\">{{ title }}</h2>\n            </div>\n            \n            <!-- Content -->\n            <div class=\"text-sm text-muted-foreground\">\n                {{ content }}\n            </div>\n            \n            <!-- Footer -->\n            <div class=\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\">\n                <button \n                    @click=\"open = false\"\n                    class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground\">\n                    Chiudi\n                </button>\n            </div>\n        </div>\n    </div>\n</div>\n"}