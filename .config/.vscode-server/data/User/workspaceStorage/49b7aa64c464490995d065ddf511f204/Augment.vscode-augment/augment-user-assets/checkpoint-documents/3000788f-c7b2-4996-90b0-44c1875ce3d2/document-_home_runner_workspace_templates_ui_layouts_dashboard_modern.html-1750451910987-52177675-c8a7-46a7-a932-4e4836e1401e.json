{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/layouts/dashboard_modern.html"}, "originalCode": "{% extends 'ui/base_modern.html' %}\n\n{% block title %}Dashboard Moderna - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"flex-1 space-y-4 p-8 pt-6\">\n    <!-- Header della dashboard -->\n    <div class=\"flex items-center justify-between space-y-2\">\n        <h2 class=\"text-3xl font-bold tracking-tight\">Dashboard Moderna</h2>\n        <div class=\"flex items-center space-x-2\">\n            <a href=\"/projects/create/\" class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4 mr-2\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12 4.5v15m7.5-7.5h-15\" />\n                </svg>\n                Nuovo Progetto\n            </a>\n            <button class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4 mr-2\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3\" />\n                </svg>\n                Esporta\n            </button>\n        </div>\n    </div>\n\n    <!-- Statistiche principali -->\n    <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n        <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n            <div class=\"flex flex-col space-y-1.5 p-6\">\n                <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Progetti Attivi</h3>\n            </div>\n            <div class=\"p-6 pt-0\">\n                <div class=\"text-2xl font-bold\">{{ stats.active_projects|default:0 }}</div>\n                <p class=\"text-xs text-muted-foreground\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"inline h-3 w-3 mr-1\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941\" />\n                    </svg>\n                    +20.1% dal mese scorso\n                </p>\n            </div>\n        </div>\n\n        <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n            <div class=\"flex flex-col space-y-1.5 p-6\">\n                <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Pratiche in Corso</h3>\n            </div>\n            <div class=\"p-6 pt-0\">\n                <div class=\"text-2xl font-bold\">{{ stats.pending_workflows|default:0 }}</div>\n                <p class=\"text-xs text-muted-foreground\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"inline h-3 w-3 mr-1\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                    +180.1% dal mese scorso\n                </p>\n            </div>\n        </div>\n\n        <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n            <div class=\"flex flex-col space-y-1.5 p-6\">\n                <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Documenti Generati</h3>\n            </div>\n            <div class=\"p-6 pt-0\">\n                <div class=\"text-2xl font-bold\">{{ stats.documents_generated|default:0 }}</div>\n                <p class=\"text-xs text-muted-foreground\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"inline h-3 w-3 mr-1\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\" />\n                    </svg>\n                    +19% dal mese scorso\n                </p>\n            </div>\n        </div>\n\n        <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n            <div class=\"flex flex-col space-y-1.5 p-6\">\n                <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Valore Totale</h3>\n            </div>\n            <div class=\"p-6 pt-0\">\n                <div class=\"text-2xl font-bold\">€{{ stats.total_value|default:0|floatformat:0 }}</div>\n                <p class=\"text-xs text-muted-foreground\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"inline h-3 w-3 mr-1\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M14.25 7.756a4.5 4.5 0 100 8.488M7.5 10.5h5.25m-5.25 3h5.25M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                    +201 dal mese scorso\n                </p>\n            </div>\n        </div>\n    </div>\n    \n    <!-- Grafici e tabelle -->\n    <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-7\">\n        <!-- Grafico principale -->\n        {% ui_card title=\"Panoramica\" class=\"col-span-4\" %}\n            {% block card_content %}\n            <div class=\"h-[200px] flex items-center justify-center text-muted-foreground\">\n                <div class=\"text-center\">\n                    {% ui_icon 'chart' size=\"12\" class=\"mx-auto mb-2 text-muted-foreground/50\" %}\n                    <p>Grafico in arrivo</p>\n                </div>\n            </div>\n            {% endblock %}\n        {% endui_card %}\n        \n        <!-- Attività recenti -->\n        {% ui_card title=\"Attività Recenti\" class=\"col-span-3\" %}\n            {% block card_content %}\n            <div class=\"space-y-8\">\n                {% for activity in recent_activities|slice:\":5\" %}\n                <div class=\"flex items-center\">\n                    <div class=\"h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center\">\n                        {% ui_icon activity.icon|default:'activity' size=\"4\" class=\"text-primary\" %}\n                    </div>\n                    <div class=\"ml-4 space-y-1\">\n                        <p class=\"text-sm font-medium leading-none\">{{ activity.title }}</p>\n                        <p class=\"text-sm text-muted-foreground\">{{ activity.description }}</p>\n                    </div>\n                    <div class=\"ml-auto font-medium text-sm text-muted-foreground\">\n                        {{ activity.created_at|timesince }}\n                    </div>\n                </div>\n                {% empty %}\n                <div class=\"text-center text-muted-foreground py-8\">\n                    {% ui_icon 'clock' size=\"8\" class=\"mx-auto mb-2 text-muted-foreground/50\" %}\n                    <p>Nessuna attività recente</p>\n                </div>\n                {% endfor %}\n            </div>\n            {% endblock %}\n        {% endui_card %}\n    </div>\n    \n    <!-- Progetti recenti -->\n    {% ui_card title=\"Progetti Recenti\" %}\n        {% block card_content %}\n        {% if recent_projects %}\n        {% ui_table headers=table_headers rows=table_rows %}\n        {% else %}\n        <div class=\"text-center py-8\">\n            <div class=\"mx-auto h-12 w-12 text-muted-foreground/50\">\n                {% ui_icon 'folder' size=\"12\" %}\n            </div>\n            <h3 class=\"mt-2 text-sm font-semibold text-foreground\">Nessun progetto</h3>\n            <p class=\"mt-1 text-sm text-muted-foreground\">Inizia creando il tuo primo progetto.</p>\n            <div class=\"mt-6\">\n                {% ui_button \"Nuovo Progetto\" variant=\"default\" icon_left=\"plus\" %}\n            </div>\n        </div>\n        {% endif %}\n        {% endblock %}\n    {% endui_card %}\n</div>\n\n<!-- Script per interattività -->\n<script>\ndocument.addEventListener('DOMContentLoaded', function() {\n    // Aggiorna statistiche ogni 30 secondi\n    setInterval(function() {\n        htmx.ajax('GET', '{% url \"core:dashboard_stats\" %}', {\n            target: '.grid.gap-4.md\\\\:grid-cols-2.lg\\\\:grid-cols-4',\n            swap: 'innerHTML'\n        });\n    }, 30000);\n    \n    // Gestione notifiche real-time\n    if ('WebSocket' in window) {\n        const ws = new WebSocket('ws://localhost:8000/ws/notifications/');\n        ws.onmessage = function(event) {\n            const data = JSON.parse(event.data);\n            // Mostra notifica\n            showNotification(data.message, data.type);\n        };\n    }\n});\n\nfunction showNotification(message, type = 'info') {\n    // Implementazione notifiche toast\n    const notification = document.createElement('div');\n    notification.className = `fixed top-4 right-4 z-50 rounded-lg border bg-card text-card-foreground shadow-lg p-4 max-w-sm`;\n    notification.innerHTML = `\n        <div class=\"flex items-start space-x-2\">\n            <div class=\"flex-shrink-0\">\n                ${type === 'success' ? '✓' : type === 'error' ? '✗' : 'ℹ'}\n            </div>\n            <div class=\"flex-1\">\n                <p class=\"text-sm font-medium\">${message}</p>\n            </div>\n            <button onclick=\"this.parentElement.parentElement.remove()\" class=\"flex-shrink-0 text-muted-foreground hover:text-foreground\">\n                ×\n            </button>\n        </div>\n    `;\n    \n    document.body.appendChild(notification);\n    \n    // Auto-remove dopo 5 secondi\n    setTimeout(() => {\n        if (notification.parentElement) {\n            notification.remove();\n        }\n    }, 5000);\n}\n</script>\n{% endblock %}\n", "modifiedCode": "{% extends 'ui/base_modern.html' %}\n\n{% block title %}Dashboard Moderna - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"flex-1 space-y-4 p-8 pt-6\">\n    <!-- Header della dashboard -->\n    <div class=\"flex items-center justify-between space-y-2\">\n        <h2 class=\"text-3xl font-bold tracking-tight\">Dashboard Moderna</h2>\n        <div class=\"flex items-center space-x-2\">\n            <a href=\"/projects/create/\" class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4 mr-2\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12 4.5v15m7.5-7.5h-15\" />\n                </svg>\n                Nuovo Progetto\n            </a>\n            <button class=\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4 mr-2\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M3 16.5v2.25A2.25 2.25 0 005.25 21h13.5A2.25 2.25 0 0021 18.75V16.5M16.5 12L12 16.5m0 0L7.5 12m4.5 4.5V3\" />\n                </svg>\n                Esporta\n            </button>\n        </div>\n    </div>\n\n    <!-- Statistiche principali -->\n    <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n        <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n            <div class=\"flex flex-col space-y-1.5 p-6\">\n                <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Progetti Attivi</h3>\n            </div>\n            <div class=\"p-6 pt-0\">\n                <div class=\"text-2xl font-bold\">{{ stats.active_projects|default:0 }}</div>\n                <p class=\"text-xs text-muted-foreground\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"inline h-3 w-3 mr-1\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941\" />\n                    </svg>\n                    +20.1% dal mese scorso\n                </p>\n            </div>\n        </div>\n\n        <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n            <div class=\"flex flex-col space-y-1.5 p-6\">\n                <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Pratiche in Corso</h3>\n            </div>\n            <div class=\"p-6 pt-0\">\n                <div class=\"text-2xl font-bold\">{{ stats.pending_workflows|default:0 }}</div>\n                <p class=\"text-xs text-muted-foreground\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"inline h-3 w-3 mr-1\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                    +180.1% dal mese scorso\n                </p>\n            </div>\n        </div>\n\n        <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n            <div class=\"flex flex-col space-y-1.5 p-6\">\n                <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Documenti Generati</h3>\n            </div>\n            <div class=\"p-6 pt-0\">\n                <div class=\"text-2xl font-bold\">{{ stats.documents_generated|default:0 }}</div>\n                <p class=\"text-xs text-muted-foreground\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"inline h-3 w-3 mr-1\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z\" />\n                    </svg>\n                    +19% dal mese scorso\n                </p>\n            </div>\n        </div>\n\n        <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n            <div class=\"flex flex-col space-y-1.5 p-6\">\n                <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Valore Totale</h3>\n            </div>\n            <div class=\"p-6 pt-0\">\n                <div class=\"text-2xl font-bold\">€{{ stats.total_value|default:0|floatformat:0 }}</div>\n                <p class=\"text-xs text-muted-foreground\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"inline h-3 w-3 mr-1\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M14.25 7.756a4.5 4.5 0 100 8.488M7.5 10.5h5.25m-5.25 3h5.25M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                    +201 dal mese scorso\n                </p>\n            </div>\n        </div>\n    </div>\n    \n    <!-- Grafici e tabelle -->\n    <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-7\">\n        <!-- Grafico principale -->\n        {% ui_card title=\"Panoramica\" class=\"col-span-4\" %}\n            {% block card_content %}\n            <div class=\"h-[200px] flex items-center justify-center text-muted-foreground\">\n                <div class=\"text-center\">\n                    {% ui_icon 'chart' size=\"12\" class=\"mx-auto mb-2 text-muted-foreground/50\" %}\n                    <p>Grafico in arrivo</p>\n                </div>\n            </div>\n            {% endblock %}\n        {% endui_card %}\n        \n        <!-- Attività recenti -->\n        {% ui_card title=\"Attività Recenti\" class=\"col-span-3\" %}\n            {% block card_content %}\n            <div class=\"space-y-8\">\n                {% for activity in recent_activities|slice:\":5\" %}\n                <div class=\"flex items-center\">\n                    <div class=\"h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center\">\n                        {% ui_icon activity.icon|default:'activity' size=\"4\" class=\"text-primary\" %}\n                    </div>\n                    <div class=\"ml-4 space-y-1\">\n                        <p class=\"text-sm font-medium leading-none\">{{ activity.title }}</p>\n                        <p class=\"text-sm text-muted-foreground\">{{ activity.description }}</p>\n                    </div>\n                    <div class=\"ml-auto font-medium text-sm text-muted-foreground\">\n                        {{ activity.created_at|timesince }}\n                    </div>\n                </div>\n                {% empty %}\n                <div class=\"text-center text-muted-foreground py-8\">\n                    {% ui_icon 'clock' size=\"8\" class=\"mx-auto mb-2 text-muted-foreground/50\" %}\n                    <p>Nessuna attività recente</p>\n                </div>\n                {% endfor %}\n            </div>\n            {% endblock %}\n        {% endui_card %}\n    </div>\n    \n    <!-- Progetti recenti -->\n    {% ui_card title=\"Progetti Recenti\" %}\n        {% block card_content %}\n        {% if recent_projects %}\n        {% ui_table headers=table_headers rows=table_rows %}\n        {% else %}\n        <div class=\"text-center py-8\">\n            <div class=\"mx-auto h-12 w-12 text-muted-foreground/50\">\n                {% ui_icon 'folder' size=\"12\" %}\n            </div>\n            <h3 class=\"mt-2 text-sm font-semibold text-foreground\">Nessun progetto</h3>\n            <p class=\"mt-1 text-sm text-muted-foreground\">Inizia creando il tuo primo progetto.</p>\n            <div class=\"mt-6\">\n                {% ui_button \"Nuovo Progetto\" variant=\"default\" icon_left=\"plus\" %}\n            </div>\n        </div>\n        {% endif %}\n        {% endblock %}\n    {% endui_card %}\n</div>\n\n<!-- Script per interattività -->\n<script>\ndocument.addEventListener('DOMContentLoaded', function() {\n    // Aggiorna statistiche ogni 30 secondi\n    setInterval(function() {\n        htmx.ajax('GET', '{% url \"core:dashboard_stats\" %}', {\n            target: '.grid.gap-4.md\\\\:grid-cols-2.lg\\\\:grid-cols-4',\n            swap: 'innerHTML'\n        });\n    }, 30000);\n    \n    // Gestione notifiche real-time\n    if ('WebSocket' in window) {\n        const ws = new WebSocket('ws://localhost:8000/ws/notifications/');\n        ws.onmessage = function(event) {\n            const data = JSON.parse(event.data);\n            // Mostra notifica\n            showNotification(data.message, data.type);\n        };\n    }\n});\n\nfunction showNotification(message, type = 'info') {\n    // Implementazione notifiche toast\n    const notification = document.createElement('div');\n    notification.className = `fixed top-4 right-4 z-50 rounded-lg border bg-card text-card-foreground shadow-lg p-4 max-w-sm`;\n    notification.innerHTML = `\n        <div class=\"flex items-start space-x-2\">\n            <div class=\"flex-shrink-0\">\n                ${type === 'success' ? '✓' : type === 'error' ? '✗' : 'ℹ'}\n            </div>\n            <div class=\"flex-1\">\n                <p class=\"text-sm font-medium\">${message}</p>\n            </div>\n            <button onclick=\"this.parentElement.parentElement.remove()\" class=\"flex-shrink-0 text-muted-foreground hover:text-foreground\">\n                ×\n            </button>\n        </div>\n    `;\n    \n    document.body.appendChild(notification);\n    \n    // Auto-remove dopo 5 secondi\n    setTimeout(() => {\n        if (notification.parentElement) {\n            notification.remove();\n        }\n    }, 5000);\n}\n</script>\n{% endblock %}\n"}