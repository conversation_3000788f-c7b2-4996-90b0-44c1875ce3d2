{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/table.html"}, "modifiedCode": "{% load ui_components %}\n{# Componente Table ispirato a shadcn/ui #}\n<div class=\"relative w-full overflow-auto\">\n    <table class=\"w-full caption-bottom text-sm {% if attrs.class %}{{ attrs.class }}{% endif %}\"\n           {% for key, value in attrs.items %}{% if key != 'class' %}{{ key }}=\"{{ value }}\"{% endif %}{% endfor %}>\n        \n        {% if headers %}\n        <thead class=\"[&_tr]:border-b\">\n            <tr class=\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\">\n                {% for header in headers %}\n                <th class=\"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\">\n                    {{ header }}\n                </th>\n                {% endfor %}\n            </tr>\n        </thead>\n        {% endif %}\n        \n        <tbody class=\"[&_tr:last-child]:border-0\">\n            {% for row in rows %}\n            <tr class=\"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\">\n                {% for cell in row %}\n                <td class=\"p-4 align-middle [&:has([role=checkbox])]:pr-0\">\n                    {{ cell }}\n                </td>\n                {% endfor %}\n            </tr>\n            {% empty %}\n            <tr>\n                <td colspan=\"{{ headers|length }}\" class=\"p-8 text-center text-muted-foreground\">\n                    <div class=\"flex flex-col items-center space-y-2\">\n                        {% ui_icon 'search' size=\"8\" class=\"text-muted-foreground/50\" %}\n                        <p>Nessun dato disponibile</p>\n                    </div>\n                </td>\n            </tr>\n            {% endfor %}\n        </tbody>\n    </table>\n</div>\n"}