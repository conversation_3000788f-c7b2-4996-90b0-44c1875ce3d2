{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/avatar.html"}, "modifiedCode": "{# Componente Avatar #}\n<div class=\"relative flex shrink-0 overflow-hidden rounded-full\n    {% if size == 'sm' %}h-8 w-8{% elif size == 'lg' %}h-12 w-12{% else %}h-10 w-10{% endif %}\">\n    {% if src %}\n    <img src=\"{{ src }}\" alt=\"{{ alt }}\" class=\"aspect-square h-full w-full object-cover\">\n    {% else %}\n    <div class=\"flex h-full w-full items-center justify-center rounded-full bg-muted\">\n        {% if fallback %}\n        <span class=\"text-sm font-medium text-muted-foreground\">{{ fallback }}</span>\n        {% else %}\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4 text-muted-foreground\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z\" />\n        </svg>\n        {% endif %}\n    </div>\n    {% endif %}\n</div>\n"}