{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components_test.html"}, "modifiedCode": "{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}Test Componenti - Design System{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <h1 class=\"text-3xl font-bold mb-8\">🧪 Test Componenti Design System</h1>\n    \n    <!-- Buttons -->\n    <section class=\"mb-12\">\n        <h2 class=\"text-2xl font-bold mb-4\">Buttons</h2>\n        <div class=\"flex flex-wrap gap-2 mb-4\">\n            {% ui_button \"Primary\" variant=\"default\" %}\n            {% ui_button \"Secondary\" variant=\"secondary\" %}\n            {% ui_button \"Outline\" variant=\"outline\" %}\n            {% ui_button \"Ghost\" variant=\"ghost\" %}\n            {% ui_button \"Destructive\" variant=\"destructive\" %}\n        </div>\n        <div class=\"flex flex-wrap gap-2\">\n            {% ui_button \"Small\" variant=\"default\" size=\"sm\" %}\n            {% ui_button \"Default\" variant=\"default\" size=\"default\" %}\n            {% ui_button \"Large\" variant=\"default\" size=\"lg\" %}\n        </div>\n    </section>\n    \n    <!-- Cards -->\n    <section class=\"mb-12\">\n        <h2 class=\"text-2xl font-bold mb-4\">Cards</h2>\n        <div class=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n            {% ui_card title=\"Card Semplice\" description=\"Descrizione della card\" content=\"<p>Contenuto della card con <strong>HTML</strong>.</p>\" %}\n            \n            {% ui_card title=\"Card con Footer\" description=\"Card con footer\" content=\"<p>Contenuto principale</p>\" footer=\"<button class='btn btn-primary'>Azione</button>\" %}\n            \n            {% ui_card title=\"Solo Titolo\" content=\"<div class='text-center'><h4>Contenuto centrato</h4><p>Senza descrizione</p></div>\" %}\n        </div>\n    </section>\n    \n    <!-- Badges -->\n    <section class=\"mb-12\">\n        <h2 class=\"text-2xl font-bold mb-4\">Badges</h2>\n        <div class=\"flex flex-wrap gap-2\">\n            {% ui_badge \"Default\" variant=\"default\" %}\n            {% ui_badge \"Secondary\" variant=\"secondary\" %}\n            {% ui_badge \"Outline\" variant=\"outline\" %}\n            {% ui_badge \"Destructive\" variant=\"destructive\" %}\n            {% ui_badge \"Con Icona\" variant=\"default\" icon=True %}\n            {% ui_badge \"Removable\" variant=\"secondary\" removable=True %}\n        </div>\n    </section>\n    \n    <!-- Alerts -->\n    <section class=\"mb-12\">\n        <h2 class=\"text-2xl font-bold mb-4\">Alerts</h2>\n        <div class=\"space-y-4\">\n            {% ui_alert \"Questo è un messaggio informativo\" variant=\"default\" title=\"Informazione\" %}\n            {% ui_alert \"Operazione completata con successo!\" variant=\"success\" title=\"Successo\" %}\n            {% ui_alert \"Attenzione: verifica i dati inseriti\" variant=\"warning\" title=\"Attenzione\" %}\n            {% ui_alert \"Si è verificato un errore\" variant=\"destructive\" title=\"Errore\" dismissible=True %}\n        </div>\n    </section>\n    \n    <!-- Inputs -->\n    <section class=\"mb-12\">\n        <h2 class=\"text-2xl font-bold mb-4\">Form Elements</h2>\n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 max-w-4xl\">\n            {% ui_input \"name\" label=\"Nome Completo\" placeholder=\"Inserisci il tuo nome\" required=True %}\n            {% ui_input \"email\" label=\"Email\" placeholder=\"<EMAIL>\" type=\"email\" %}\n            {% ui_input \"password\" label=\"Password\" placeholder=\"Password sicura\" type=\"password\" help_text=\"Minimo 8 caratteri\" %}\n            {% ui_input \"error_field\" label=\"Campo con Errore\" placeholder=\"Valore non valido\" error=\"Questo campo è obbligatorio\" %}\n        </div>\n    </section>\n    \n    <!-- Select -->\n    <section class=\"mb-12\">\n        <h2 class=\"text-2xl font-bold mb-4\">Select</h2>\n        <div class=\"max-w-sm\">\n            {% ui_select \"country\" options=countries label=\"Paese\" %}\n        </div>\n    </section>\n    \n    <!-- Progress -->\n    <section class=\"mb-12\">\n        <h2 class=\"text-2xl font-bold mb-4\">Progress</h2>\n        <div class=\"space-y-4 max-w-md\">\n            {% ui_progress value=25 %}\n            {% ui_progress value=50 %}\n            {% ui_progress value=75 %}\n            {% ui_progress value=100 %}\n        </div>\n    </section>\n    \n    <!-- Skeleton -->\n    <section class=\"mb-12\">\n        <h2 class=\"text-2xl font-bold mb-4\">Skeleton Loading</h2>\n        <div class=\"space-y-2 max-w-md\">\n            {% ui_skeleton width=\"100%\" height=\"20px\" %}\n            {% ui_skeleton width=\"80%\" height=\"20px\" %}\n            {% ui_skeleton width=\"60%\" height=\"20px\" %}\n        </div>\n    </section>\n    \n    <!-- Separator -->\n    <section class=\"mb-12\">\n        <h2 class=\"text-2xl font-bold mb-4\">Separator</h2>\n        <div class=\"space-y-4\">\n            <p>Testo sopra</p>\n            {% ui_separator %}\n            <p>Testo sotto</p>\n        </div>\n    </section>\n    \n    <!-- Tabs -->\n    <section class=\"mb-12\">\n        <h2 class=\"text-2xl font-bold mb-4\">Tabs</h2>\n        {% ui_tabs tabs=tab_data %}\n    </section>\n    \n    <!-- Dialog -->\n    <section class=\"mb-12\">\n        <h2 class=\"text-2xl font-bold mb-4\">Dialog/Modal</h2>\n        {% ui_dialog title=\"Conferma Azione\" content=\"Sei sicuro di voler procedere?\" trigger_text=\"Apri Modal\" %}\n    </section>\n</div>\n{% endblock %}\n"}