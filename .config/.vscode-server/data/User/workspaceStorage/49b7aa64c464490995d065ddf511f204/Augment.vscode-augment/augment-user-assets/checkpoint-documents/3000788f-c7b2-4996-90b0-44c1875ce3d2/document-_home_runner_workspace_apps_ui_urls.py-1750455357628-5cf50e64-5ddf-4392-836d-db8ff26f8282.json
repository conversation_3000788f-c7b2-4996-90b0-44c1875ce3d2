{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/urls.py"}, "originalCode": "from django.urls import path\nfrom . import views\n\napp_name = 'ui'\n\nurlpatterns = [\n    # Viste principali\n    path('components/', views.ComponentLibraryView.as_view(), name='component_library'),\n    path('themes/preview/', views.ThemePreviewView.as_view(), name='theme_preview'),\n    path('themes/preview/<slug:theme_slug>/', views.ThemePreviewView.as_view(), name='theme_preview_specific'),\n    path('docs/', views.DesignSystemDocsView.as_view(), name='design_system_docs'),\n    \n    # API endpoints\n    path('api/components/<slug:component_slug>/render/', views.component_render_api, name='component_render_api'),\n    path('api/themes/<slug:theme_slug>/export/', views.theme_export_api, name='theme_export_api'),\n    path('api/themes/import/', views.theme_import_api, name='theme_import_api'),\n    \n    # CSS dinamico per white label\n    path('css/theme.css', views.ThemeCSSView.as_view(), name='theme_css'),\n    path('css/theme/<slug:client_slug>.css', views.ThemeCSSView.as_view(), name='client_theme_css'),\n]\n", "modifiedCode": "from django.urls import path\nfrom . import views\n\napp_name = 'ui'\n\nurlpatterns = [\n    # Viste principali\n    path('components/', views.ComponentLibraryView.as_view(), name='component_library'),\n    path('themes/preview/', views.ThemePreviewView.as_view(), name='theme_preview'),\n    path('themes/preview/<slug:theme_slug>/', views.ThemePreviewView.as_view(), name='theme_preview_specific'),\n    path('docs/', views.DesignSystemDocsView.as_view(), name='design_system_docs'),\n    path('components/test/', views.components_test, name='components_test'),\n    \n    # API endpoints\n    path('api/components/<slug:component_slug>/render/', views.component_render_api, name='component_render_api'),\n    path('api/themes/<slug:theme_slug>/export/', views.theme_export_api, name='theme_export_api'),\n    path('api/themes/import/', views.theme_import_api, name='theme_import_api'),\n    \n    # CSS dinamico per white label\n    path('css/theme.css', views.ThemeCSSView.as_view(), name='theme_css'),\n    path('css/theme/<slug:client_slug>.css', views.ThemeCSSView.as_view(), name='client_theme_css'),\n]\n"}