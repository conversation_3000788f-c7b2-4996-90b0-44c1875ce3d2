{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/layouts/dashboard_modern.html"}, "originalCode": "{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}Dashboard - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"flex-1 space-y-4 p-8 pt-6\">\n    <!-- Header della dashboard -->\n    <div class=\"flex items-center justify-between space-y-2\">\n        <h2 class=\"text-3xl font-bold tracking-tight\">Dashboard</h2>\n        <div class=\"flex items-center space-x-2\">\n            {% ui_button \"Nuovo Progetto\" variant=\"default\" icon_left=\"plus\" %}\n            {% ui_button \"Esporta\" variant=\"outline\" icon_left=\"download\" %}\n        </div>\n    </div>\n    \n    <!-- Statistiche principali -->\n    <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n        <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n            <div class=\"flex flex-col space-y-1.5 p-6\">\n                <h3 class=\"text-2xl font-semibold leading-none tracking-tight\"><PERSON><PERSON><PERSON></h3>\n            </div>\n            <div class=\"p-6 pt-0\">\n                <div class=\"text-2xl font-bold\">{{ stats.active_projects|default:0 }}</div>\n                <p class=\"text-xs text-muted-foreground\">\n                    +20.1% dal mese scorso\n                </p>\n            </div>\n        </div>\n        \n        {% ui_card title=\"Pratiche in Corso\" %}\n            {% block card_content %}\n            <div class=\"text-2xl font-bold\">{{ stats.pending_workflows|default:0 }}</div>\n            <p class=\"text-xs text-muted-foreground\">\n                +180.1% dal mese scorso\n            </p>\n            {% endblock %}\n        {% endui_card %}\n        \n        {% ui_card title=\"Documenti Generati\" %}\n            {% block card_content %}\n            <div class=\"text-2xl font-bold\">{{ stats.documents_generated|default:0 }}</div>\n            <p class=\"text-xs text-muted-foreground\">\n                +19% dal mese scorso\n            </p>\n            {% endblock %}\n        {% endui_card %}\n        \n        {% ui_card title=\"Valore Totale\" %}\n            {% block card_content %}\n            <div class=\"text-2xl font-bold\">€{{ stats.total_value|default:0|floatformat:0 }}</div>\n            <p class=\"text-xs text-muted-foreground\">\n                +201 dal mese scorso\n            </p>\n            {% endblock %}\n        {% endui_card %}\n    </div>\n    \n    <!-- Grafici e tabelle -->\n    <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-7\">\n        <!-- Grafico principale -->\n        {% ui_card title=\"Panoramica\" class=\"col-span-4\" %}\n            {% block card_content %}\n            <div class=\"h-[200px] flex items-center justify-center text-muted-foreground\">\n                <div class=\"text-center\">\n                    {% ui_icon 'chart' size=\"12\" class=\"mx-auto mb-2 text-muted-foreground/50\" %}\n                    <p>Grafico in arrivo</p>\n                </div>\n            </div>\n            {% endblock %}\n        {% endui_card %}\n        \n        <!-- Attività recenti -->\n        {% ui_card title=\"Attività Recenti\" class=\"col-span-3\" %}\n            {% block card_content %}\n            <div class=\"space-y-8\">\n                {% for activity in recent_activities|slice:\":5\" %}\n                <div class=\"flex items-center\">\n                    <div class=\"h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center\">\n                        {% ui_icon activity.icon|default:'activity' size=\"4\" class=\"text-primary\" %}\n                    </div>\n                    <div class=\"ml-4 space-y-1\">\n                        <p class=\"text-sm font-medium leading-none\">{{ activity.title }}</p>\n                        <p class=\"text-sm text-muted-foreground\">{{ activity.description }}</p>\n                    </div>\n                    <div class=\"ml-auto font-medium text-sm text-muted-foreground\">\n                        {{ activity.created_at|timesince }}\n                    </div>\n                </div>\n                {% empty %}\n                <div class=\"text-center text-muted-foreground py-8\">\n                    {% ui_icon 'clock' size=\"8\" class=\"mx-auto mb-2 text-muted-foreground/50\" %}\n                    <p>Nessuna attività recente</p>\n                </div>\n                {% endfor %}\n            </div>\n            {% endblock %}\n        {% endui_card %}\n    </div>\n    \n    <!-- Progetti recenti -->\n    {% ui_card title=\"Progetti Recenti\" %}\n        {% block card_content %}\n        {% if recent_projects %}\n        {% ui_table headers=table_headers rows=table_rows %}\n        {% else %}\n        <div class=\"text-center py-8\">\n            <div class=\"mx-auto h-12 w-12 text-muted-foreground/50\">\n                {% ui_icon 'folder' size=\"12\" %}\n            </div>\n            <h3 class=\"mt-2 text-sm font-semibold text-foreground\">Nessun progetto</h3>\n            <p class=\"mt-1 text-sm text-muted-foreground\">Inizia creando il tuo primo progetto.</p>\n            <div class=\"mt-6\">\n                {% ui_button \"Nuovo Progetto\" variant=\"default\" icon_left=\"plus\" %}\n            </div>\n        </div>\n        {% endif %}\n        {% endblock %}\n    {% endui_card %}\n</div>\n\n<!-- Script per interattività -->\n<script>\ndocument.addEventListener('DOMContentLoaded', function() {\n    // Aggiorna statistiche ogni 30 secondi\n    setInterval(function() {\n        htmx.ajax('GET', '{% url \"core:dashboard_stats\" %}', {\n            target: '.grid.gap-4.md\\\\:grid-cols-2.lg\\\\:grid-cols-4',\n            swap: 'innerHTML'\n        });\n    }, 30000);\n    \n    // Gestione notifiche real-time\n    if ('WebSocket' in window) {\n        const ws = new WebSocket('ws://localhost:8000/ws/notifications/');\n        ws.onmessage = function(event) {\n            const data = JSON.parse(event.data);\n            // Mostra notifica\n            showNotification(data.message, data.type);\n        };\n    }\n});\n\nfunction showNotification(message, type = 'info') {\n    // Implementazione notifiche toast\n    const notification = document.createElement('div');\n    notification.className = `fixed top-4 right-4 z-50 rounded-lg border bg-card text-card-foreground shadow-lg p-4 max-w-sm`;\n    notification.innerHTML = `\n        <div class=\"flex items-start space-x-2\">\n            <div class=\"flex-shrink-0\">\n                ${type === 'success' ? '✓' : type === 'error' ? '✗' : 'ℹ'}\n            </div>\n            <div class=\"flex-1\">\n                <p class=\"text-sm font-medium\">${message}</p>\n            </div>\n            <button onclick=\"this.parentElement.parentElement.remove()\" class=\"flex-shrink-0 text-muted-foreground hover:text-foreground\">\n                ×\n            </button>\n        </div>\n    `;\n    \n    document.body.appendChild(notification);\n    \n    // Auto-remove dopo 5 secondi\n    setTimeout(() => {\n        if (notification.parentElement) {\n            notification.remove();\n        }\n    }, 5000);\n}\n</script>\n{% endblock %}\n", "modifiedCode": "{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}Dashboard - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"flex-1 space-y-4 p-8 pt-6\">\n    <!-- Header della dashboard -->\n    <div class=\"flex items-center justify-between space-y-2\">\n        <h2 class=\"text-3xl font-bold tracking-tight\">Dashboard</h2>\n        <div class=\"flex items-center space-x-2\">\n            {% ui_button \"Nuovo Progetto\" variant=\"default\" icon_left=\"plus\" %}\n            {% ui_button \"Esporta\" variant=\"outline\" icon_left=\"download\" %}\n        </div>\n    </div>\n    \n    <!-- Statistiche principali -->\n    <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n        <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n            <div class=\"flex flex-col space-y-1.5 p-6\">\n                <h3 class=\"text-2xl font-semibold leading-none tracking-tight\"><PERSON><PERSON><PERSON></h3>\n            </div>\n            <div class=\"p-6 pt-0\">\n                <div class=\"text-2xl font-bold\">{{ stats.active_projects|default:0 }}</div>\n                <p class=\"text-xs text-muted-foreground\">\n                    +20.1% dal mese scorso\n                </p>\n            </div>\n        </div>\n        \n        <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n            <div class=\"flex flex-col space-y-1.5 p-6\">\n                <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Pratiche in Corso</h3>\n            </div>\n            <div class=\"p-6 pt-0\">\n                <div class=\"text-2xl font-bold\">{{ stats.pending_workflows|default:0 }}</div>\n                <p class=\"text-xs text-muted-foreground\">\n                    +180.1% dal mese scorso\n                </p>\n            </div>\n        </div>\n\n        <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n            <div class=\"flex flex-col space-y-1.5 p-6\">\n                <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Documenti Generati</h3>\n            </div>\n            <div class=\"p-6 pt-0\">\n                <div class=\"text-2xl font-bold\">{{ stats.documents_generated|default:0 }}</div>\n                <p class=\"text-xs text-muted-foreground\">\n                    +19% dal mese scorso\n                </p>\n            </div>\n        </div>\n\n        <div class=\"rounded-lg border bg-card text-card-foreground shadow-sm\">\n            <div class=\"flex flex-col space-y-1.5 p-6\">\n                <h3 class=\"text-2xl font-semibold leading-none tracking-tight\">Valore Totale</h3>\n            </div>\n            <div class=\"p-6 pt-0\">\n                <div class=\"text-2xl font-bold\">€{{ stats.total_value|default:0|floatformat:0 }}</div>\n                <p class=\"text-xs text-muted-foreground\">\n                    +201 dal mese scorso\n                </p>\n            </div>\n        </div>\n    </div>\n    \n    <!-- Grafici e tabelle -->\n    <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-7\">\n        <!-- Grafico principale -->\n        {% ui_card title=\"Panoramica\" class=\"col-span-4\" %}\n            {% block card_content %}\n            <div class=\"h-[200px] flex items-center justify-center text-muted-foreground\">\n                <div class=\"text-center\">\n                    {% ui_icon 'chart' size=\"12\" class=\"mx-auto mb-2 text-muted-foreground/50\" %}\n                    <p>Grafico in arrivo</p>\n                </div>\n            </div>\n            {% endblock %}\n        {% endui_card %}\n        \n        <!-- Attività recenti -->\n        {% ui_card title=\"Attività Recenti\" class=\"col-span-3\" %}\n            {% block card_content %}\n            <div class=\"space-y-8\">\n                {% for activity in recent_activities|slice:\":5\" %}\n                <div class=\"flex items-center\">\n                    <div class=\"h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center\">\n                        {% ui_icon activity.icon|default:'activity' size=\"4\" class=\"text-primary\" %}\n                    </div>\n                    <div class=\"ml-4 space-y-1\">\n                        <p class=\"text-sm font-medium leading-none\">{{ activity.title }}</p>\n                        <p class=\"text-sm text-muted-foreground\">{{ activity.description }}</p>\n                    </div>\n                    <div class=\"ml-auto font-medium text-sm text-muted-foreground\">\n                        {{ activity.created_at|timesince }}\n                    </div>\n                </div>\n                {% empty %}\n                <div class=\"text-center text-muted-foreground py-8\">\n                    {% ui_icon 'clock' size=\"8\" class=\"mx-auto mb-2 text-muted-foreground/50\" %}\n                    <p>Nessuna attività recente</p>\n                </div>\n                {% endfor %}\n            </div>\n            {% endblock %}\n        {% endui_card %}\n    </div>\n    \n    <!-- Progetti recenti -->\n    {% ui_card title=\"Progetti Recenti\" %}\n        {% block card_content %}\n        {% if recent_projects %}\n        {% ui_table headers=table_headers rows=table_rows %}\n        {% else %}\n        <div class=\"text-center py-8\">\n            <div class=\"mx-auto h-12 w-12 text-muted-foreground/50\">\n                {% ui_icon 'folder' size=\"12\" %}\n            </div>\n            <h3 class=\"mt-2 text-sm font-semibold text-foreground\">Nessun progetto</h3>\n            <p class=\"mt-1 text-sm text-muted-foreground\">Inizia creando il tuo primo progetto.</p>\n            <div class=\"mt-6\">\n                {% ui_button \"Nuovo Progetto\" variant=\"default\" icon_left=\"plus\" %}\n            </div>\n        </div>\n        {% endif %}\n        {% endblock %}\n    {% endui_card %}\n</div>\n\n<!-- Script per interattività -->\n<script>\ndocument.addEventListener('DOMContentLoaded', function() {\n    // Aggiorna statistiche ogni 30 secondi\n    setInterval(function() {\n        htmx.ajax('GET', '{% url \"core:dashboard_stats\" %}', {\n            target: '.grid.gap-4.md\\\\:grid-cols-2.lg\\\\:grid-cols-4',\n            swap: 'innerHTML'\n        });\n    }, 30000);\n    \n    // Gestione notifiche real-time\n    if ('WebSocket' in window) {\n        const ws = new WebSocket('ws://localhost:8000/ws/notifications/');\n        ws.onmessage = function(event) {\n            const data = JSON.parse(event.data);\n            // Mostra notifica\n            showNotification(data.message, data.type);\n        };\n    }\n});\n\nfunction showNotification(message, type = 'info') {\n    // Implementazione notifiche toast\n    const notification = document.createElement('div');\n    notification.className = `fixed top-4 right-4 z-50 rounded-lg border bg-card text-card-foreground shadow-lg p-4 max-w-sm`;\n    notification.innerHTML = `\n        <div class=\"flex items-start space-x-2\">\n            <div class=\"flex-shrink-0\">\n                ${type === 'success' ? '✓' : type === 'error' ? '✗' : 'ℹ'}\n            </div>\n            <div class=\"flex-1\">\n                <p class=\"text-sm font-medium\">${message}</p>\n            </div>\n            <button onclick=\"this.parentElement.parentElement.remove()\" class=\"flex-shrink-0 text-muted-foreground hover:text-foreground\">\n                ×\n            </button>\n        </div>\n    `;\n    \n    document.body.appendChild(notification);\n    \n    // Auto-remove dopo 5 secondi\n    setTimeout(() => {\n        if (notification.parentElement) {\n            notification.remove();\n        }\n    }, 5000);\n}\n</script>\n{% endblock %}\n"}