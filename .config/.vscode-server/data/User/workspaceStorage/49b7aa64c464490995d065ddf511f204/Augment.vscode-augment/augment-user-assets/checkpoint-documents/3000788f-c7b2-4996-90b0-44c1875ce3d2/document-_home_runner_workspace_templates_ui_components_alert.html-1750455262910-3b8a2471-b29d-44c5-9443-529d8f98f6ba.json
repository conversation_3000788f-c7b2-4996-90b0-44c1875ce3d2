{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/alert.html"}, "originalCode": "{# Componente Alert ispirato a shadcn/ui #}\n<div class=\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\n    {% if variant == 'destructive' %}border-destructive/50 text-destructive{% elif variant == 'warning' %}border-warning/50 text-warning{% elif variant == 'success' %}border-success/50 text-success{% else %}bg-background text-foreground border{% endif %}\n    {% if attrs.class %}{{ attrs.class }}{% endif %}\"\n    {% for key, value in attrs.items %}{% if key != 'class' %}{{ key }}=\"{{ value }}\"{% endif %}{% endfor %}>\n\n    {% if variant == 'destructive' %}\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\" />\n        </svg>\n    {% elif variant == 'warning' %}\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z\" />\n        </svg>\n    {% elif variant == 'success' %}\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n        </svg>\n    {% else %}\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z\" />\n        </svg>\n    {% endif %}\n\n    <div>\n        {% if title %}\n        <h5 class=\"mb-1 font-medium leading-none tracking-tight\">{{ title }}</h5>\n        {% endif %}\n        <div class=\"text-sm [&_p]:leading-relaxed\">{{ message }}</div>\n    </div>\n\n    {% if attrs.dismissible %}\n    <button class=\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none\"\n            onclick=\"this.parentElement.remove()\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M6 18 18 6M6 6l12 12\" />\n        </svg>\n        <span class=\"sr-only\">Chiudi</span>\n    </button>\n    {% endif %}\n</div>\n", "modifiedCode": "{# Componente Alert ispirato a shadcn/ui #}\n<div class=\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\n    {% if variant == 'destructive' %}border-destructive/50 text-destructive{% elif variant == 'warning' %}border-warning/50 text-warning{% elif variant == 'success' %}border-success/50 text-success{% else %}bg-background text-foreground border{% endif %}\n    {% if attrs.class %}{{ attrs.class }}{% endif %}\"\n    {% for key, value in attrs.items %}{% if key != 'class' %}{{ key }}=\"{{ value }}\"{% endif %}{% endfor %}>\n\n    {% if variant == 'destructive' %}\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z\" />\n        </svg>\n    {% elif variant == 'warning' %}\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z\" />\n        </svg>\n    {% elif variant == 'success' %}\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\" />\n        </svg>\n    {% else %}\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"m11.25 11.25.041-.02a.75.75 0 0 1 1.063.852l-.708 2.836a.75.75 0 0 0 1.063.853l.041-.021M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9-3.75h.008v.008H12V8.25Z\" />\n        </svg>\n    {% endif %}\n\n    <div>\n        {% if title %}\n        <h5 class=\"mb-1 font-medium leading-none tracking-tight\">{{ title }}</h5>\n        {% endif %}\n        <div class=\"text-sm [&_p]:leading-relaxed\">{{ message }}</div>\n    </div>\n\n    {% if attrs.dismissible %}\n    <button class=\"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none\"\n            onclick=\"this.parentElement.remove()\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"h-4 w-4\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"M6 18 18 6M6 6l12 12\" />\n        </svg>\n        <span class=\"sr-only\">Chiudi</span>\n    </button>\n    {% endif %}\n</div>\n"}