{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/dashboard.html"}, "originalCode": "{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}Dashboard - ExProject{% endblock %}\n\n\n\n{% block content %}\n<div class=\"flex-1 space-y-4 p-8 pt-6\">\n    <!-- Header Dashboard -->\n    <div class=\"flex items-center justify-between space-y-2\">\n        <h2 class=\"text-3xl font-bold tracking-tight\">Dashboard</h2>\n        <div class=\"flex items-center space-x-2\">\n            {% ui_button \"Nuovo Progetto\" variant=\"default\" hx-get=\"/projects/create/\" hx-target=\"#main-content\" %}\n            {% ui_button \"Altre Azioni\" variant=\"outline\" %}\n        </div>\n    </div>\n\n    <!-- Statistiche principali -->\n    <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n        {% ui_card title=\"Progetti Totali\" content=\"<div class='text-2xl font-bold'>{{ stats.projects_total|default:0 }}</div><p class='text-xs text-muted-foreground'><svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' class='inline h-3 w-3 mr-1'><path stroke-linecap='round' stroke-linejoin='round' d='M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941' /></svg>+20.1% dal mese scorso</p>\" %}\n        \n        <div class=\"dashboard-stat-card bg-white rounded-lg shadow p-6 border-l-4 border-green-500\">\n            <div class=\"flex items-center\">\n                <div class=\"p-3 rounded-full bg-green-100 mr-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-green-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                </div>\n                <div>\n                    <p class=\"text-gray-500 text-sm\">In Lavorazione</p>\n                    <p class=\"text-2xl font-semibold\">{{ stats.projects_active|default:\"0\" }}</p>\n                </div>\n            </div>\n            <div class=\"mt-2\">\n                <a href=\"#\" class=\"text-sm text-green-600 hover:text-green-800\">Gestisci attivi →</a>\n            </div>\n        </div>\n        \n        <div class=\"dashboard-stat-card bg-white rounded-lg shadow p-6 border-l-4 border-yellow-500\">\n            <div class=\"flex items-center\">\n                <div class=\"p-3 rounded-full bg-yellow-100 mr-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-yellow-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\" />\n                    </svg>\n                </div>\n                <div>\n                    <p class=\"text-gray-500 text-sm\">Particelle Totali</p>\n                    <p class=\"text-2xl font-semibold\">{{ stats.parcels_total|default:\"0\" }}</p>\n                </div>\n            </div>\n            <div class=\"mt-2\">\n                <a href=\"#\" class=\"text-sm text-yellow-600 hover:text-yellow-800\">Visualizza mappa →</a>\n            </div>\n        </div>\n        \n        <div class=\"dashboard-stat-card bg-white rounded-lg shadow p-6 border-l-4 border-red-500\">\n            <div class=\"flex items-center\">\n                <div class=\"p-3 rounded-full bg-red-100 mr-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z\" />\n                    </svg>\n                </div>\n                <div>\n                    <p class=\"text-gray-500 text-sm\">In Scadenza</p>\n                    <p class=\"text-2xl font-semibold\">{{ stats.expiring_tasks|default:\"0\" }}</p>\n                </div>\n            </div>\n            <div class=\"mt-2\">\n                <a href=\"#\" class=\"text-sm text-red-600 hover:text-red-800\">Gestisci scadenze →</a>\n            </div>\n        </div>\n    </div>\n\n    <!-- Moduli Principali -->\n    <div class=\"mb-8\">\n        <h2 class=\"text-xl font-semibold text-gray-900 mb-4\">Moduli Operativi</h2>\n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div class=\"module-card bg-white rounded-lg shadow border border-gray-100 overflow-hidden\">\n                <div class=\"h-2 bg-blue-600\"></div>\n                <div class=\"p-6\">\n                    <div class=\"flex items-center mb-4\">\n                        <div class=\"p-2 rounded-full bg-blue-100 mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n                            </svg>\n                        </div>\n                        <h3 class=\"text-lg font-semibold text-gray-900\">Progetti</h3>\n                    </div>\n                    <p class=\"text-gray-600 mb-4\">Gestione completa dei progetti espropriativi: creazione, pianificazione, monitoraggio avanzamento.</p>\n                    <div class=\"flex justify-between\">\n                        <a href=\"#\" hx-get=\"{% url 'projects:list' %}\" hx-target=\"#main-content\" hx-push-url=\"true\"\n                           class=\"text-blue-600 hover:text-blue-800 font-medium\">Vai ai progetti</a>\n                        <span class=\"text-gray-500\">{{ stats.projects_total|default:\"0\" }} progetti</span>\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"module-card bg-white rounded-lg shadow border border-gray-100 overflow-hidden\">\n                <div class=\"h-2 bg-green-600\"></div>\n                <div class=\"p-6\">\n                    <div class=\"flex items-center mb-4\">\n                        <div class=\"p-2 rounded-full bg-green-100 mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-green-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\" />\n                            </svg>\n                        </div>\n                        <h3 class=\"text-lg font-semibold text-gray-900\">Workflow</h3>\n                    </div>\n                    <p class=\"text-gray-600 mb-4\">Automazione dei flussi procedurali secondo DPR 327/2001 con controlli integrati e scadenzario.</p>\n                    <div class=\"flex justify-between\">\n                        <a href=\"#\" hx-get=\"{% url 'workflow:list' %}\" hx-target=\"#main-content\" hx-push-url=\"true\"\n                           class=\"text-green-600 hover:text-green-800 font-medium\">Gestione workflow</a>\n                        <span class=\"text-gray-500\">{{ stats.active_workflows|default:\"0\" }} attivi</span>\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"module-card bg-white rounded-lg shadow border border-gray-100 overflow-hidden\">\n                <div class=\"h-2 bg-purple-600\"></div>\n                <div class=\"p-6\">\n                    <div class=\"flex items-center mb-4\">\n                        <div class=\"p-2 rounded-full bg-purple-100 mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-purple-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2\" />\n                            </svg>\n                        </div>\n                        <h3 class=\"text-lg font-semibold text-gray-900\">Documenti</h3>\n                    </div>\n                    <p class=\"text-gray-600 mb-4\">Gestione documentale con template personalizzabili, generazione automatica e firma digitale.</p>\n                    <div class=\"flex justify-between\">\n                        <a href=\"#\" hx-get=\"{% url 'documents:list' %}\" hx-target=\"#main-content\" hx-push-url=\"true\"\n                           class=\"text-purple-600 hover:text-purple-800 font-medium\">Archivio documenti</a>\n                        <span class=\"text-gray-500\">{{ stats.documents_total|default:\"0\" }} documenti</span>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        <!-- Progetti Recenti -->\n        <div class=\"lg:col-span-2 bg-white rounded-lg shadow\">\n            <div class=\"px-6 py-4 border-b flex justify-between items-center\">\n                <h3 class=\"text-lg font-semibold text-gray-900\">Progetti Recenti</h3>\n                <a href=\"#\" hx-get=\"{% url 'projects:list' %}\" hx-target=\"#main-content\" hx-push-url=\"true\"\n                   class=\"text-sm text-blue-600 hover:text-blue-800\">Vedi tutti</a>\n            </div>\n            <div class=\"p-6\">\n                {% if recent_projects %}\n                <div class=\"overflow-x-auto\">\n                    <table class=\"min-w-full divide-y divide-gray-200\">\n                        <thead>\n                            <tr>\n                                <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Progetto</th>\n                                <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Stato</th>\n                                <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Budget</th>\n                                <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Ente</th>\n                                <th class=\"px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Azioni</th>\n                            </tr>\n                        </thead>\n                        <tbody class=\"bg-white divide-y divide-gray-200\">\n                            {% for project in recent_projects %}\n                            <tr class=\"hover:bg-gray-50\">\n                                <td class=\"px-6 py-4 whitespace-nowrap\">\n                                    <div class=\"flex items-center\">\n                                        <div class=\"flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium\">\n                                            {{ project.name|slice:\":1\" }}\n                                        </div>\n                                        <div class=\"ml-3\">\n                                            <a href=\"#\" class=\"text-sm font-medium text-gray-900 hover:text-blue-600\">{{ project.name }}</a>\n                                            <div class=\"text-xs text-gray-500\">Creato: {{ project.created_at|date:\"d/m/Y\" }}</div>\n                                        </div>\n                                    </div>\n                                </td>\n                                <td class=\"px-6 py-4 whitespace-nowrap\">\n                                    <span class=\"project-status-{{ project.status|lower }}\">\n                                        {{ project.get_status_display }}\n                                    </span>\n                                </td>\n                                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-700\">\n                                    € {{ project.budget|floatformat:2 }}\n                                </td>\n                                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-700\">\n                                    {{ project.entities.first.name|default:\"—\" }}\n                                </td>\n                                <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                                    <div class=\"flex justify-end space-x-2\">\n                                        <a href=\"#\" class=\"text-blue-600 hover:text-blue-900\" title=\"Visualizza\">\n                                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                                            </svg>\n                                        </a>\n                                        <a href=\"#\" class=\"text-gray-600 hover:text-gray-900\" title=\"Modifica\">\n                                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                                            </svg>\n                                        </a>\n                                    </div>\n                                </td>\n                            </tr>\n                            {% endfor %}\n                        </tbody>\n                    </table>\n                </div>\n                {% else %}\n                <div class=\"flex flex-col items-center justify-center py-8 text-center\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12 text-gray-400 mb-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n                    </svg>\n                    <p class=\"text-gray-500 mb-2\">Nessun progetto presente</p>\n                    <p class=\"text-gray-400 text-sm mb-4\">Inizia creando il tuo primo progetto espropriativo.</p>\n                    <button class=\"btn btn-primary flex items-center\"\n                            hx-get=\"{% url 'projects:create' %}\" \n                            hx-target=\"#main-content\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z\" clip-rule=\"evenodd\" />\n                        </svg>\n                        Crea Progetto\n                    </button>\n                </div>\n                {% endif %}\n            </div>\n        </div>\n\n        <!-- Notifiche e Attività Recenti -->\n        <div class=\"bg-white rounded-lg shadow\">\n            <div class=\"px-6 py-4 border-b\">\n                <h3 class=\"text-lg font-semibold text-gray-900\">Attività Recenti</h3>\n            </div>\n            <div class=\"p-6\" id=\"activities-container\">\n                {% if recent_activities %}\n                <div class=\"space-y-6\">\n                    {% for activity in recent_activities %}\n                    <div class=\"flex\">\n                        <div class=\"flex-shrink-0\">\n                            <div class=\"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600\">\n                                {{ activity.user.username|slice:\":1\" }}\n                            </div>\n                        </div>\n                        <div class=\"ml-4\">\n                            <p class=\"text-sm font-medium text-gray-900\">{{ activity.description }}</p>\n                            <p class=\"text-xs text-gray-500\">{{ activity.created_at|date:\"d/m/Y H:i\" }}</p>\n                        </div>\n                    </div>\n                    {% endfor %}\n                </div>\n                {% else %}\n                <div class=\"text-center py-8\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-10 w-10 text-gray-400 mx-auto mb-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                    <p class=\"text-gray-500\">Nessuna attività recente</p>\n                </div>\n                {% endif %}\n            </div>\n            <div class=\"px-6 py-3 bg-gray-50 border-t\">\n                <div class=\"flex justify-between items-center\">\n                    <div class=\"text-xs text-gray-500\">Aggiornato: {% now \"H:i\" %}</div>\n                    <div class=\"flex space-x-2\">\n                        <button class=\"text-sm text-blue-600 hover:text-blue-800\"\n                                hx-get=\"/exproject/activities/\" \n                                hx-target=\"#activities-container\" \n                                hx-swap=\"innerHTML\"\n                                hx-trigger=\"click\"\n                                id=\"refresh-activities-btn\"\n                                onclick=\"refreshActivities()\">\n                            Aggiorna\n                        </button>\n\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <!-- Moduli Aggiuntivi -->\n    <div class=\"mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <div class=\"bg-white rounded-lg shadow p-4 flex items-center\">\n            <div class=\"p-3 rounded-full bg-yellow-100 mr-3\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-yellow-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n            </div>\n            <div>\n                <h4 class=\"font-medium text-gray-900\">Modulo Economico</h4>\n                <a href=\"#\" class=\"text-sm text-yellow-600 hover:text-yellow-800\">Gestisci indennità →</a>\n            </div>\n        </div>\n        \n        <div class=\"bg-white rounded-lg shadow p-4 flex items-center\">\n            <div class=\"p-3 rounded-full bg-indigo-100 mr-3\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-indigo-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7\" />\n                </svg>\n            </div>\n            <div>\n                <h4 class=\"font-medium text-gray-900\">Mappa Particelle</h4>\n                <a href=\"#\" class=\"text-sm text-indigo-600 hover:text-indigo-800\">Visualizza mappa →</a>\n            </div>\n        </div>\n        \n        <div class=\"bg-white rounded-lg shadow p-4 flex items-center\">\n            <div class=\"p-3 rounded-full bg-pink-100 mr-3\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-pink-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z\" />\n                </svg>\n            </div>\n            <div>\n                <h4 class=\"font-medium text-gray-900\">Integrazioni</h4>\n                <a href=\"#\" class=\"text-sm text-pink-600 hover:text-pink-800\">Sister/Catasto →</a>\n            </div>\n        </div>\n        \n        <div class=\"bg-white rounded-lg shadow p-4 flex items-center\">\n            <div class=\"p-3 rounded-full bg-red-100 mr-3\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n            </div>\n            <div>\n                <h4 class=\"font-medium text-gray-900\">Report e Statistiche</h4>\n                <a href=\"#\" class=\"text-sm text-red-600 hover:text-red-800\">Genera report →</a>\n            </div>\n        </div>\n    </div>\n</div>\n{% endblock %}\n\n{% block extra_js %}\n<script>\n    document.addEventListener('DOMContentLoaded', function() {\n        // Debug per htmx\n        console.log('DOMContentLoaded triggered');\n        \n        // Verifica se htmx è disponibile\n        if (typeof htmx !== 'undefined') {\n            console.log('HTMX caricato correttamente', htmx.version);\n            \n            // Aggiungi un listener per gli eventi htmx\n            document.body.addEventListener('htmx:afterRequest', function(event) {\n                console.log('HTMX request completata:', event.detail);\n            });\n            \n            document.body.addEventListener('htmx:beforeRequest', function(event) {\n                console.log('HTMX request in corso:', event.detail);\n            });\n            \n            document.body.addEventListener('htmx:responseError', function(event) {\n                console.error('HTMX error:', event.detail);\n            });\n        } else {\n            console.error('HTMX non disponibile!');\n            \n            // Se HTMX non è disponibile, sostituisci con AJAX standard per aggiornare le attività\n            const refreshBtn = document.getElementById('refresh-activities-btn');\n            if (refreshBtn) {\n                refreshBtn.addEventListener('click', function(e) {\n                    e.preventDefault();\n                    const xhr = new XMLHttpRequest();\n                    xhr.open('GET', '/exproject/activities/');\n                    xhr.onload = function() {\n                        if (xhr.status === 200) {\n                            document.getElementById('activities-container').innerHTML = xhr.responseText;\n                        }\n                    };\n                    xhr.send();\n                });\n            }\n        }\n        \n        // Intervallo per aggiornare le attività ogni 5 minuti\n        setInterval(function() {\n            refreshActivities();\n        }, 300000); // 5 minuti\n    });\n    \n    // Funzione per aggiornare le attività (fallback JavaScript)\n    function refreshActivities() {\n        const activityBtn = document.getElementById('refresh-activities-btn');\n        if (activityBtn) {\n            try {\n                // Prova ad usare HTMX\n                if (typeof htmx !== 'undefined') {\n                    console.log('Triggering htmx refresh via refreshActivities()');\n                    htmx.trigger(activityBtn, 'click');\n                } else {\n                    // Fallback con XHR\n                    const xhr = new XMLHttpRequest();\n                    xhr.open('GET', '/exproject/activities/');\n                    xhr.onload = function() {\n                        if (xhr.status === 200) {\n                            document.getElementById('activities-container').innerHTML = xhr.responseText;\n                        }\n                    };\n                    xhr.send();\n                }\n            } catch (e) {\n                console.warn('Errore durante l\\'aggiornamento delle attività', e);\n            }\n        }\n    }\n</script>\n{% endblock %}", "modifiedCode": "{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}Dashboard - ExProject{% endblock %}\n\n\n\n{% block content %}\n<div class=\"flex-1 space-y-4 p-8 pt-6\">\n    <!-- Header Dashboard -->\n    <div class=\"flex items-center justify-between space-y-2\">\n        <h2 class=\"text-3xl font-bold tracking-tight\">Dashboard</h2>\n        <div class=\"flex items-center space-x-2\">\n            {% ui_button \"Nuovo Progetto\" variant=\"default\" hx-get=\"/projects/create/\" hx-target=\"#main-content\" %}\n            {% ui_button \"Altre Azioni\" variant=\"outline\" %}\n        </div>\n    </div>\n\n    <!-- Statistiche principali -->\n    <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n        {% ui_card title=\"Progetti Totali\" content=\"<div class='text-2xl font-bold'>{{ stats.projects_total|default:0 }}</div><p class='text-xs text-muted-foreground'><svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' class='inline h-3 w-3 mr-1'><path stroke-linecap='round' stroke-linejoin='round' d='M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941' /></svg>+20.1% dal mese scorso</p>\" %}\n        \n        <div class=\"dashboard-stat-card bg-white rounded-lg shadow p-6 border-l-4 border-green-500\">\n            <div class=\"flex items-center\">\n                <div class=\"p-3 rounded-full bg-green-100 mr-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-green-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                </div>\n                <div>\n                    <p class=\"text-gray-500 text-sm\">In Lavorazione</p>\n                    <p class=\"text-2xl font-semibold\">{{ stats.projects_active|default:\"0\" }}</p>\n                </div>\n            </div>\n            <div class=\"mt-2\">\n                <a href=\"#\" class=\"text-sm text-green-600 hover:text-green-800\">Gestisci attivi →</a>\n            </div>\n        </div>\n        \n        <div class=\"dashboard-stat-card bg-white rounded-lg shadow p-6 border-l-4 border-yellow-500\">\n            <div class=\"flex items-center\">\n                <div class=\"p-3 rounded-full bg-yellow-100 mr-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-yellow-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z\" />\n                    </svg>\n                </div>\n                <div>\n                    <p class=\"text-gray-500 text-sm\">Particelle Totali</p>\n                    <p class=\"text-2xl font-semibold\">{{ stats.parcels_total|default:\"0\" }}</p>\n                </div>\n            </div>\n            <div class=\"mt-2\">\n                <a href=\"#\" class=\"text-sm text-yellow-600 hover:text-yellow-800\">Visualizza mappa →</a>\n            </div>\n        </div>\n        \n        <div class=\"dashboard-stat-card bg-white rounded-lg shadow p-6 border-l-4 border-red-500\">\n            <div class=\"flex items-center\">\n                <div class=\"p-3 rounded-full bg-red-100 mr-4\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z\" />\n                    </svg>\n                </div>\n                <div>\n                    <p class=\"text-gray-500 text-sm\">In Scadenza</p>\n                    <p class=\"text-2xl font-semibold\">{{ stats.expiring_tasks|default:\"0\" }}</p>\n                </div>\n            </div>\n            <div class=\"mt-2\">\n                <a href=\"#\" class=\"text-sm text-red-600 hover:text-red-800\">Gestisci scadenze →</a>\n            </div>\n        </div>\n    </div>\n\n    <!-- Moduli Principali -->\n    <div class=\"mb-8\">\n        <h2 class=\"text-xl font-semibold text-gray-900 mb-4\">Moduli Operativi</h2>\n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div class=\"module-card bg-white rounded-lg shadow border border-gray-100 overflow-hidden\">\n                <div class=\"h-2 bg-blue-600\"></div>\n                <div class=\"p-6\">\n                    <div class=\"flex items-center mb-4\">\n                        <div class=\"p-2 rounded-full bg-blue-100 mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-blue-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n                            </svg>\n                        </div>\n                        <h3 class=\"text-lg font-semibold text-gray-900\">Progetti</h3>\n                    </div>\n                    <p class=\"text-gray-600 mb-4\">Gestione completa dei progetti espropriativi: creazione, pianificazione, monitoraggio avanzamento.</p>\n                    <div class=\"flex justify-between\">\n                        <a href=\"#\" hx-get=\"{% url 'projects:list' %}\" hx-target=\"#main-content\" hx-push-url=\"true\"\n                           class=\"text-blue-600 hover:text-blue-800 font-medium\">Vai ai progetti</a>\n                        <span class=\"text-gray-500\">{{ stats.projects_total|default:\"0\" }} progetti</span>\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"module-card bg-white rounded-lg shadow border border-gray-100 overflow-hidden\">\n                <div class=\"h-2 bg-green-600\"></div>\n                <div class=\"p-6\">\n                    <div class=\"flex items-center mb-4\">\n                        <div class=\"p-2 rounded-full bg-green-100 mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-green-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4\" />\n                            </svg>\n                        </div>\n                        <h3 class=\"text-lg font-semibold text-gray-900\">Workflow</h3>\n                    </div>\n                    <p class=\"text-gray-600 mb-4\">Automazione dei flussi procedurali secondo DPR 327/2001 con controlli integrati e scadenzario.</p>\n                    <div class=\"flex justify-between\">\n                        <a href=\"#\" hx-get=\"{% url 'workflow:list' %}\" hx-target=\"#main-content\" hx-push-url=\"true\"\n                           class=\"text-green-600 hover:text-green-800 font-medium\">Gestione workflow</a>\n                        <span class=\"text-gray-500\">{{ stats.active_workflows|default:\"0\" }} attivi</span>\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"module-card bg-white rounded-lg shadow border border-gray-100 overflow-hidden\">\n                <div class=\"h-2 bg-purple-600\"></div>\n                <div class=\"p-6\">\n                    <div class=\"flex items-center mb-4\">\n                        <div class=\"p-2 rounded-full bg-purple-100 mr-2\">\n                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-purple-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2\" />\n                            </svg>\n                        </div>\n                        <h3 class=\"text-lg font-semibold text-gray-900\">Documenti</h3>\n                    </div>\n                    <p class=\"text-gray-600 mb-4\">Gestione documentale con template personalizzabili, generazione automatica e firma digitale.</p>\n                    <div class=\"flex justify-between\">\n                        <a href=\"#\" hx-get=\"{% url 'documents:list' %}\" hx-target=\"#main-content\" hx-push-url=\"true\"\n                           class=\"text-purple-600 hover:text-purple-800 font-medium\">Archivio documenti</a>\n                        <span class=\"text-gray-500\">{{ stats.documents_total|default:\"0\" }} documenti</span>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <div class=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        <!-- Progetti Recenti -->\n        <div class=\"lg:col-span-2 bg-white rounded-lg shadow\">\n            <div class=\"px-6 py-4 border-b flex justify-between items-center\">\n                <h3 class=\"text-lg font-semibold text-gray-900\">Progetti Recenti</h3>\n                <a href=\"#\" hx-get=\"{% url 'projects:list' %}\" hx-target=\"#main-content\" hx-push-url=\"true\"\n                   class=\"text-sm text-blue-600 hover:text-blue-800\">Vedi tutti</a>\n            </div>\n            <div class=\"p-6\">\n                {% if recent_projects %}\n                <div class=\"overflow-x-auto\">\n                    <table class=\"min-w-full divide-y divide-gray-200\">\n                        <thead>\n                            <tr>\n                                <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Progetto</th>\n                                <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Stato</th>\n                                <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Budget</th>\n                                <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Ente</th>\n                                <th class=\"px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Azioni</th>\n                            </tr>\n                        </thead>\n                        <tbody class=\"bg-white divide-y divide-gray-200\">\n                            {% for project in recent_projects %}\n                            <tr class=\"hover:bg-gray-50\">\n                                <td class=\"px-6 py-4 whitespace-nowrap\">\n                                    <div class=\"flex items-center\">\n                                        <div class=\"flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium\">\n                                            {{ project.name|slice:\":1\" }}\n                                        </div>\n                                        <div class=\"ml-3\">\n                                            <a href=\"#\" class=\"text-sm font-medium text-gray-900 hover:text-blue-600\">{{ project.name }}</a>\n                                            <div class=\"text-xs text-gray-500\">Creato: {{ project.created_at|date:\"d/m/Y\" }}</div>\n                                        </div>\n                                    </div>\n                                </td>\n                                <td class=\"px-6 py-4 whitespace-nowrap\">\n                                    <span class=\"project-status-{{ project.status|lower }}\">\n                                        {{ project.get_status_display }}\n                                    </span>\n                                </td>\n                                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-700\">\n                                    € {{ project.budget|floatformat:2 }}\n                                </td>\n                                <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-700\">\n                                    {{ project.entities.first.name|default:\"—\" }}\n                                </td>\n                                <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                                    <div class=\"flex justify-end space-x-2\">\n                                        <a href=\"#\" class=\"text-blue-600 hover:text-blue-900\" title=\"Visualizza\">\n                                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                                            </svg>\n                                        </a>\n                                        <a href=\"#\" class=\"text-gray-600 hover:text-gray-900\" title=\"Modifica\">\n                                            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                                            </svg>\n                                        </a>\n                                    </div>\n                                </td>\n                            </tr>\n                            {% endfor %}\n                        </tbody>\n                    </table>\n                </div>\n                {% else %}\n                <div class=\"flex flex-col items-center justify-center py-8 text-center\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12 text-gray-400 mb-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\" />\n                    </svg>\n                    <p class=\"text-gray-500 mb-2\">Nessun progetto presente</p>\n                    <p class=\"text-gray-400 text-sm mb-4\">Inizia creando il tuo primo progetto espropriativo.</p>\n                    <button class=\"btn btn-primary flex items-center\"\n                            hx-get=\"{% url 'projects:create' %}\" \n                            hx-target=\"#main-content\">\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                            <path fill-rule=\"evenodd\" d=\"M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z\" clip-rule=\"evenodd\" />\n                        </svg>\n                        Crea Progetto\n                    </button>\n                </div>\n                {% endif %}\n            </div>\n        </div>\n\n        <!-- Notifiche e Attività Recenti -->\n        <div class=\"bg-white rounded-lg shadow\">\n            <div class=\"px-6 py-4 border-b\">\n                <h3 class=\"text-lg font-semibold text-gray-900\">Attività Recenti</h3>\n            </div>\n            <div class=\"p-6\" id=\"activities-container\">\n                {% if recent_activities %}\n                <div class=\"space-y-6\">\n                    {% for activity in recent_activities %}\n                    <div class=\"flex\">\n                        <div class=\"flex-shrink-0\">\n                            <div class=\"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600\">\n                                {{ activity.user.username|slice:\":1\" }}\n                            </div>\n                        </div>\n                        <div class=\"ml-4\">\n                            <p class=\"text-sm font-medium text-gray-900\">{{ activity.description }}</p>\n                            <p class=\"text-xs text-gray-500\">{{ activity.created_at|date:\"d/m/Y H:i\" }}</p>\n                        </div>\n                    </div>\n                    {% endfor %}\n                </div>\n                {% else %}\n                <div class=\"text-center py-8\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-10 w-10 text-gray-400 mx-auto mb-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                    <p class=\"text-gray-500\">Nessuna attività recente</p>\n                </div>\n                {% endif %}\n            </div>\n            <div class=\"px-6 py-3 bg-gray-50 border-t\">\n                <div class=\"flex justify-between items-center\">\n                    <div class=\"text-xs text-gray-500\">Aggiornato: {% now \"H:i\" %}</div>\n                    <div class=\"flex space-x-2\">\n                        <button class=\"text-sm text-blue-600 hover:text-blue-800\"\n                                hx-get=\"/exproject/activities/\" \n                                hx-target=\"#activities-container\" \n                                hx-swap=\"innerHTML\"\n                                hx-trigger=\"click\"\n                                id=\"refresh-activities-btn\"\n                                onclick=\"refreshActivities()\">\n                            Aggiorna\n                        </button>\n\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n\n    <!-- Moduli Aggiuntivi -->\n    <div class=\"mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <div class=\"bg-white rounded-lg shadow p-4 flex items-center\">\n            <div class=\"p-3 rounded-full bg-yellow-100 mr-3\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-yellow-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n            </div>\n            <div>\n                <h4 class=\"font-medium text-gray-900\">Modulo Economico</h4>\n                <a href=\"#\" class=\"text-sm text-yellow-600 hover:text-yellow-800\">Gestisci indennità →</a>\n            </div>\n        </div>\n        \n        <div class=\"bg-white rounded-lg shadow p-4 flex items-center\">\n            <div class=\"p-3 rounded-full bg-indigo-100 mr-3\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-indigo-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7\" />\n                </svg>\n            </div>\n            <div>\n                <h4 class=\"font-medium text-gray-900\">Mappa Particelle</h4>\n                <a href=\"#\" class=\"text-sm text-indigo-600 hover:text-indigo-800\">Visualizza mappa →</a>\n            </div>\n        </div>\n        \n        <div class=\"bg-white rounded-lg shadow p-4 flex items-center\">\n            <div class=\"p-3 rounded-full bg-pink-100 mr-3\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-pink-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z\" />\n                </svg>\n            </div>\n            <div>\n                <h4 class=\"font-medium text-gray-900\">Integrazioni</h4>\n                <a href=\"#\" class=\"text-sm text-pink-600 hover:text-pink-800\">Sister/Catasto →</a>\n            </div>\n        </div>\n        \n        <div class=\"bg-white rounded-lg shadow p-4 flex items-center\">\n            <div class=\"p-3 rounded-full bg-red-100 mr-3\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n            </div>\n            <div>\n                <h4 class=\"font-medium text-gray-900\">Report e Statistiche</h4>\n                <a href=\"#\" class=\"text-sm text-red-600 hover:text-red-800\">Genera report →</a>\n            </div>\n        </div>\n    </div>\n</div>\n{% endblock %}\n\n{% block extra_js %}\n<script>\n    document.addEventListener('DOMContentLoaded', function() {\n        // Debug per htmx\n        console.log('DOMContentLoaded triggered');\n        \n        // Verifica se htmx è disponibile\n        if (typeof htmx !== 'undefined') {\n            console.log('HTMX caricato correttamente', htmx.version);\n            \n            // Aggiungi un listener per gli eventi htmx\n            document.body.addEventListener('htmx:afterRequest', function(event) {\n                console.log('HTMX request completata:', event.detail);\n            });\n            \n            document.body.addEventListener('htmx:beforeRequest', function(event) {\n                console.log('HTMX request in corso:', event.detail);\n            });\n            \n            document.body.addEventListener('htmx:responseError', function(event) {\n                console.error('HTMX error:', event.detail);\n            });\n        } else {\n            console.error('HTMX non disponibile!');\n            \n            // Se HTMX non è disponibile, sostituisci con AJAX standard per aggiornare le attività\n            const refreshBtn = document.getElementById('refresh-activities-btn');\n            if (refreshBtn) {\n                refreshBtn.addEventListener('click', function(e) {\n                    e.preventDefault();\n                    const xhr = new XMLHttpRequest();\n                    xhr.open('GET', '/exproject/activities/');\n                    xhr.onload = function() {\n                        if (xhr.status === 200) {\n                            document.getElementById('activities-container').innerHTML = xhr.responseText;\n                        }\n                    };\n                    xhr.send();\n                });\n            }\n        }\n        \n        // Intervallo per aggiornare le attività ogni 5 minuti\n        setInterval(function() {\n            refreshActivities();\n        }, 300000); // 5 minuti\n    });\n    \n    // Funzione per aggiornare le attività (fallback JavaScript)\n    function refreshActivities() {\n        const activityBtn = document.getElementById('refresh-activities-btn');\n        if (activityBtn) {\n            try {\n                // Prova ad usare HTMX\n                if (typeof htmx !== 'undefined') {\n                    console.log('Triggering htmx refresh via refreshActivities()');\n                    htmx.trigger(activityBtn, 'click');\n                } else {\n                    // Fallback con XHR\n                    const xhr = new XMLHttpRequest();\n                    xhr.open('GET', '/exproject/activities/');\n                    xhr.onload = function() {\n                        if (xhr.status === 200) {\n                            document.getElementById('activities-container').innerHTML = xhr.responseText;\n                        }\n                    };\n                    xhr.send();\n                }\n            } catch (e) {\n                console.warn('Errore durante l\\'aggiornamento delle attività', e);\n            }\n        }\n    }\n</script>\n{% endblock %}"}