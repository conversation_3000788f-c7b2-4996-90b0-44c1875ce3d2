{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/views.py"}, "originalCode": "from django.shortcuts import render, get_object_or_404\nfrom django.http import JsonResponse, HttpResponse\nfrom django.views.generic import TemplateView\nfrom django.contrib.auth.mixins import LoginRequiredMixin\nfrom django.views.decorators.cache import cache_page\nfrom django.utils.decorators import method_decorator\nfrom .models import Theme, ComponentLibrary, LayoutConfiguration\nfrom .services import ThemeService, ComponentService, LayoutService, WhiteLabelService\n\n\nclass ComponentLibraryView(LoginRequiredMixin, TemplateView):\n    \"\"\"Vista per visualizzare la libreria di componenti\"\"\"\n    template_name = 'ui/component_library.html'\n    \n    def get_context_data(self, **kwargs):\n        context = super().get_context_data(**kwargs)\n        \n        # Raggruppa componenti per categoria\n        components_by_category = {}\n        for category, _ in ComponentLibrary._meta.get_field('category').choices:\n            components_by_category[category] = ComponentService.get_components_by_category(category)\n        \n        context.update({\n            'components_by_category': components_by_category,\n            'active_theme': ThemeService.get_active_theme_config(),\n        })\n        return context\n\n\nclass ThemePreviewView(LoginRequiredMixin, TemplateView):\n    \"\"\"Vista per preview dei temi\"\"\"\n    template_name = 'ui/theme_preview.html'\n    \n    def get_context_data(self, **kwargs):\n        context = super().get_context_data(**kwargs)\n        theme_slug = kwargs.get('theme_slug')\n        \n        if theme_slug:\n            try:\n                theme = Theme.objects.get(slug=theme_slug)\n                context['preview_theme'] = theme\n            except Theme.DoesNotExist:\n                pass\n        \n        context['available_themes'] = Theme.objects.filter(is_active=True)\n        return context\n\n\n@method_decorator(cache_page(60 * 15), name='dispatch')  # Cache per 15 minuti\nclass ThemeCSSView(TemplateView):\n    \"\"\"Vista per servire CSS del tema dinamicamente\"\"\"\n    content_type = 'text/css'\n    \n    def get(self, request, *args, **kwargs):\n        client_slug = kwargs.get('client_slug')\n        css = WhiteLabelService.generate_client_css(client_slug)\n        return HttpResponse(css, content_type=self.content_type)\n\n\ndef component_render_api(request, component_slug):\n    \"\"\"API per renderizzare componenti dinamicamente\"\"\"\n    if request.method == 'POST':\n        import json\n        data = json.loads(request.body)\n        props = data.get('props', {})\n        variant = data.get('variant')\n        \n        html = ComponentService.render_component(component_slug, props, variant)\n        return JsonResponse({'html': html})\n    \n    return JsonResponse({'error': 'Method not allowed'}, status=405)\n\n\ndef theme_export_api(request, theme_slug):\n    \"\"\"API per esportare configurazione tema\"\"\"\n    if not request.user.is_staff:\n        return JsonResponse({'error': 'Permission denied'}, status=403)\n    \n    config_json = WhiteLabelService.export_theme_config(theme_slug)\n    if config_json:\n        response = HttpResponse(config_json, content_type='application/json')\n        response['Content-Disposition'] = f'attachment; filename=\"{theme_slug}-theme.json\"'\n        return response\n    \n    return JsonResponse({'error': 'Theme not found'}, status=404)\n\n\ndef theme_import_api(request):\n    \"\"\"API per importare configurazione tema\"\"\"\n    if not request.user.is_staff:\n        return JsonResponse({'error': 'Permission denied'}, status=403)\n    \n    if request.method == 'POST' and request.FILES.get('theme_file'):\n        try:\n            config_json = request.FILES['theme_file'].read().decode('utf-8')\n            theme = WhiteLabelService.import_theme_config(config_json, request.user)\n            return JsonResponse({\n                'success': True,\n                'theme_id': theme.id,\n                'theme_name': theme.name\n            })\n        except Exception as e:\n            return JsonResponse({'error': str(e)}, status=400)\n    \n    return JsonResponse({'error': 'Invalid request'}, status=400)\n\n\nclass DesignSystemDocsView(LoginRequiredMixin, TemplateView):\n    \"\"\"Vista per la documentazione del design system\"\"\"\n    template_name = 'ui/design_system_docs.html'\n    \n    def get_context_data(self, **kwargs):\n        context = super().get_context_data(**kwargs)\n        \n        # Esempi di utilizzo dei componenti\n        component_examples = {\n            'buttons': [\n                {'variant': 'default', 'text': 'Primary Button'},\n                {'variant': 'secondary', 'text': 'Secondary Button'},\n                {'variant': 'outline', 'text': 'Outline Button'},\n                {'variant': 'ghost', 'text': 'Ghost Button'},\n                {'variant': 'destructive', 'text': 'Destructive Button'},\n            ],\n            'badges': [\n                {'variant': 'default', 'text': 'Default'},\n                {'variant': 'secondary', 'text': 'Secondary'},\n                {'variant': 'outline', 'text': 'Outline'},\n                {'variant': 'destructive', 'text': 'Destructive'},\n            ],\n            'alerts': [\n                {'variant': 'default', 'message': 'This is a default alert'},\n                {'variant': 'destructive', 'message': 'This is a destructive alert'},\n                {'variant': 'warning', 'message': 'This is a warning alert'},\n                {'variant': 'success', 'message': 'This is a success alert'},\n            ]\n        }\n        \n        context.update({\n            'component_examples': component_examples,\n            'active_theme': ThemeService.get_active_theme_config(),\n            'design_tokens': ThemeService.get_active_theme_config(),\n        })\n        return context\n\ndef components_test(request):\n    \"\"\"View per testare tutti i componenti\"\"\"\n    context = {\n        'countries': [\n            {'value': 'it', 'label': 'Italia'},\n            {'value': 'fr', 'label': 'Francia'},\n            {'value': 'de', 'label': 'Germania'},\n        ],\n        'tab_data': [\n            {'id': 'tab1', 'label': 'Tab 1', 'content': '<p>Contenuto del primo tab</p>'},\n            {'id': 'tab2', 'label': 'Tab 2', 'content': '<p>Contenuto del secondo tab</p>'},\n            {'id': 'tab3', 'label': 'Tab 3', 'content': '<p>Contenuto del terzo tab</p>'},\n        ]\n    }\n    return render(request, 'ui/components_test.html', context)\n", "modifiedCode": "from django.shortcuts import render, get_object_or_404\nfrom django.http import JsonResponse, HttpResponse\nfrom django.views.generic import TemplateView\nfrom django.contrib.auth.mixins import LoginRequiredMixin\nfrom django.views.decorators.cache import cache_page\nfrom django.utils.decorators import method_decorator\nfrom .models import Theme, ComponentLibrary, LayoutConfiguration\nfrom .services import ThemeService, ComponentService, LayoutService, WhiteLabelService\n\n\nclass ComponentLibraryView(LoginRequiredMixin, TemplateView):\n    \"\"\"Vista per visualizzare la libreria di componenti\"\"\"\n    template_name = 'ui/component_library.html'\n    \n    def get_context_data(self, **kwargs):\n        context = super().get_context_data(**kwargs)\n        \n        # Raggruppa componenti per categoria\n        components_by_category = {}\n        for category, _ in ComponentLibrary._meta.get_field('category').choices:\n            components_by_category[category] = ComponentService.get_components_by_category(category)\n        \n        context.update({\n            'components_by_category': components_by_category,\n            'active_theme': ThemeService.get_active_theme_config(),\n        })\n        return context\n\n\nclass ThemePreviewView(LoginRequiredMixin, TemplateView):\n    \"\"\"Vista per preview dei temi\"\"\"\n    template_name = 'ui/theme_preview.html'\n    \n    def get_context_data(self, **kwargs):\n        context = super().get_context_data(**kwargs)\n        theme_slug = kwargs.get('theme_slug')\n        \n        if theme_slug:\n            try:\n                theme = Theme.objects.get(slug=theme_slug)\n                context['preview_theme'] = theme\n            except Theme.DoesNotExist:\n                pass\n        \n        context['available_themes'] = Theme.objects.filter(is_active=True)\n        return context\n\n\n@method_decorator(cache_page(60 * 15), name='dispatch')  # Cache per 15 minuti\nclass ThemeCSSView(TemplateView):\n    \"\"\"Vista per servire CSS del tema dinamicamente\"\"\"\n    content_type = 'text/css'\n    \n    def get(self, request, *args, **kwargs):\n        client_slug = kwargs.get('client_slug')\n        css = WhiteLabelService.generate_client_css(client_slug)\n        return HttpResponse(css, content_type=self.content_type)\n\n\ndef component_render_api(request, component_slug):\n    \"\"\"API per renderizzare componenti dinamicamente\"\"\"\n    if request.method == 'POST':\n        import json\n        data = json.loads(request.body)\n        props = data.get('props', {})\n        variant = data.get('variant')\n        \n        html = ComponentService.render_component(component_slug, props, variant)\n        return JsonResponse({'html': html})\n    \n    return JsonResponse({'error': 'Method not allowed'}, status=405)\n\n\ndef theme_export_api(request, theme_slug):\n    \"\"\"API per esportare configurazione tema\"\"\"\n    if not request.user.is_staff:\n        return JsonResponse({'error': 'Permission denied'}, status=403)\n    \n    config_json = WhiteLabelService.export_theme_config(theme_slug)\n    if config_json:\n        response = HttpResponse(config_json, content_type='application/json')\n        response['Content-Disposition'] = f'attachment; filename=\"{theme_slug}-theme.json\"'\n        return response\n    \n    return JsonResponse({'error': 'Theme not found'}, status=404)\n\n\ndef theme_import_api(request):\n    \"\"\"API per importare configurazione tema\"\"\"\n    if not request.user.is_staff:\n        return JsonResponse({'error': 'Permission denied'}, status=403)\n    \n    if request.method == 'POST' and request.FILES.get('theme_file'):\n        try:\n            config_json = request.FILES['theme_file'].read().decode('utf-8')\n            theme = WhiteLabelService.import_theme_config(config_json, request.user)\n            return JsonResponse({\n                'success': True,\n                'theme_id': theme.id,\n                'theme_name': theme.name\n            })\n        except Exception as e:\n            return JsonResponse({'error': str(e)}, status=400)\n    \n    return JsonResponse({'error': 'Invalid request'}, status=400)\n\n\nclass DesignSystemDocsView(LoginRequiredMixin, TemplateView):\n    \"\"\"Vista per la documentazione del design system\"\"\"\n    template_name = 'ui/design_system_docs.html'\n    \n    def get_context_data(self, **kwargs):\n        context = super().get_context_data(**kwargs)\n        \n        # Esempi di utilizzo dei componenti\n        component_examples = {\n            'buttons': [\n                {'variant': 'default', 'text': 'Primary Button'},\n                {'variant': 'secondary', 'text': 'Secondary Button'},\n                {'variant': 'outline', 'text': 'Outline Button'},\n                {'variant': 'ghost', 'text': 'Ghost Button'},\n                {'variant': 'destructive', 'text': 'Destructive Button'},\n            ],\n            'badges': [\n                {'variant': 'default', 'text': 'Default'},\n                {'variant': 'secondary', 'text': 'Secondary'},\n                {'variant': 'outline', 'text': 'Outline'},\n                {'variant': 'destructive', 'text': 'Destructive'},\n            ],\n            'alerts': [\n                {'variant': 'default', 'message': 'This is a default alert'},\n                {'variant': 'destructive', 'message': 'This is a destructive alert'},\n                {'variant': 'warning', 'message': 'This is a warning alert'},\n                {'variant': 'success', 'message': 'This is a success alert'},\n            ]\n        }\n        \n        context.update({\n            'component_examples': component_examples,\n            'active_theme': ThemeService.get_active_theme_config(),\n            'design_tokens': ThemeService.get_active_theme_config(),\n        })\n        return context\n\ndef components_test(request):\n    \"\"\"View per testare tutti i componenti\"\"\"\n    context = {\n        'countries': [\n            {'value': 'it', 'label': 'Italia'},\n            {'value': 'fr', 'label': 'Francia'},\n            {'value': 'de', 'label': 'Germania'},\n        ],\n        'tab_data': [\n            {'id': 'tab1', 'label': 'Tab 1', 'content': '<p>Contenuto del primo tab</p>'},\n            {'id': 'tab2', 'label': 'Tab 2', 'content': '<p>Contenuto del secondo tab</p>'},\n            {'id': 'tab3', 'label': 'Tab 3', 'content': '<p>Contenuto del terzo tab</p>'},\n        ]\n    }\n    return render(request, 'ui/components_test.html', context)\n"}