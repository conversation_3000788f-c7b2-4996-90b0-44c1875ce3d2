{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/profile.html"}, "originalCode": "{% extends \"base.html\" %}\n\n{% block title %}Profilo Utente{% endblock %}\n\n{% block content %}\n<div class=\"max-w-2xl mx-auto py-8\">\n    <h1 class=\"text-2xl font-bold mb-6\">Profilo Utente</h1>\n    \n    {% if messages %}\n    <div class=\"mb-6\">\n        {% for message in messages %}\n        <div class=\"p-4 mb-4 rounded {% if message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-red-100 text-red-700{% endif %}\">\n            {{ message }}\n        </div>\n        {% endfor %}\n    </div>\n    {% endif %}\n    \n    <form method=\"post\" class=\"space-y-6\">\n        {% csrf_token %}\n        \n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n                <label for=\"first_name\" class=\"block text-sm font-medium text-gray-700\">Nome</label>\n                <input type=\"text\" name=\"first_name\" id=\"first_name\" value=\"{{ user.first_name }}\"\n                       class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n            </div>\n            \n            <div>\n                <label for=\"last_name\" class=\"block text-sm font-medium text-gray-700\">Cognome</label>\n                <input type=\"text\" name=\"last_name\" id=\"last_name\" value=\"{{ user.last_name }}\"\n                       class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n            </div>\n        </div>\n        \n        <div>\n            <label for=\"email\" class=\"block text-sm font-medium text-gray-700\">Email</label>\n            <input type=\"email\" name=\"email\" id=\"email\" value=\"{{ user.email }}\"\n                   class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n        </div>\n        \n        <div class=\"border-t pt-6\">\n            <h2 class=\"text-lg font-medium mb-4\">Cambio Password</h2>\n            \n            <div class=\"space-y-4\">\n                <div>\n                    <label for=\"current_password\" class=\"block text-sm font-medium text-gray-700\">Password Attuale</label>\n                    <input type=\"password\" name=\"current_password\" id=\"current_password\"\n                           class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n                </div>\n                \n                <div>\n                    <label for=\"new_password\" class=\"block text-sm font-medium text-gray-700\">Nuova Password</label>\n                    <input type=\"password\" name=\"new_password\" id=\"new_password\"\n                           class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"flex justify-end\">\n            <button type=\"submit\" class=\"btn btn-primary\">\n                Salva Modifiche\n            </button>\n        </div>\n    </form>\n</div>\n{% endblock %} ", "modifiedCode": "{% extends \"base.html\" %}\n\n{% block title %}Profilo Utente{% endblock %}\n\n{% block content %}\n<div class=\"max-w-2xl mx-auto py-8\">\n    <h1 class=\"text-2xl font-bold mb-6\">Profilo Utente</h1>\n    \n    {% if messages %}\n    <div class=\"mb-6\">\n        {% for message in messages %}\n        <div class=\"p-4 mb-4 rounded {% if message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-red-100 text-red-700{% endif %}\">\n            {{ message }}\n        </div>\n        {% endfor %}\n    </div>\n    {% endif %}\n    \n    <form method=\"post\" class=\"space-y-6\">\n        {% csrf_token %}\n        \n        <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n                <label for=\"first_name\" class=\"block text-sm font-medium text-gray-700\">Nome</label>\n                <input type=\"text\" name=\"first_name\" id=\"first_name\" value=\"{{ user.first_name }}\"\n                       class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n            </div>\n            \n            <div>\n                <label for=\"last_name\" class=\"block text-sm font-medium text-gray-700\">Cognome</label>\n                <input type=\"text\" name=\"last_name\" id=\"last_name\" value=\"{{ user.last_name }}\"\n                       class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n            </div>\n        </div>\n        \n        <div>\n            <label for=\"email\" class=\"block text-sm font-medium text-gray-700\">Email</label>\n            <input type=\"email\" name=\"email\" id=\"email\" value=\"{{ user.email }}\"\n                   class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n        </div>\n        \n        <div class=\"border-t pt-6\">\n            <h2 class=\"text-lg font-medium mb-4\">Cambio Password</h2>\n            \n            <div class=\"space-y-4\">\n                <div>\n                    <label for=\"current_password\" class=\"block text-sm font-medium text-gray-700\">Password Attuale</label>\n                    <input type=\"password\" name=\"current_password\" id=\"current_password\"\n                           class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n                </div>\n                \n                <div>\n                    <label for=\"new_password\" class=\"block text-sm font-medium text-gray-700\">Nuova Password</label>\n                    <input type=\"password\" name=\"new_password\" id=\"new_password\"\n                           class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"flex justify-end\">\n            <button type=\"submit\" class=\"btn btn-primary\">\n                Salva Modifiche\n            </button>\n        </div>\n    </form>\n</div>\n{% endblock %} "}