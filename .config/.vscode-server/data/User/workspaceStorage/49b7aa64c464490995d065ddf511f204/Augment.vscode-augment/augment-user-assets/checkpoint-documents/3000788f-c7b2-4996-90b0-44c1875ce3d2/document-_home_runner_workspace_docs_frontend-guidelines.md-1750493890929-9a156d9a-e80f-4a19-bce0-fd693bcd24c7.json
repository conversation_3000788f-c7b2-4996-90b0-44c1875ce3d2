{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "docs/frontend-guidelines.md"}, "modifiedCode": "# 📐 Frontend Development Guidelines\n\n## 🎯 Design System Components Reference\n\n### **UI Components Syntax**\n\n#### **Buttons**\n```html\n<!-- Basic Button -->\n{% ui_button \"Text\" %}\n\n<!-- With Variants -->\n{% ui_button \"Save\" variant=\"default\" %}\n{% ui_button \"Cancel\" variant=\"secondary\" %}\n{% ui_button \"Delete\" variant=\"destructive\" %}\n{% ui_button \"Edit\" variant=\"outline\" %}\n{% ui_button \"More\" variant=\"ghost\" %}\n\n<!-- With Sizes -->\n{% ui_button \"Small\" size=\"sm\" %}\n{% ui_button \"Default\" size=\"default\" %}\n{% ui_button \"Large\" size=\"lg\" %}\n\n<!-- With Actions -->\n{% ui_button \"Submit\" onclick=\"submitForm()\" %}\n{% ui_button \"Save\" type=\"submit\" class=\"w-full\" %}\n```\n\n#### **Cards**\n```html\n<!-- Simple Card -->\n{% ui_card title=\"Card Title\" content=\"<p>Card content here</p>\" %}\n\n<!-- Full Card -->\n{% ui_card \n    title=\"Project Details\" \n    description=\"Manage project information\"\n    content=\"<div class='space-y-4'>Content here</div>\"\n    footer=\"<div class='flex justify-end space-x-2'>Footer buttons</div>\"\n%}\n```\n\n#### **Alerts**\n```html\n<!-- Basic Alert -->\n{% ui_alert \"Success message\" variant=\"success\" %}\n{% ui_alert \"Warning message\" variant=\"warning\" %}\n{% ui_alert \"Error message\" variant=\"destructive\" %}\n\n<!-- With Title -->\n{% ui_alert \"Message\" variant=\"success\" title=\"Success!\" %}\n\n<!-- Dismissible -->\n{% ui_alert \"Message\" variant=\"info\" dismissible=True %}\n```\n\n#### **Forms**\n```html\n<!-- Input Field -->\n{% ui_input name=\"title\" label=\"Project Title\" placeholder=\"Enter title\" required=True %}\n\n<!-- Textarea -->\n{% ui_textarea name=\"description\" label=\"Description\" rows=4 %}\n\n<!-- Select -->\n{% ui_select name=\"status\" label=\"Status\" options=status_choices %}\n\n<!-- Checkbox -->\n{% ui_checkbox name=\"active\" label=\"Active Project\" %}\n```\n\n## 🏗️ Template Patterns\n\n### **Pattern 1: Dashboard**\n```html\n{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}Dashboard{% endblock %}\n\n{% block content %}\n<div class=\"flex-1 space-y-4 p-8 pt-6\">\n    <!-- Header Section -->\n    <div class=\"flex items-center justify-between\">\n        <h2 class=\"text-3xl font-bold tracking-tight\">Dashboard</h2>\n        <div class=\"flex items-center space-x-2\">\n            {% ui_button \"New Project\" variant=\"default\" onclick=\"location.href='{% url 'projects:create' %}'\" %}\n        </div>\n    </div>\n    \n    <!-- Stats Grid -->\n    <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n        {% ui_card \n            title=\"Total Projects\" \n            content=\"<div class='text-2xl font-bold'>{{ stats.total_projects }}</div>\"\n        %}\n        {% ui_card \n            title=\"Active Projects\" \n            content=\"<div class='text-2xl font-bold text-green-600'>{{ stats.active_projects }}</div>\"\n        %}\n        {% ui_card \n            title=\"Pending Tasks\" \n            content=\"<div class='text-2xl font-bold text-yellow-600'>{{ stats.pending_tasks }}</div>\"\n        %}\n        {% ui_card \n            title=\"Completed\" \n            content=\"<div class='text-2xl font-bold text-blue-600'>{{ stats.completed }}</div>\"\n        %}\n    </div>\n    \n    <!-- Content Sections -->\n    <div class=\"grid gap-4 md:grid-cols-2 lg:grid-cols-7\">\n        <!-- Main Content -->\n        <div class=\"col-span-4\">\n            {% ui_card \n                title=\"Recent Projects\"\n                content=\"<div class='space-y-4'>Recent projects list here</div>\"\n            %}\n        </div>\n        \n        <!-- Sidebar -->\n        <div class=\"col-span-3\">\n            {% ui_card \n                title=\"Quick Actions\"\n                content=\"<div class='space-y-2'>Quick action buttons here</div>\"\n            %}\n        </div>\n    </div>\n</div>\n{% endblock %}\n```\n\n### **Pattern 2: List View**\n```html\n{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}{{ page_title }}{% endblock %}\n\n{% block content %}\n<div class=\"flex-1 space-y-4 p-8 pt-6\">\n    <!-- Header with Actions -->\n    <div class=\"flex items-center justify-between\">\n        <h2 class=\"text-3xl font-bold tracking-tight\">{{ page_title }}</h2>\n        <div class=\"flex items-center space-x-2\">\n            {% ui_button \"Add New\" variant=\"default\" onclick=\"location.href='{{ create_url }}'\" %}\n            {% ui_button \"Export\" variant=\"outline\" %}\n        </div>\n    </div>\n    \n    <!-- Filters -->\n    <div class=\"flex items-center space-x-4\">\n        <div class=\"flex-1\">\n            {% ui_input name=\"search\" placeholder=\"Search...\" %}\n        </div>\n        <div>\n            {% ui_select name=\"status\" options=status_options %}\n        </div>\n        {% ui_button \"Filter\" variant=\"secondary\" %}\n    </div>\n    \n    <!-- Data Table -->\n    {% ui_card \n        content=\"<div class='overflow-x-auto'>Table content here</div>\"\n    %}\n    \n    <!-- Pagination -->\n    <div class=\"flex items-center justify-between\">\n        <p class=\"text-sm text-muted-foreground\">\n            Showing {{ start_index }} to {{ end_index }} of {{ total_count }} results\n        </p>\n        <div class=\"flex items-center space-x-2\">\n            <!-- Pagination buttons -->\n        </div>\n    </div>\n</div>\n{% endblock %}\n```\n\n### **Pattern 3: Form View**\n```html\n{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}{{ form_title }}{% endblock %}\n\n{% block content %}\n<div class=\"flex-1 space-y-4 p-8 pt-6\">\n    <!-- Header -->\n    <div class=\"flex items-center justify-between\">\n        <h2 class=\"text-3xl font-bold tracking-tight\">{{ form_title }}</h2>\n        <div class=\"flex items-center space-x-2\">\n            {% ui_button \"Cancel\" variant=\"outline\" onclick=\"history.back()\" %}\n        </div>\n    </div>\n    \n    <!-- Form Card -->\n    <div class=\"max-w-2xl\">\n        {% ui_card \n            title=\"Form Details\"\n            content=\"\n            <form method='post' class='space-y-6'>\n                {% csrf_token %}\n                <!-- Form fields here -->\n                <div class='flex justify-end space-x-2'>\n                    {% ui_button 'Cancel' variant='outline' onclick='history.back()' %}\n                    {% ui_button 'Save' type='submit' variant='default' %}\n                </div>\n            </form>\n            \"\n        %}\n    </div>\n</div>\n{% endblock %}\n```\n\n## ✅ Development Checklist\n\n### **Before Writing Template:**\n- [ ] Identify pattern type (Dashboard/List/Form/Detail)\n- [ ] Check required context variables from view\n- [ ] Plan component usage\n- [ ] Consider responsive design\n- [ ] Plan theme compatibility\n\n### **Template Structure:**\n- [ ] Extends `ui/base_modern.html`\n- [ ] Loads `ui_components`\n- [ ] Sets proper `{% block title %}`\n- [ ] Uses consistent spacing classes\n- [ ] Follows mobile-first approach\n\n### **Component Usage:**\n- [ ] Correct syntax (positional args first)\n- [ ] Proper variant selection\n- [ ] Consistent sizing\n- [ ] Accessible labels\n- [ ] Theme-compatible colors\n\n### **Testing:**\n- [ ] Test with different screen sizes\n- [ ] Test with light/dark themes\n- [ ] Test with empty data\n- [ ] Test with long content\n- [ ] Test form validation\n\n## 🎨 Theme Configuration\n\n### **Color Classes (Theme-aware):**\n```html\n<!-- Text Colors -->\ntext-foreground          <!-- Primary text -->\ntext-muted-foreground    <!-- Secondary text -->\ntext-destructive         <!-- Error text -->\ntext-success            <!-- Success text -->\n\n<!-- Background Colors -->\nbg-background           <!-- Main background -->\nbg-card                <!-- Card background -->\nbg-muted               <!-- Muted background -->\n\n<!-- Border Colors -->\nborder-border          <!-- Default border -->\nborder-input          <!-- Input border -->\n```\n\n### **Spacing System:**\n```html\n<!-- Consistent spacing -->\nspace-y-4             <!-- Vertical spacing -->\nspace-x-2             <!-- Horizontal spacing -->\np-8 pt-6             <!-- Padding -->\ngap-4                <!-- Grid gap -->\n```\n\n## 🚀 Quick Start Template\n\n```html\n{% extends 'ui/base_modern.html' %}\n{% load ui_components %}\n\n{% block title %}Page Title{% endblock %}\n\n{% block content %}\n<div class=\"flex-1 space-y-4 p-8 pt-6\">\n    <!-- Your content here -->\n</div>\n{% endblock %}\n```\n"}