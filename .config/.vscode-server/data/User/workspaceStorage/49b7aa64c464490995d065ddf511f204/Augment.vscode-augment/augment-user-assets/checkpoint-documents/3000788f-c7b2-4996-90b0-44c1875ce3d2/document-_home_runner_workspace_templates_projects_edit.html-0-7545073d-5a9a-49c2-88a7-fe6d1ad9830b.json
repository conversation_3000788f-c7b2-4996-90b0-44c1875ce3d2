{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/projects/edit.html"}, "originalCode": "{% extends 'base.html' %}\n\n{% block title %}Modifica {{ project.name }} - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <div class=\"mb-6\">\n        <a href=\"{% url 'projects:detail' project.id %}\" class=\"flex items-center text-blue-600 hover:text-blue-800\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\" />\n            </svg>\n            Torna ai dettagli del progetto\n        </a>\n    </div>\n    \n    <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n        <div class=\"px-6 py-4 border-b border-gray-200\">\n            <h1 class=\"text-xl font-semibold text-gray-900\">Modifica Progetto</h1>\n        </div>\n        \n        <form method=\"post\" class=\"p-6\">\n            {% csrf_token %}\n            \n            {% if messages %}\n            <div class=\"mb-6\">\n                {% for message in messages %}\n                <div class=\"p-4 mb-4 rounded {% if message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-red-100 text-red-700{% endif %}\">\n                    {{ message }}\n                </div>\n                {% endfor %}\n            </div>\n            {% endif %}\n            \n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div class=\"col-span-2\">\n                    <label for=\"name\" class=\"block text-sm font-medium text-gray-700 mb-1\">Nome Progetto *</label>\n                    <input type=\"text\" name=\"name\" id=\"name\" value=\"{{ project.name }}\" required class=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\">\n                </div>\n                \n                <div class=\"col-span-2\">\n                    <label for=\"description\" class=\"block text-sm font-medium text-gray-700 mb-1\">Descrizione *</label>\n                    <textarea name=\"description\" id=\"description\" rows=\"3\" required class=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\">{{ project.description }}</textarea>\n                </div>\n                \n                <div>\n                    <label for=\"budget\" class=\"block text-sm font-medium text-gray-700 mb-1\">Budget (€) *</label>\n                    <input type=\"number\" name=\"budget\" id=\"budget\" step=\"0.01\" min=\"0\" value=\"{{ project.budget }}\" required class=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\">\n                </div>\n                \n                <div>\n                    <label for=\"status\" class=\"block text-sm font-medium text-gray-700 mb-1\">Stato</label>\n                    <select name=\"status\" id=\"status\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\">\n                        {% for status_value, status_label in status_choices %}\n                        <option value=\"{{ status_value }}\" {% if project.status == status_value %}selected{% endif %}>\n                            {{ status_label }}\n                        </option>\n                        {% endfor %}\n                    </select>\n                </div>\n                \n                <div class=\"col-span-2\">\n                    <label for=\"entities\" class=\"block text-sm font-medium text-gray-700 mb-1\">Entità coinvolte</label>\n                    <select name=\"entities\" id=\"entities\" multiple class=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\" size=\"5\">\n                        {% for entity in entities %}\n                        <option value=\"{{ entity.id }}\" {% if entity in project.entities.all %}selected{% endif %}>\n                            {{ entity.name }}\n                        </option>\n                        {% endfor %}\n                    </select>\n                    <p class=\"mt-1 text-xs text-gray-500\">Tieni premuto Ctrl (o Cmd su Mac) per selezionare più entità</p>\n                </div>\n            </div>\n            \n            <div class=\"mt-8 flex justify-end\">\n                <a href=\"{% url 'projects:detail' project.id %}\" class=\"btn btn-secondary mr-3\">Annulla</a>\n                <button type=\"submit\" class=\"btn btn-primary\">Salva Modifiche</button>\n            </div>\n        </form>\n    </div>\n</div>\n{% endblock %} ", "modifiedCode": "{% extends 'base.html' %}\n\n{% block title %}Modifica {{ project.name }} - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <div class=\"mb-6\">\n        <a href=\"{% url 'projects:detail' project.id %}\" class=\"flex items-center text-blue-600 hover:text-blue-800\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\" />\n            </svg>\n            Torna ai dettagli del progetto\n        </a>\n    </div>\n    \n    <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n        <div class=\"px-6 py-4 border-b border-gray-200\">\n            <h1 class=\"text-xl font-semibold text-gray-900\">Modifica Progetto</h1>\n        </div>\n        \n        <form method=\"post\" class=\"p-6\">\n            {% csrf_token %}\n            \n            {% if messages %}\n            <div class=\"mb-6\">\n                {% for message in messages %}\n                <div class=\"p-4 mb-4 rounded {% if message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-red-100 text-red-700{% endif %}\">\n                    {{ message }}\n                </div>\n                {% endfor %}\n            </div>\n            {% endif %}\n            \n            <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div class=\"col-span-2\">\n                    <label for=\"name\" class=\"block text-sm font-medium text-gray-700 mb-1\">Nome Progetto *</label>\n                    <input type=\"text\" name=\"name\" id=\"name\" value=\"{{ project.name }}\" required class=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\">\n                </div>\n                \n                <div class=\"col-span-2\">\n                    <label for=\"description\" class=\"block text-sm font-medium text-gray-700 mb-1\">Descrizione *</label>\n                    <textarea name=\"description\" id=\"description\" rows=\"3\" required class=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\">{{ project.description }}</textarea>\n                </div>\n                \n                <div>\n                    <label for=\"budget\" class=\"block text-sm font-medium text-gray-700 mb-1\">Budget (€) *</label>\n                    <input type=\"number\" name=\"budget\" id=\"budget\" step=\"0.01\" min=\"0\" value=\"{{ project.budget }}\" required class=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\">\n                </div>\n                \n                <div>\n                    <label for=\"status\" class=\"block text-sm font-medium text-gray-700 mb-1\">Stato</label>\n                    <select name=\"status\" id=\"status\" class=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\">\n                        {% for status_value, status_label in status_choices %}\n                        <option value=\"{{ status_value }}\" {% if project.status == status_value %}selected{% endif %}>\n                            {{ status_label }}\n                        </option>\n                        {% endfor %}\n                    </select>\n                </div>\n                \n                <div class=\"col-span-2\">\n                    <label for=\"entities\" class=\"block text-sm font-medium text-gray-700 mb-1\">Entità coinvolte</label>\n                    <select name=\"entities\" id=\"entities\" multiple class=\"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\" size=\"5\">\n                        {% for entity in entities %}\n                        <option value=\"{{ entity.id }}\" {% if entity in project.entities.all %}selected{% endif %}>\n                            {{ entity.name }}\n                        </option>\n                        {% endfor %}\n                    </select>\n                    <p class=\"mt-1 text-xs text-gray-500\">Tieni premuto Ctrl (o Cmd su Mac) per selezionare più entità</p>\n                </div>\n            </div>\n            \n            <div class=\"mt-8 flex justify-end\">\n                <a href=\"{% url 'projects:detail' project.id %}\" class=\"btn btn-secondary mr-3\">Annulla</a>\n                <button type=\"submit\" class=\"btn btn-primary\">Salva Modifiche</button>\n            </div>\n        </form>\n    </div>\n</div>\n{% endblock %} "}