{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "exproject/settings/base.py"}, "originalCode": "import os\nfrom pathlib import Path\nfrom decouple import config\nimport dj_database_url\n\nBASE_DIR = Path(__file__).resolve().parent.parent.parent\n\nSECRET_KEY = config('SECRET_KEY', default='django-insecure-dev-key-change-in-production')\n\nDEBUG = config('DEBUG', default=True, cast=bool)\n\nALLOWED_HOSTS = ['*']\n\nCSRF_TRUSTED_ORIGINS = [\n    'https://*.replit.app',\n    'https://*.replit.dev',\n    'https://*.replit.co',\n    'http://localhost:8000',\n    'https://localhost:8000',\n    'http://127.0.0.1:8000',\n    'https://127.0.0.1:8000',\n]\n\nINSTALLED_APPS = [\n    'django.contrib.admin',\n    'django.contrib.auth',\n    'django.contrib.contenttypes',\n    'django.contrib.sessions',\n    'django.contrib.messages',\n    'django.contrib.staticfiles',\n    # 'django.contrib.gis',  # GeoDjango - Commentato finché GDAL non è installato\n    'rest_framework',\n    'rest_framework.authtoken',\n    'django_filters',\n    'corsheaders',\n    'drf_spectacular',\n    # Custom apps\n    'apps.core',\n    'apps.projects',\n    'apps.workflow',\n    'apps.documents',\n    'apps.economic',\n    'apps.integrations',\n    'apps.public',\n    'apps.api',\n]\n\nMIDDLEWARE = [\n    'corsheaders.middleware.CorsMiddleware',\n    'django.middleware.security.SecurityMiddleware',\n    'django.contrib.sessions.middleware.SessionMiddleware',\n    'django.middleware.common.CommonMiddleware',\n    'django.middleware.csrf.CsrfViewMiddleware',\n    'django.contrib.auth.middleware.AuthenticationMiddleware',\n    'django.contrib.messages.middleware.MessageMiddleware',\n    'django.middleware.clickjacking.XFrameOptionsMiddleware',\n]\n\nROOT_URLCONF = 'exproject.urls'\n\nTEMPLATES = [\n    {\n        'BACKEND': 'django.template.backends.django.DjangoTemplates',\n        'DIRS': [BASE_DIR / 'templates'],\n        'APP_DIRS': True,\n        'OPTIONS': {\n            'context_processors': [\n                'django.template.context_processors.debug',\n                'django.template.context_processors.request',\n                'django.contrib.auth.context_processors.auth',\n                'django.contrib.messages.context_processors.messages',\n            ],\n        },\n    },\n]\n\nWSGI_APPLICATION = 'exproject.wsgi.application'\n\nDATABASES = {\n    'default': dj_database_url.config(\n        default=config('DATABASE_URL'),\n        conn_max_age=600,\n        conn_health_checks=True,\n    )\n}\n\n# Per PostGIS - Commentato temporaneamente finché GDAL non è installato\n# if 'postgresql' in DATABASES['default']['ENGINE']:\n#     DATABASES['default']['ENGINE'] = 'django.contrib.gis.db.backends.postgis'\n\nAUTH_PASSWORD_VALIDATORS = [\n    {\n        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',\n    },\n    {\n        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',\n    },\n    {\n        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',\n    },\n    {\n        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',\n    },\n]\n\nLANGUAGE_CODE = 'it-it'\nTIME_ZONE = 'Europe/Rome'\nUSE_I18N = True\nUSE_TZ = True\n\nSTATIC_URL = '/static/'\nSTATIC_ROOT = BASE_DIR / 'staticfiles'\nSTATICFILES_DIRS = [\n    BASE_DIR / 'static',\n]\n\nMEDIA_URL = '/media/'\nMEDIA_ROOT = BASE_DIR / 'media'\n\nCORS_ALLOW_ALL_ORIGINS = True\n\nREST_FRAMEWORK = {\n    'DEFAULT_AUTHENTICATION_CLASSES': [\n        'rest_framework.authentication.SessionAuthentication',\n        'rest_framework.authentication.TokenAuthentication',\n    ],\n    'DEFAULT_PERMISSION_CLASSES': [\n        'rest_framework.permissions.IsAuthenticated',\n    ],\n    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',\n    'PAGE_SIZE': 20,\n    'DEFAULT_FILTER_BACKENDS': [\n        'django_filters.rest_framework.DjangoFilterBackend',\n        'rest_framework.filters.SearchFilter',\n        'rest_framework.filters.OrderingFilter',\n    ],\n    'DEFAULT_RENDERER_CLASSES': [\n        'rest_framework.renderers.JSONRenderer',\n        'rest_framework.renderers.BrowsableAPIRenderer',\n    ],\n    'DEFAULT_PARSER_CLASSES': [\n        'rest_framework.parsers.JSONParser',\n        'rest_framework.parsers.FormParser',\n        'rest_framework.parsers.MultiPartParser',\n    ],\n    'EXCEPTION_HANDLER': 'apps.api.exceptions.custom_exception_handler',\n    'DEFAULT_THROTTLE_CLASSES': [\n        'rest_framework.throttling.AnonRateThrottle',\n        'rest_framework.throttling.UserRateThrottle'\n    ],\n    'DEFAULT_THROTTLE_RATES': {\n        'anon': '100/hour',\n        'user': '1000/hour'\n    },\n    'DEFAULT_METADATA_CLASS': 'rest_framework.metadata.SimpleMetadata',\n    'DEFAULT_VERSIONING_CLASS': 'rest_framework.versioning.URLPathVersioning',\n    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',\n}\n\n# Configurazione drf-spectacular per documentazione API\nSPECTACULAR_SETTINGS = {\n    'TITLE': 'ExProject API',\n    'DESCRIPTION': '''\n    API REST per ExProject - Sistema di gestione procedure espropriative secondo DPR 327/2001.\n    \n    Questa API fornisce accesso completo alle funzionalità del sistema ExProject per la gestione \n    di procedure di espropriazione per pubblica utilità, inclusi:\n    \n    - Gestione enti e progetti\n    - Anagrafica proprietari e particelle\n    - Generazione documenti da template\n    - Firme digitali e audit trail\n    - Workflow e notifiche\n    \n    ## Autenticazione\n    \n    L'API utilizza Token Authentication. Includi l'header:\n    ```\n    Authorization: Token your_token_here\n    ```\n    \n    ## Versioning\n    \n    L'API è versionata tramite URL. La versione corrente è v1:\n    ```\n    /api/v1/endpoint/\n    ```\n    ''',\n    'VERSION': '1.0.0',\n    'SERVE_INCLUDE_SCHEMA': False,\n    'CONTACT': {\n        'name': 'ExProject Team',\n        'email': '<EMAIL>'\n    },\n    'LICENSE': {\n        'name': 'Proprietario',\n    },\n    'TAGS': [\n        {\n            'name': 'Entities',\n            'description': 'Gestione enti pubblici e privati'\n        },\n        {\n            'name': 'Projects', \n            'description': 'Gestione progetti di espropriazione'\n        },\n        {\n            'name': 'Parcels',\n            'description': 'Gestione particelle catastali'\n        },\n        {\n            'name': 'Owners',\n            'description': 'Anagrafica proprietari'\n        },\n        {\n            'name': 'Documents',\n            'description': 'Gestione documenti e template'\n        },\n        {\n            'name': 'Audit',\n            'description': 'Audit trail e log di sistema'\n        }\n    ],\n    'COMPONENT_SPLIT_REQUEST': True,\n    'SCHEMA_PATH_PREFIX': '/api/',\n    'SCHEMA_PATH_PREFIX_TRIM': False,\n    'PREPROCESSING_HOOKS': [],\n    'POSTPROCESSING_HOOKS': [],\n    'ENUM_NAME_OVERRIDES': {\n        'EntityTypeChoices': 'apps.core.models.ENTITY_TYPE_CHOICES',\n        'ProjectStatusChoices': 'apps.core.models.PROJECT_STATUS_CHOICES',\n        'DocumentStatusChoices': 'apps.core.models.DOCUMENT_STATUS_CHOICES',\n    },\n    'SORT_OPERATIONS': True,\n    'SWAGGER_UI_SETTINGS': {\n        'deepLinking': True,\n        'persistAuthorization': True,\n        'displayOperationId': False,\n        'defaultModelsExpandDepth': 1,\n        'defaultModelExpandDepth': 1,\n    },\n    'SCHEMA_COERCE_PATH_PK': True,\n    'DISABLE_ERRORS_AND_WARNINGS': True,\n    'SECURITY': [\n        {\n            'tokenAuth': []\n        }\n    ],\n    'COMPONENTS': {\n        'securitySchemes': {\n            'tokenAuth': {\n                'type': 'apiKey',\n                'in': 'header',\n                'name': 'Authorization',\n                'description': 'Token-based authentication with required prefix \"Token\"'\n            }\n        }\n    },\n}\n\nLOGIN_URL = '/exproject/login/'\nLOGIN_REDIRECT_URL = '/exproject/'\nLOGOUT_REDIRECT_URL = '/'\n\n# CSRF exemption for API endpoints\nCSRF_EXEMPT_URLS = [\n    r'^/api/',\n]\n\nDEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField' ", "modifiedCode": "import os\nfrom pathlib import Path\nfrom decouple import config\nimport dj_database_url\n\nBASE_DIR = Path(__file__).resolve().parent.parent.parent\n\nSECRET_KEY = config('SECRET_KEY', default='django-insecure-dev-key-change-in-production')\n\nDEBUG = config('DEBUG', default=True, cast=bool)\n\nALLOWED_HOSTS = ['*']\n\nCSRF_TRUSTED_ORIGINS = [\n    'https://*.replit.app',\n    'https://*.replit.dev',\n    'https://*.replit.co',\n    'http://localhost:8000',\n    'https://localhost:8000',\n    'http://127.0.0.1:8000',\n    'https://127.0.0.1:8000',\n]\n\nINSTALLED_APPS = [\n    'django.contrib.admin',\n    'django.contrib.auth',\n    'django.contrib.contenttypes',\n    'django.contrib.sessions',\n    'django.contrib.messages',\n    'django.contrib.staticfiles',\n    # 'django.contrib.gis',  # GeoDjango - Commentato finché GDAL non è installato\n    'rest_framework',\n    'rest_framework.authtoken',\n    'django_filters',\n    'corsheaders',\n    'drf_spectacular',\n    # Custom apps\n    'apps.core',\n    'apps.projects',\n    'apps.workflow',\n    'apps.documents',\n    'apps.economic',\n    'apps.integrations',\n    'apps.public',\n    'apps.api',\n    'apps.ui',  # Design System e UI Components\n]\n\nMIDDLEWARE = [\n    'corsheaders.middleware.CorsMiddleware',\n    'django.middleware.security.SecurityMiddleware',\n    'django.contrib.sessions.middleware.SessionMiddleware',\n    'django.middleware.common.CommonMiddleware',\n    'django.middleware.csrf.CsrfViewMiddleware',\n    'django.contrib.auth.middleware.AuthenticationMiddleware',\n    'django.contrib.messages.middleware.MessageMiddleware',\n    'django.middleware.clickjacking.XFrameOptionsMiddleware',\n]\n\nROOT_URLCONF = 'exproject.urls'\n\nTEMPLATES = [\n    {\n        'BACKEND': 'django.template.backends.django.DjangoTemplates',\n        'DIRS': [BASE_DIR / 'templates'],\n        'APP_DIRS': True,\n        'OPTIONS': {\n            'context_processors': [\n                'django.template.context_processors.debug',\n                'django.template.context_processors.request',\n                'django.contrib.auth.context_processors.auth',\n                'django.contrib.messages.context_processors.messages',\n            ],\n        },\n    },\n]\n\nWSGI_APPLICATION = 'exproject.wsgi.application'\n\nDATABASES = {\n    'default': dj_database_url.config(\n        default=config('DATABASE_URL'),\n        conn_max_age=600,\n        conn_health_checks=True,\n    )\n}\n\n# Per PostGIS - Commentato temporaneamente finché GDAL non è installato\n# if 'postgresql' in DATABASES['default']['ENGINE']:\n#     DATABASES['default']['ENGINE'] = 'django.contrib.gis.db.backends.postgis'\n\nAUTH_PASSWORD_VALIDATORS = [\n    {\n        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',\n    },\n    {\n        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',\n    },\n    {\n        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',\n    },\n    {\n        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',\n    },\n]\n\nLANGUAGE_CODE = 'it-it'\nTIME_ZONE = 'Europe/Rome'\nUSE_I18N = True\nUSE_TZ = True\n\nSTATIC_URL = '/static/'\nSTATIC_ROOT = BASE_DIR / 'staticfiles'\nSTATICFILES_DIRS = [\n    BASE_DIR / 'static',\n]\n\nMEDIA_URL = '/media/'\nMEDIA_ROOT = BASE_DIR / 'media'\n\nCORS_ALLOW_ALL_ORIGINS = True\n\nREST_FRAMEWORK = {\n    'DEFAULT_AUTHENTICATION_CLASSES': [\n        'rest_framework.authentication.SessionAuthentication',\n        'rest_framework.authentication.TokenAuthentication',\n    ],\n    'DEFAULT_PERMISSION_CLASSES': [\n        'rest_framework.permissions.IsAuthenticated',\n    ],\n    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',\n    'PAGE_SIZE': 20,\n    'DEFAULT_FILTER_BACKENDS': [\n        'django_filters.rest_framework.DjangoFilterBackend',\n        'rest_framework.filters.SearchFilter',\n        'rest_framework.filters.OrderingFilter',\n    ],\n    'DEFAULT_RENDERER_CLASSES': [\n        'rest_framework.renderers.JSONRenderer',\n        'rest_framework.renderers.BrowsableAPIRenderer',\n    ],\n    'DEFAULT_PARSER_CLASSES': [\n        'rest_framework.parsers.JSONParser',\n        'rest_framework.parsers.FormParser',\n        'rest_framework.parsers.MultiPartParser',\n    ],\n    'EXCEPTION_HANDLER': 'apps.api.exceptions.custom_exception_handler',\n    'DEFAULT_THROTTLE_CLASSES': [\n        'rest_framework.throttling.AnonRateThrottle',\n        'rest_framework.throttling.UserRateThrottle'\n    ],\n    'DEFAULT_THROTTLE_RATES': {\n        'anon': '100/hour',\n        'user': '1000/hour'\n    },\n    'DEFAULT_METADATA_CLASS': 'rest_framework.metadata.SimpleMetadata',\n    'DEFAULT_VERSIONING_CLASS': 'rest_framework.versioning.URLPathVersioning',\n    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',\n}\n\n# Configurazione drf-spectacular per documentazione API\nSPECTACULAR_SETTINGS = {\n    'TITLE': 'ExProject API',\n    'DESCRIPTION': '''\n    API REST per ExProject - Sistema di gestione procedure espropriative secondo DPR 327/2001.\n    \n    Questa API fornisce accesso completo alle funzionalità del sistema ExProject per la gestione \n    di procedure di espropriazione per pubblica utilità, inclusi:\n    \n    - Gestione enti e progetti\n    - Anagrafica proprietari e particelle\n    - Generazione documenti da template\n    - Firme digitali e audit trail\n    - Workflow e notifiche\n    \n    ## Autenticazione\n    \n    L'API utilizza Token Authentication. Includi l'header:\n    ```\n    Authorization: Token your_token_here\n    ```\n    \n    ## Versioning\n    \n    L'API è versionata tramite URL. La versione corrente è v1:\n    ```\n    /api/v1/endpoint/\n    ```\n    ''',\n    'VERSION': '1.0.0',\n    'SERVE_INCLUDE_SCHEMA': False,\n    'CONTACT': {\n        'name': 'ExProject Team',\n        'email': '<EMAIL>'\n    },\n    'LICENSE': {\n        'name': 'Proprietario',\n    },\n    'TAGS': [\n        {\n            'name': 'Entities',\n            'description': 'Gestione enti pubblici e privati'\n        },\n        {\n            'name': 'Projects', \n            'description': 'Gestione progetti di espropriazione'\n        },\n        {\n            'name': 'Parcels',\n            'description': 'Gestione particelle catastali'\n        },\n        {\n            'name': 'Owners',\n            'description': 'Anagrafica proprietari'\n        },\n        {\n            'name': 'Documents',\n            'description': 'Gestione documenti e template'\n        },\n        {\n            'name': 'Audit',\n            'description': 'Audit trail e log di sistema'\n        }\n    ],\n    'COMPONENT_SPLIT_REQUEST': True,\n    'SCHEMA_PATH_PREFIX': '/api/',\n    'SCHEMA_PATH_PREFIX_TRIM': False,\n    'PREPROCESSING_HOOKS': [],\n    'POSTPROCESSING_HOOKS': [],\n    'ENUM_NAME_OVERRIDES': {\n        'EntityTypeChoices': 'apps.core.models.ENTITY_TYPE_CHOICES',\n        'ProjectStatusChoices': 'apps.core.models.PROJECT_STATUS_CHOICES',\n        'DocumentStatusChoices': 'apps.core.models.DOCUMENT_STATUS_CHOICES',\n    },\n    'SORT_OPERATIONS': True,\n    'SWAGGER_UI_SETTINGS': {\n        'deepLinking': True,\n        'persistAuthorization': True,\n        'displayOperationId': False,\n        'defaultModelsExpandDepth': 1,\n        'defaultModelExpandDepth': 1,\n    },\n    'SCHEMA_COERCE_PATH_PK': True,\n    'DISABLE_ERRORS_AND_WARNINGS': True,\n    'SECURITY': [\n        {\n            'tokenAuth': []\n        }\n    ],\n    'COMPONENTS': {\n        'securitySchemes': {\n            'tokenAuth': {\n                'type': 'apiKey',\n                'in': 'header',\n                'name': 'Authorization',\n                'description': 'Token-based authentication with required prefix \"Token\"'\n            }\n        }\n    },\n}\n\nLOGIN_URL = '/exproject/login/'\nLOGIN_REDIRECT_URL = '/exproject/'\nLOGOUT_REDIRECT_URL = '/'\n\n# CSRF exemption for API endpoints\nCSRF_EXEMPT_URLS = [\n    r'^/api/',\n]\n\nDEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField' "}