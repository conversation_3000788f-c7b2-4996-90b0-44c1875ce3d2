{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "apps/ui/templatetags/ui_components.py"}, "modifiedCode": "from django import template\nfrom django.utils.safestring import mark_safe\nfrom django.template.loader import render_to_string\nimport json\n\nregister = template.Library()\n\n\n@register.inclusion_tag('ui/components/button.html')\ndef ui_button(text=\"Button\", variant=\"default\", size=\"default\", **kwargs):\n    \"\"\"\n    Componente Button con varianti shadcn/ui style\n    \n    Varianti: default, destructive, outline, secondary, ghost, link\n    Sizes: default, sm, lg, icon\n    \"\"\"\n    return {\n        'text': text,\n        'variant': variant,\n        'size': size,\n        'attrs': kwargs\n    }\n\n\n@register.inclusion_tag('ui/components/card.html')\ndef ui_card(title=None, description=None, **kwargs):\n    \"\"\"\n    Componente Card con header, content e footer\n    \"\"\"\n    return {\n        'title': title,\n        'description': description,\n        'attrs': kwargs\n    }\n\n\n@register.inclusion_tag('ui/components/badge.html')\ndef ui_badge(text, variant=\"default\", **kwargs):\n    \"\"\"\n    Componente Badge per status e labels\n    \n    Varianti: default, secondary, destructive, outline\n    \"\"\"\n    return {\n        'text': text,\n        'variant': variant,\n        'attrs': kwargs\n    }\n\n\n@register.inclusion_tag('ui/components/input.html')\ndef ui_input(name, label=None, placeholder=None, type=\"text\", **kwargs):\n    \"\"\"\n    Componente Input con label e validazione\n    \"\"\"\n    return {\n        'name': name,\n        'label': label,\n        'placeholder': placeholder,\n        'type': type,\n        'attrs': kwargs\n    }\n\n\n@register.inclusion_tag('ui/components/select.html')\ndef ui_select(name, options, label=None, **kwargs):\n    \"\"\"\n    Componente Select dropdown\n    \"\"\"\n    return {\n        'name': name,\n        'options': options,\n        'label': label,\n        'attrs': kwargs\n    }\n\n\n@register.inclusion_tag('ui/components/table.html')\ndef ui_table(headers, rows, **kwargs):\n    \"\"\"\n    Componente Table responsive\n    \"\"\"\n    return {\n        'headers': headers,\n        'rows': rows,\n        'attrs': kwargs\n    }\n\n\n@register.inclusion_tag('ui/components/alert.html')\ndef ui_alert(message, variant=\"default\", title=None, **kwargs):\n    \"\"\"\n    Componente Alert per messaggi\n    \n    Varianti: default, destructive, warning, success\n    \"\"\"\n    return {\n        'message': message,\n        'variant': variant,\n        'title': title,\n        'attrs': kwargs\n    }\n\n\n@register.inclusion_tag('ui/components/dialog.html')\ndef ui_dialog(title, content, trigger_text=\"Open\", **kwargs):\n    \"\"\"\n    Componente Dialog/Modal\n    \"\"\"\n    return {\n        'title': title,\n        'content': content,\n        'trigger_text': trigger_text,\n        'attrs': kwargs\n    }\n\n\n@register.inclusion_tag('ui/components/tabs.html')\ndef ui_tabs(tabs, **kwargs):\n    \"\"\"\n    Componente Tabs\n    \n    tabs: [{'id': 'tab1', 'label': 'Tab 1', 'content': 'Content 1'}]\n    \"\"\"\n    return {\n        'tabs': tabs,\n        'attrs': kwargs\n    }\n\n\n@register.inclusion_tag('ui/components/progress.html')\ndef ui_progress(value, max_value=100, **kwargs):\n    \"\"\"\n    Componente Progress bar\n    \"\"\"\n    percentage = (value / max_value) * 100 if max_value > 0 else 0\n    return {\n        'value': value,\n        'max_value': max_value,\n        'percentage': percentage,\n        'attrs': kwargs\n    }\n\n\n@register.inclusion_tag('ui/components/skeleton.html')\ndef ui_skeleton(width=\"100%\", height=\"20px\", **kwargs):\n    \"\"\"\n    Componente Skeleton per loading states\n    \"\"\"\n    return {\n        'width': width,\n        'height': height,\n        'attrs': kwargs\n    }\n\n\n@register.inclusion_tag('ui/components/avatar.html')\ndef ui_avatar(src=None, alt=\"Avatar\", fallback=None, size=\"default\", **kwargs):\n    \"\"\"\n    Componente Avatar\n    \n    Sizes: sm, default, lg\n    \"\"\"\n    return {\n        'src': src,\n        'alt': alt,\n        'fallback': fallback,\n        'size': size,\n        'attrs': kwargs\n    }\n\n\n@register.inclusion_tag('ui/components/separator.html')\ndef ui_separator(orientation=\"horizontal\", **kwargs):\n    \"\"\"\n    Componente Separator/Divider\n    \"\"\"\n    return {\n        'orientation': orientation,\n        'attrs': kwargs\n    }\n\n\n@register.inclusion_tag('ui/layouts/dashboard.html')\ndef ui_dashboard_layout(title, **kwargs):\n    \"\"\"\n    Layout Dashboard con sidebar e header\n    \"\"\"\n    return {\n        'title': title,\n        'attrs': kwargs\n    }\n\n\n@register.inclusion_tag('ui/layouts/form.html')\ndef ui_form_layout(title, **kwargs):\n    \"\"\"\n    Layout per form con validazione\n    \"\"\"\n    return {\n        'title': title,\n        'attrs': kwargs\n    }\n\n\n@register.simple_tag\ndef ui_icon(name, size=\"4\", **kwargs):\n    \"\"\"\n    Componente Icon usando Heroicons\n    \"\"\"\n    # Mapping degli icon names più comuni\n    icons = {\n        'plus': 'M12 4.5v15m7.5-7.5h-15',\n        'edit': 'm16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10',\n        'delete': 'm14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0',\n        'view': 'M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z',\n        'search': 'm21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z',\n        'check': 'm4.5 12.75 6 6 9-13.5',\n        'x': 'M6 18 18 6M6 6l12 12',\n        'chevron-down': 'm19.5 8.25-7.5 7.5-7.5-7.5',\n        'chevron-up': 'm4.5 15.75 7.5-7.5 7.5 7.5',\n        'chevron-left': 'M15.75 19.5 8.25 12l7.5-7.5',\n        'chevron-right': 'm8.25 4.5 7.5 7.5-7.5 7.5',\n    }\n    \n    path = icons.get(name, icons['plus'])  # Default to plus icon\n    \n    classes = f\"h-{size} w-{size}\"\n    if 'class' in kwargs:\n        classes += f\" {kwargs['class']}\"\n    \n    svg = f'''\n    <svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" class=\"{classes}\">\n        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" d=\"{path}\" />\n    </svg>\n    '''\n    \n    return mark_safe(svg)\n\n\n@register.simple_tag\ndef ui_theme_css():\n    \"\"\"\n    Genera CSS custom properties per il tema attivo\n    \"\"\"\n    from apps.ui.models import Theme\n    from apps.ui.design_tokens import get_css_variables, DEFAULT_THEME_CONFIG\n    \n    theme = Theme.get_active_theme()\n    if theme:\n        theme_config = {\n            \"colors\": theme.colors,\n            \"dark_colors\": theme.colors.get(\"dark\", {}),\n            \"typography\": theme.typography,\n            \"spacing\": theme.spacing,\n            \"borders\": theme.borders,\n            \"shadows\": theme.shadows,\n        }\n    else:\n        theme_config = DEFAULT_THEME_CONFIG\n    \n    css = get_css_variables(theme_config)\n    return mark_safe(f\"<style>{css}</style>\")\n\n\*****************\ndef ui_variant_classes(component_type, variant):\n    \"\"\"\n    Restituisce le classi CSS per una variante di componente\n    \"\"\"\n    variant_map = {\n        'button': {\n            'default': 'bg-primary text-primary-foreground hover:bg-primary/90',\n            'destructive': 'bg-destructive text-destructive-foreground hover:bg-destructive/90',\n            'outline': 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',\n            'secondary': 'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n            'ghost': 'hover:bg-accent hover:text-accent-foreground',\n            'link': 'text-primary underline-offset-4 hover:underline',\n        },\n        'badge': {\n            'default': 'bg-primary text-primary-foreground hover:bg-primary/80',\n            'secondary': 'bg-secondary text-secondary-foreground hover:bg-secondary/80',\n            'destructive': 'bg-destructive text-destructive-foreground hover:bg-destructive/80',\n            'outline': 'text-foreground border border-input',\n        },\n        'alert': {\n            'default': 'bg-background text-foreground border',\n            'destructive': 'border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive',\n            'warning': 'border-warning/50 text-warning dark:border-warning [&>svg]:text-warning',\n            'success': 'border-success/50 text-success dark:border-success [&>svg]:text-success',\n        }\n    }\n    \n    return variant_map.get(component_type, {}).get(variant, '')\n"}