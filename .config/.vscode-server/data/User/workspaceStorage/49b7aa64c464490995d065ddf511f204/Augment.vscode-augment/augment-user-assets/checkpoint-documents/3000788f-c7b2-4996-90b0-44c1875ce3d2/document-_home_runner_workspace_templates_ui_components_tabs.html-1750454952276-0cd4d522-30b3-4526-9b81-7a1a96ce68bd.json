{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/tabs.html"}, "modifiedCode": "{# Componente Tabs #}\n<div x-data=\"{ activeTab: '{{ tabs.0.id }}' }\" class=\"w-full\">\n    <!-- Tab List -->\n    <div class=\"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\">\n        {% for tab in tabs %}\n        <button \n            @click=\"activeTab = '{{ tab.id }}'\"\n            :class=\"activeTab === '{{ tab.id }}' ? 'bg-background text-foreground shadow-sm' : ''\"\n            class=\"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\">\n            {{ tab.label }}\n        </button>\n        {% endfor %}\n    </div>\n    \n    <!-- Tab Content -->\n    {% for tab in tabs %}\n    <div x-show=\"activeTab === '{{ tab.id }}'\" class=\"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\">\n        {{ tab.content }}\n    </div>\n    {% endfor %}\n</div>\n"}