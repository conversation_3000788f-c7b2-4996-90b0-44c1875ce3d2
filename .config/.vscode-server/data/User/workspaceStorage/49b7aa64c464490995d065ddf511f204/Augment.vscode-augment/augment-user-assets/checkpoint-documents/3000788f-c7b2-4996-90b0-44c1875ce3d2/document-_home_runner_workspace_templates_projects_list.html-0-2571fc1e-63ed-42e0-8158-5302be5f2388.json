{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/projects/list.html"}, "originalCode": "{% extends 'base.html' %}\n\n{% block title %}Progetti - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <div class=\"flex justify-between items-center mb-6\">\n        <h1 class=\"text-2xl font-bold text-gray-900\"><PERSON><PERSON><PERSON></h1>\n        <a href=\"{% url 'projects:create' %}\" class=\"btn btn-primary flex items-center\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fill-rule=\"evenodd\" d=\"M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z\" clip-rule=\"evenodd\" />\n            </svg>\n            Nuovo Progetto\n        </a>\n    </div>\n    \n    {% if messages %}\n    <div class=\"mb-6\">\n        {% for message in messages %}\n        <div class=\"p-4 mb-4 rounded {% if message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-red-100 text-red-700{% endif %}\">\n            {{ message }}\n        </div>\n        {% endfor %}\n    </div>\n    {% endif %}\n    \n    {% if projects %}\n    <div class=\"bg-white shadow overflow-hidden rounded-lg\">\n        <table class=\"min-w-full divide-y divide-gray-200\">\n            <thead>\n                <tr>\n                    <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Progetto\n                    </th>\n                    <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Stato\n                    </th>\n                    <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Budget\n                    </th>\n                    <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Particelle\n                    </th>\n                    <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Data Creazione\n                    </th>\n                    <th class=\"px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Azioni\n                    </th>\n                </tr>\n            </thead>\n            <tbody class=\"bg-white divide-y divide-gray-200\">\n                {% for project in projects %}\n                <tr class=\"hover:bg-gray-50\">\n                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                        <div class=\"flex items-center\">\n                            <div class=\"flex-shrink-0 h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium\">\n                                {{ project.name|slice:\":1\" }}\n                            </div>\n                            <div class=\"ml-4\">\n                                <div class=\"text-sm font-medium text-gray-900\">\n                                    <a href=\"{% url 'projects:detail' project.id %}\" class=\"hover:text-blue-600\">\n                                        {{ project.name }}\n                                    </a>\n                                </div>\n                                <div class=\"text-sm text-gray-500\">\n                                    {{ project.description|truncatechars:60 }}\n                                </div>\n                            </div>\n                        </div>\n                    </td>\n                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                        <span class=\"project-status-{{ project.status }}\">\n                            {{ project.get_status_display }}\n                        </span>\n                    </td>\n                    <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        € {{ project.budget|floatformat:2 }}\n                    </td>\n                    <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {{ project.parcels.count }}\n                    </td>\n                    <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {{ project.created_at|date:\"d/m/Y\" }}\n                    </td>\n                    <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                        <div class=\"flex justify-end space-x-2\">\n                            <a href=\"{% url 'projects:detail' project.id %}\" class=\"text-blue-600 hover:text-blue-900\" title=\"Visualizza\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                                </svg>\n                            </a>\n                            <a href=\"{% url 'projects:edit' project.id %}\" class=\"text-gray-600 hover:text-gray-900\" title=\"Modifica\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                                </svg>\n                            </a>\n                            <a href=\"{% url 'projects:delete' project.id %}\" class=\"text-red-600 hover:text-red-900\" title=\"Elimina\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                                </svg>\n                            </a>\n                        </div>\n                    </td>\n                </tr>\n                {% endfor %}\n            </tbody>\n        </table>\n    </div>\n    {% else %}\n    <div class=\"bg-white shadow rounded-lg p-8 text-center\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12 mx-auto text-gray-400 mb-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n        </svg>\n        <p class=\"text-gray-600 mb-4\">Non sono stati trovati progetti.</p>\n        <a href=\"{% url 'projects:create' %}\" class=\"btn btn-primary\">\n            Crea Nuovo Progetto\n        </a>\n    </div>\n    {% endif %}\n</div>\n{% endblock %} ", "modifiedCode": "{% extends 'base.html' %}\n\n{% block title %}Progetti - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <div class=\"flex justify-between items-center mb-6\">\n        <h1 class=\"text-2xl font-bold text-gray-900\"><PERSON><PERSON><PERSON></h1>\n        <a href=\"{% url 'projects:create' %}\" class=\"btn btn-primary flex items-center\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fill-rule=\"evenodd\" d=\"M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z\" clip-rule=\"evenodd\" />\n            </svg>\n            Nuovo Progetto\n        </a>\n    </div>\n    \n    {% if messages %}\n    <div class=\"mb-6\">\n        {% for message in messages %}\n        <div class=\"p-4 mb-4 rounded {% if message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-red-100 text-red-700{% endif %}\">\n            {{ message }}\n        </div>\n        {% endfor %}\n    </div>\n    {% endif %}\n    \n    {% if projects %}\n    <div class=\"bg-white shadow overflow-hidden rounded-lg\">\n        <table class=\"min-w-full divide-y divide-gray-200\">\n            <thead>\n                <tr>\n                    <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Progetto\n                    </th>\n                    <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Stato\n                    </th>\n                    <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Budget\n                    </th>\n                    <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Particelle\n                    </th>\n                    <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Data Creazione\n                    </th>\n                    <th class=\"px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        Azioni\n                    </th>\n                </tr>\n            </thead>\n            <tbody class=\"bg-white divide-y divide-gray-200\">\n                {% for project in projects %}\n                <tr class=\"hover:bg-gray-50\">\n                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                        <div class=\"flex items-center\">\n                            <div class=\"flex-shrink-0 h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium\">\n                                {{ project.name|slice:\":1\" }}\n                            </div>\n                            <div class=\"ml-4\">\n                                <div class=\"text-sm font-medium text-gray-900\">\n                                    <a href=\"{% url 'projects:detail' project.id %}\" class=\"hover:text-blue-600\">\n                                        {{ project.name }}\n                                    </a>\n                                </div>\n                                <div class=\"text-sm text-gray-500\">\n                                    {{ project.description|truncatechars:60 }}\n                                </div>\n                            </div>\n                        </div>\n                    </td>\n                    <td class=\"px-6 py-4 whitespace-nowrap\">\n                        <span class=\"project-status-{{ project.status }}\">\n                            {{ project.get_status_display }}\n                        </span>\n                    </td>\n                    <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        € {{ project.budget|floatformat:2 }}\n                    </td>\n                    <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {{ project.parcels.count }}\n                    </td>\n                    <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                        {{ project.created_at|date:\"d/m/Y\" }}\n                    </td>\n                    <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                        <div class=\"flex justify-end space-x-2\">\n                            <a href=\"{% url 'projects:detail' project.id %}\" class=\"text-blue-600 hover:text-blue-900\" title=\"Visualizza\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                                </svg>\n                            </a>\n                            <a href=\"{% url 'projects:edit' project.id %}\" class=\"text-gray-600 hover:text-gray-900\" title=\"Modifica\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                                </svg>\n                            </a>\n                            <a href=\"{% url 'projects:delete' project.id %}\" class=\"text-red-600 hover:text-red-900\" title=\"Elimina\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                                </svg>\n                            </a>\n                        </div>\n                    </td>\n                </tr>\n                {% endfor %}\n            </tbody>\n        </table>\n    </div>\n    {% else %}\n    <div class=\"bg-white shadow rounded-lg p-8 text-center\">\n        <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12 mx-auto text-gray-400 mb-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n        </svg>\n        <p class=\"text-gray-600 mb-4\">Non sono stati trovati progetti.</p>\n        <a href=\"{% url 'projects:create' %}\" class=\"btn btn-primary\">\n            Crea Nuovo Progetto\n        </a>\n    </div>\n    {% endif %}\n</div>\n{% endblock %} "}