{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/login.html"}, "originalCode": "{% extends 'base.html' %}\n\n{% block title %}Login - ExProject{% endblock %}\n\n{% block header %}{% endblock %}\n{% block navigation %}{% endblock %}\n\n{% block main %}\n<div class=\"min-h-screen flex items-center justify-center bg-gray-50\">\n    <div class=\"max-w-md w-full space-y-8\">\n        <div class=\"text-center\">\n            <h2 class=\"mt-6 text-3xl font-extrabold text-gray-900\">\n                Accesso Area Riservata\n            </h2>\n            <p class=\"mt-2 text-sm text-gray-600\">\n                ExProject - Sistema Gestione Espropri\n            </p>\n        </div>\n        \n        {% if messages %}\n            <div class=\"mt-4\">\n                {% for message in messages %}\n                    <div class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\" role=\"alert\">\n                        <span class=\"block sm:inline\">{{ message }}</span>\n                    </div>\n                {% endfor %}\n            </div>\n        {% endif %}\n        \n        <form class=\"mt-8 space-y-6\" method=\"post\">\n            {% csrf_token %}\n            <div class=\"rounded-md shadow-sm -space-y-px\">\n                <div>\n                    <label for=\"username\" class=\"sr-only\">Username</label>\n                    <input id=\"username\" name=\"username\" type=\"text\" required \n                           class=\"relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\" \n                           placeholder=\"Username\">\n                </div>\n                <div>\n                    <label for=\"password\" class=\"sr-only\">Password</label>\n                    <input id=\"password\" name=\"password\" type=\"password\" required \n                           class=\"relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\" \n                           placeholder=\"Password\">\n                </div>\n            </div>\n\n            <div>\n                <button type=\"submit\" \n                        class=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                    Accedi\n                </button>\n            </div>\n            \n            <div class=\"text-center\">\n                <a href=\"{% url 'public:home' %}\" class=\"text-blue-600 hover:text-blue-500\">\n                    ← Torna al sito pubblico\n                </a>\n            </div>\n        </form>\n    </div>\n</div>\n{% endblock %}\n\n{% block footer %}{% endblock %}", "modifiedCode": "{% extends 'base.html' %}\n\n{% block title %}Login - ExProject{% endblock %}\n\n{% block header %}{% endblock %}\n{% block navigation %}{% endblock %}\n\n{% block main %}\n<div class=\"min-h-screen flex items-center justify-center bg-gray-50\">\n    <div class=\"max-w-md w-full space-y-8\">\n        <div class=\"text-center\">\n            <h2 class=\"mt-6 text-3xl font-extrabold text-gray-900\">\n                Accesso Area Riservata\n            </h2>\n            <p class=\"mt-2 text-sm text-gray-600\">\n                ExProject - Sistema Gestione Espropri\n            </p>\n        </div>\n        \n        {% if messages %}\n            <div class=\"mt-4\">\n                {% for message in messages %}\n                    <div class=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative\" role=\"alert\">\n                        <span class=\"block sm:inline\">{{ message }}</span>\n                    </div>\n                {% endfor %}\n            </div>\n        {% endif %}\n        \n        <form class=\"mt-8 space-y-6\" method=\"post\">\n            {% csrf_token %}\n            <div class=\"rounded-md shadow-sm -space-y-px\">\n                <div>\n                    <label for=\"username\" class=\"sr-only\">Username</label>\n                    <input id=\"username\" name=\"username\" type=\"text\" required \n                           class=\"relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\" \n                           placeholder=\"Username\">\n                </div>\n                <div>\n                    <label for=\"password\" class=\"sr-only\">Password</label>\n                    <input id=\"password\" name=\"password\" type=\"password\" required \n                           class=\"relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\" \n                           placeholder=\"Password\">\n                </div>\n            </div>\n\n            <div>\n                <button type=\"submit\" \n                        class=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                    Accedi\n                </button>\n            </div>\n            \n            <div class=\"text-center\">\n                <a href=\"{% url 'public:home' %}\" class=\"text-blue-600 hover:text-blue-500\">\n                    ← Torna al sito pubblico\n                </a>\n            </div>\n        </form>\n    </div>\n</div>\n{% endblock %}\n\n{% block footer %}{% endblock %}"}