{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/settings.html"}, "originalCode": "{% extends \"base.html\" %}\n\n{% block title %}Impostazioni{% endblock %}\n\n{% block content %}\n<div class=\"max-w-2xl mx-auto py-8\">\n    <h1 class=\"text-2xl font-bold mb-6\">Impostazioni</h1>\n    \n    {% if messages %}\n    <div class=\"mb-6\">\n        {% for message in messages %}\n        <div class=\"p-4 mb-4 rounded {% if message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-red-100 text-red-700{% endif %}\">\n            {{ message }}\n        </div>\n        {% endfor %}\n    </div>\n    {% endif %}\n    \n    <form method=\"post\" class=\"space-y-6\">\n        {% csrf_token %}\n        \n        <div class=\"bg-white shadow rounded-lg divide-y\">\n            <!-- Notifiche -->\n            <div class=\"p-6\">\n                <h2 class=\"text-lg font-medium mb-4\">Notifiche</h2>\n                <div class=\"space-y-4\">\n                    <div class=\"flex items-center\">\n                        <input type=\"checkbox\" name=\"notify_email\" id=\"notify_email\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\">\n                        <label for=\"notify_email\" class=\"ml-2 block text-sm text-gray-700\">\n                            Ricevi notifiche via email\n                        </label>\n                    </div>\n                    <div class=\"flex items-center\">\n                        <input type=\"checkbox\" name=\"notify_browser\" id=\"notify_browser\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\">\n                        <label for=\"notify_browser\" class=\"ml-2 block text-sm text-gray-700\">\n                            Ricevi notifiche nel browser\n                        </label>\n                    </div>\n                </div>\n            </div>\n            \n            <!-- Preferenze Interfaccia -->\n            <div class=\"p-6\">\n                <h2 class=\"text-lg font-medium mb-4\">Preferenze Interfaccia</h2>\n                <div class=\"space-y-4\">\n                    <div>\n                        <label for=\"theme\" class=\"block text-sm font-medium text-gray-700\">Tema</label>\n                        <select name=\"theme\" id=\"theme\" class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n                            <option value=\"light\">Chiaro</option>\n                            <option value=\"dark\">Scuro</option>\n                            <option value=\"system\">Sistema</option>\n                        </select>\n                    </div>\n                    <div>\n                        <label for=\"language\" class=\"block text-sm font-medium text-gray-700\">Lingua</label>\n                        <select name=\"language\" id=\"language\" class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n                            <option value=\"it\">Italiano</option>\n                            <option value=\"en\">English</option>\n                        </select>\n                    </div>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"flex justify-end\">\n            <button type=\"submit\" class=\"btn btn-primary\">\n                Salva Impostazioni\n            </button>\n        </div>\n    </form>\n</div>\n{% endblock %} ", "modifiedCode": "{% extends \"base.html\" %}\n\n{% block title %}Impostazioni{% endblock %}\n\n{% block content %}\n<div class=\"max-w-2xl mx-auto py-8\">\n    <h1 class=\"text-2xl font-bold mb-6\">Impostazioni</h1>\n    \n    {% if messages %}\n    <div class=\"mb-6\">\n        {% for message in messages %}\n        <div class=\"p-4 mb-4 rounded {% if message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-red-100 text-red-700{% endif %}\">\n            {{ message }}\n        </div>\n        {% endfor %}\n    </div>\n    {% endif %}\n    \n    <form method=\"post\" class=\"space-y-6\">\n        {% csrf_token %}\n        \n        <div class=\"bg-white shadow rounded-lg divide-y\">\n            <!-- Notifiche -->\n            <div class=\"p-6\">\n                <h2 class=\"text-lg font-medium mb-4\">Notifiche</h2>\n                <div class=\"space-y-4\">\n                    <div class=\"flex items-center\">\n                        <input type=\"checkbox\" name=\"notify_email\" id=\"notify_email\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\">\n                        <label for=\"notify_email\" class=\"ml-2 block text-sm text-gray-700\">\n                            Ricevi notifiche via email\n                        </label>\n                    </div>\n                    <div class=\"flex items-center\">\n                        <input type=\"checkbox\" name=\"notify_browser\" id=\"notify_browser\" class=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\">\n                        <label for=\"notify_browser\" class=\"ml-2 block text-sm text-gray-700\">\n                            Ricevi notifiche nel browser\n                        </label>\n                    </div>\n                </div>\n            </div>\n            \n            <!-- Preferenze Interfaccia -->\n            <div class=\"p-6\">\n                <h2 class=\"text-lg font-medium mb-4\">Preferenze Interfaccia</h2>\n                <div class=\"space-y-4\">\n                    <div>\n                        <label for=\"theme\" class=\"block text-sm font-medium text-gray-700\">Tema</label>\n                        <select name=\"theme\" id=\"theme\" class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n                            <option value=\"light\">Chiaro</option>\n                            <option value=\"dark\">Scuro</option>\n                            <option value=\"system\">Sistema</option>\n                        </select>\n                    </div>\n                    <div>\n                        <label for=\"language\" class=\"block text-sm font-medium text-gray-700\">Lingua</label>\n                        <select name=\"language\" id=\"language\" class=\"mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500\">\n                            <option value=\"it\">Italiano</option>\n                            <option value=\"en\">English</option>\n                        </select>\n                    </div>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"flex justify-end\">\n            <button type=\"submit\" class=\"btn btn-primary\">\n                Salva Impostazioni\n            </button>\n        </div>\n    </form>\n</div>\n{% endblock %} "}