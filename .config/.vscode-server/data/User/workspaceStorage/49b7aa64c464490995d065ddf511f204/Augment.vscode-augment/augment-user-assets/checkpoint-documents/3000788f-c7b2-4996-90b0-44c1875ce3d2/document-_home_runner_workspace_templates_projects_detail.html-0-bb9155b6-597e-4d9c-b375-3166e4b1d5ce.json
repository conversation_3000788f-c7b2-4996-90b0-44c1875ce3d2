{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/projects/detail.html"}, "originalCode": "{% extends 'base.html' %}\n\n{% block title %}{{ project.name }} - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <div class=\"mb-6\">\n        <a href=\"{% url 'projects:list' %}\" class=\"flex items-center text-blue-600 hover:text-blue-800\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\" />\n            </svg>\n            Torna all'elenco progetti\n        </a>\n    </div>\n    \n    {% if messages %}\n    <div class=\"mb-6\">\n        {% for message in messages %}\n        <div class=\"p-4 mb-4 rounded {% if message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-red-100 text-red-700{% endif %}\">\n            {{ message }}\n        </div>\n        {% endfor %}\n    </div>\n    {% endif %}\n    \n    <div class=\"bg-white shadow rounded-lg overflow-hidden mb-8\">\n        <div class=\"px-6 py-4 border-b border-gray-200 flex justify-between items-center\">\n            <h1 class=\"text-xl font-semibold text-gray-900\">{{ project.name }}</h1>\n            <span class=\"project-status-{{ project.status }}\">\n                {{ project.get_status_display }}\n            </span>\n        </div>\n        \n        <div class=\"p-6\">\n            <div class=\"mb-6\">\n                <h2 class=\"text-lg font-medium text-gray-800 mb-3\">Dettagli Progetto</h2>\n                <div class=\"bg-gray-50 rounded-lg p-4\">\n                    <p class=\"text-gray-700 mb-4\">{{ project.description }}</p>\n                    \n                    <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\">\n                        <div>\n                            <p class=\"text-sm text-gray-500\">Data creazione</p>\n                            <p class=\"font-medium\">{{ project.created_at|date:\"d/m/Y H:i\" }}</p>\n                        </div>\n                        <div>\n                            <p class=\"text-sm text-gray-500\">Ultimo aggiornamento</p>\n                            <p class=\"font-medium\">{{ project.updated_at|date:\"d/m/Y H:i\" }}</p>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div class=\"bg-blue-50 rounded-lg p-4\">\n                    <h3 class=\"font-medium text-blue-800 mb-2\">Budget Totale</h3>\n                    <p class=\"text-2xl font-bold\">€ {{ project.budget|floatformat:2 }}</p>\n                </div>\n                \n                <div class=\"bg-green-50 rounded-lg p-4\">\n                    <h3 class=\"font-medium text-green-800 mb-2\">Budget Disponibile</h3>\n                    <p class=\"text-2xl font-bold\">€ {{ budget_remaining|floatformat:2 }}</p>\n                </div>\n                \n                <div class=\"bg-yellow-50 rounded-lg p-4\">\n                    <h3 class=\"font-medium text-yellow-800 mb-2\">Budget Utilizzato</h3>\n                    <p class=\"text-2xl font-bold\">€ {{ budget_used|floatformat:2 }}</p>\n                </div>\n            </div>\n            \n            {% if project.entities.exists %}\n            <div class=\"mt-8\">\n                <h2 class=\"text-lg font-medium text-gray-800 mb-3\">Entità Coinvolte</h2>\n                <div class=\"flex flex-wrap gap-2\">\n                    {% for entity in project.entities.all %}\n                    <span class=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\">\n                        {{ entity.name }}\n                    </span>\n                    {% endfor %}\n                </div>\n            </div>\n            {% endif %}\n            \n            <div class=\"mt-8 flex justify-end space-x-3\">\n                <a href=\"{% url 'projects:edit' project.id %}\" class=\"btn btn-outline\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1 inline\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                    </svg>\n                    Modifica Progetto\n                </a>\n                <a href=\"{% url 'projects:delete' project.id %}\" class=\"btn btn-danger\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1 inline\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                    </svg>\n                    Elimina Progetto\n                </a>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n        <div class=\"px-6 py-4 border-b border-gray-200 flex justify-between items-center\">\n            <h2 class=\"text-lg font-medium text-gray-800\">Particelle ({{ parcels|length }})</h2>\n            <a href=\"#\" class=\"btn btn-primary btn-sm\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-1 inline\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fill-rule=\"evenodd\" d=\"M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z\" clip-rule=\"evenodd\" />\n                </svg>\n                Aggiungi Particella\n            </a>\n        </div>\n        \n        {% if parcels %}\n        <div class=\"overflow-x-auto\">\n            <table class=\"min-w-full divide-y divide-gray-200\">\n                <thead>\n                    <tr>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Codice\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Comune\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Foglio\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Particella\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Superficie (mq)\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Valore (€)\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Stato\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Azioni\n                        </th>\n                    </tr>\n                </thead>\n                <tbody class=\"bg-white divide-y divide-gray-200\">\n                    {% for parcel in parcels %}\n                    <tr class=\"hover:bg-gray-50\">\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                            {{ parcel.code }}\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            {{ parcel.municipality }}\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            {{ parcel.sheet }}\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            {{ parcel.number }}\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            {{ parcel.area|floatformat:2 }}\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            € {{ parcel.value|floatformat:2 }}\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                            <span class=\"parcel-badge bg-{{ parcel.status }}-100 text-{{ parcel.status }}-800\">\n                                {{ parcel.get_status_display }}\n                            </span>\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                            <a href=\"#\" class=\"text-blue-600 hover:text-blue-900\" title=\"Visualizza\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 inline\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                                </svg>\n                            </a>\n                        </td>\n                    </tr>\n                    {% endfor %}\n                </tbody>\n            </table>\n        </div>\n        {% else %}\n        <div class=\"p-8 text-center\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12 mx-auto text-gray-400 mb-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n            </svg>\n            <p class=\"text-gray-600 mb-4\">Non sono state trovate particelle associate a questo progetto.</p>\n            <a href=\"#\" class=\"btn btn-primary\">\n                Aggiungi Prima Particella\n            </a>\n        </div>\n        {% endif %}\n    </div>\n</div>\n{% endblock %} ", "modifiedCode": "{% extends 'base.html' %}\n\n{% block title %}{{ project.name }} - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <div class=\"mb-6\">\n        <a href=\"{% url 'projects:list' %}\" class=\"flex items-center text-blue-600 hover:text-blue-800\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fill-rule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clip-rule=\"evenodd\" />\n            </svg>\n            Torna all'elenco progetti\n        </a>\n    </div>\n    \n    {% if messages %}\n    <div class=\"mb-6\">\n        {% for message in messages %}\n        <div class=\"p-4 mb-4 rounded {% if message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-red-100 text-red-700{% endif %}\">\n            {{ message }}\n        </div>\n        {% endfor %}\n    </div>\n    {% endif %}\n    \n    <div class=\"bg-white shadow rounded-lg overflow-hidden mb-8\">\n        <div class=\"px-6 py-4 border-b border-gray-200 flex justify-between items-center\">\n            <h1 class=\"text-xl font-semibold text-gray-900\">{{ project.name }}</h1>\n            <span class=\"project-status-{{ project.status }}\">\n                {{ project.get_status_display }}\n            </span>\n        </div>\n        \n        <div class=\"p-6\">\n            <div class=\"mb-6\">\n                <h2 class=\"text-lg font-medium text-gray-800 mb-3\">Dettagli Progetto</h2>\n                <div class=\"bg-gray-50 rounded-lg p-4\">\n                    <p class=\"text-gray-700 mb-4\">{{ project.description }}</p>\n                    \n                    <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\">\n                        <div>\n                            <p class=\"text-sm text-gray-500\">Data creazione</p>\n                            <p class=\"font-medium\">{{ project.created_at|date:\"d/m/Y H:i\" }}</p>\n                        </div>\n                        <div>\n                            <p class=\"text-sm text-gray-500\">Ultimo aggiornamento</p>\n                            <p class=\"font-medium\">{{ project.updated_at|date:\"d/m/Y H:i\" }}</p>\n                        </div>\n                    </div>\n                </div>\n            </div>\n            \n            <div class=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div class=\"bg-blue-50 rounded-lg p-4\">\n                    <h3 class=\"font-medium text-blue-800 mb-2\">Budget Totale</h3>\n                    <p class=\"text-2xl font-bold\">€ {{ project.budget|floatformat:2 }}</p>\n                </div>\n                \n                <div class=\"bg-green-50 rounded-lg p-4\">\n                    <h3 class=\"font-medium text-green-800 mb-2\">Budget Disponibile</h3>\n                    <p class=\"text-2xl font-bold\">€ {{ budget_remaining|floatformat:2 }}</p>\n                </div>\n                \n                <div class=\"bg-yellow-50 rounded-lg p-4\">\n                    <h3 class=\"font-medium text-yellow-800 mb-2\">Budget Utilizzato</h3>\n                    <p class=\"text-2xl font-bold\">€ {{ budget_used|floatformat:2 }}</p>\n                </div>\n            </div>\n            \n            {% if project.entities.exists %}\n            <div class=\"mt-8\">\n                <h2 class=\"text-lg font-medium text-gray-800 mb-3\">Entità Coinvolte</h2>\n                <div class=\"flex flex-wrap gap-2\">\n                    {% for entity in project.entities.all %}\n                    <span class=\"inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800\">\n                        {{ entity.name }}\n                    </span>\n                    {% endfor %}\n                </div>\n            </div>\n            {% endif %}\n            \n            <div class=\"mt-8 flex justify-end space-x-3\">\n                <a href=\"{% url 'projects:edit' project.id %}\" class=\"btn btn-outline\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1 inline\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\" />\n                    </svg>\n                    Modifica Progetto\n                </a>\n                <a href=\"{% url 'projects:delete' project.id %}\" class=\"btn btn-danger\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 mr-1 inline\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                    </svg>\n                    Elimina Progetto\n                </a>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n        <div class=\"px-6 py-4 border-b border-gray-200 flex justify-between items-center\">\n            <h2 class=\"text-lg font-medium text-gray-800\">Particelle ({{ parcels|length }})</h2>\n            <a href=\"#\" class=\"btn btn-primary btn-sm\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-4 w-4 mr-1 inline\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fill-rule=\"evenodd\" d=\"M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z\" clip-rule=\"evenodd\" />\n                </svg>\n                Aggiungi Particella\n            </a>\n        </div>\n        \n        {% if parcels %}\n        <div class=\"overflow-x-auto\">\n            <table class=\"min-w-full divide-y divide-gray-200\">\n                <thead>\n                    <tr>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Codice\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Comune\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Foglio\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Particella\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Superficie (mq)\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Valore (€)\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Stato\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Azioni\n                        </th>\n                    </tr>\n                </thead>\n                <tbody class=\"bg-white divide-y divide-gray-200\">\n                    {% for parcel in parcels %}\n                    <tr class=\"hover:bg-gray-50\">\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                            {{ parcel.code }}\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            {{ parcel.municipality }}\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            {{ parcel.sheet }}\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            {{ parcel.number }}\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            {{ parcel.area|floatformat:2 }}\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            € {{ parcel.value|floatformat:2 }}\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                            <span class=\"parcel-badge bg-{{ parcel.status }}-100 text-{{ parcel.status }}-800\">\n                                {{ parcel.get_status_display }}\n                            </span>\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\n                            <a href=\"#\" class=\"text-blue-600 hover:text-blue-900\" title=\"Visualizza\">\n                                <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-5 w-5 inline\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n                                    <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z\" />\n                                </svg>\n                            </a>\n                        </td>\n                    </tr>\n                    {% endfor %}\n                </tbody>\n            </table>\n        </div>\n        {% else %}\n        <div class=\"p-8 text-center\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" class=\"h-12 w-12 mx-auto text-gray-400 mb-4\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n            </svg>\n            <p class=\"text-gray-600 mb-4\">Non sono state trovate particelle associate a questo progetto.</p>\n            <a href=\"#\" class=\"btn btn-primary\">\n                Aggiungi Prima Particella\n            </a>\n        </div>\n        {% endif %}\n    </div>\n</div>\n{% endblock %} "}