{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/public/procedures.html"}, "originalCode": "{% extends \"base.html\" %}\n\n{% block title %}{{ title }}{% endblock %}\n\n{% block content %}\n<div class=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n    <!-- Header -->\n    <div class=\"text-center mb-12\">\n        <h1 class=\"text-4xl font-bold text-gray-900 mb-4\">{{ title }}</h1>\n        <p class=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Scopri le fasi principali del processo di esproprio e come ExProject ti supporta in ogni step\n        </p>\n    </div>\n    \n    <!-- Procedure -->\n    <div class=\"space-y-8\">\n        {% for procedure in procedures %}\n        <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div class=\"p-6\">\n                <h2 class=\"text-2xl font-bold text-gray-900 mb-4\">{{ procedure.title }}</h2>\n                <p class=\"text-gray-600 mb-6\">{{ procedure.description }}</p>\n                \n                <div class=\"border-t border-gray-200 pt-6\">\n                    <h3 class=\"text-lg font-semibold text-gray-900 mb-4\">Step Procedurali</h3>\n                    <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        {% for step in procedure.steps %}\n                        <div class=\"flex items-start\">\n                            <div class=\"flex-shrink-0\">\n                                <div class=\"flex items-center justify-center h-8 w-8 rounded-full bg-blue-100\">\n                                    <svg class=\"h-5 w-5 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\"/>\n                                    </svg>\n                                </div>\n                            </div>\n                            <div class=\"ml-4\">\n                                <p class=\"text-gray-700\">{{ step }}</p>\n                            </div>\n                        </div>\n                        {% endfor %}\n                    </div>\n                </div>\n            </div>\n        </div>\n        {% endfor %}\n    </div>\n    \n    <!-- Call to Action -->\n    <div class=\"mt-12 bg-blue-50 rounded-lg p-8 text-center\">\n        <h2 class=\"text-2xl font-bold text-gray-900 mb-4\">Pronto a Iniziare?</h2>\n        <p class=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n            Scopri come ExProject può semplificare la gestione dei tuoi progetti di esproprio.\n            Richiedi una demo gratuita e inizia a ottimizzare i tuoi processi.\n        </p>\n        <a href=\"{% url 'public:contacts' %}\" class=\"btn btn-primary\">\n            Richiedi Demo\n        </a>\n    </div>\n</div>\n{% endblock %}", "modifiedCode": "{% extends \"base.html\" %}\n\n{% block title %}{{ title }}{% endblock %}\n\n{% block content %}\n<div class=\"max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8\">\n    <!-- Header -->\n    <div class=\"text-center mb-12\">\n        <h1 class=\"text-4xl font-bold text-gray-900 mb-4\">{{ title }}</h1>\n        <p class=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Scopri le fasi principali del processo di esproprio e come ExProject ti supporta in ogni step\n        </p>\n    </div>\n    \n    <!-- Procedure -->\n    <div class=\"space-y-8\">\n        {% for procedure in procedures %}\n        <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div class=\"p-6\">\n                <h2 class=\"text-2xl font-bold text-gray-900 mb-4\">{{ procedure.title }}</h2>\n                <p class=\"text-gray-600 mb-6\">{{ procedure.description }}</p>\n                \n                <div class=\"border-t border-gray-200 pt-6\">\n                    <h3 class=\"text-lg font-semibold text-gray-900 mb-4\">Step Procedurali</h3>\n                    <div class=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        {% for step in procedure.steps %}\n                        <div class=\"flex items-start\">\n                            <div class=\"flex-shrink-0\">\n                                <div class=\"flex items-center justify-center h-8 w-8 rounded-full bg-blue-100\">\n                                    <svg class=\"h-5 w-5 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                        <path stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\" d=\"M5 13l4 4L19 7\"/>\n                                    </svg>\n                                </div>\n                            </div>\n                            <div class=\"ml-4\">\n                                <p class=\"text-gray-700\">{{ step }}</p>\n                            </div>\n                        </div>\n                        {% endfor %}\n                    </div>\n                </div>\n            </div>\n        </div>\n        {% endfor %}\n    </div>\n    \n    <!-- Call to Action -->\n    <div class=\"mt-12 bg-blue-50 rounded-lg p-8 text-center\">\n        <h2 class=\"text-2xl font-bold text-gray-900 mb-4\">Pronto a Iniziare?</h2>\n        <p class=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n            Scopri come ExProject può semplificare la gestione dei tuoi progetti di esproprio.\n            Richiedi una demo gratuita e inizia a ottimizzare i tuoi processi.\n        </p>\n        <a href=\"{% url 'public:contacts' %}\" class=\"btn btn-primary\">\n            Richiedi Demo\n        </a>\n    </div>\n</div>\n{% endblock %}"}