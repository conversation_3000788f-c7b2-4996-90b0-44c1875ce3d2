{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/core/notifications.html"}, "originalCode": "{% for notification in notifications %}\n<div class=\"notification-item px-4 py-2 hover:bg-gray-50 border-b last:border-b-0\"\n     hx-post=\"{% url 'core:mark_notification_read' notification.id %}\"\n     hx-swap=\"outerHTML\"\n     hx-trigger=\"click\">\n    <div class=\"flex justify-between items-start\">\n        <div>\n            <h4 class=\"font-medium text-gray-900\">{{ notification.title }}</h4>\n            <p class=\"text-sm text-gray-600\">{{ notification.message }}</p>\n            <span class=\"text-xs text-gray-500\">{{ notification.created_at|timesince }} fa</span>\n        </div>\n        {% if notification.link %}\n        <a href=\"{{ notification.link }}\" class=\"text-blue-600 hover:text-blue-800 text-sm\">→</a>\n        {% endif %}\n    </div>\n</div>\n{% endfor %} ", "modifiedCode": "{% for notification in notifications %}\n<div class=\"notification-item px-4 py-2 hover:bg-gray-50 border-b last:border-b-0\"\n     hx-post=\"{% url 'core:mark_notification_read' notification.id %}\"\n     hx-swap=\"outerHTML\"\n     hx-trigger=\"click\">\n    <div class=\"flex justify-between items-start\">\n        <div>\n            <h4 class=\"font-medium text-gray-900\">{{ notification.title }}</h4>\n            <p class=\"text-sm text-gray-600\">{{ notification.message }}</p>\n            <span class=\"text-xs text-gray-500\">{{ notification.created_at|timesince }} fa</span>\n        </div>\n        {% if notification.link %}\n        <a href=\"{{ notification.link }}\" class=\"text-blue-600 hover:text-blue-800 text-sm\">→</a>\n        {% endif %}\n    </div>\n</div>\n{% endfor %} "}