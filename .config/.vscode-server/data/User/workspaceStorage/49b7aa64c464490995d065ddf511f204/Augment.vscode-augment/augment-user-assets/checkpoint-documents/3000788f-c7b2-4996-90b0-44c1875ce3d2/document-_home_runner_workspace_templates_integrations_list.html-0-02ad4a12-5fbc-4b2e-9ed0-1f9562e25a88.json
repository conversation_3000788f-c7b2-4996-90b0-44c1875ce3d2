{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/integrations/list.html"}, "originalCode": "{% extends 'base.html' %}\n\n{% block title %}Integrazioni - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <h1 class=\"text-2xl font-bold text-gray-900 mb-6\">Integrazioni</h1>\n    \n    {% if messages %}\n    <div class=\"mb-6\">\n        {% for message in messages %}\n        <div class=\"p-4 mb-4 rounded {% if message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-red-100 text-red-700{% endif %}\">\n            {{ message }}\n        </div>\n        {% endfor %}\n    </div>\n    {% endif %}\n    \n    <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\">\n        <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div class=\"p-6 border-b border-gray-200\">\n                <h2 class=\"text-lg font-medium text-gray-900\">Integrazione Catasto</h2>\n                <p class=\"mt-1 text-sm text-gray-500\">Collegamento ai servizi catastali</p>\n            </div>\n            <div class=\"p-6\">\n                <p class=\"text-gray-700 mb-4\">Importa dati catastali, visualizza mappe e aggiorna automaticamente le informazioni delle particelle.</p>\n                <div class=\"flex justify-end\">\n                    <a href=\"{% url 'integrations:cadastre' %}\" class=\"btn btn-primary\">\n                        Configura Catasto\n                    </a>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div class=\"p-6 border-b border-gray-200\">\n                <h2 class=\"text-lg font-medium text-gray-900\">Integrazione GIS</h2>\n                <p class=\"mt-1 text-sm text-gray-500\">Collegamento ai sistemi GIS</p>\n            </div>\n            <div class=\"p-6\">\n                <p class=\"text-gray-700 mb-4\">Visualizza e modifica le particelle su mappa, importa e esporta dati geospaziali.</p>\n                <div class=\"flex justify-end\">\n                    <a href=\"{% url 'integrations:gis' %}\" class=\"btn btn-primary\">\n                        Configura GIS\n                    </a>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div class=\"p-6 border-b border-gray-200\">\n                <h2 class=\"text-lg font-medium text-gray-900\">Integrazione PEC</h2>\n                <p class=\"mt-1 text-sm text-gray-500\">Gestione comunicazioni certificate</p>\n            </div>\n            <div class=\"p-6\">\n                <p class=\"text-gray-700 mb-4\">Invia notifiche e documenti tramite PEC ai proprietari, monitora lo stato delle comunicazioni.</p>\n                <div class=\"flex justify-end\">\n                    <a href=\"{% url 'integrations:pec' %}\" class=\"btn btn-primary\">\n                        Configura PEC\n                    </a>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div class=\"p-6 border-b border-gray-200\">\n                <h2 class=\"text-lg font-medium text-gray-900\">Impostazioni Integrazioni</h2>\n                <p class=\"mt-1 text-sm text-gray-500\">Gestione configurazioni globali</p>\n            </div>\n            <div class=\"p-6\">\n                <p class=\"text-gray-700 mb-4\">Configura API keys, credenziali di accesso e altre impostazioni per le integrazioni.</p>\n                <div class=\"flex justify-end\">\n                    <a href=\"{% url 'integrations:settings' %}\" class=\"btn btn-primary\">\n                        Impostazioni\n                    </a>\n                </div>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n        <div class=\"px-6 py-4 border-b border-gray-200\">\n            <h2 class=\"text-lg font-medium text-gray-900\">Stato delle Integrazioni</h2>\n        </div>\n        <div class=\"overflow-x-auto\">\n            <table class=\"min-w-full divide-y divide-gray-200\">\n                <thead>\n                    <tr>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Servizio\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Stato\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Ultimo Controllo\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Configurazione\n                        </th>\n                    </tr>\n                </thead>\n                <tbody class=\"bg-white divide-y divide-gray-200\">\n                    <tr>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                            Catasto Nazionale\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                            <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                                Attivo\n                            </span>\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            2 ore fa\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            Completa\n                        </td>\n                    </tr>\n                    <tr>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                            Sistema GIS\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                            <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n                                Da configurare\n                            </span>\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            Mai\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            Incompleta\n                        </td>\n                    </tr>\n                    <tr>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                            Servizio PEC\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                            <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n                                Errore\n                            </span>\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            1 giorno fa\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            Credenziali scadute\n                        </td>\n                    </tr>\n                </tbody>\n            </table>\n        </div>\n    </div>\n</div>\n{% endblock %} ", "modifiedCode": "{% extends 'base.html' %}\n\n{% block title %}Integrazioni - ExProject{% endblock %}\n\n{% block content %}\n<div class=\"container mx-auto px-4 py-8\">\n    <h1 class=\"text-2xl font-bold text-gray-900 mb-6\">Integrazioni</h1>\n    \n    {% if messages %}\n    <div class=\"mb-6\">\n        {% for message in messages %}\n        <div class=\"p-4 mb-4 rounded {% if message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-red-100 text-red-700{% endif %}\">\n            {{ message }}\n        </div>\n        {% endfor %}\n    </div>\n    {% endif %}\n    \n    <div class=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8\">\n        <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div class=\"p-6 border-b border-gray-200\">\n                <h2 class=\"text-lg font-medium text-gray-900\">Integrazione Catasto</h2>\n                <p class=\"mt-1 text-sm text-gray-500\">Collegamento ai servizi catastali</p>\n            </div>\n            <div class=\"p-6\">\n                <p class=\"text-gray-700 mb-4\">Importa dati catastali, visualizza mappe e aggiorna automaticamente le informazioni delle particelle.</p>\n                <div class=\"flex justify-end\">\n                    <a href=\"{% url 'integrations:cadastre' %}\" class=\"btn btn-primary\">\n                        Configura Catasto\n                    </a>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div class=\"p-6 border-b border-gray-200\">\n                <h2 class=\"text-lg font-medium text-gray-900\">Integrazione GIS</h2>\n                <p class=\"mt-1 text-sm text-gray-500\">Collegamento ai sistemi GIS</p>\n            </div>\n            <div class=\"p-6\">\n                <p class=\"text-gray-700 mb-4\">Visualizza e modifica le particelle su mappa, importa e esporta dati geospaziali.</p>\n                <div class=\"flex justify-end\">\n                    <a href=\"{% url 'integrations:gis' %}\" class=\"btn btn-primary\">\n                        Configura GIS\n                    </a>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div class=\"p-6 border-b border-gray-200\">\n                <h2 class=\"text-lg font-medium text-gray-900\">Integrazione PEC</h2>\n                <p class=\"mt-1 text-sm text-gray-500\">Gestione comunicazioni certificate</p>\n            </div>\n            <div class=\"p-6\">\n                <p class=\"text-gray-700 mb-4\">Invia notifiche e documenti tramite PEC ai proprietari, monitora lo stato delle comunicazioni.</p>\n                <div class=\"flex justify-end\">\n                    <a href=\"{% url 'integrations:pec' %}\" class=\"btn btn-primary\">\n                        Configura PEC\n                    </a>\n                </div>\n            </div>\n        </div>\n        \n        <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n            <div class=\"p-6 border-b border-gray-200\">\n                <h2 class=\"text-lg font-medium text-gray-900\">Impostazioni Integrazioni</h2>\n                <p class=\"mt-1 text-sm text-gray-500\">Gestione configurazioni globali</p>\n            </div>\n            <div class=\"p-6\">\n                <p class=\"text-gray-700 mb-4\">Configura API keys, credenziali di accesso e altre impostazioni per le integrazioni.</p>\n                <div class=\"flex justify-end\">\n                    <a href=\"{% url 'integrations:settings' %}\" class=\"btn btn-primary\">\n                        Impostazioni\n                    </a>\n                </div>\n            </div>\n        </div>\n    </div>\n    \n    <div class=\"bg-white shadow rounded-lg overflow-hidden\">\n        <div class=\"px-6 py-4 border-b border-gray-200\">\n            <h2 class=\"text-lg font-medium text-gray-900\">Stato delle Integrazioni</h2>\n        </div>\n        <div class=\"overflow-x-auto\">\n            <table class=\"min-w-full divide-y divide-gray-200\">\n                <thead>\n                    <tr>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Servizio\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Stato\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Ultimo Controllo\n                        </th>\n                        <th class=\"px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                            Configurazione\n                        </th>\n                    </tr>\n                </thead>\n                <tbody class=\"bg-white divide-y divide-gray-200\">\n                    <tr>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                            Catasto Nazionale\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                            <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800\">\n                                Attivo\n                            </span>\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            2 ore fa\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            Completa\n                        </td>\n                    </tr>\n                    <tr>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                            Sistema GIS\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                            <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800\">\n                                Da configurare\n                            </span>\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            Mai\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            Incompleta\n                        </td>\n                    </tr>\n                    <tr>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                            Servizio PEC\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap\">\n                            <span class=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n                                Errore\n                            </span>\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            1 giorno fa\n                        </td>\n                        <td class=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                            Credenziali scadute\n                        </td>\n                    </tr>\n                </tbody>\n            </table>\n        </div>\n    </div>\n</div>\n{% endblock %} "}