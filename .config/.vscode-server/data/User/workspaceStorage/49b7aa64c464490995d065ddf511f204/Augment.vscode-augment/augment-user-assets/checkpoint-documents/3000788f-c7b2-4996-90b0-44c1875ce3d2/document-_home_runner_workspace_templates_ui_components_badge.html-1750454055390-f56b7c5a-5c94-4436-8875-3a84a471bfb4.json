{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "templates/ui/components/badge.html"}, "originalCode": "{% load ui_components %}\n{# Componente Badge ispirato a shadcn/ui #}\n<div class=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\n    {% ui_variant_classes 'badge' variant %}\n    {% if attrs.class %}{{ attrs.class }}{% endif %}\"\n    {% for key, value in attrs.items %}{% if key != 'class' %}{{ key }}=\"{{ value }}\"{% endif %}{% endfor %}>\n    \n    {% if attrs.icon %}\n        {% ui_icon attrs.icon size=\"3\" class=\"mr-1\" %}\n    {% endif %}\n    \n    {{ text }}\n    \n    {% if attrs.removable %}\n        <button class=\"ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2\" \n                onclick=\"this.parentElement.remove()\">\n            {% ui_icon 'x' size=\"3\" %}\n        </button>\n    {% endif %}\n</div>\n", "modifiedCode": "{# Componente Badge ispirato a shadcn/ui #}\n<div class=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-primary text-primary-foreground hover:bg-primary/80\">\n    {{ text }}\n</div>\n"}