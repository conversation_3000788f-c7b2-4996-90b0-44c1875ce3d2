# Design System
- User questions whether the design system implementation is truly based on shadcn/ui, indicating concern about design system authenticity.
- User wants to redesign existing pages with the new design system.
- User wants to properly implement the design system with reusable components as the next priority.

# Architecture
- User is interested in exploring HTMX + Django for creating true reusable components, suggesting preference for component-based architecture.

# Implementation
- User wants to implement missing functionalities as next steps.
- User prefers to rebuild all templates in /templates from scratch rather than fixing individual template errors.