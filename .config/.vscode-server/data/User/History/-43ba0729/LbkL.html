{% extends 'ui/base_modern.html' %}
{% load ui_components %}

{% block title %}Dashboard - ExProject{% endblock %}



{% block content %}
<div class="flex-1 space-y-4 p-8 pt-6">
    <!-- Header Dashboard -->
    <div class="flex items-center justify-between space-y-2">
        <h2 class="text-3xl font-bold tracking-tight">Dashboard</h2>
        <div class="flex items-center space-x-2">
            {% ui_button "Nuovo Progetto" variant="default" hx_get="/projects/create/" hx_target="#main-content" %}
            {% ui_button "Altre Azioni" variant="outline" %}
        </div>
    </div>

    <!-- Statistiche principali -->
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {% ui_card title="Progetti Totali" content="<div class='text-2xl font-bold'>{{ stats.projects_total|default:0 }}</div><p class='text-xs text-muted-foreground'><svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' class='inline h-3 w-3 mr-1'><path stroke-linecap='round' stroke-linejoin='round' d='M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941' /></svg>+20.1% dal mese scorso</p>" %}

        {% ui_card title="In Lavorazione" content="<div class='text-2xl font-bold'>{{ stats.projects_active|default:0 }}</div><p class='text-xs text-muted-foreground'><svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' class='inline h-3 w-3 mr-1'><path stroke-linecap='round' stroke-linejoin='round' d='M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z' /></svg>+180.1% dal mese scorso</p>" %}

        {% ui_card title="Particelle Totali" content="<div class='text-2xl font-bold'>{{ stats.parcels_total|default:0 }}</div><p class='text-xs text-muted-foreground'><svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' class='inline h-3 w-3 mr-1'><path stroke-linecap='round' stroke-linejoin='round' d='M9 6.75V15m6-6v8.25m.503 3.498l4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.38-1.628-1.006l-3.869 1.934c-.317.159-.69.159-1.006 0L9.503 3.252a1.125 1.125 0 00-1.006 0L3.622 5.689C3.24 5.88 3 6.27 3 6.695V19.18c0 .836.88 1.38 1.628 1.006l3.869-1.934c.317-.159.69-.159 1.006 0l4.994 2.497c.317.158.69.158 1.007 0z' /></svg>Visualizza mappa</p>" %}

        {% ui_card title="In Scadenza" content="<div class='text-2xl font-bold'>{{ stats.expiring_tasks|default:0 }}</div><p class='text-xs text-muted-foreground'><svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke-width='1.5' stroke='currentColor' class='inline h-3 w-3 mr-1'><path stroke-linecap='round' stroke-linejoin='round' d='M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z' /></svg>Gestisci scadenze</p>" %}
    </div>

    <!-- Moduli Principali -->
    <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Moduli Operativi</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="module-card bg-white rounded-lg shadow border border-gray-100 overflow-hidden">
                <div class="h-2 bg-blue-600"></div>
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="p-2 rounded-full bg-blue-100 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Progetti</h3>
                    </div>
                    <p class="text-gray-600 mb-4">Gestione completa dei progetti espropriativi: creazione, pianificazione, monitoraggio avanzamento.</p>
                    <div class="flex justify-between">
                        <a href="#" hx-get="{% url 'projects:list' %}" hx-target="#main-content" hx-push-url="true"
                           class="text-blue-600 hover:text-blue-800 font-medium">Vai ai progetti</a>
                        <span class="text-gray-500">{{ stats.projects_total|default:"0" }} progetti</span>
                    </div>
                </div>
            </div>
            
            <div class="module-card bg-white rounded-lg shadow border border-gray-100 overflow-hidden">
                <div class="h-2 bg-green-600"></div>
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="p-2 rounded-full bg-green-100 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Workflow</h3>
                    </div>
                    <p class="text-gray-600 mb-4">Automazione dei flussi procedurali secondo DPR 327/2001 con controlli integrati e scadenzario.</p>
                    <div class="flex justify-between">
                        <a href="#" hx-get="{% url 'workflow:list' %}" hx-target="#main-content" hx-push-url="true"
                           class="text-green-600 hover:text-green-800 font-medium">Gestione workflow</a>
                        <span class="text-gray-500">{{ stats.active_workflows|default:"0" }} attivi</span>
                    </div>
                </div>
            </div>
            
            <div class="module-card bg-white rounded-lg shadow border border-gray-100 overflow-hidden">
                <div class="h-2 bg-purple-600"></div>
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="p-2 rounded-full bg-purple-100 mr-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
                            </svg>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900">Documenti</h3>
                    </div>
                    <p class="text-gray-600 mb-4">Gestione documentale con template personalizzabili, generazione automatica e firma digitale.</p>
                    <div class="flex justify-between">
                        <a href="#" hx-get="{% url 'documents:list' %}" hx-target="#main-content" hx-push-url="true"
                           class="text-purple-600 hover:text-purple-800 font-medium">Archivio documenti</a>
                        <span class="text-gray-500">{{ stats.documents_total|default:"0" }} documenti</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Progetti Recenti -->
        <div class="lg:col-span-2 bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b flex justify-between items-center">
                <h3 class="text-lg font-semibold text-gray-900">Progetti Recenti</h3>
                <a href="#" hx-get="{% url 'projects:list' %}" hx-target="#main-content" hx-push-url="true"
                   class="text-sm text-blue-600 hover:text-blue-800">Vedi tutti</a>
            </div>
            <div class="p-6">
                {% if recent_projects %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead>
                            <tr>
                                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Progetto</th>
                                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stato</th>
                                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget</th>
                                <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ente</th>
                                <th class="px-6 py-3 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Azioni</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for project in recent_projects %}
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium">
                                            {{ project.name|slice:":1" }}
                                        </div>
                                        <div class="ml-3">
                                            <a href="#" class="text-sm font-medium text-gray-900 hover:text-blue-600">{{ project.name }}</a>
                                            <div class="text-xs text-gray-500">Creato: {{ project.created_at|date:"d/m/Y" }}</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="project-status-{{ project.status|lower }}">
                                        {{ project.get_status_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                                    € {{ project.budget|floatformat:2 }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">
                                    {{ project.entities.first.name|default:"—" }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex justify-end space-x-2">
                                        <a href="#" class="text-blue-600 hover:text-blue-900" title="Visualizza">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                            </svg>
                                        </a>
                                        <a href="#" class="text-gray-600 hover:text-gray-900" title="Modifica">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                            </svg>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="flex flex-col items-center justify-center py-8 text-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    <p class="text-gray-500 mb-2">Nessun progetto presente</p>
                    <p class="text-gray-400 text-sm mb-4">Inizia creando il tuo primo progetto espropriativo.</p>
                    <button class="btn btn-primary flex items-center"
                            hx-get="{% url 'projects:create' %}" 
                            hx-target="#main-content">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                        Crea Progetto
                    </button>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Notifiche e Attività Recenti -->
        <div class="bg-white rounded-lg shadow">
            <div class="px-6 py-4 border-b">
                <h3 class="text-lg font-semibold text-gray-900">Attività Recenti</h3>
            </div>
            <div class="p-6" id="activities-container">
                {% if recent_activities %}
                <div class="space-y-6">
                    {% for activity in recent_activities %}
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center text-blue-600">
                                {{ activity.user.username|slice:":1" }}
                            </div>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-900">{{ activity.description }}</p>
                            <p class="text-xs text-gray-500">{{ activity.created_at|date:"d/m/Y H:i" }}</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-8">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-gray-400 mx-auto mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p class="text-gray-500">Nessuna attività recente</p>
                </div>
                {% endif %}
            </div>
            <div class="px-6 py-3 bg-gray-50 border-t">
                <div class="flex justify-between items-center">
                    <div class="text-xs text-gray-500">Aggiornato: {% now "H:i" %}</div>
                    <div class="flex space-x-2">
                        <button class="text-sm text-blue-600 hover:text-blue-800"
                                hx-get="/exproject/activities/" 
                                hx-target="#activities-container" 
                                hx-swap="innerHTML"
                                hx-trigger="click"
                                id="refresh-activities-btn"
                                onclick="refreshActivities()">
                            Aggiorna
                        </button>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Moduli Aggiuntivi -->
    <div class="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="bg-white rounded-lg shadow p-4 flex items-center">
            <div class="p-3 rounded-full bg-yellow-100 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <div>
                <h4 class="font-medium text-gray-900">Modulo Economico</h4>
                <a href="#" class="text-sm text-yellow-600 hover:text-yellow-800">Gestisci indennità →</a>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-4 flex items-center">
            <div class="p-3 rounded-full bg-indigo-100 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                </svg>
            </div>
            <div>
                <h4 class="font-medium text-gray-900">Mappa Particelle</h4>
                <a href="#" class="text-sm text-indigo-600 hover:text-indigo-800">Visualizza mappa →</a>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-4 flex items-center">
            <div class="p-3 rounded-full bg-pink-100 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                </svg>
            </div>
            <div>
                <h4 class="font-medium text-gray-900">Integrazioni</h4>
                <a href="#" class="text-sm text-pink-600 hover:text-pink-800">Sister/Catasto →</a>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow p-4 flex items-center">
            <div class="p-3 rounded-full bg-red-100 mr-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
            </div>
            <div>
                <h4 class="font-medium text-gray-900">Report e Statistiche</h4>
                <a href="#" class="text-sm text-red-600 hover:text-red-800">Genera report →</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Debug per htmx
        console.log('DOMContentLoaded triggered');
        
        // Verifica se htmx è disponibile
        if (typeof htmx !== 'undefined') {
            console.log('HTMX caricato correttamente', htmx.version);
            
            // Aggiungi un listener per gli eventi htmx
            document.body.addEventListener('htmx:afterRequest', function(event) {
                console.log('HTMX request completata:', event.detail);
            });
            
            document.body.addEventListener('htmx:beforeRequest', function(event) {
                console.log('HTMX request in corso:', event.detail);
            });
            
            document.body.addEventListener('htmx:responseError', function(event) {
                console.error('HTMX error:', event.detail);
            });
        } else {
            console.error('HTMX non disponibile!');
            
            // Se HTMX non è disponibile, sostituisci con AJAX standard per aggiornare le attività
            const refreshBtn = document.getElementById('refresh-activities-btn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const xhr = new XMLHttpRequest();
                    xhr.open('GET', '/exproject/activities/');
                    xhr.onload = function() {
                        if (xhr.status === 200) {
                            document.getElementById('activities-container').innerHTML = xhr.responseText;
                        }
                    };
                    xhr.send();
                });
            }
        }
        
        // Intervallo per aggiornare le attività ogni 5 minuti
        setInterval(function() {
            refreshActivities();
        }, 300000); // 5 minuti
    });
    
    // Funzione per aggiornare le attività (fallback JavaScript)
    function refreshActivities() {
        const activityBtn = document.getElementById('refresh-activities-btn');
        if (activityBtn) {
            try {
                // Prova ad usare HTMX
                if (typeof htmx !== 'undefined') {
                    console.log('Triggering htmx refresh via refreshActivities()');
                    htmx.trigger(activityBtn, 'click');
                } else {
                    // Fallback con XHR
                    const xhr = new XMLHttpRequest();
                    xhr.open('GET', '/exproject/activities/');
                    xhr.onload = function() {
                        if (xhr.status === 200) {
                            document.getElementById('activities-container').innerHTML = xhr.responseText;
                        }
                    };
                    xhr.send();
                }
            } catch (e) {
                console.warn('Errore durante l\'aggiornamento delle attività', e);
            }
        }
    }
</script>
{% endblock %}