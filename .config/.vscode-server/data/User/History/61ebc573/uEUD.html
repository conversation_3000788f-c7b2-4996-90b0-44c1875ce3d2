{% extends 'ui/base_modern.html' %}
{% load ui_components %}

{% block title %}Confronto Design - ExProject{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold tracking-tight mb-2">Confronto Design System</h1>
        <p class="text-muted-foreground">Confronto tra il vecchio design e il nuovo design system moderno</p>
    </div>
    
    <!-- Toggle per confronto -->
    <div class="mb-8" x-data="{ showOld: false }">
        <div class="flex items-center space-x-4">
            <span class="text-sm font-medium">Mostra design:</span>
            <button @click="showOld = false" 
                    :class="!showOld ? 'bg-primary text-primary-foreground' : 'bg-secondary text-secondary-foreground'"
                    class="px-3 py-1 rounded-md text-sm font-medium transition-colors">
                Nuovo (shadcn/ui)
            </button>
            <button @click="showOld = true"
                    :class="showOld ? 'bg-primary text-primary-foreground' : 'bg-secondary text-secondary-foreground'" 
                    class="px-3 py-1 rounded-md text-sm font-medium transition-colors">
                Vecchio (TailwindCSS base)
            </button>
        </div>
        
        <!-- Nuovo Design -->
        <div x-show="!showOld" class="mt-8">
            <h2 class="text-2xl font-bold mb-6">✨ Nuovo Design System (shadcn/ui inspired)</h2>
            
            <!-- Buttons -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Buttons</h3>
                <div class="flex flex-wrap gap-2">
                    <button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90">Primary</button>
                    <button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 bg-secondary text-secondary-foreground hover:bg-secondary/80">Secondary</button>
                    <button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground">Outline</button>
                    <button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 hover:bg-accent hover:text-accent-foreground">Ghost</button>
                    <button class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 bg-destructive text-destructive-foreground hover:bg-destructive/90">Destructive</button>
                </div>
            </div>
            
            <!-- Cards -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Cards</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                        <div class="flex flex-col space-y-1.5 p-6">
                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Progetti Attivi</h3>
                            <p class="text-sm text-muted-foreground">Panoramica progetti in corso</p>
                        </div>
                        <div class="p-6 pt-0">
                            <div class="text-2xl font-bold">24</div>
                            <p class="text-xs text-muted-foreground">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="inline h-3 w-3 mr-1">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.519l2.74-1.22m0 0l-5.94-2.28m5.94 2.28l-2.28 5.941" />
                                </svg>
                                +12% dal mese scorso
                            </p>
                        </div>
                    </div>

                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                        <div class="flex flex-col space-y-1.5 p-6">
                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Documenti Generati</h3>
                        </div>
                        <div class="p-6 pt-0">
                            <div class="text-2xl font-bold">156</div>
                            <p class="text-xs text-muted-foreground">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="inline h-3 w-3 mr-1">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m2.25 0H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                                </svg>
                                +8% dal mese scorso
                            </p>
                        </div>
                    </div>

                    <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
                        <div class="flex flex-col space-y-1.5 p-6">
                            <h3 class="text-2xl font-semibold leading-none tracking-tight">Valore Totale</h3>
                        </div>
                        <div class="p-6 pt-0">
                            <div class="text-2xl font-bold">€2.4M</div>
                            <p class="text-xs text-muted-foreground">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="inline h-3 w-3 mr-1">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M14.25 7.756a4.5 4.5 0 100 8.488M7.5 10.5h5.25m-5.25 3h5.25M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                +15% dal mese scorso
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Badges -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Badges</h3>
                <div class="flex flex-wrap gap-2">
                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-primary text-primary-foreground hover:bg-primary/80">Attivo</div>
                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-secondary text-secondary-foreground hover:bg-secondary/80">In Attesa</div>
                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 text-foreground border-input">Completato</div>
                    <div class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 bg-destructive text-destructive-foreground hover:bg-destructive/80">Errore</div>
                </div>
            </div>
            
            <!-- Alerts -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Alerts</h3>
                <div class="space-y-4">
                    {% ui_alert "Questo è un messaggio informativo" variant="default" title="Informazione" %}
                    {% ui_alert "Operazione completata con successo" variant="success" title="Successo" %}
                    {% ui_alert "Attenzione: verifica i dati inseriti" variant="warning" title="Attenzione" %}
                    {% ui_alert "Si è verificato un errore" variant="destructive" title="Errore" %}
                </div>
            </div>
            
            <!-- Inputs -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Form Elements</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl">
                    {% ui_input "name" label="Nome Progetto" placeholder="Inserisci il nome del progetto" %}
                    {% ui_input "email" label="Email" placeholder="<EMAIL>" type="email" %}
                    {% ui_input "budget" label="Budget" placeholder="0.00" type="number" help_text="Inserisci il budget in euro" %}
                    {% ui_input "error_field" label="Campo con Errore" placeholder="Valore non valido" error="Questo campo è obbligatorio" %}
                </div>
            </div>
        </div>
        
        <!-- Vecchio Design -->
        <div x-show="showOld" class="mt-8">
            <h2 class="text-2xl font-bold mb-6">🔧 Vecchio Design (TailwindCSS base)</h2>
            
            <!-- Old Buttons -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Buttons</h3>
                <div class="flex flex-wrap gap-2">
                    <button class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Primary</button>
                    <button class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">Secondary</button>
                    <button class="border border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-bold py-2 px-4 rounded">Outline</button>
                    <button class="text-blue-600 hover:bg-blue-100 font-bold py-2 px-4 rounded">Ghost</button>
                    <button class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">Destructive</button>
                </div>
            </div>
            
            <!-- Old Cards -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Cards</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div class="bg-white rounded-lg shadow p-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Progetti Attivi</h4>
                        <p class="text-gray-600 text-sm mb-4">Panoramica progetti in corso</p>
                        <div class="text-2xl font-bold text-blue-600">24</div>
                        <p class="text-xs text-gray-500">+12% dal mese scorso</p>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Documenti Generati</h4>
                        <div class="text-2xl font-bold text-green-600">156</div>
                        <p class="text-xs text-gray-500">+8% dal mese scorso</p>
                    </div>
                    
                    <div class="bg-white rounded-lg shadow p-6">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Valore Totale</h4>
                        <div class="text-2xl font-bold text-purple-600">€2.4M</div>
                        <p class="text-xs text-gray-500">+15% dal mese scorso</p>
                    </div>
                </div>
            </div>
            
            <!-- Old Badges -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Badges</h3>
                <div class="flex flex-wrap gap-2">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">Attivo</span>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800">In Attesa</span>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">Completato</span>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">Errore</span>
                </div>
            </div>
            
            <!-- Old Alerts -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Alerts</h3>
                <div class="space-y-4">
                    <div class="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded">
                        <strong>Informazione:</strong> Questo è un messaggio informativo
                    </div>
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                        <strong>Successo:</strong> Operazione completata con successo
                    </div>
                    <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded">
                        <strong>Attenzione:</strong> Verifica i dati inseriti
                    </div>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                        <strong>Errore:</strong> Si è verificato un errore
                    </div>
                </div>
            </div>
            
            <!-- Old Inputs -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold mb-4">Form Elements</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nome Progetto</label>
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Inserisci il nome del progetto">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <input type="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="<EMAIL>">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Budget</label>
                        <input type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="0.00">
                        <p class="text-xs text-gray-500 mt-1">Inserisci il budget in euro</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Campo con Errore</label>
                        <input type="text" class="w-full px-3 py-2 border border-red-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500" placeholder="Valore non valido">
                        <p class="text-xs text-red-500 mt-1">Questo campo è obbligatorio</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Differenze principali -->
    <div class="mt-12 p-6 bg-muted rounded-lg">
        <h2 class="text-xl font-bold mb-4">🎯 Principali Differenze</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h3 class="font-semibold text-green-600 mb-2">✅ Nuovo Design System</h3>
                <ul class="text-sm space-y-1">
                    <li>• Design tokens consistenti</li>
                    <li>• Componenti riutilizzabili</li>
                    <li>• Varianti predefinite</li>
                    <li>• Dark mode ready</li>
                    <li>• Accessibilità migliorata</li>
                    <li>• Animazioni fluide</li>
                    <li>• White label support</li>
                </ul>
            </div>
            <div>
                <h3 class="font-semibold text-orange-600 mb-2">⚠️ Vecchio Design</h3>
                <ul class="text-sm space-y-1">
                    <li>• Stili hardcoded</li>
                    <li>• Inconsistenze visive</li>
                    <li>• Difficile manutenzione</li>
                    <li>• No standardizzazione</li>
                    <li>• Accessibilità limitata</li>
                    <li>• Personalizzazione complessa</li>
                    <li>• No sistema di temi</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
