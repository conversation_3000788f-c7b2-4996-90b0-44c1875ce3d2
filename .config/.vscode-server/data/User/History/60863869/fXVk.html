{% load static %}
{% load ui_components %}
<!DOCTYPE html>
<html lang="it" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ExProject{% endblock %}</title>
    
    <!-- Favicon e PWA -->
    <link rel="icon" href="{% static 'img/favicon.ico' %}" type="image/x-icon">
    <meta name="theme-color" content="hsl(221.2 83.2% 53.3%)">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <!-- Design System CSS -->
    {% ui_theme_css %}
    
    <!-- TailwindCSS con configurazione custom -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        border: "hsl(var(--border))",
                        input: "hsl(var(--input))",
                        ring: "hsl(var(--ring))",
                        background: "hsl(var(--background))",
                        foreground: "hsl(var(--foreground))",
                        primary: {
                            DEFAULT: "hsl(var(--primary))",
                            foreground: "hsl(var(--primary-foreground))",
                        },
                        secondary: {
                            DEFAULT: "hsl(var(--secondary))",
                            foreground: "hsl(var(--secondary-foreground))",
                        },
                        destructive: {
                            DEFAULT: "hsl(var(--destructive))",
                            foreground: "hsl(var(--destructive-foreground))",
                        },
                        muted: {
                            DEFAULT: "hsl(var(--muted))",
                            foreground: "hsl(var(--muted-foreground))",
                        },
                        accent: {
                            DEFAULT: "hsl(var(--accent))",
                            foreground: "hsl(var(--accent-foreground))",
                        },
                        popover: {
                            DEFAULT: "hsl(var(--popover))",
                            foreground: "hsl(var(--popover-foreground))",
                        },
                        card: {
                            DEFAULT: "hsl(var(--card))",
                            foreground: "hsl(var(--card-foreground))",
                        },
                    },
                    fontFamily: {
                        sans: ['Inter', 'ui-sans-serif', 'system-ui'],
                        mono: ['JetBrains Mono', 'ui-monospace'],
                    },
                    borderRadius: {
                        lg: "var(--radius)",
                        md: "calc(var(--radius) - 2px)",
                        sm: "calc(var(--radius) - 4px)",
                    },
                    keyframes: {
                        "accordion-down": {
                            from: { height: 0 },
                            to: { height: "var(--radix-accordion-content-height)" },
                        },
                        "accordion-up": {
                            from: { height: "var(--radix-accordion-content-height)" },
                            to: { height: 0 },
                        },
                    },
                    animation: {
                        "accordion-down": "accordion-down 0.2s ease-out",
                        "accordion-up": "accordion-up 0.2s ease-out",
                    },
                }
            }
        }
    </script>
    
    <!-- HTMX e Alpine.js -->
    <script src="https://cdn.jsdelivr.net/npm/htmx.org@1.9.10"></script>
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.3/dist/cdn.min.js" defer></script>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="min-h-screen bg-background font-sans antialiased"
      hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
      x-data="{ sidebarOpen: false, darkMode: false }"
      x-init="darkMode = localStorage.getItem('darkMode') === 'true'"
      :class="{ 'dark': darkMode }">
    
    <!-- Header moderno -->
    {% block header %}
    <header class="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div class="container flex h-14 items-center">
            <!-- Mobile menu button -->
            <button @click="sidebarOpen = !sidebarOpen"
                    class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10 md:hidden">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-4 w-4">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5" />
                </svg>
            </button>
            
            <!-- Logo -->
            <div class="mr-4 hidden md:flex">
                <a href="{% url 'public:home' %}" class="mr-6 flex items-center space-x-2">
                    <div class="h-6 w-6 rounded bg-primary"></div>
                    <span class="hidden font-bold sm:inline-block">ExProject</span>
                </a>
            </div>
            
            <!-- Navigation -->
            {% if user.is_authenticated %}
            <nav class="flex items-center space-x-6 text-sm font-medium hidden md:flex">
                <a href="{% url 'core:dashboard' %}" 
                   class="transition-colors hover:text-foreground/80 {% if request.resolver_match.url_name == 'dashboard' %}text-foreground{% else %}text-foreground/60{% endif %}">
                    Dashboard
                </a>
                <a href="{% url 'projects:list' %}" 
                   class="transition-colors hover:text-foreground/80 {% if request.resolver_match.namespace == 'projects' %}text-foreground{% else %}text-foreground/60{% endif %}">
                    Progetti
                </a>
                <a href="{% url 'workflow:list' %}" 
                   class="transition-colors hover:text-foreground/80 {% if request.resolver_match.namespace == 'workflow' %}text-foreground{% else %}text-foreground/60{% endif %}">
                    Workflow
                </a>
                <a href="{% url 'documents:list' %}" 
                   class="transition-colors hover:text-foreground/80 {% if request.resolver_match.namespace == 'documents' %}text-foreground{% else %}text-foreground/60{% endif %}">
                    Documenti
                </a>
            </nav>
            {% endif %}
            
            <div class="flex flex-1 items-center justify-between space-x-2 md:justify-end">
                <!-- Search -->
                {% if user.is_authenticated %}
                <div class="w-full flex-1 md:w-auto md:flex-none">
                    <form method="get" action="{% url 'core:search' %}" class="relative">
                        <input type="text" name="q" placeholder="Cerca..." 
                               class="inline-flex items-center rounded-md font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground h-9 px-4 py-2 relative w-full justify-start text-sm text-muted-foreground sm:pr-12 md:w-40 lg:w-64">
                        <button type="submit" class="absolute right-0 top-0 h-9 w-9 inline-flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-4 w-4">
                                <path stroke-linecap="round" stroke-linejoin="round" d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                            </svg>
                        </button>
                    </form>
                </div>
                {% endif %}
                
                <!-- Theme toggle -->
                <button @click="darkMode = !darkMode; localStorage.setItem('darkMode', darkMode)" 
                        class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10">
                    <span x-show="!darkMode">{% ui_icon 'moon' size="4" %}</span>
                    <span x-show="darkMode">{% ui_icon 'sun' size="4" %}</span>
                </button>
                
                <!-- User menu -->
                {% if user.is_authenticated %}
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" 
                            class="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 w-10">
                        <div class="h-8 w-8 rounded-full bg-primary flex items-center justify-center text-primary-foreground text-sm font-medium">
                            {{ user.username|slice:":1"|upper }}
                        </div>
                    </button>
                    
                    <div x-show="open" @click.away="open = false" 
                         x-transition:enter="transition ease-out duration-100"
                         x-transition:enter-start="transform opacity-0 scale-95"
                         x-transition:enter-end="transform opacity-100 scale-100"
                         x-transition:leave="transition ease-in duration-75"
                         x-transition:leave-start="transform opacity-100 scale-100"
                         x-transition:leave-end="transform opacity-0 scale-95"
                         class="absolute right-0 mt-2 w-56 rounded-md border bg-popover p-1 text-popover-foreground shadow-md outline-none z-50">
                        <div class="px-2 py-1.5 text-sm font-semibold">{{ user.get_full_name|default:user.username }}</div>
                        <div class="h-px bg-border my-1"></div>
                        <a href="{% url 'core:profile' %}" class="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground">
                            {% ui_icon 'user' size="4" class="mr-2" %}
                            Profilo
                        </a>
                        <a href="{% url 'core:settings' %}" class="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground">
                            {% ui_icon 'settings' size="4" class="mr-2" %}
                            Impostazioni
                        </a>
                        <div class="h-px bg-border my-1"></div>
                        <a href="{% url 'core:logout' %}" class="relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors hover:bg-accent hover:text-accent-foreground">
                            {% ui_icon 'logout' size="4" class="mr-2" %}
                            Logout
                        </a>
                    </div>
                </div>
                {% else %}
                {% ui_button "Accedi" variant="default" size="sm" href="/login/" %}
                {% endif %}
            </div>
        </div>
    </header>
    {% endblock %}
    
    <!-- Mobile sidebar overlay -->
    <div x-show="sidebarOpen" @click="sidebarOpen = false" 
         class="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm md:hidden"
         x-transition:enter="transition-all ease-in-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-all ease-in-out duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"></div>
    
    <!-- Main content -->
    <div class="flex-1">
        {% block messages %}
        {% if messages %}
        <div class="container mx-auto px-4 py-2">
            {% for message in messages %}
            {% ui_alert message.message variant=message.tags title="Notifica" %}
            {% endfor %}
        </div>
        {% endif %}
        {% endblock %}
        
        {% block main %}
        <main class="container mx-auto px-4 py-6">
            {% block content %}{% endblock %}
        </main>
        {% endblock %}
    </div>
    
    <!-- Footer -->
    {% block footer %}
    <footer class="border-t bg-background">
        <div class="container flex flex-col items-center justify-between gap-4 py-10 md:h-24 md:flex-row md:py-0">
            <div class="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0">
                <p class="text-center text-sm leading-loose text-muted-foreground md:text-left">
                    © 2025 ExProject. Tutti i diritti riservati.
                </p>
            </div>
        </div>
    </footer>
    {% endblock %}
    
    {% block extra_js %}{% endblock %}
</body>
</html>
