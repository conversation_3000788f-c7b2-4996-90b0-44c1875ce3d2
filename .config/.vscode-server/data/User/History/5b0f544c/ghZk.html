{% extends 'ui/base_modern.html' %}
{% load ui_components %}

{% block title %}Dashboard - ExProject{% endblock %}

{% block content %}
<div class="flex-1 space-y-4 p-8 pt-6">
    <!-- Header della dashboard -->
    <div class="flex items-center justify-between space-y-2">
        <h2 class="text-3xl font-bold tracking-tight">Dashboard</h2>
        <div class="flex items-center space-x-2">
            {% ui_button "Nuovo Progetto" variant="default" icon_left="plus" %}
            {% ui_button "Esporta" variant="outline" icon_left="download" %}
        </div>
    </div>
    
    <!-- Statistiche principali -->
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
            <div class="flex flex-col space-y-1.5 p-6">
                <h3 class="text-2xl font-semibold leading-none tracking-tight"><PERSON><PERSON><PERSON></h3>
            </div>
            <div class="p-6 pt-0">
                <div class="text-2xl font-bold">{{ stats.active_projects|default:0 }}</div>
                <p class="text-xs text-muted-foreground">
                    +20.1% dal mese scorso
                </p>
            </div>
        </div>
        
        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
            <div class="flex flex-col space-y-1.5 p-6">
                <h3 class="text-2xl font-semibold leading-none tracking-tight">Pratiche in Corso</h3>
            </div>
            <div class="p-6 pt-0">
                <div class="text-2xl font-bold">{{ stats.pending_workflows|default:0 }}</div>
                <p class="text-xs text-muted-foreground">
                    +180.1% dal mese scorso
                </p>
            </div>
        </div>

        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
            <div class="flex flex-col space-y-1.5 p-6">
                <h3 class="text-2xl font-semibold leading-none tracking-tight">Documenti Generati</h3>
            </div>
            <div class="p-6 pt-0">
                <div class="text-2xl font-bold">{{ stats.documents_generated|default:0 }}</div>
                <p class="text-xs text-muted-foreground">
                    +19% dal mese scorso
                </p>
            </div>
        </div>

        <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
            <div class="flex flex-col space-y-1.5 p-6">
                <h3 class="text-2xl font-semibold leading-none tracking-tight">Valore Totale</h3>
            </div>
            <div class="p-6 pt-0">
                <div class="text-2xl font-bold">€{{ stats.total_value|default:0|floatformat:0 }}</div>
                <p class="text-xs text-muted-foreground">
                    +201 dal mese scorso
                </p>
            </div>
        </div>
    </div>
    
    <!-- Grafici e tabelle -->
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <!-- Grafico principale -->
        {% ui_card title="Panoramica" class="col-span-4" %}
            {% block card_content %}
            <div class="h-[200px] flex items-center justify-center text-muted-foreground">
                <div class="text-center">
                    {% ui_icon 'chart' size="12" class="mx-auto mb-2 text-muted-foreground/50" %}
                    <p>Grafico in arrivo</p>
                </div>
            </div>
            {% endblock %}
        {% endui_card %}
        
        <!-- Attività recenti -->
        {% ui_card title="Attività Recenti" class="col-span-3" %}
            {% block card_content %}
            <div class="space-y-8">
                {% for activity in recent_activities|slice:":5" %}
                <div class="flex items-center">
                    <div class="h-9 w-9 rounded-full bg-primary/10 flex items-center justify-center">
                        {% ui_icon activity.icon|default:'activity' size="4" class="text-primary" %}
                    </div>
                    <div class="ml-4 space-y-1">
                        <p class="text-sm font-medium leading-none">{{ activity.title }}</p>
                        <p class="text-sm text-muted-foreground">{{ activity.description }}</p>
                    </div>
                    <div class="ml-auto font-medium text-sm text-muted-foreground">
                        {{ activity.created_at|timesince }}
                    </div>
                </div>
                {% empty %}
                <div class="text-center text-muted-foreground py-8">
                    {% ui_icon 'clock' size="8" class="mx-auto mb-2 text-muted-foreground/50" %}
                    <p>Nessuna attività recente</p>
                </div>
                {% endfor %}
            </div>
            {% endblock %}
        {% endui_card %}
    </div>
    
    <!-- Progetti recenti -->
    {% ui_card title="Progetti Recenti" %}
        {% block card_content %}
        {% if recent_projects %}
        {% ui_table headers=table_headers rows=table_rows %}
        {% else %}
        <div class="text-center py-8">
            <div class="mx-auto h-12 w-12 text-muted-foreground/50">
                {% ui_icon 'folder' size="12" %}
            </div>
            <h3 class="mt-2 text-sm font-semibold text-foreground">Nessun progetto</h3>
            <p class="mt-1 text-sm text-muted-foreground">Inizia creando il tuo primo progetto.</p>
            <div class="mt-6">
                {% ui_button "Nuovo Progetto" variant="default" icon_left="plus" %}
            </div>
        </div>
        {% endif %}
        {% endblock %}
    {% endui_card %}
</div>

<!-- Script per interattività -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Aggiorna statistiche ogni 30 secondi
    setInterval(function() {
        htmx.ajax('GET', '{% url "core:dashboard_stats" %}', {
            target: '.grid.gap-4.md\\:grid-cols-2.lg\\:grid-cols-4',
            swap: 'innerHTML'
        });
    }, 30000);
    
    // Gestione notifiche real-time
    if ('WebSocket' in window) {
        const ws = new WebSocket('ws://localhost:8000/ws/notifications/');
        ws.onmessage = function(event) {
            const data = JSON.parse(event.data);
            // Mostra notifica
            showNotification(data.message, data.type);
        };
    }
});

function showNotification(message, type = 'info') {
    // Implementazione notifiche toast
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 rounded-lg border bg-card text-card-foreground shadow-lg p-4 max-w-sm`;
    notification.innerHTML = `
        <div class="flex items-start space-x-2">
            <div class="flex-shrink-0">
                ${type === 'success' ? '✓' : type === 'error' ? '✗' : 'ℹ'}
            </div>
            <div class="flex-1">
                <p class="text-sm font-medium">${message}</p>
            </div>
            <button onclick="this.parentElement.parentElement.remove()" class="flex-shrink-0 text-muted-foreground hover:text-foreground">
                ×
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove dopo 5 secondi
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
