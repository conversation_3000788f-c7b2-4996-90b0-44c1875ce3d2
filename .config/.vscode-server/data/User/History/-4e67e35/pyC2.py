import os
from pathlib import Path
from decouple import config
import dj_database_url

BASE_DIR = Path(__file__).resolve().parent.parent.parent

SECRET_KEY = config('SECRET_KEY', default='django-insecure-dev-key-change-in-production')

DEBUG = config('DEBUG', default=True, cast=bool)

ALLOWED_HOSTS = ['*']

CSRF_TRUSTED_ORIGINS = [
    'https://*.replit.app',
    'https://*.replit.dev',
    'https://*.replit.co',
    'http://localhost:8000',
    'https://localhost:8000',
    'http://127.0.0.1:8000',
    'https://127.0.0.1:8000',
]

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    # 'django.contrib.gis',  # GeoDjango - Commentato finché GDAL non è installato
    'rest_framework',
    'rest_framework.authtoken',
    'django_filters',
    'corsheaders',
    'drf_spectacular',
    # Custom apps
    'apps.core',
    'apps.projects',
    'apps.workflow',
    'apps.documents',
    'apps.economic',
    'apps.integrations',
    'apps.public',
    'apps.api',
    'apps.ui',  # Design System e UI Components
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'exproject.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'exproject.wsgi.application'

DATABASES = {
    'default': dj_database_url.config(
        default=config('DATABASE_URL'),
        conn_max_age=600,
        conn_health_checks=True,
    )
}

# Per PostGIS - Commentato temporaneamente finché GDAL non è installato
# if 'postgresql' in DATABASES['default']['ENGINE']:
#     DATABASES['default']['ENGINE'] = 'django.contrib.gis.db.backends.postgis'

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]

LANGUAGE_CODE = 'it-it'
TIME_ZONE = 'Europe/Rome'
USE_I18N = True
USE_TZ = True

STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
STATICFILES_DIRS = [
    BASE_DIR / 'static',
]

MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

CORS_ALLOW_ALL_ORIGINS = True

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'rest_framework.authentication.TokenAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
    'DEFAULT_FILTER_BACKENDS': [
        'django_filters.rest_framework.DjangoFilterBackend',
        'rest_framework.filters.SearchFilter',
        'rest_framework.filters.OrderingFilter',
    ],
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        'rest_framework.renderers.BrowsableAPIRenderer',
    ],
    'DEFAULT_PARSER_CLASSES': [
        'rest_framework.parsers.JSONParser',
        'rest_framework.parsers.FormParser',
        'rest_framework.parsers.MultiPartParser',
    ],
    'EXCEPTION_HANDLER': 'apps.api.exceptions.custom_exception_handler',
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle'
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '100/hour',
        'user': '1000/hour'
    },
    'DEFAULT_METADATA_CLASS': 'rest_framework.metadata.SimpleMetadata',
    'DEFAULT_VERSIONING_CLASS': 'rest_framework.versioning.URLPathVersioning',
    'DEFAULT_SCHEMA_CLASS': 'drf_spectacular.openapi.AutoSchema',
}

# Configurazione drf-spectacular per documentazione API
SPECTACULAR_SETTINGS = {
    'TITLE': 'ExProject API',
    'DESCRIPTION': '''
    API REST per ExProject - Sistema di gestione procedure espropriative secondo DPR 327/2001.
    
    Questa API fornisce accesso completo alle funzionalità del sistema ExProject per la gestione 
    di procedure di espropriazione per pubblica utilità, inclusi:
    
    - Gestione enti e progetti
    - Anagrafica proprietari e particelle
    - Generazione documenti da template
    - Firme digitali e audit trail
    - Workflow e notifiche
    
    ## Autenticazione
    
    L'API utilizza Token Authentication. Includi l'header:
    ```
    Authorization: Token your_token_here
    ```
    
    ## Versioning
    
    L'API è versionata tramite URL. La versione corrente è v1:
    ```
    /api/v1/endpoint/
    ```
    ''',
    'VERSION': '1.0.0',
    'SERVE_INCLUDE_SCHEMA': False,
    'CONTACT': {
        'name': 'ExProject Team',
        'email': '<EMAIL>'
    },
    'LICENSE': {
        'name': 'Proprietario',
    },
    'TAGS': [
        {
            'name': 'Entities',
            'description': 'Gestione enti pubblici e privati'
        },
        {
            'name': 'Projects', 
            'description': 'Gestione progetti di espropriazione'
        },
        {
            'name': 'Parcels',
            'description': 'Gestione particelle catastali'
        },
        {
            'name': 'Owners',
            'description': 'Anagrafica proprietari'
        },
        {
            'name': 'Documents',
            'description': 'Gestione documenti e template'
        },
        {
            'name': 'Audit',
            'description': 'Audit trail e log di sistema'
        }
    ],
    'COMPONENT_SPLIT_REQUEST': True,
    'SCHEMA_PATH_PREFIX': '/api/',
    'SCHEMA_PATH_PREFIX_TRIM': False,
    'PREPROCESSING_HOOKS': [],
    'POSTPROCESSING_HOOKS': [],
    'ENUM_NAME_OVERRIDES': {
        'EntityTypeChoices': 'apps.core.models.ENTITY_TYPE_CHOICES',
        'ProjectStatusChoices': 'apps.core.models.PROJECT_STATUS_CHOICES',
        'DocumentStatusChoices': 'apps.core.models.DOCUMENT_STATUS_CHOICES',
    },
    'SORT_OPERATIONS': True,
    'SWAGGER_UI_SETTINGS': {
        'deepLinking': True,
        'persistAuthorization': True,
        'displayOperationId': False,
        'defaultModelsExpandDepth': 1,
        'defaultModelExpandDepth': 1,
    },
    'SCHEMA_COERCE_PATH_PK': True,
    'DISABLE_ERRORS_AND_WARNINGS': True,
    'SECURITY': [
        {
            'tokenAuth': []
        }
    ],
    'COMPONENTS': {
        'securitySchemes': {
            'tokenAuth': {
                'type': 'apiKey',
                'in': 'header',
                'name': 'Authorization',
                'description': 'Token-based authentication with required prefix "Token"'
            }
        }
    },
}

LOGIN_URL = '/exproject/login/'
LOGIN_REDIRECT_URL = '/exproject/'
LOGOUT_REDIRECT_URL = '/'

# CSRF exemption for API endpoints
CSRF_EXEMPT_URLS = [
    r'^/api/',
]

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField' 