*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[18:08:42] 




[18:08:42] Extension host agent started.
[18:08:43] [<unknown>][95296dd8][ExtensionHostConnection] New connection established.
[18:08:43] [<unknown>][512be93b][ManagementConnection] New connection established.
[18:08:43] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[18:08:43] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[18:08:43] [<unknown>][95296dd8][ExtensionHostConnection] <497> Launched Extension Host Process.
[18:08:43] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[18:08:43] ComputeTargetPlatform: linux-x64
[18:08:43] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[18:08:44] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[18:08:46] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[18:08:46] ComputeTargetPlatform: linux-x64
[18:08:46] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[18:08:46] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[18:08:47] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[18:08:47] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[18:08:47] Getting Manifest... github.copilot
[18:08:47] Getting Manifest... github.copilot-chat
[18:08:47] Installing extension: github.copilot {
  productVersion: { version: '1.101.1', date: '2025-06-18T13:35:12.605Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  profileLocation: Br {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[18:08:47] Installing extension: github.copilot-chat {
  productVersion: { version: '1.101.1', date: '2025-06-18T13:35:12.605Z' },
  pinned: false,
  operation: 3,
  isApplicationScoped: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  profileLocation: Br {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  }
}
[18:08:47] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[18:08:47] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[18:08:50] Extension signature verification result for github.copilot-chat: Success. Internal Code: 0. Executed: true. Duration: 1770ms.
[18:08:50] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 1828ms.
[18:08:51] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-chat-0.28.1: github.copilot-chat
[18:08:51] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.28.1
[18:08:51] [/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2]: This extension is using the API proposal 'languageModelDataPart' that is not compatible with the current version of VS Code.
[18:08:51] Marked extension as removed github.copilot-chat-0.27.2
[18:08:51] Extension installed successfully: github.copilot-chat file:///home/<USER>/.vscode-server/extensions/extensions.json
[18:08:51] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-1.336.0: github.copilot
[18:08:51] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-1.336.0
[18:08:51] Marked extension as removed github.copilot-1.326.0
[18:08:51] Extension installed successfully: github.copilot file:///home/<USER>/.vscode-server/extensions/extensions.json
[18:10:03] Getting Manifest... augment.vscode-augment
[18:10:03] Installing extension: augment.vscode-augment {
  installPreReleaseVersion: false,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  isApplicationScoped: false,
  profileLocation: Br {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.101.1', date: '2025-06-18T13:35:12.605Z' }
}
[18:10:04] Extension signature verification result for augment.vscode-augment: Success. Internal Code: 0. Executed: true. Duration: 911ms.
[18:10:05] Extracted extension to file:///home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1: augment.vscode-augment
[18:10:05] Renamed to /home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1
[18:10:05] Extension installed successfully: augment.vscode-augment file:///home/<USER>/.vscode-server/extensions/extensions.json
[18:11:05] Installing extension: anthropic.claude-code {
  isMachineScoped: true,
  installGivenVersion: true,
  isApplicationScoped: false,
  profileLocation: Br {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.101.1', date: '2025-06-18T13:35:12.605Z' }
}
[18:11:05] Extracted extension to file:///home/<USER>/.vscode-server/extensions/anthropic.claude-code-1.0.11: anthropic.claude-code
[18:11:05] Renamed to /home/<USER>/.vscode-server/extensions/anthropic.claude-code-1.0.11
[18:11:05] Extension installed successfully: anthropic.claude-code file:///home/<USER>/.vscode-server/extensions/extensions.json
New EH opened, aborting shutdown
[18:13:42] New EH opened, aborting shutdown
