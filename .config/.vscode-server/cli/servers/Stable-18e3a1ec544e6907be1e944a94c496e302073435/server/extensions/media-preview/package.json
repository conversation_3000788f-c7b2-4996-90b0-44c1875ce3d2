{"name": "media-preview", "displayName": "%displayName%", "description": "%description%", "extensionKind": ["ui", "workspace"], "version": "1.0.0", "publisher": "vscode", "icon": "icon.png", "license": "MIT", "aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "engines": {"vscode": "^1.70.0"}, "main": "./dist/extension", "browser": "./dist/browser/extension.js", "categories": ["Other"], "activationEvents": [], "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": true}}, "contributes": {"configuration": {"type": "object", "title": "Media Previewer", "properties": {"mediaPreview.video.autoPlay": {"type": "boolean", "default": false, "markdownDescription": "%videoPreviewerAutoPlay%"}, "mediaPreview.video.loop": {"type": "boolean", "default": false, "markdownDescription": "%videoPreviewerLoop%"}}}, "customEditors": [{"viewType": "imagePreview.previewEditor", "displayName": "%customEditor.imagePreview.displayName%", "priority": "builtin", "selector": [{"filenamePattern": "*.{jpg,jpe,jpeg,png,bmp,gif,ico,webp,avif,svg}"}]}, {"viewType": "vscode.audioPreview", "displayName": "%customEditor.audioPreview.displayName%", "priority": "builtin", "selector": [{"filenamePattern": "*.{mp3,wav,ogg,oga}"}]}, {"viewType": "vscode.videoPreview", "displayName": "%customEditor.videoPreview.displayName%", "priority": "builtin", "selector": [{"filenamePattern": "*.{mp4,webm}"}]}], "commands": [{"command": "imagePreview.zoomIn", "title": "%command.zoomIn%", "category": "Image Preview"}, {"command": "imagePreview.zoomOut", "title": "%command.zoomOut%", "category": "Image Preview"}, {"command": "imagePreview.copyImage", "title": "%command.copyImage%", "category": "Image Preview"}, {"command": "imagePreview.reopenAsPreview", "title": "%command.reopenAsPreview%", "category": "Image Preview", "icon": "$(preview)"}, {"command": "imagePreview.reopenAsText", "title": "%command.reopenAsText%", "category": "Image Preview", "icon": "$(go-to-file)"}], "menus": {"commandPalette": [{"command": "imagePreview.zoomIn", "when": "activeCustomEditorId == 'imagePreview.previewEditor'", "group": "1_imagePreview"}, {"command": "imagePreview.zoomOut", "when": "activeCustomEditorId == 'imagePreview.previewEditor'", "group": "1_imagePreview"}, {"command": "imagePreview.copyImage", "when": "false"}, {"command": "imagePreview.reopenAsPreview", "when": "activeEditor == workbench.editors.files.textFileEditor && resourceExtname == '.svg'", "group": "navigation"}, {"command": "imagePreview.reopenAsText", "when": "activeCustomEditorId == 'imagePreview.previewEditor' && resourceExtname == '.svg'", "group": "navigation"}], "webview/context": [{"command": "imagePreview.copyImage", "when": "webviewId == 'imagePreview.previewEditor'"}], "editor/title": [{"command": "imagePreview.reopenAsPreview", "when": "editorFocus && resourceExtname == '.svg'", "group": "navigation"}, {"command": "imagePreview.reopenAsText", "when": "activeCustomEditorId == 'imagePreview.previewEditor' && resourceExtname == '.svg'", "group": "navigation"}]}}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}