{"name": "json-language-features", "displayName": "%displayName%", "description": "%description%", "version": "1.0.0", "publisher": "vscode", "license": "MIT", "aiKey": "0c6ae279ed8443289764825290e4f9e2-1a736e7c-1324-4338-be46-fc2a58ae4d14-7255", "engines": {"vscode": "^1.77.0"}, "enabledApiProposals": ["extensionsAny"], "icon": "icons/json.png", "activationEvents": ["onLanguage:json", "onLanguage:jsonc", "onLanguage:snippets", "onCommand:json.validate"], "main": "./client/dist/node/jsonClientMain", "browser": "./client/dist/browser/jsonClientMain", "capabilities": {"virtualWorkspaces": true, "untrustedWorkspaces": {"supported": true}}, "categories": ["Programming Languages"], "contributes": {"configuration": {"id": "json", "order": 20, "type": "object", "title": "JSON", "properties": {"json.schemas": {"type": "array", "scope": "resource", "description": "%json.schemas.desc%", "items": {"type": "object", "default": {"fileMatch": ["/myfile"], "url": "schemaURL"}, "properties": {"url": {"type": "string", "default": "/user.schema.json", "description": "%json.schemas.url.desc%"}, "fileMatch": {"type": "array", "items": {"type": "string", "default": "MyFile.json", "description": "%json.schemas.fileMatch.item.desc%"}, "minItems": 1, "description": "%json.schemas.fileMatch.desc%"}, "schema": {"$ref": "http://json-schema.org/draft-07/schema#", "description": "%json.schemas.schema.desc%"}}}}, "json.validate.enable": {"type": "boolean", "scope": "window", "default": true, "description": "%json.validate.enable.desc%"}, "json.format.enable": {"type": "boolean", "scope": "window", "default": true, "description": "%json.format.enable.desc%"}, "json.format.keepLines": {"type": "boolean", "scope": "window", "default": false, "description": "%json.format.keepLines.desc%"}, "json.trace.server": {"type": "string", "scope": "window", "enum": ["off", "messages", "verbose"], "default": "off", "description": "%json.tracing.desc%"}, "json.colorDecorators.enable": {"type": "boolean", "scope": "window", "default": true, "description": "%json.colorDecorators.enable.desc%", "deprecationMessage": "%json.colorDecorators.enable.deprecationMessage%"}, "json.maxItemsComputed": {"type": "number", "default": 5000, "description": "%json.maxItemsComputed.desc%"}, "json.schemaDownload.enable": {"type": "boolean", "default": true, "description": "%json.enableSchemaDownload.desc%", "tags": ["usesOnlineServices"]}}}, "configurationDefaults": {"[json]": {"editor.quickSuggestions": {"strings": true}, "editor.suggest.insertMode": "replace"}, "[jsonc]": {"editor.quickSuggestions": {"strings": true}, "editor.suggest.insertMode": "replace"}, "[snippets]": {"editor.quickSuggestions": {"strings": true}, "editor.suggest.insertMode": "replace"}}, "jsonValidation": [{"fileMatch": "*.schema.json", "url": "http://json-schema.org/draft-07/schema#"}], "commands": [{"command": "json.clearCache", "title": "%json.command.clearCache%", "category": "JSON"}, {"command": "json.sort", "title": "%json.command.sort%", "category": "JSON"}]}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}}