*
* Visual Studio Code Server
*
* By using the software, you agree to
* the Visual Studio Code Server License Terms (https://aka.ms/vscode-server-license) and
* the Microsoft Privacy Statement (https://privacy.microsoft.com/en-US/privacystatement).
*
Server bound to /tmp/code-************************************
Extension host agent listening on /tmp/code-************************************

[13:41:39] 




[13:41:39] Extension host agent started.
[13:41:39] Started initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
[13:41:40] ComputeTargetPlatform: linux-x64
[13:41:40] Completed initializing default profile extensions in extensions installation folder. file:///home/<USER>/.vscode-server/extensions
[13:41:40] [<unknown>][bb76898a][ManagementConnection] New connection established.
[13:41:40] [<unknown>][ff22982a][ExtensionHostConnection] New connection established.
[13:41:40] [<unknown>][ff22982a][ExtensionHostConnection] <9623> Launched Extension Host Process.
[13:41:42] ComputeTargetPlatform: linux-x64
[13:41:43] Getting Manifest... github.copilot-chat
[13:41:43] Getting Manifest... github.copilot
[13:41:43] Installing extension: github.copilot {
  installPreReleaseVersion: false,
  donotIncludePackAndDependencies: true,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  isApplicationScoped: false,
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.100.2', date: '2025-05-14T21:47:40.416Z' }
}
[13:41:43] Installing the extension without checking dependencies and pack github.copilot
[13:41:43] Installing extension: github.copilot-chat {
  installPreReleaseVersion: false,
  donotIncludePackAndDependencies: true,
  donotVerifySignature: false,
  context: { clientTargetPlatform: 'darwin-arm64' },
  isApplicationScoped: false,
  profileLocation: _r {
    scheme: 'file',
    authority: '',
    path: '/home/<USER>/.vscode-server/extensions/extensions.json',
    query: '',
    fragment: '',
    _formatted: 'file:///home/<USER>/.vscode-server/extensions/extensions.json',
    _fsPath: '/home/<USER>/.vscode-server/extensions/extensions.json'
  },
  productVersion: { version: '1.100.2', date: '2025-05-14T21:47:40.416Z' }
}
[13:41:43] Installing the extension without checking dependencies and pack github.copilot-chat
[13:41:45] Extension signature verification result for github.copilot-chat: Success. Internal Code: 0. Executed: true. Duration: 1271ms.
[13:41:45] Extension signature verification result for github.copilot: Success. Internal Code: 0. Executed: true. Duration: 1304ms.
[13:41:45] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2: github.copilot-chat
[13:41:45] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-chat-0.27.2
[13:41:45] Extension installed successfully: github.copilot-chat file:///home/<USER>/.vscode-server/extensions/extensions.json
[13:41:46] Extracted extension to file:///home/<USER>/.vscode-server/extensions/github.copilot-1.326.0: github.copilot
[13:41:46] Renamed to /home/<USER>/.vscode-server/extensions/github.copilot-1.326.0
[13:41:46] Extension installed successfully: github.copilot file:///home/<USER>/.vscode-server/extensions/extensions.json
New EH opened, aborting shutdown
[13:46:39] New EH opened, aborting shutdown
[19:28:39] [<unknown>][bb76898a][ManagementConnection] The client has disconnected gracefully, so the connection will be disposed.
[19:28:39] [<unknown>][ff22982a][ExtensionHostConnection] <9623> Extension Host Process exited with code: 0, signal: null.
Last EH closed, waiting before shutting down
[19:28:39] Last EH closed, waiting before shutting down
