<?xml version="1.0" encoding="utf-8"?>
	<PackageManifest Version="2.0.0" xmlns="http://schemas.microsoft.com/developer/vsx-schema/2011" xmlns:d="http://schemas.microsoft.com/developer/vsx-schema-design/2011">
		<Metadata>
			<Identity Language="en-US" Id="stagewise-vscode-extension" Version="0.6.0" Publisher="stagewise" />
			<DisplayName>stagewise</DisplayName>
			<Description xml:space="preserve">Visual vibe coding. Right in your codebase.</Description>
			<Tags>stagewise,vscode,extension,ai,code,agent,debugging</Tags>
			<Categories>AI,Debuggers,Machine Learning,Other</Categories>
			<GalleryFlags>Public</GalleryFlags>
			
			<Properties>
				<Property Id="Microsoft.VisualStudio.Code.Engine" Value="^1.85.0" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionDependencies" Value="" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionPack" Value="" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionKind" Value="workspace" />
				<Property Id="Microsoft.VisualStudio.Code.LocalizedLanguages" Value="" />
				<Property Id="Microsoft.VisualStudio.Code.EnabledApiProposals" Value="" />
				
				<Property Id="Microsoft.VisualStudio.Code.ExecutesCode" Value="true" />
				
				<Property Id="Microsoft.VisualStudio.Services.Links.Source" Value="https://github.com/stagewise-io/stagewise.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Getstarted" Value="https://github.com/stagewise-io/stagewise.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.GitHub" Value="https://github.com/stagewise-io/stagewise.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Support" Value="https://github.com/stagewise-io/stagewise/issues" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Learn" Value="https://stagewise.io" />
				
				
				<Property Id="Microsoft.VisualStudio.Services.GitHubFlavoredMarkdown" Value="true" />
				<Property Id="Microsoft.VisualStudio.Services.Content.Pricing" Value="Free"/>

				
				
			</Properties>
			<License>extension/LICENSE.txt</License>
			<Icon>extension/icon.png</Icon>
		</Metadata>
		<Installation>
			<InstallationTarget Id="Microsoft.VisualStudio.Code"/>
		</Installation>
		<Dependencies/>
		<Assets>
			<Asset Type="Microsoft.VisualStudio.Code.Manifest" Path="extension/package.json" Addressable="true" />
			<Asset Type="Microsoft.VisualStudio.Services.Content.Details" Path="extension/readme.md" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Content.Changelog" Path="extension/changelog.md" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Content.License" Path="extension/LICENSE.txt" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Icons.Default" Path="extension/icon.png" Addressable="true" />
		</Assets>
	</PackageManifest>