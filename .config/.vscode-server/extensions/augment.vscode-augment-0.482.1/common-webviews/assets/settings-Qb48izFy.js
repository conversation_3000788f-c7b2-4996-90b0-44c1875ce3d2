var bi=Object.defineProperty;var ki=(s,e,t)=>e in s?bi(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var ve=(s,e,t)=>ki(s,typeof e!="symbol"?e+"":e,t);import{a3 as Ve,aj as Si,ak as mn,S as he,i as ge,s as me,V as M,I as Z,C as w,L as F,c as _,e as x,f as C,D as b,a8 as Ge,ad as jt,q as H,r as W,u as p,t as m,h as $,F as k,aa as Bs,X as xe,M as we,T as de,aC as nr,J as Ce,n as G,G as Js,b as qe,_ as Fe,a as ke,W as ts,$ as ze,a0 as De,a1 as Ue,g as yt,Y as ns,Z as Oa,j as Xe,ab as Ia,aA as St,ah as bt,K as sn,a9 as Ci,a4 as sr,H as Un,w as Vn,x as qn,y as Bn,d as lt,z as Jn,am as Pa,A as Oe,B as Ie,E as Pe,aE as Ti,ao as Mi,a6 as ja,ap as rr,a5 as Ra}from"./SpinnerAugment-Cx9dt_ox.js";import{P as _s,B as La,T as Ai,M as Ni,F as Zi,A as Ei,L as Oi}from"./arrow-up-right-from-square-D2UwhhNo.js";import"./design-system-init-BCZOObrS.js";import{W as se,a as tt,e as $e,u as Et,o as Ot,h as Me,g as Ii,H as ar}from"./BaseButton-BqzdgpkK.js";import{T as Gt,M as Fa}from"./TextTooltipAugment-DTMpOwfF.js";import{G as Pi,S as ji,a as Ri,C as Li,N as Fi,J as zi,L as Di,F as _t,b as Ze,D as Ui,c as Vi,M as qi}from"./mcp-logo-DslCzNpc.js";import{a as Ts,b as mt,G as Bi,L as Te,F as Ji,c as Gi,R as Hi,d as Wi,C as Ki,T as Yi,U as Xi,e as Qi}from"./github-7gPAsyj4.js";import{M as qt,C as eo}from"./magnifying-glass-Fv6Gz5Ea.js";import{V as za}from"./VSCodeCodicon-B3px2_jp.js";import{l as Es,A as Ms,n as Yn,o as Da,R as to,a as no,b as so}from"./types-8LwCBeyq.js";import{I as xs,A as ro}from"./IconButtonAugment-BjDqXmYl.js";import{o as ao}from"./keypress-DD1aQVr0.js";import{D as io}from"./Drawer-CBKSV_8p.js";import{B as Ne}from"./ButtonAugment-DhtPLzGu.js";import{T as oo}from"./Content-BiWRcmeV.js";import{T as Wt,D as Ae}from"./index-CGnj6T3o.js";import{C as co}from"./CalloutAugment-BFrX0piu.js";import{E as lo}from"./ellipsis-Cm0UKVWz.js";import{P as uo}from"./pen-to-square-CZwCjcp0.js";import{T as Ua}from"./TextAreaAugment-DEYj-_0J.js";import{a as po,C as mo}from"./CollapseButtonAugment-D3vAw6HE.js";import{M as fo}from"./index-8X-F_Twk.js";import{M as ho}from"./MarkdownEditor-ChD76zyi.js";import{C as go,E as vo}from"./chat-flags-model-GjgruWjX.js";import{R as ir}from"./chat-types-B-te1sXh.js";import{R as $o}from"./RulesDropdown-Df3D6er-.js";import{C as yo}from"./chevron-down-DYf4hfS2.js";import{n as _o}from"./lodash-Drc0SN5U.js";import"./index-DUiNNixO.js";import"./resize-observer-DdAtcrRr.js";import"./globals-D0QH3NT1.js";import"./CardAugment-RumqAz-v.js";import"./index-BxQII05L.js";import"./file-paths-BcSg4gks.js";const on={maxMS:9e5,initialMS:6e4,mult:2,maxSteps:4};class xo{constructor(e,t=on){ve(this,"timerId",null);ve(this,"currentMS");ve(this,"step",0);ve(this,"params");this.callback=e;const n={...t};n.maxMS<0&&(console.warn("PollingManager: Negative maxMS detected, using default value of 15 minutes"),n.maxMS=on.maxMS),n.initialMS<=0&&(console.warn("PollingManager: Negative or zero initialMS detected, using default value of 1 minute"),n.initialMS=on.initialMS),n.mult<=0&&(console.warn("PollingManager: Negative or zero multiplier detected, using default value of 2"),n.mult=on.mult),n.maxSteps!==void 0&&n.maxSteps<0&&(console.warn("PollingManager: Negative maxSteps detected, using default value of 4"),n.maxSteps=on.maxSteps),this.params=n,this.currentMS=this.params.maxMS}startPolling(){this.stopPolling(),this.currentMS=this.params.initialMS,this.step=0,this.safeExecute(),this.scheduleNext()}stopPolling(){this.timerId!==null&&(window.clearTimeout(this.timerId),this.timerId=null)}dispose(){this.stopPolling()}scheduleNext(){this.timerId=window.setTimeout(()=>{if(this.safeExecute(),this.params.maxMS===0){if(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps)return void this.stopPolling()}else this.currentMS<this.params.maxMS&&(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps?(this.currentMS=this.params.maxMS,this.step=0):this.currentMS=Math.min(this.currentMS*this.params.mult,this.params.maxMS));this.scheduleNext()},this.currentMS)}safeExecute(){try{const e=this.callback();e instanceof Promise&&e.catch(t=>console.error("Error in polling callback:",t))}catch(e){console.error("Error in polling callback:",e)}}}class wo{constructor(e){ve(this,"configs",Ve([]));ve(this,"pollingManager");ve(this,"_enableDebugFeatures",Ve(!1));ve(this,"_settingsComponentSupported",Ve({workspaceContext:!1,mcpServerList:!1,mcpServerImport:!1,orientation:!1,remoteTools:!1,userGuidelines:!1,terminal:!1,rules:!1}));ve(this,"_enableAgentMode",Ve(!1));ve(this,"_enableInitialOrientation",Ve(!1));ve(this,"_userTier",Ve("unknown"));ve(this,"_guidelines",Ve({}));this._host=e,this.pollingManager=new xo(()=>this.requestToolStatus(!1),{maxMS:0,initialMS:2e3,mult:1,maxSteps:150}),this.requestToolStatus(!1)}transformToolDisplay(e){const t=!e.isConfigured,n=e.oauthUrl;if(e.identifier.hostName===Ts.remoteToolHost){let r=e.identifier.toolId;switch(typeof r=="string"&&/^\d+$/.test(r)&&(r=Number(r)),r){case mt.GitHubApi:return{displayName:"GitHub",description:"Configure GitHub API access for repository operations",icon:Bi,requiresAuthentication:t,authUrl:n};case mt.Linear:return{displayName:"Linear",description:"Configure Linear API access for issue tracking",icon:Di,requiresAuthentication:t,authUrl:n};case mt.Jira:return{displayName:"Jira",description:"Configure Jira API access for issue tracking",icon:zi,requiresAuthentication:t,authUrl:n};case mt.Notion:return{displayName:"Notion",description:"Configure Notion API access",icon:Fi,requiresAuthentication:t,authUrl:n};case mt.Confluence:return{displayName:"Confluence",description:"Configure Confluence API access",icon:Li,requiresAuthentication:t,authUrl:n};case mt.WebSearch:return{displayName:"Web Search",description:"Configure web search capabilities",icon:Ri,requiresAuthentication:t,authUrl:n};case mt.Supabase:return{displayName:"Supabase",description:"Configure Supabase API access",icon:ji,requiresAuthentication:t,authUrl:n};case mt.Glean:return{displayName:"Glean",description:"Configure Glean API access",icon:Pi,requiresAuthentication:t,authUrl:n};case mt.Unknown:return{displayName:"Unknown",description:"Unknown tool",requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled RemoteToolId: ${r}`)}}else if(e.identifier.hostName===Ts.localToolHost){const r=e.identifier.toolId;switch(r){case Te.readFile:case Te.editFile:case Te.saveFile:case Te.launchProcess:case Te.killProcess:case Te.readProcess:case Te.writeProcess:case Te.listProcesses:case Te.waitProcess:case Te.openBrowser:case Te.clarify:case Te.onboardingSubAgent:case Te.strReplaceEditor:case Te.remember:case Te.diagnostics:case Te.setupScript:case Te.readTerminal:case Te.gitCommitRetrieval:case Te.spawnSubAgent:return{displayName:e.definition.name.toString(),description:"Local tool",icon:_t,requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled LocalToolType: ${r}`)}}else if(e.identifier.hostName===Ts.sidecarToolHost){const r=e.identifier.toolId;switch(r){case Ze.codebaseRetrieval:return{displayName:"Code Search",description:"Configure codebase search capabilities",icon:qt,requiresAuthentication:t,authUrl:n};case Ze.shell:return{displayName:"Shell",description:"Shell",icon:qt,requiresAuthentication:t,authUrl:n};case Ze.strReplaceEditor:return{displayName:"File Edit",description:"File Editor",icon:qt,requiresAuthentication:t,authUrl:n};case Ze.view:return{displayName:"File View",description:"File Viewer",icon:qt,requiresAuthentication:t,authUrl:n};case Ze.webFetch:return{displayName:"Web Fetch",description:"Retrieve information from the web",icon:qt,requiresAuthentication:t,authUrl:n};case Ze.removeFiles:return{displayName:"Remove Files",description:"Remove files from the codebase",icon:Vi,requiresAuthentication:t,authUrl:n};case Ze.remember:return{displayName:e.definition.name.toString(),description:"Remember",icon:_t,requiresAuthentication:t,authUrl:n};case Ze.saveFile:return{displayName:"Save File",description:"Save a new file",icon:Ji,requiresAuthentication:t,authUrl:n};case Ze.viewTaskList:return{displayName:"View Task List",description:"View the current task list",icon:_t,requiresAuthentication:t,authUrl:n};case Ze.reorganizeTaskList:return{displayName:"Reorganize Task List",description:"Reorganize the task list structure for major restructuring",icon:_t,requiresAuthentication:t,authUrl:n};case Ze.viewRangeUntruncated:return{displayName:e.definition.name.toString(),description:"View Range",icon:_t,requiresAuthentication:t,authUrl:n};case Ze.updateTasks:return{displayName:"Update Tasks",description:"Update one or more tasks in the task list",icon:_t,requiresAuthentication:t,authUrl:n};case Ze.addTasks:return{displayName:"Add Tasks",description:"Add one or more new tasks to the task list",icon:_t,requiresAuthentication:t,authUrl:n};case Ze.searchUntruncated:return{displayName:e.definition.name.toString(),description:"Search Untruncated",icon:_t,requiresAuthentication:t,authUrl:n};case Ze.renderMermaid:return{displayName:"View Mermaid Diagram",description:"View a mermaid diagram",icon:Ui,requiresAuthentication:t,authUrl:n};case Ze.grepSearch:return{displayName:"Grep search",description:"Run grep search",icon:qt,requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled SidecarToolType: ${r}`)}}return{displayName:e.definition.name.toString(),description:e.definition.description||"",requiresAuthentication:t,authUrl:n}}handleMessageFromExtension(e){const t=e.data;switch(t.type){case se.toolConfigInitialize:return this.createConfigsFromHostTools(t.data.hostTools,t.data.toolConfigs),t.data&&t.data.enableDebugFeatures!==void 0&&this._enableDebugFeatures.set(t.data.enableDebugFeatures),t.data&&t.data.settingsComponentSupported!==void 0&&this._settingsComponentSupported.set(t.data.settingsComponentSupported),t.data.enableAgentMode!==void 0&&this._enableAgentMode.set(t.data.enableAgentMode),t.data.enableInitialOrientation!==void 0&&this._enableInitialOrientation.set(t.data.enableInitialOrientation),t.data.userTier!==void 0&&this._userTier.set(t.data.userTier),t.data.guidelines!==void 0&&this._guidelines.set(t.data.guidelines),!0;case se.toolConfigDefinitionsResponse:return this.configs.update(n=>this.createConfigsFromHostTools(t.data.hostTools,[]).map(r=>{const a=n.find(i=>i.name===r.name);return a?{...a,displayName:r.displayName,description:r.description,icon:r.icon,requiresAuthentication:r.requiresAuthentication,authUrl:r.authUrl,isConfigured:r.isConfigured}:r})),!0}return!1}createConfigsFromHostTools(e,t){return e.map(n=>{const r=this.transformToolDisplay(n),a=t.find(o=>o.name===n.definition.name),i=(a==null?void 0:a.isConfigured)??!r.requiresAuthentication;return{config:(a==null?void 0:a.config)??{},configString:JSON.stringify((a==null?void 0:a.config)??{},null,2),isConfigured:i,name:n.definition.name.toString(),displayName:r.displayName,description:r.description,identifier:n.identifier,icon:r.icon,requiresAuthentication:r.requiresAuthentication,authUrl:r.authUrl,showStatus:!1,statusMessage:"",statusType:"info"}})}getConfigs(){return this.configs}isDisplayableTool(e){return["github","linear","notion","jira","confluence","supabase"].includes(e.displayName.toLowerCase())}getDisplayableTools(){return Si(this.configs,e=>{const t=e.filter(r=>this.isDisplayableTool(r)),n=new Map;for(const r of t)n.set(r.displayName,r);return Array.from(n.values()).sort((r,a)=>{const i={GitHub:1,Linear:2,Notion:3},o=Number.MAX_SAFE_INTEGER,c=i[r.displayName]||o,l=i[a.displayName]||o;return c<o&&l<o||c===o&&l===o?c!==l?c-l:r.displayName.localeCompare(a.displayName):c-l})})}saveConfig(e){this.startPolling()}notifyLoaded(){this._host.postMessage({type:se.toolConfigLoaded})}startPolling(){this.pollingManager.startPolling()}requestToolStatus(e=!0){this._host.postMessage({type:se.toolConfigGetDefinitions,data:{useCache:e}})}dispose(){this.pollingManager.dispose()}getEnableDebugFeatures(){return this._enableDebugFeatures}getEnableAgentMode(){return this._enableAgentMode}getEnableInitialOrientation(){return this._enableInitialOrientation}getUserTier(){return this._userTier}getGuidelines(){return this._guidelines}updateLocalUserGuidelines(e){this._guidelines.update(t=>t.userGuidelines?{...t,userGuidelines:{...t.userGuidelines,contents:e,enabled:e.length>0}}:t)}getSettingsComponentSupported(){return this._settingsComponentSupported}}var ue,Os;(function(s){s.assertEqual=e=>e,s.assertIs=function(e){},s.assertNever=function(e){throw new Error},s.arrayToEnum=e=>{const t={};for(const n of e)t[n]=n;return t},s.getValidEnumValues=e=>{const t=s.objectKeys(e).filter(r=>typeof e[e[r]]!="number"),n={};for(const r of t)n[r]=e[r];return s.objectValues(n)},s.objectValues=e=>s.objectKeys(e).map(function(t){return e[t]}),s.objectKeys=typeof Object.keys=="function"?e=>Object.keys(e):e=>{const t=[];for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},s.find=(e,t)=>{for(const n of e)if(t(n))return n},s.isInteger=typeof Number.isInteger=="function"?e=>Number.isInteger(e):e=>typeof e=="number"&&isFinite(e)&&Math.floor(e)===e,s.joinValues=function(e,t=" | "){return e.map(n=>typeof n=="string"?`'${n}'`:n).join(t)},s.jsonStringifyReplacer=(e,t)=>typeof t=="bigint"?t.toString():t})(ue||(ue={})),(Os||(Os={})).mergeShapes=(s,e)=>({...s,...e});const z=ue.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),ft=s=>{switch(typeof s){case"undefined":return z.undefined;case"string":return z.string;case"number":return isNaN(s)?z.nan:z.number;case"boolean":return z.boolean;case"function":return z.function;case"bigint":return z.bigint;case"symbol":return z.symbol;case"object":return Array.isArray(s)?z.array:s===null?z.null:s.then&&typeof s.then=="function"&&s.catch&&typeof s.catch=="function"?z.promise:typeof Map<"u"&&s instanceof Map?z.map:typeof Set<"u"&&s instanceof Set?z.set:typeof Date<"u"&&s instanceof Date?z.date:z.object;default:return z.unknown}},E=ue.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);let at=class Va extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(a){return a.message},n={_errors:[]},r=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(r);else if(i.code==="invalid_return_type")r(i.returnTypeError);else if(i.code==="invalid_arguments")r(i.argumentsError);else if(i.path.length===0)n._errors.push(t(i));else{let o=n,c=0;for(;c<i.path.length;){const l=i.path[c];c===i.path.length-1?(o[l]=o[l]||{_errors:[]},o[l]._errors.push(t(i))):o[l]=o[l]||{_errors:[]},o=o[l],c++}}};return r(this),n}static assert(e){if(!(e instanceof Va))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,ue.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},n=[];for(const r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}};at.create=s=>new at(s);const Kt=(s,e)=>{let t;switch(s.code){case E.invalid_type:t=s.received===z.undefined?"Required":`Expected ${s.expected}, received ${s.received}`;break;case E.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(s.expected,ue.jsonStringifyReplacer)}`;break;case E.unrecognized_keys:t=`Unrecognized key(s) in object: ${ue.joinValues(s.keys,", ")}`;break;case E.invalid_union:t="Invalid input";break;case E.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${ue.joinValues(s.options)}`;break;case E.invalid_enum_value:t=`Invalid enum value. Expected ${ue.joinValues(s.options)}, received '${s.received}'`;break;case E.invalid_arguments:t="Invalid function arguments";break;case E.invalid_return_type:t="Invalid function return type";break;case E.invalid_date:t="Invalid date";break;case E.invalid_string:typeof s.validation=="object"?"includes"in s.validation?(t=`Invalid input: must include "${s.validation.includes}"`,typeof s.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${s.validation.position}`)):"startsWith"in s.validation?t=`Invalid input: must start with "${s.validation.startsWith}"`:"endsWith"in s.validation?t=`Invalid input: must end with "${s.validation.endsWith}"`:ue.assertNever(s.validation):t=s.validation!=="regex"?`Invalid ${s.validation}`:"Invalid";break;case E.too_small:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at least":"more than"} ${s.minimum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at least":"over"} ${s.minimum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${s.minimum}`:s.type==="date"?`Date must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(s.minimum))}`:"Invalid input";break;case E.too_big:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at most":"less than"} ${s.maximum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at most":"under"} ${s.maximum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="bigint"?`BigInt must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="date"?`Date must be ${s.exact?"exactly":s.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(s.maximum))}`:"Invalid input";break;case E.custom:t="Invalid input";break;case E.invalid_intersection_types:t="Intersection results could not be merged";break;case E.not_multiple_of:t=`Number must be a multiple of ${s.multipleOf}`;break;case E.not_finite:t="Number must be finite";break;default:t=e.defaultError,ue.assertNever(s)}return{message:t}};let qa=Kt;function ss(){return qa}const rs=s=>{const{data:e,path:t,errorMaps:n,issueData:r}=s,a=[...t,...r.path||[]],i={...r,path:a};if(r.message!==void 0)return{...r,path:a,message:r.message};let o="";const c=n.filter(l=>!!l).slice().reverse();for(const l of c)o=l(i,{data:e,defaultError:o}).message;return{...r,path:a,message:o}};function j(s,e){const t=ss(),n=rs({issueData:e,data:s.data,path:s.path,errorMaps:[s.common.contextualErrorMap,s.schemaErrorMap,t,t===Kt?void 0:Kt].filter(r=>!!r)});s.common.issues.push(n)}let Be=class Ba{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const r of t){if(r.status==="aborted")return ee;r.status==="dirty"&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const n=[];for(const r of t){const a=await r.key,i=await r.value;n.push({key:a,value:i})}return Ba.mergeObjectSync(e,n)}static mergeObjectSync(e,t){const n={};for(const r of t){const{key:a,value:i}=r;if(a.status==="aborted"||i.status==="aborted")return ee;a.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),a.value==="__proto__"||i.value===void 0&&!r.alwaysSet||(n[a.value]=i.value)}return{status:e.value,value:n}}};const ee=Object.freeze({status:"aborted"}),as=s=>({status:"dirty",value:s}),Re=s=>({status:"valid",value:s}),Is=s=>s.status==="aborted",Ps=s=>s.status==="dirty",Rt=s=>s.status==="valid",fn=s=>typeof Promise<"u"&&s instanceof Promise;function is(s,e,t,n){if(typeof e=="function"?s!==e||!n:!e.has(s))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(s)}function Ja(s,e,t,n,r){if(typeof e=="function"?s!==e||!r:!e.has(s))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(s,t),t}var q,cn,ln;typeof SuppressedError=="function"&&SuppressedError,function(s){s.errToObj=e=>typeof e=="string"?{message:e}:e||{},s.toString=e=>typeof e=="string"?e:e==null?void 0:e.message}(q||(q={}));let dt=class{constructor(s,e,t,n){this._cachedPath=[],this.parent=s,this.data=e,this._path=t,this._key=n}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}};const or=(s,e)=>{if(Rt(e))return{success:!0,data:e.value};if(!s.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new at(s.common.issues);return this._error=t,this._error}}};function re(s){if(!s)return{};const{errorMap:e,invalid_type_error:t,required_error:n,description:r}=s;if(e&&(t||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:r}:{errorMap:(a,i)=>{var o,c;const{message:l}=s;return a.code==="invalid_enum_value"?{message:l??i.defaultError}:i.data===void 0?{message:(o=l??n)!==null&&o!==void 0?o:i.defaultError}:a.code!=="invalid_type"?{message:i.defaultError}:{message:(c=l??t)!==null&&c!==void 0?c:i.defaultError}},description:r}}let ce=class{get description(){return this._def.description}_getType(s){return ft(s.data)}_getOrReturnCtx(s,e){return e||{common:s.parent.common,data:s.data,parsedType:ft(s.data),schemaErrorMap:this._def.errorMap,path:s.path,parent:s.parent}}_processInputParams(s){return{status:new Be,ctx:{common:s.parent.common,data:s.data,parsedType:ft(s.data),schemaErrorMap:this._def.errorMap,path:s.path,parent:s.parent}}}_parseSync(s){const e=this._parse(s);if(fn(e))throw new Error("Synchronous parse encountered promise.");return e}_parseAsync(s){const e=this._parse(s);return Promise.resolve(e)}parse(s,e){const t=this.safeParse(s,e);if(t.success)return t.data;throw t.error}safeParse(s,e){var t;const n={common:{issues:[],async:(t=e==null?void 0:e.async)!==null&&t!==void 0&&t,contextualErrorMap:e==null?void 0:e.errorMap},path:(e==null?void 0:e.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:s,parsedType:ft(s)},r=this._parseSync({data:s,path:n.path,parent:n});return or(n,r)}"~validate"(s){var e,t;const n={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:s,parsedType:ft(s)};if(!this["~standard"].async)try{const r=this._parseSync({data:s,path:[],parent:n});return Rt(r)?{value:r.value}:{issues:n.common.issues}}catch(r){!((t=(e=r==null?void 0:r.message)===null||e===void 0?void 0:e.toLowerCase())===null||t===void 0)&&t.includes("encountered")&&(this["~standard"].async=!0),n.common={issues:[],async:!0}}return this._parseAsync({data:s,path:[],parent:n}).then(r=>Rt(r)?{value:r.value}:{issues:n.common.issues})}async parseAsync(s,e){const t=await this.safeParseAsync(s,e);if(t.success)return t.data;throw t.error}async safeParseAsync(s,e){const t={common:{issues:[],contextualErrorMap:e==null?void 0:e.errorMap,async:!0},path:(e==null?void 0:e.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:s,parsedType:ft(s)},n=this._parse({data:s,path:t.path,parent:t}),r=await(fn(n)?n:Promise.resolve(n));return or(t,r)}refine(s,e){const t=n=>typeof e=="string"||e===void 0?{message:e}:typeof e=="function"?e(n):e;return this._refinement((n,r)=>{const a=s(n),i=()=>r.addIssue({code:E.custom,...t(n)});return typeof Promise<"u"&&a instanceof Promise?a.then(o=>!!o||(i(),!1)):!!a||(i(),!1)})}refinement(s,e){return this._refinement((t,n)=>!!s(t)||(n.addIssue(typeof e=="function"?e(t,n):e),!1))}_refinement(s){return new nt({schema:this,typeName:X.ZodEffects,effect:{type:"refinement",refinement:s}})}superRefine(s){return this._refinement(s)}constructor(s){this.spa=this.safeParseAsync,this._def=s,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return ot.create(this,this._def)}nullable(){return Tt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return Lt.create(this)}promise(){return Qt.create(this,this._def)}or(s){return xn.create([this,s],this._def)}and(s){return wn.create(this,s,this._def)}transform(s){return new nt({...re(this._def),schema:this,typeName:X.ZodEffects,effect:{type:"transform",transform:s}})}default(s){const e=typeof s=="function"?s:()=>s;return new Cn({...re(this._def),innerType:this,defaultValue:e,typeName:X.ZodDefault})}brand(){return new Gs({typeName:X.ZodBranded,type:this,...re(this._def)})}catch(s){const e=typeof s=="function"?s:()=>s;return new Tn({...re(this._def),innerType:this,catchValue:e,typeName:X.ZodCatch})}describe(s){return new this.constructor({...this._def,description:s})}pipe(s){return Hs.create(this,s)}readonly(){return Mn.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}};const bo=/^c[^\s-]{8,}$/i,ko=/^[0-9a-z]+$/,So=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Co=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,To=/^[a-z0-9_-]{21}$/i,Mo=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Ao=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,No=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let As;const Zo=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Eo=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Oo=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Io=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Po=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,jo=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Ga="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Ro=new RegExp(`^${Ga}$`);function Ha(s){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return s.precision?e=`${e}\\.\\d{${s.precision}}`:s.precision==null&&(e=`${e}(\\.\\d+)?`),e}function Wa(s){let e=`${Ga}T${Ha(s)}`;const t=[];return t.push(s.local?"Z?":"Z"),s.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function Lo(s,e){if(!Mo.test(s))return!1;try{const[t]=s.split("."),n=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),r=JSON.parse(atob(n));return typeof r=="object"&&r!==null&&!(!r.typ||!r.alg)&&(!e||r.alg===e)}catch{return!1}}function Fo(s,e){return!(e!=="v4"&&e||!Eo.test(s))||!(e!=="v6"&&e||!Io.test(s))}let Yt=class dn extends ce{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==z.string){const i=this._getOrReturnCtx(e);return j(i,{code:E.invalid_type,expected:z.string,received:i.parsedType}),ee}const t=new Be;let n;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(n=this._getOrReturnCtx(e,n),j(n,{code:E.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="max")e.data.length>i.value&&(n=this._getOrReturnCtx(e,n),j(n,{code:E.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="length"){const o=e.data.length>i.value,c=e.data.length<i.value;(o||c)&&(n=this._getOrReturnCtx(e,n),o?j(n,{code:E.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):c&&j(n,{code:E.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if(i.kind==="email")No.test(e.data)||(n=this._getOrReturnCtx(e,n),j(n,{validation:"email",code:E.invalid_string,message:i.message}),t.dirty());else if(i.kind==="emoji")As||(As=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),As.test(e.data)||(n=this._getOrReturnCtx(e,n),j(n,{validation:"emoji",code:E.invalid_string,message:i.message}),t.dirty());else if(i.kind==="uuid")Co.test(e.data)||(n=this._getOrReturnCtx(e,n),j(n,{validation:"uuid",code:E.invalid_string,message:i.message}),t.dirty());else if(i.kind==="nanoid")To.test(e.data)||(n=this._getOrReturnCtx(e,n),j(n,{validation:"nanoid",code:E.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid")bo.test(e.data)||(n=this._getOrReturnCtx(e,n),j(n,{validation:"cuid",code:E.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid2")ko.test(e.data)||(n=this._getOrReturnCtx(e,n),j(n,{validation:"cuid2",code:E.invalid_string,message:i.message}),t.dirty());else if(i.kind==="ulid")So.test(e.data)||(n=this._getOrReturnCtx(e,n),j(n,{validation:"ulid",code:E.invalid_string,message:i.message}),t.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),j(n,{validation:"url",code:E.invalid_string,message:i.message}),t.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),j(n,{validation:"regex",code:E.invalid_string,message:i.message}),t.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(n=this._getOrReturnCtx(e,n),j(n,{code:E.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(n=this._getOrReturnCtx(e,n),j(n,{code:E.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(n=this._getOrReturnCtx(e,n),j(n,{code:E.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty()):i.kind==="datetime"?Wa(i).test(e.data)||(n=this._getOrReturnCtx(e,n),j(n,{code:E.invalid_string,validation:"datetime",message:i.message}),t.dirty()):i.kind==="date"?Ro.test(e.data)||(n=this._getOrReturnCtx(e,n),j(n,{code:E.invalid_string,validation:"date",message:i.message}),t.dirty()):i.kind==="time"?new RegExp(`^${Ha(i)}$`).test(e.data)||(n=this._getOrReturnCtx(e,n),j(n,{code:E.invalid_string,validation:"time",message:i.message}),t.dirty()):i.kind==="duration"?Ao.test(e.data)||(n=this._getOrReturnCtx(e,n),j(n,{validation:"duration",code:E.invalid_string,message:i.message}),t.dirty()):i.kind==="ip"?(r=e.data,((a=i.version)!=="v4"&&a||!Zo.test(r))&&(a!=="v6"&&a||!Oo.test(r))&&(n=this._getOrReturnCtx(e,n),j(n,{validation:"ip",code:E.invalid_string,message:i.message}),t.dirty())):i.kind==="jwt"?Lo(e.data,i.alg)||(n=this._getOrReturnCtx(e,n),j(n,{validation:"jwt",code:E.invalid_string,message:i.message}),t.dirty()):i.kind==="cidr"?Fo(e.data,i.version)||(n=this._getOrReturnCtx(e,n),j(n,{validation:"cidr",code:E.invalid_string,message:i.message}),t.dirty()):i.kind==="base64"?Po.test(e.data)||(n=this._getOrReturnCtx(e,n),j(n,{validation:"base64",code:E.invalid_string,message:i.message}),t.dirty()):i.kind==="base64url"?jo.test(e.data)||(n=this._getOrReturnCtx(e,n),j(n,{validation:"base64url",code:E.invalid_string,message:i.message}),t.dirty()):ue.assertNever(i);var r,a;return{status:t.value,value:e.data}}_regex(e,t,n){return this.refinement(r=>e.test(r),{validation:t,code:E.invalid_string,...q.errToObj(n)})}_addCheck(e){return new dn({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...q.errToObj(e)})}url(e){return this._addCheck({kind:"url",...q.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...q.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...q.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...q.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...q.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...q.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...q.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...q.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...q.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...q.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...q.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...q.errToObj(e)})}datetime(e){var t,n;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!==null&&t!==void 0&&t,local:(n=e==null?void 0:e.local)!==null&&n!==void 0&&n,...q.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,...q.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...q.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...q.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...q.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...q.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...q.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...q.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...q.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...q.errToObj(t)})}nonempty(e){return this.min(1,q.errToObj(e))}trim(){return new dn({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new dn({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new dn({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}};function zo(s,e){const t=(s.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,r=t>n?t:n;return parseInt(s.toFixed(r).replace(".",""))%parseInt(e.toFixed(r).replace(".",""))/Math.pow(10,r)}Yt.create=s=>{var e;return new Yt({checks:[],typeName:X.ZodString,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...re(s)})};let hn=class js extends ce{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==z.number){const r=this._getOrReturnCtx(e);return j(r,{code:E.invalid_type,expected:z.number,received:r.parsedType}),ee}let t;const n=new Be;for(const r of this._def.checks)r.kind==="int"?ue.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),j(t,{code:E.invalid_type,expected:"integer",received:"float",message:r.message}),n.dirty()):r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),j(t,{code:E.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),j(t,{code:E.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="multipleOf"?zo(e.data,r.value)!==0&&(t=this._getOrReturnCtx(e,t),j(t,{code:E.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):r.kind==="finite"?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),j(t,{code:E.not_finite,message:r.message}),n.dirty()):ue.assertNever(r);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,q.toString(t))}gt(e,t){return this.setLimit("min",e,!1,q.toString(t))}lte(e,t){return this.setLimit("max",e,!0,q.toString(t))}lt(e,t){return this.setLimit("max",e,!1,q.toString(t))}setLimit(e,t,n,r){return new js({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:q.toString(r)}]})}_addCheck(e){return new js({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:q.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:q.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:q.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:q.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:q.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:q.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:q.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:q.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:q.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&ue.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(t===null||n.value>t)&&(t=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}};hn.create=s=>new hn({checks:[],typeName:X.ZodNumber,coerce:(s==null?void 0:s.coerce)||!1,...re(s)});let gn=class Rs extends ce{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==z.bigint)return this._getInvalidInput(e);let t;const n=new Be;for(const r of this._def.checks)r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),j(t,{code:E.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),j(t,{code:E.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="multipleOf"?e.data%r.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),j(t,{code:E.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):ue.assertNever(r);return{status:n.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return j(t,{code:E.invalid_type,expected:z.bigint,received:t.parsedType}),ee}gte(e,t){return this.setLimit("min",e,!0,q.toString(t))}gt(e,t){return this.setLimit("min",e,!1,q.toString(t))}lte(e,t){return this.setLimit("max",e,!0,q.toString(t))}lt(e,t){return this.setLimit("max",e,!1,q.toString(t))}setLimit(e,t,n,r){return new Rs({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:q.toString(r)}]})}_addCheck(e){return new Rs({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:q.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:q.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:q.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:q.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:q.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}};gn.create=s=>{var e;return new gn({checks:[],typeName:X.ZodBigInt,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...re(s)})};let vn=class extends ce{_parse(s){if(this._def.coerce&&(s.data=!!s.data),this._getType(s)!==z.boolean){const e=this._getOrReturnCtx(s);return j(e,{code:E.invalid_type,expected:z.boolean,received:e.parsedType}),ee}return Re(s.data)}};vn.create=s=>new vn({typeName:X.ZodBoolean,coerce:(s==null?void 0:s.coerce)||!1,...re(s)});let $n=class Ka extends ce{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==z.date){const r=this._getOrReturnCtx(e);return j(r,{code:E.invalid_type,expected:z.date,received:r.parsedType}),ee}if(isNaN(e.data.getTime()))return j(this._getOrReturnCtx(e),{code:E.invalid_date}),ee;const t=new Be;let n;for(const r of this._def.checks)r.kind==="min"?e.data.getTime()<r.value&&(n=this._getOrReturnCtx(e,n),j(n,{code:E.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),t.dirty()):r.kind==="max"?e.data.getTime()>r.value&&(n=this._getOrReturnCtx(e,n),j(n,{code:E.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),t.dirty()):ue.assertNever(r);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new Ka({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:q.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:q.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}};$n.create=s=>new $n({checks:[],coerce:(s==null?void 0:s.coerce)||!1,typeName:X.ZodDate,...re(s)});let os=class extends ce{_parse(s){if(this._getType(s)!==z.symbol){const e=this._getOrReturnCtx(s);return j(e,{code:E.invalid_type,expected:z.symbol,received:e.parsedType}),ee}return Re(s.data)}};os.create=s=>new os({typeName:X.ZodSymbol,...re(s)});let yn=class extends ce{_parse(s){if(this._getType(s)!==z.undefined){const e=this._getOrReturnCtx(s);return j(e,{code:E.invalid_type,expected:z.undefined,received:e.parsedType}),ee}return Re(s.data)}};yn.create=s=>new yn({typeName:X.ZodUndefined,...re(s)});let _n=class extends ce{_parse(s){if(this._getType(s)!==z.null){const e=this._getOrReturnCtx(s);return j(e,{code:E.invalid_type,expected:z.null,received:e.parsedType}),ee}return Re(s.data)}};_n.create=s=>new _n({typeName:X.ZodNull,...re(s)});let Xt=class extends ce{constructor(){super(...arguments),this._any=!0}_parse(s){return Re(s.data)}};Xt.create=s=>new Xt({typeName:X.ZodAny,...re(s)});let It=class extends ce{constructor(){super(...arguments),this._unknown=!0}_parse(s){return Re(s.data)}};It.create=s=>new It({typeName:X.ZodUnknown,...re(s)});let vt=class extends ce{_parse(s){const e=this._getOrReturnCtx(s);return j(e,{code:E.invalid_type,expected:z.never,received:e.parsedType}),ee}};vt.create=s=>new vt({typeName:X.ZodNever,...re(s)});let cs=class extends ce{_parse(s){if(this._getType(s)!==z.undefined){const e=this._getOrReturnCtx(s);return j(e,{code:E.invalid_type,expected:z.void,received:e.parsedType}),ee}return Re(s.data)}};cs.create=s=>new cs({typeName:X.ZodVoid,...re(s)});let Lt=class Qn extends ce{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==z.array)return j(t,{code:E.invalid_type,expected:z.array,received:t.parsedType}),ee;if(r.exactLength!==null){const i=t.data.length>r.exactLength.value,o=t.data.length<r.exactLength.value;(i||o)&&(j(t,{code:i?E.too_big:E.too_small,minimum:o?r.exactLength.value:void 0,maximum:i?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(r.minLength!==null&&t.data.length<r.minLength.value&&(j(t,{code:E.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),r.maxLength!==null&&t.data.length>r.maxLength.value&&(j(t,{code:E.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>r.type._parseAsync(new dt(t,i,t.path,o)))).then(i=>Be.mergeArray(n,i));const a=[...t.data].map((i,o)=>r.type._parseSync(new dt(t,i,t.path,o)));return Be.mergeArray(n,a)}get element(){return this._def.type}min(e,t){return new Qn({...this._def,minLength:{value:e,message:q.toString(t)}})}max(e,t){return new Qn({...this._def,maxLength:{value:e,message:q.toString(t)}})}length(e,t){return new Qn({...this._def,exactLength:{value:e,message:q.toString(t)}})}nonempty(e){return this.min(1,e)}};function Bt(s){if(s instanceof We){const e={};for(const t in s.shape){const n=s.shape[t];e[t]=ot.create(Bt(n))}return new We({...s._def,shape:()=>e})}return s instanceof Lt?new Lt({...s._def,type:Bt(s.element)}):s instanceof ot?ot.create(Bt(s.unwrap())):s instanceof Tt?Tt.create(Bt(s.unwrap())):s instanceof Ct?Ct.create(s.items.map(e=>Bt(e))):s}Lt.create=(s,e)=>new Lt({type:s,minLength:null,maxLength:null,exactLength:null,typeName:X.ZodArray,...re(e)});let We=class et extends ce{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=ue.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==z.object){const c=this._getOrReturnCtx(e);return j(c,{code:E.invalid_type,expected:z.object,received:c.parsedType}),ee}const{status:t,ctx:n}=this._processInputParams(e),{shape:r,keys:a}=this._getCached(),i=[];if(!(this._def.catchall instanceof vt&&this._def.unknownKeys==="strip"))for(const c in n.data)a.includes(c)||i.push(c);const o=[];for(const c of a){const l=r[c],d=n.data[c];o.push({key:{status:"valid",value:c},value:l._parse(new dt(n,d,n.path,c)),alwaysSet:c in n.data})}if(this._def.catchall instanceof vt){const c=this._def.unknownKeys;if(c==="passthrough")for(const l of i)o.push({key:{status:"valid",value:l},value:{status:"valid",value:n.data[l]}});else if(c==="strict")i.length>0&&(j(n,{code:E.unrecognized_keys,keys:i}),t.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const l of i){const d=n.data[l];o.push({key:{status:"valid",value:l},value:c._parse(new dt(n,d,n.path,l)),alwaysSet:l in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const c=[];for(const l of o){const d=await l.key,u=await l.value;c.push({key:d,value:u,alwaysSet:l.alwaysSet})}return c}).then(c=>Be.mergeObjectSync(t,c)):Be.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return q.errToObj,new et({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,n)=>{var r,a,i,o;const c=(i=(a=(r=this._def).errorMap)===null||a===void 0?void 0:a.call(r,t,n).message)!==null&&i!==void 0?i:n.defaultError;return t.code==="unrecognized_keys"?{message:(o=q.errToObj(e).message)!==null&&o!==void 0?o:c}:{message:c}}}:{}})}strip(){return new et({...this._def,unknownKeys:"strip"})}passthrough(){return new et({...this._def,unknownKeys:"passthrough"})}extend(e){return new et({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new et({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:X.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new et({...this._def,catchall:e})}pick(e){const t={};return ue.objectKeys(e).forEach(n=>{e[n]&&this.shape[n]&&(t[n]=this.shape[n])}),new et({...this._def,shape:()=>t})}omit(e){const t={};return ue.objectKeys(this.shape).forEach(n=>{e[n]||(t[n]=this.shape[n])}),new et({...this._def,shape:()=>t})}deepPartial(){return Bt(this)}partial(e){const t={};return ue.objectKeys(this.shape).forEach(n=>{const r=this.shape[n];e&&!e[n]?t[n]=r:t[n]=r.optional()}),new et({...this._def,shape:()=>t})}required(e){const t={};return ue.objectKeys(this.shape).forEach(n=>{if(e&&!e[n])t[n]=this.shape[n];else{let r=this.shape[n];for(;r instanceof ot;)r=r._def.innerType;t[n]=r}}),new et({...this._def,shape:()=>t})}keyof(){return si(ue.objectKeys(this.shape))}};We.create=(s,e)=>new We({shape:()=>s,unknownKeys:"strip",catchall:vt.create(),typeName:X.ZodObject,...re(e)}),We.strictCreate=(s,e)=>new We({shape:()=>s,unknownKeys:"strict",catchall:vt.create(),typeName:X.ZodObject,...re(e)}),We.lazycreate=(s,e)=>new We({shape:s,unknownKeys:"strip",catchall:vt.create(),typeName:X.ZodObject,...re(e)});let xn=class extends ce{_parse(s){const{ctx:e}=this._processInputParams(s),t=this._def.options;if(e.common.async)return Promise.all(t.map(async n=>{const r={...e,common:{...e.common,issues:[]},parent:null};return{result:await n._parseAsync({data:e.data,path:e.path,parent:r}),ctx:r}})).then(function(n){for(const a of n)if(a.result.status==="valid")return a.result;for(const a of n)if(a.result.status==="dirty")return e.common.issues.push(...a.ctx.common.issues),a.result;const r=n.map(a=>new at(a.ctx.common.issues));return j(e,{code:E.invalid_union,unionErrors:r}),ee});{let n;const r=[];for(const i of t){const o={...e,common:{...e.common,issues:[]},parent:null},c=i._parseSync({data:e.data,path:e.path,parent:o});if(c.status==="valid")return c;c.status!=="dirty"||n||(n={result:c,ctx:o}),o.common.issues.length&&r.push(o.common.issues)}if(n)return e.common.issues.push(...n.ctx.common.issues),n.result;const a=r.map(i=>new at(i));return j(e,{code:E.invalid_union,unionErrors:a}),ee}}get options(){return this._def.options}};xn.create=(s,e)=>new xn({options:s,typeName:X.ZodUnion,...re(e)});const xt=s=>s instanceof bn?xt(s.schema):s instanceof nt?xt(s.innerType()):s instanceof kn?[s.value]:s instanceof Gn?s.options:s instanceof Sn?ue.objectValues(s.enum):s instanceof Cn?xt(s._def.innerType):s instanceof yn?[void 0]:s instanceof _n?[null]:s instanceof ot?[void 0,...xt(s.unwrap())]:s instanceof Tt?[null,...xt(s.unwrap())]:s instanceof Gs||s instanceof Mn?xt(s.unwrap()):s instanceof Tn?xt(s._def.innerType):[];let Ya=class Xa extends ce{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==z.object)return j(t,{code:E.invalid_type,expected:z.object,received:t.parsedType}),ee;const n=this.discriminator,r=t.data[n],a=this.optionsMap.get(r);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):(j(t,{code:E.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),ee)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){const r=new Map;for(const a of t){const i=xt(a.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const o of i){if(r.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);r.set(o,a)}}return new Xa({typeName:X.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...re(n)})}};function Ls(s,e){const t=ft(s),n=ft(e);if(s===e)return{valid:!0,data:s};if(t===z.object&&n===z.object){const r=ue.objectKeys(e),a=ue.objectKeys(s).filter(o=>r.indexOf(o)!==-1),i={...s,...e};for(const o of a){const c=Ls(s[o],e[o]);if(!c.valid)return{valid:!1};i[o]=c.data}return{valid:!0,data:i}}if(t===z.array&&n===z.array){if(s.length!==e.length)return{valid:!1};const r=[];for(let a=0;a<s.length;a++){const i=Ls(s[a],e[a]);if(!i.valid)return{valid:!1};r.push(i.data)}return{valid:!0,data:r}}return t===z.date&&n===z.date&&+s==+e?{valid:!0,data:s}:{valid:!1}}let wn=class extends ce{_parse(s){const{status:e,ctx:t}=this._processInputParams(s),n=(r,a)=>{if(Is(r)||Is(a))return ee;const i=Ls(r.value,a.value);return i.valid?((Ps(r)||Ps(a))&&e.dirty(),{status:e.value,value:i.data}):(j(t,{code:E.invalid_intersection_types}),ee)};return t.common.async?Promise.all([this._def.left._parseAsync({data:t.data,path:t.path,parent:t}),this._def.right._parseAsync({data:t.data,path:t.path,parent:t})]).then(([r,a])=>n(r,a)):n(this._def.left._parseSync({data:t.data,path:t.path,parent:t}),this._def.right._parseSync({data:t.data,path:t.path,parent:t}))}};wn.create=(s,e,t)=>new wn({left:s,right:e,typeName:X.ZodIntersection,...re(t)});let Ct=class Qa extends ce{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==z.array)return j(n,{code:E.invalid_type,expected:z.array,received:n.parsedType}),ee;if(n.data.length<this._def.items.length)return j(n,{code:E.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),ee;!this._def.rest&&n.data.length>this._def.items.length&&(j(n,{code:E.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const r=[...n.data].map((a,i)=>{const o=this._def.items[i]||this._def.rest;return o?o._parse(new dt(n,a,n.path,i)):null}).filter(a=>!!a);return n.common.async?Promise.all(r).then(a=>Be.mergeArray(t,a)):Be.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new Qa({...this._def,rest:e})}};Ct.create=(s,e)=>{if(!Array.isArray(s))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Ct({items:s,typeName:X.ZodTuple,rest:null,...re(e)})};let ei=class ti extends ce{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==z.object)return j(n,{code:E.invalid_type,expected:z.object,received:n.parsedType}),ee;const r=[],a=this._def.keyType,i=this._def.valueType;for(const o in n.data)r.push({key:a._parse(new dt(n,o,n.path,o)),value:i._parse(new dt(n,n.data[o],n.path,o)),alwaysSet:o in n.data});return n.common.async?Be.mergeObjectAsync(t,r):Be.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,n){return new ti(t instanceof ce?{keyType:e,valueType:t,typeName:X.ZodRecord,...re(n)}:{keyType:Yt.create(),valueType:e,typeName:X.ZodRecord,...re(t)})}},ls=class extends ce{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(s){const{status:e,ctx:t}=this._processInputParams(s);if(t.parsedType!==z.map)return j(t,{code:E.invalid_type,expected:z.map,received:t.parsedType}),ee;const n=this._def.keyType,r=this._def.valueType,a=[...t.data.entries()].map(([i,o],c)=>({key:n._parse(new dt(t,i,t.path,[c,"key"])),value:r._parse(new dt(t,o,t.path,[c,"value"]))}));if(t.common.async){const i=new Map;return Promise.resolve().then(async()=>{for(const o of a){const c=await o.key,l=await o.value;if(c.status==="aborted"||l.status==="aborted")return ee;c.status!=="dirty"&&l.status!=="dirty"||e.dirty(),i.set(c.value,l.value)}return{status:e.value,value:i}})}{const i=new Map;for(const o of a){const c=o.key,l=o.value;if(c.status==="aborted"||l.status==="aborted")return ee;c.status!=="dirty"&&l.status!=="dirty"||e.dirty(),i.set(c.value,l.value)}return{status:e.value,value:i}}}};ls.create=(s,e,t)=>new ls({valueType:e,keyType:s,typeName:X.ZodMap,...re(t)});let ds=class Fs extends ce{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==z.set)return j(n,{code:E.invalid_type,expected:z.set,received:n.parsedType}),ee;const r=this._def;r.minSize!==null&&n.data.size<r.minSize.value&&(j(n,{code:E.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),r.maxSize!==null&&n.data.size>r.maxSize.value&&(j(n,{code:E.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const a=this._def.valueType;function i(c){const l=new Set;for(const d of c){if(d.status==="aborted")return ee;d.status==="dirty"&&t.dirty(),l.add(d.value)}return{status:t.value,value:l}}const o=[...n.data.values()].map((c,l)=>a._parse(new dt(n,c,n.path,l)));return n.common.async?Promise.all(o).then(c=>i(c)):i(o)}min(e,t){return new Fs({...this._def,minSize:{value:e,message:q.toString(t)}})}max(e,t){return new Fs({...this._def,maxSize:{value:e,message:q.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}};ds.create=(s,e)=>new ds({valueType:s,minSize:null,maxSize:null,typeName:X.ZodSet,...re(e)});let ni=class es extends ce{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==z.function)return j(t,{code:E.invalid_type,expected:z.function,received:t.parsedType}),ee;function n(o,c){return rs({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,ss(),Kt].filter(l=>!!l),issueData:{code:E.invalid_arguments,argumentsError:c}})}function r(o,c){return rs({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,ss(),Kt].filter(l=>!!l),issueData:{code:E.invalid_return_type,returnTypeError:c}})}const a={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof Qt){const o=this;return Re(async function(...c){const l=new at([]),d=await o._def.args.parseAsync(c,a).catch(h=>{throw l.addIssue(n(c,h)),l}),u=await Reflect.apply(i,this,d);return await o._def.returns._def.type.parseAsync(u,a).catch(h=>{throw l.addIssue(r(u,h)),l})})}{const o=this;return Re(function(...c){const l=o._def.args.safeParse(c,a);if(!l.success)throw new at([n(c,l.error)]);const d=Reflect.apply(i,this,l.data),u=o._def.returns.safeParse(d,a);if(!u.success)throw new at([r(d,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new es({...this._def,args:Ct.create(e).rest(It.create())})}returns(e){return new es({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new es({args:e||Ct.create([]).rest(It.create()),returns:t||It.create(),typeName:X.ZodFunction,...re(n)})}},bn=class extends ce{get schema(){return this._def.getter()}_parse(s){const{ctx:e}=this._processInputParams(s);return this._def.getter()._parse({data:e.data,path:e.path,parent:e})}};bn.create=(s,e)=>new bn({getter:s,typeName:X.ZodLazy,...re(e)});let kn=class extends ce{_parse(s){if(s.data!==this._def.value){const e=this._getOrReturnCtx(s);return j(e,{received:e.data,code:E.invalid_literal,expected:this._def.value}),ee}return{status:"valid",value:s.data}}get value(){return this._def.value}};function si(s,e){return new Gn({values:s,typeName:X.ZodEnum,...re(e)})}kn.create=(s,e)=>new kn({value:s,typeName:X.ZodLiteral,...re(e)});let Gn=class zs extends ce{constructor(){super(...arguments),cn.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),n=this._def.values;return j(t,{expected:ue.joinValues(n),received:t.parsedType,code:E.invalid_type}),ee}if(is(this,cn)||Ja(this,cn,new Set(this._def.values)),!is(this,cn).has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return j(t,{received:t.data,code:E.invalid_enum_value,options:n}),ee}return Re(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return zs.create(e,{...this._def,...t})}exclude(e,t=this._def){return zs.create(this.options.filter(n=>!e.includes(n)),{...this._def,...t})}};cn=new WeakMap,Gn.create=si;let Sn=class extends ce{constructor(){super(...arguments),ln.set(this,void 0)}_parse(s){const e=ue.getValidEnumValues(this._def.values),t=this._getOrReturnCtx(s);if(t.parsedType!==z.string&&t.parsedType!==z.number){const n=ue.objectValues(e);return j(t,{expected:ue.joinValues(n),received:t.parsedType,code:E.invalid_type}),ee}if(is(this,ln)||Ja(this,ln,new Set(ue.getValidEnumValues(this._def.values))),!is(this,ln).has(s.data)){const n=ue.objectValues(e);return j(t,{received:t.data,code:E.invalid_enum_value,options:n}),ee}return Re(s.data)}get enum(){return this._def.values}};ln=new WeakMap,Sn.create=(s,e)=>new Sn({values:s,typeName:X.ZodNativeEnum,...re(e)});let Qt=class extends ce{unwrap(){return this._def.type}_parse(s){const{ctx:e}=this._processInputParams(s);if(e.parsedType!==z.promise&&e.common.async===!1)return j(e,{code:E.invalid_type,expected:z.promise,received:e.parsedType}),ee;const t=e.parsedType===z.promise?e.data:Promise.resolve(e.data);return Re(t.then(n=>this._def.type.parseAsync(n,{path:e.path,errorMap:e.common.contextualErrorMap})))}};Qt.create=(s,e)=>new Qt({type:s,typeName:X.ZodPromise,...re(e)});let nt=class extends ce{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===X.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(s){const{status:e,ctx:t}=this._processInputParams(s),n=this._def.effect||null,r={addIssue:a=>{j(t,a),a.fatal?e.abort():e.dirty()},get path(){return t.path}};if(r.addIssue=r.addIssue.bind(r),n.type==="preprocess"){const a=n.transform(t.data,r);if(t.common.async)return Promise.resolve(a).then(async i=>{if(e.value==="aborted")return ee;const o=await this._def.schema._parseAsync({data:i,path:t.path,parent:t});return o.status==="aborted"?ee:o.status==="dirty"||e.value==="dirty"?as(o.value):o});{if(e.value==="aborted")return ee;const i=this._def.schema._parseSync({data:a,path:t.path,parent:t});return i.status==="aborted"?ee:i.status==="dirty"||e.value==="dirty"?as(i.value):i}}if(n.type==="refinement"){const a=i=>{const o=n.refinement(i,r);if(t.common.async)return Promise.resolve(o);if(o instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return i};if(t.common.async===!1){const i=this._def.schema._parseSync({data:t.data,path:t.path,parent:t});return i.status==="aborted"?ee:(i.status==="dirty"&&e.dirty(),a(i.value),{status:e.value,value:i.value})}return this._def.schema._parseAsync({data:t.data,path:t.path,parent:t}).then(i=>i.status==="aborted"?ee:(i.status==="dirty"&&e.dirty(),a(i.value).then(()=>({status:e.value,value:i.value}))))}if(n.type==="transform"){if(t.common.async===!1){const a=this._def.schema._parseSync({data:t.data,path:t.path,parent:t});if(!Rt(a))return a;const i=n.transform(a.value,r);if(i instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:e.value,value:i}}return this._def.schema._parseAsync({data:t.data,path:t.path,parent:t}).then(a=>Rt(a)?Promise.resolve(n.transform(a.value,r)).then(i=>({status:e.value,value:i})):a)}ue.assertNever(n)}};nt.create=(s,e,t)=>new nt({schema:s,typeName:X.ZodEffects,effect:e,...re(t)}),nt.createWithPreprocess=(s,e,t)=>new nt({schema:e,effect:{type:"preprocess",transform:s},typeName:X.ZodEffects,...re(t)});let ot=class extends ce{_parse(s){return this._getType(s)===z.undefined?Re(void 0):this._def.innerType._parse(s)}unwrap(){return this._def.innerType}};ot.create=(s,e)=>new ot({innerType:s,typeName:X.ZodOptional,...re(e)});let Tt=class extends ce{_parse(s){return this._getType(s)===z.null?Re(null):this._def.innerType._parse(s)}unwrap(){return this._def.innerType}};Tt.create=(s,e)=>new Tt({innerType:s,typeName:X.ZodNullable,...re(e)});let Cn=class extends ce{_parse(s){const{ctx:e}=this._processInputParams(s);let t=e.data;return e.parsedType===z.undefined&&(t=this._def.defaultValue()),this._def.innerType._parse({data:t,path:e.path,parent:e})}removeDefault(){return this._def.innerType}};Cn.create=(s,e)=>new Cn({innerType:s,typeName:X.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...re(e)});let Tn=class extends ce{_parse(s){const{ctx:e}=this._processInputParams(s),t={...e,common:{...e.common,issues:[]}},n=this._def.innerType._parse({data:t.data,path:t.path,parent:{...t}});return fn(n)?n.then(r=>({status:"valid",value:r.status==="valid"?r.value:this._def.catchValue({get error(){return new at(t.common.issues)},input:t.data})})):{status:"valid",value:n.status==="valid"?n.value:this._def.catchValue({get error(){return new at(t.common.issues)},input:t.data})}}removeCatch(){return this._def.innerType}};Tn.create=(s,e)=>new Tn({innerType:s,typeName:X.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...re(e)});let us=class extends ce{_parse(s){if(this._getType(s)!==z.nan){const e=this._getOrReturnCtx(s);return j(e,{code:E.invalid_type,expected:z.nan,received:e.parsedType}),ee}return{status:"valid",value:s.data}}};us.create=s=>new us({typeName:X.ZodNaN,...re(s)});const Do=Symbol("zod_brand");let Gs=class extends ce{_parse(s){const{ctx:e}=this._processInputParams(s),t=e.data;return this._def.type._parse({data:t,path:e.path,parent:e})}unwrap(){return this._def.type}},Hs=class ri extends ce{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const r=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?ee:r.status==="dirty"?(t.dirty(),as(r.value)):this._def.out._parseAsync({data:r.value,path:n.path,parent:n})})();{const r=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?ee:r.status==="dirty"?(t.dirty(),{status:"dirty",value:r.value}):this._def.out._parseSync({data:r.value,path:n.path,parent:n})}}static create(e,t){return new ri({in:e,out:t,typeName:X.ZodPipeline})}},Mn=class extends ce{_parse(s){const e=this._def.innerType._parse(s),t=n=>(Rt(n)&&(n.value=Object.freeze(n.value)),n);return fn(e)?e.then(n=>t(n)):t(e)}unwrap(){return this._def.innerType}};function cr(s,e={},t){return s?Xt.create().superRefine((n,r)=>{var a,i;if(!s(n)){const o=typeof e=="function"?e(n):typeof e=="string"?{message:e}:e,c=(i=(a=o.fatal)!==null&&a!==void 0?a:t)===null||i===void 0||i,l=typeof o=="string"?{message:o}:o;r.addIssue({code:"custom",...l,fatal:c})}}):Xt.create()}Mn.create=(s,e)=>new Mn({innerType:s,typeName:X.ZodReadonly,...re(e)});const Uo={object:We.lazycreate};var X;(function(s){s.ZodString="ZodString",s.ZodNumber="ZodNumber",s.ZodNaN="ZodNaN",s.ZodBigInt="ZodBigInt",s.ZodBoolean="ZodBoolean",s.ZodDate="ZodDate",s.ZodSymbol="ZodSymbol",s.ZodUndefined="ZodUndefined",s.ZodNull="ZodNull",s.ZodAny="ZodAny",s.ZodUnknown="ZodUnknown",s.ZodNever="ZodNever",s.ZodVoid="ZodVoid",s.ZodArray="ZodArray",s.ZodObject="ZodObject",s.ZodUnion="ZodUnion",s.ZodDiscriminatedUnion="ZodDiscriminatedUnion",s.ZodIntersection="ZodIntersection",s.ZodTuple="ZodTuple",s.ZodRecord="ZodRecord",s.ZodMap="ZodMap",s.ZodSet="ZodSet",s.ZodFunction="ZodFunction",s.ZodLazy="ZodLazy",s.ZodLiteral="ZodLiteral",s.ZodEnum="ZodEnum",s.ZodEffects="ZodEffects",s.ZodNativeEnum="ZodNativeEnum",s.ZodOptional="ZodOptional",s.ZodNullable="ZodNullable",s.ZodDefault="ZodDefault",s.ZodCatch="ZodCatch",s.ZodPromise="ZodPromise",s.ZodBranded="ZodBranded",s.ZodPipeline="ZodPipeline",s.ZodReadonly="ZodReadonly"})(X||(X={}));const lr=Yt.create,dr=hn.create,Vo=us.create,qo=gn.create,ur=vn.create,Bo=$n.create,Jo=os.create,Go=yn.create,Ho=_n.create,Wo=Xt.create,Ko=It.create,Yo=vt.create,Xo=cs.create,Qo=Lt.create,ec=We.create,tc=We.strictCreate,nc=xn.create,sc=Ya.create,rc=wn.create,ac=Ct.create,ic=ei.create,oc=ls.create,cc=ds.create,lc=ni.create,dc=bn.create,uc=kn.create,pc=Gn.create,mc=Sn.create,fc=Qt.create,pr=nt.create,hc=ot.create,gc=Tt.create,vc=nt.createWithPreprocess,$c=Hs.create,yc={string:s=>Yt.create({...s,coerce:!0}),number:s=>hn.create({...s,coerce:!0}),boolean:s=>vn.create({...s,coerce:!0}),bigint:s=>gn.create({...s,coerce:!0}),date:s=>$n.create({...s,coerce:!0})},_c=ee;var ye=Object.freeze({__proto__:null,defaultErrorMap:Kt,setErrorMap:function(s){qa=s},getErrorMap:ss,makeIssue:rs,EMPTY_PATH:[],addIssueToContext:j,ParseStatus:Be,INVALID:ee,DIRTY:as,OK:Re,isAborted:Is,isDirty:Ps,isValid:Rt,isAsync:fn,get util(){return ue},get objectUtil(){return Os},ZodParsedType:z,getParsedType:ft,ZodType:ce,datetimeRegex:Wa,ZodString:Yt,ZodNumber:hn,ZodBigInt:gn,ZodBoolean:vn,ZodDate:$n,ZodSymbol:os,ZodUndefined:yn,ZodNull:_n,ZodAny:Xt,ZodUnknown:It,ZodNever:vt,ZodVoid:cs,ZodArray:Lt,ZodObject:We,ZodUnion:xn,ZodDiscriminatedUnion:Ya,ZodIntersection:wn,ZodTuple:Ct,ZodRecord:ei,ZodMap:ls,ZodSet:ds,ZodFunction:ni,ZodLazy:bn,ZodLiteral:kn,ZodEnum:Gn,ZodNativeEnum:Sn,ZodPromise:Qt,ZodEffects:nt,ZodTransformer:nt,ZodOptional:ot,ZodNullable:Tt,ZodDefault:Cn,ZodCatch:Tn,ZodNaN:us,BRAND:Do,ZodBranded:Gs,ZodPipeline:Hs,ZodReadonly:Mn,custom:cr,Schema:ce,ZodSchema:ce,late:Uo,get ZodFirstPartyTypeKind(){return X},coerce:yc,any:Wo,array:Qo,bigint:qo,boolean:ur,date:Bo,discriminatedUnion:sc,effect:pr,enum:pc,function:lc,instanceof:(s,e={message:`Input not instance of ${s.name}`})=>cr(t=>t instanceof s,e),intersection:rc,lazy:dc,literal:uc,map:oc,nan:Vo,nativeEnum:mc,never:Yo,null:Ho,nullable:gc,number:dr,object:ec,oboolean:()=>ur().optional(),onumber:()=>dr().optional(),optional:hc,ostring:()=>lr().optional(),pipeline:$c,preprocess:vc,promise:fc,record:ic,set:cc,strictObject:tc,string:lr,symbol:Jo,transformer:pr,tuple:ac,undefined:Go,union:nc,unknown:Ko,void:Xo,NEVER:_c,ZodIssueCode:E,quotelessJson:s=>JSON.stringify(s,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:at});const Ke=ye.object({name:ye.string().optional(),title:ye.string().optional(),command:ye.string().optional(),args:ye.array(ye.union([ye.string(),ye.number(),ye.boolean()])).optional(),env:ye.record(ye.union([ye.string(),ye.number(),ye.boolean(),ye.null(),ye.undefined()])).optional()}).passthrough(),xc=ye.array(Ke),wc=ye.object({servers:ye.array(Ke)}).passthrough(),bc=ye.object({mcpServers:ye.array(Ke)}).passthrough(),kc=ye.object({servers:ye.record(Ke)}).passthrough(),Sc=ye.object({mcpServers:ye.record(Ke)}).passthrough(),Cc=ye.record(Ke),Tc=Ke.refine(s=>s.command!==void 0,{message:"Server must have a 'command' property"}),Mc=Symbol("MCPServerError");let He=class ai extends Error{constructor(e){super(e),this.name="MCPServerError",Object.setPrototypeOf(this,ai.prototype)}};var Za,Zs;let Ac=(Za=Mc,Zs=class{constructor(s){ve(this,"servers",Ve([]));this.host=s,this.loadServersFromStorage()}handleMessageFromExtension(s){const e=s.data;if(e.type===se.getStoredMCPServersResponse){const t=e.data;return Array.isArray(t)&&this.servers.set(t),!0}return!1}async importServersFromJSON(s){return this.importFromJSON(s)}loadServersFromStorage(){try{this.host.postMessage({type:se.getStoredMCPServers})}catch(s){console.error("Failed to load MCP servers:",s),this.servers.set([])}}saveServers(s){try{this.host.postMessage({type:se.setStoredMCPServers,data:s})}catch(e){throw console.error("Failed to save MCP servers:",e),new He("Failed to save MCP servers")}}getServers(){return this.servers}addServer(s){this.checkExistingServerName(s.name),this.servers.update(e=>{const t=[...e,{...s,id:crypto.randomUUID()}];return this.saveServers(t),t})}addServers(s){for(const e of s)this.checkExistingServerName(e.name);this.servers.update(e=>{const t=[...e,...s.map(n=>({...n,id:crypto.randomUUID()}))];return this.saveServers(t),t})}checkExistingServerName(s,e){const t=mn(this.servers).find(n=>n.name===s);if(t&&(t==null?void 0:t.id)!==e)throw new He(`Server name '${s}' already exists`)}updateServer(s){this.checkExistingServerName(s.name,s.id),this.servers.update(e=>{const t=e.map(n=>n.id===s.id?s:n);return this.saveServers(t),t})}deleteServer(s){this.servers.update(e=>{const t=e.filter(n=>n.id!==s);return this.saveServers(t),t})}toggleDisabledServer(s){this.servers.update(e=>{const t=e.map(n=>n.id===s?{...n,disabled:!n.disabled}:n);return this.saveServers(t),t})}static convertServerToJSON(s){return JSON.stringify({mcpServers:{[s.name]:{command:s.command.split(" ")[0],args:s.command.split(" ").slice(1),env:s.env}}},null,2)}static parseServerValidationMessages(s){const e=new Map,t=new Map;s.forEach(r=>{var a;r.disabled?e.set(r.id,"MCP server has been manually disabled"):r.tools&&r.tools.length===0?e.set(r.id,"No tools are available for this MCP server"):r.disabledTools&&r.disabledTools.length===((a=r.tools)==null?void 0:a.length)?e.set(r.id,"All tools for this MCP server have validation errors: "+r.disabledTools.join(", ")):r.disabledTools&&r.disabledTools.length>0&&t.set(r.id,"MCP server has validation errors in the following tools which have been disabled: "+r.disabledTools.join(", "))});const n=this.parseDuplicateServerIds(s);return{errors:new Map([...e,...n]),warnings:t}}static parseDuplicateServerIds(s){const e=new Map;for(const n of s)e.has(n.name)||e.set(n.name,[]),e.get(n.name).push(n.id);const t=new Map;for(const[,n]of e)if(n.length>1)for(let r=1;r<n.length;r++)t.set(n[r],"MCP server is disabled due to duplicate server names");return t}parseServerConfigFromJSON(s){try{const e=JSON.parse(s),t=ye.union([xc.transform(n=>n.map(r=>this.normalizeServerConfig(r))),wc.transform(n=>n.servers.map(r=>this.normalizeServerConfig(r))),bc.transform(n=>n.mcpServers.map(r=>this.normalizeServerConfig(r))),kc.transform(n=>Object.entries(n.servers).map(([r,a])=>{const i=Ke.parse(a);return this.normalizeServerConfig({...i,name:i.name||r})})),Sc.transform(n=>Object.entries(n.mcpServers).map(([r,a])=>{const i=Ke.parse(a);return this.normalizeServerConfig({...i,name:i.name||r})})),Cc.transform(n=>{if(!Object.values(n).some(r=>{const a=Ke.safeParse(r);return a.success&&a.data.command!==void 0}))throw new Error("No command property found in any server config");return Object.entries(n).map(([r,a])=>{const i=Ke.parse(a);return this.normalizeServerConfig({...i,name:i.name||r})})}),Tc.transform(n=>[this.normalizeServerConfig(n)])]).safeParse(e);if(t.success)return t.data;throw new He("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(e){throw e instanceof He?e:new He("Failed to parse MCP servers from JSON. Please check the format.")}}importFromJSON(s){try{const e=this.parseServerConfigFromJSON(s),t=mn(this.servers),n=new Set(t.map(r=>r.name));for(const r of e){if(!r.name)throw new He("All servers must have a name.");if(n.has(r.name))throw new He(`A server with the name '${r.name}' already exists.`);n.add(r.name)}return this.servers.update(r=>{const a=[...r,...e.map(i=>({...i,id:crypto.randomUUID()}))];return this.saveServers(a),a}),e.length}catch(e){throw e instanceof He?e:new He("Failed to import MCP servers from JSON. Please check the format.")}}normalizeServerConfig(s){try{const e=Ke.transform(t=>{const n=t.command||"",r=t.args?t.args.map(c=>String(c)):[];if(!n)throw new Error("Server must have a 'command' property");const a=r.length>0?`${n} ${r.join(" ")}`:n,i=t.name||t.title||(n?n.split(" ")[0]:""),o=t.env?Object.fromEntries(Object.entries(t.env).filter(([c,l])=>l!=null).map(([c,l])=>[c,String(l)])):void 0;return{name:i,command:a,arguments:"",useShellInterpolation:!0,env:Object.keys(o||{}).length>0?o:void 0}}).refine(t=>!!t.name,{message:"Server must have a name",path:["name"]}).refine(t=>!!t.command,{message:"Server must have a command",path:["command"]}).safeParse(s);if(!e.success)throw new He(e.error.message);return e.data}catch(e){throw e instanceof Error?new He(`Invalid server configuration: ${e.message}`):new He("Invalid server configuration")}}},ve(Zs,Za,"MCPServerError"),Zs);class Nc{constructor(e){ve(this,"_terminalSettings",Ve({supportedShells:[],selectedShell:void 0,startupScript:void 0}));this._host=e,this.requestTerminalSettings()}handleMessageFromExtension(e){const t=e.data;return t.type===se.terminalSettingsResponse&&(this._terminalSettings.set(t.data),!0)}getTerminalSettings(){return this._terminalSettings}requestTerminalSettings(){this._host.postMessage({type:se.getTerminalSettings})}updateSelectedShell(e){this._terminalSettings.update(t=>({...t,selectedShell:e})),this._host.postMessage({type:se.updateTerminalSettings,data:{selectedShell:e}})}updateStartupScript(e){this._terminalSettings.update(t=>({...t,startupScript:e})),this._host.postMessage({type:se.updateTerminalSettings,data:{startupScript:e}})}}function An(s,e){return t=>!t.shiftKey&&t.key===s&&(e(t),!0)}var gt=(s=>(s.file="file",s.folder="folder",s))(gt||{});class kt{constructor(e,t){ve(this,"subscribe");ve(this,"set");ve(this,"update");ve(this,"handleMessageFromExtension",async e=>{const t=e.data;switch(t.type){case se.wsContextSourceFoldersChanged:case se.wsContextFolderContentsChanged:this.updateSourceFolders(await this.getSourceFolders());break;case se.sourceFoldersSyncStatus:this.update(n=>({...n,syncStatus:t.data.status}))}});ve(this,"getSourceFolders",async()=>(await this.asyncMsgSender.send({type:se.wsContextGetSourceFoldersRequest},1e4)).data.workspaceFolders);ve(this,"getChildren",async e=>(await this.asyncMsgSender.send({type:se.wsContextGetChildrenRequest,data:{fileId:e}},1e4)).data.children.map(t=>t.type==="folder"?{...t,children:[],expanded:!1}:{...t}).sort((t,n)=>t.type===n.type?t.name.localeCompare(n.name):t.type==="folder"?-1:1));this.host=e,this.asyncMsgSender=t;const{subscribe:n,set:r,update:a}=Ve({sourceFolders:[],sourceTree:[],syncStatus:Es.done});this.subscribe=n,this.set=r,this.update=a,this.getSourceFolders().then(i=>{this.update(o=>({...o,sourceFolders:i,sourceTree:kt.sourceFoldersToSourceNodes(i)}))})}async expandNode(e){e.children=await this.getChildren(e.fileId),e.expanded=!0,this.update(t=>t)}collapseNode(e){this.update(t=>(e.children=[],e.expanded=!1,t))}toggleNode(e){e.type==="folder"&&e.inclusionState!==tt.excluded&&(e.expanded?this.collapseNode(e):this.expandNode(e))}addMoreSourceFolders(){this.host.postMessage({type:se.wsContextAddMoreSourceFolders})}removeSourceFolder(e){this.host.postMessage({type:se.wsContextRemoveSourceFolder,data:e})}requestRefresh(){this.host.postMessage({type:se.wsContextUserRequestedRefresh})}async updateSourceFolders(e){let t=mn(this);const n=await this.getRefreshedSourceTree(t.sourceTree,e);this.update(r=>({...r,sourceFolders:e,sourceTree:n}))}async getRefreshedSourceTree(e,t){const n=kt.sourceFoldersToSourceNodes(t);return this.getRefreshedSourceTreeRecurse(e,n)}async getRefreshedSourceTreeRecurse(e,t){const n=new Map(e.map(r=>[JSON.stringify([r.fileId.folderRoot,r.fileId.relPath]),r]));for(let r of t){const a=kt.fileIdToString(r.fileId);if(r.type==="folder"){const i=n.get(a);i&&(r.expanded=i.type==="folder"&&i.expanded,r.expanded&&(r.children=await this.getChildren(r.fileId),r.children=await this.getRefreshedSourceTreeRecurse(i.children,r.children)))}}return t}static fileIdToString(e){return JSON.stringify([e.folderRoot,e.relPath])}static sourceFoldersToSourceNodes(e){return e.filter(t=>!t.isNestedFolder&&!t.isPending).sort((t,n)=>t.name.localeCompare(n.name)).map(t=>({name:t.name,fileId:t.fileId,children:[],expanded:!1,type:"folder",inclusionState:t.inclusionState,reason:"",trackedFileCount:t.trackedFileCount}))}}function mr(s,e,t){const n=s.slice();return n[6]=e[t],n}function fr(s){let e,t;function n(){return s[5](s[6])}return e=new xs({props:{title:"Remove source folder from Augment context",variant:"ghost",color:"neutral",size:1,class:"source-folder-v-adjust",$$slots:{default:[Zc]},$$scope:{ctx:s}}}),e.$on("click",function(){return s[4](s[6])}),e.$on("keyup",function(){jt(An("Enter",n))&&An("Enter",n).apply(this,arguments)}),{c(){w(e.$$.fragment)},m(r,a){b(e,r,a),t=!0},p(r,a){s=r;const i={};512&a&&(i.$$scope={dirty:a,ctx:s}),e.$set(i)},i(r){t||(p(e.$$.fragment,r),t=!0)},o(r){m(e.$$.fragment,r),t=!1},d(r){k(e,r)}}}function Zc(s){let e,t;return e=new Gi({}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function hr(s){let e,t;return e=new de({props:{size:1,class:"file-count",$$slots:{default:[Ec]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};513&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Ec(s){let e,t=s[6].trackedFileCount.toLocaleString()+"";return{c(){e=F(t)},m(n,r){x(n,e,r)},p(n,r){1&r&&t!==(t=n[6].trackedFileCount.toLocaleString()+"")&&we(e,t)},d(n){n&&$(e)}}}function gr(s,e){let t,n,r,a,i,o,c,l,d,u,h,v=e[6].name+"",y=(e[6].isPending?"(pending)":e[6].fileId.folderRoot)+"",f=!e[6].isWorkspaceFolder&&fr(e);r=new za({props:{class:"source-folder-v-adjust",icon:e[3](e[6])}});let g=e[6].trackedFileCount&&hr(e);return{key:s,first:null,c(){t=M("div"),f&&f.c(),n=Z(),w(r.$$.fragment),a=Z(),i=M("span"),o=F(v),c=Z(),l=M("span"),d=F(y),u=Z(),g&&g.c(),_(l,"class","folderRoot svelte-1skknri"),_(i,"class","name svelte-1skknri"),_(t,"class","item svelte-1skknri"),xe(t,"workspace-folder",e[6].isWorkspaceFolder),this.first=t},m(S,I){x(S,t,I),f&&f.m(t,null),C(t,n),b(r,t,null),C(t,a),C(t,i),C(i,o),C(i,c),C(i,l),C(l,d),C(t,u),g&&g.m(t,null),h=!0},p(S,I){(e=S)[6].isWorkspaceFolder?f&&(H(),m(f,1,1,()=>{f=null}),W()):f?(f.p(e,I),1&I&&p(f,1)):(f=fr(e),f.c(),p(f,1),f.m(t,n));const N={};1&I&&(N.icon=e[3](e[6])),r.$set(N),(!h||1&I)&&v!==(v=e[6].name+"")&&we(o,v),(!h||1&I)&&y!==(y=(e[6].isPending?"(pending)":e[6].fileId.folderRoot)+"")&&we(d,y),e[6].trackedFileCount?g?(g.p(e,I),1&I&&p(g,1)):(g=hr(e),g.c(),p(g,1),g.m(t,null)):g&&(H(),m(g,1,1,()=>{g=null}),W()),(!h||1&I)&&xe(t,"workspace-folder",e[6].isWorkspaceFolder)},i(S){h||(p(f),p(r.$$.fragment,S),p(g),h=!0)},o(S){m(f),m(r.$$.fragment,S),m(g),h=!1},d(S){S&&$(t),f&&f.d(),k(r),g&&g.d()}}}function Oc(s){let e,t,n,r,a,i,o,c,l=[],d=new Map,u=$e(s[0]);const h=v=>kt.fileIdToString(v[6].fileId);for(let v=0;v<u.length;v+=1){let y=mr(s,u,v),f=h(y);d.set(f,l[v]=gr(f,y))}return r=new _s({}),{c(){e=M("div");for(let v=0;v<l.length;v+=1)l[v].c();t=Z(),n=M("div"),w(r.$$.fragment),a=F(" Add more..."),_(n,"role","button"),_(n,"tabindex","0"),_(n,"class","add-more svelte-1skknri"),_(e,"class","source-folder svelte-1skknri")},m(v,y){x(v,e,y);for(let f=0;f<l.length;f+=1)l[f]&&l[f].m(e,null);C(e,t),C(e,n),b(r,n,null),C(n,a),i=!0,o||(c=[Ge(n,"keyup",function(){jt(An("Enter",s[1]))&&An("Enter",s[1]).apply(this,arguments)}),Ge(n,"click",function(){jt(s[1])&&s[1].apply(this,arguments)})],o=!0)},p(v,[y]){s=v,13&y&&(u=$e(s[0]),H(),l=Et(l,y,h,1,s,u,d,e,Ot,gr,t,mr),W())},i(v){if(!i){for(let y=0;y<u.length;y+=1)p(l[y]);p(r.$$.fragment,v),i=!0}},o(v){for(let y=0;y<l.length;y+=1)m(l[y]);m(r.$$.fragment,v),i=!1},d(v){v&&$(e);for(let y=0;y<l.length;y+=1)l[y].d();k(r),o=!1,Bs(c)}}}function Ic(s,e,t){let{folders:n=[]}=e,{onAddMore:r}=e,{onRemove:a}=e;return s.$$set=i=>{"folders"in i&&t(0,n=i.folders),"onAddMore"in i&&t(1,r=i.onAddMore),"onRemove"in i&&t(2,a=i.onRemove)},[n,r,a,i=>i.isWorkspaceFolder?"root-folder":"folder",i=>a(i.fileId.folderRoot),i=>a(i.fileId.folderRoot)]}class Pc extends he{constructor(e){super(),ge(this,e,Ic,Oc,me,{folders:0,onAddMore:1,onRemove:2})}}function vr(s,e,t){const n=s.slice();return n[10]=e[t],n}function $r(s){let e,t;return e=new de({props:{size:1,class:"file-count",$$slots:{default:[jc]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};8193&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function jc(s){let e,t=s[0].trackedFileCount.toLocaleString()+"";return{c(){e=F(t)},m(n,r){x(n,e,r)},p(n,r){1&r&&t!==(t=n[0].trackedFileCount.toLocaleString()+"")&&we(e,t)},d(n){n&&$(e)}}}function yr(s){let e,t,n=[],r=new Map,a=$e(s[5].children);const i=o=>kt.fileIdToString(o[10].fileId);for(let o=0;o<a.length;o+=1){let c=vr(s,a,o),l=i(c);r.set(l,n[o]=_r(l,c))}return{c(){e=M("div");for(let o=0;o<n.length;o+=1)n[o].c();_(e,"class","children-container")},m(o,c){x(o,e,c);for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(e,null);t=!0},p(o,c){38&c&&(a=$e(o[5].children),H(),n=Et(n,c,i,1,o,a,r,e,Ot,_r,null,vr),W())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&$(e);for(let c=0;c<n.length;c+=1)n[c].d()}}}function _r(s,e){let t,n,r;return n=new ii({props:{data:e[10],wsContextModel:e[1],indentLevel:e[2]+1}}),{key:s,first:null,c(){t=Ce(),w(n.$$.fragment),this.first=t},m(a,i){x(a,t,i),b(n,a,i),r=!0},p(a,i){e=a;const o={};32&i&&(o.data=e[10]),2&i&&(o.wsContextModel=e[1]),4&i&&(o.indentLevel=e[2]+1),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&$(t),k(n,a)}}}function Rc(s){let e,t,n,r,a,i,o,c,l,d,u,h,v,y,f,g,S,I,N=s[0].name+"";n=new za({props:{icon:s[4]}});let T=s[0].type===gt.folder&&s[0].inclusionState!==tt.excluded&&typeof s[0].trackedFileCount=="number"&&$r(s),A=s[5]&&yr(s);return{c(){e=M("div"),t=M("div"),w(n.$$.fragment),r=Z(),a=M("span"),i=F(N),o=Z(),T&&T.c(),c=Z(),l=M("img"),f=Z(),A&&A.c(),_(a,"class","name svelte-sympus"),nr(l.src,d=s[7][s[0].inclusionState])||_(l,"src",d),_(l,"alt",u=s[8][s[0].inclusionState]),_(t,"class","tree-item svelte-sympus"),_(t,"role","treeitem"),_(t,"aria-selected","false"),_(t,"tabindex","0"),_(t,"title",h=s[0].reason),_(t,"aria-expanded",v=s[0].type===gt.folder&&s[0].expanded),_(t,"aria-level",s[2]),_(t,"style",y=`padding-left: ${10*s[2]+20}px;`),xe(t,"included-folder",s[3])},m(P,U){x(P,e,U),C(e,t),b(n,t,null),C(t,r),C(t,a),C(a,i),C(t,o),T&&T.m(t,null),C(t,c),C(t,l),C(e,f),A&&A.m(e,null),g=!0,S||(I=[Ge(t,"click",s[6]),Ge(t,"keyup",An("Enter",s[6]))],S=!0)},p(P,[U]){const ne={};16&U&&(ne.icon=P[4]),n.$set(ne),(!g||1&U)&&N!==(N=P[0].name+"")&&we(i,N),P[0].type===gt.folder&&P[0].inclusionState!==tt.excluded&&typeof P[0].trackedFileCount=="number"?T?(T.p(P,U),1&U&&p(T,1)):(T=$r(P),T.c(),p(T,1),T.m(t,c)):T&&(H(),m(T,1,1,()=>{T=null}),W()),(!g||1&U&&!nr(l.src,d=P[7][P[0].inclusionState]))&&_(l,"src",d),(!g||1&U&&u!==(u=P[8][P[0].inclusionState]))&&_(l,"alt",u),(!g||1&U&&h!==(h=P[0].reason))&&_(t,"title",h),(!g||1&U&&v!==(v=P[0].type===gt.folder&&P[0].expanded))&&_(t,"aria-expanded",v),(!g||4&U)&&_(t,"aria-level",P[2]),(!g||4&U&&y!==(y=`padding-left: ${10*P[2]+20}px;`))&&_(t,"style",y),(!g||8&U)&&xe(t,"included-folder",P[3]),P[5]?A?(A.p(P,U),32&U&&p(A,1)):(A=yr(P),A.c(),p(A,1),A.m(e,null)):A&&(H(),m(A,1,1,()=>{A=null}),W())},i(P){g||(p(n.$$.fragment,P),p(T),p(A),g=!0)},o(P){m(n.$$.fragment,P),m(T),m(A),g=!1},d(P){P&&$(e),k(n),T&&T.d(),A&&A.d(),S=!1,Bs(I)}}}function Lc(s,e,t){let{data:n}=e,{wsContextModel:r}=e,{indentLevel:a}=e;const i={[tt.included]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM5.86301%208.67273L9.44256%203.9L8.24256%203L5.12729%207.15368L3.17471%205.59162L2.23767%206.76292L4.79449%208.80838L5.86301%208.67273Z'%20fill='%23388A34'/%3e%3c/svg%3e",[tt.excluded]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01119C3.65328%200.351896%204.81332%200%206%200C7.5907%200.00195419%209.11569%200.634726%2010.2405%201.75953C11.3653%202.88433%2011.998%204.40933%2012%206.00003C12%207.18673%2011.6481%208.34677%2010.9888%209.33347C10.3295%2010.3202%209.39246%2011.0892%208.2961%2011.5433C7.19975%2011.9975%205.99335%2012.1163%204.82946%2011.8848C3.66558%2011.6533%202.59648%2011.0818%201.75736%2010.2427C0.918247%209.40358%200.346802%208.33447%200.115291%207.17058C-0.11622%206.00669%200.00259969%204.80028%200.456726%203.70392C0.910851%202.60756%201.67989%201.67048%202.66658%201.01119ZM6.00007%207.07359L8.1213%209.19482L9.18196%208.13416L7.06073%206.01292L9.18198%203.89166L8.12132%202.83099L6.00007%204.95225L3.87866%202.83083L2.818%203.89149L4.93941%206.01292L2.81802%208.13432L3.87868%209.19499L6.00007%207.07359Z'%20fill='%23E51400'/%3e%3c/svg%3e",[tt.partial]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM3.66667%205.83333C3.66667%205.99815%203.61779%206.15927%203.52623%206.29631C3.43466%206.43335%203.30451%206.54016%203.15224%206.60323C2.99997%206.66631%202.83241%206.68281%202.67076%206.65065C2.50911%206.6185%202.36062%206.53913%202.24408%206.42259C2.12753%206.30605%202.04817%206.15756%202.01601%205.99591C1.98386%205.83426%202.00036%205.6667%202.06343%205.51443C2.12651%205.36216%202.23332%205.23201%202.37036%205.14044C2.5074%205.04887%202.66852%205%202.83333%205C3.05435%205%203.26631%205.0878%203.42259%205.24408C3.57887%205.40036%203.66667%205.61232%203.66667%205.83333ZM6.83333%205.83333C6.83333%205.99815%206.78446%206.15927%206.69289%206.29631C6.60132%206.43335%206.47117%206.54016%206.3189%206.60323C6.16663%206.66631%205.99908%206.68281%205.83742%206.65065C5.67577%206.6185%205.52729%206.53913%205.41074%206.42259C5.2942%206.30605%205.21483%206.15756%205.18268%205.99591C5.15052%205.83426%205.16703%205.6667%205.2301%205.51443C5.29317%205.36216%205.39998%205.23201%205.53702%205.14044C5.67407%205.04887%205.83518%205%206%205C6.22101%205%206.43297%205.0878%206.58926%205.24408C6.74554%205.40036%206.83333%205.61232%206.83333%205.83333ZM9.85956%206.29631C9.95113%206.15927%2010%205.99815%2010%205.83333C10%205.61232%209.9122%205.40036%209.75592%205.24408C9.59964%205.0878%209.38768%205%209.16667%205C9.00185%205%208.84073%205.04887%208.70369%205.14044C8.56665%205.23201%208.45984%205.36216%208.39677%205.51443C8.33369%205.6667%208.31719%205.83426%208.34935%205.99591C8.3815%206.15756%208.46087%206.30605%208.57741%206.42259C8.69395%206.53913%208.84244%206.6185%209.00409%206.65065C9.16574%206.68281%209.3333%206.66631%209.48557%206.60323C9.63784%206.54016%209.76799%206.43335%209.85956%206.29631Z'%20fill='%23388A34'/%3e%3c/svg%3e"},o={[tt.included]:"included",[tt.excluded]:"excluded",[tt.partial]:"partially included"};let c,l,d;return s.$$set=u=>{"data"in u&&t(0,n=u.data),"wsContextModel"in u&&t(1,r=u.wsContextModel),"indentLevel"in u&&t(2,a=u.indentLevel)},s.$$.update=()=>{var u;1&s.$$.dirty&&t(4,l=(u=n).type===gt.folder&&u.inclusionState!==tt.excluded?u.expanded?"chevron-down":"chevron-right":u.type===gt.folder?"folder":"file"),1&s.$$.dirty&&t(3,c=n.type===gt.folder&&n.inclusionState!==tt.excluded),1&s.$$.dirty&&t(5,d=n.type===gt.folder&&n.expanded&&n.children&&n.children.length>0?n:null)},[n,r,a,c,l,d,()=>{r.toggleNode(n)},i,o]}class ii extends he{constructor(e){super(),ge(this,e,Lc,Rc,me,{data:0,wsContextModel:1,indentLevel:2})}}function xr(s,e,t){const n=s.slice();return n[3]=e[t],n}function wr(s,e){let t,n,r;return n=new ii({props:{wsContextModel:e[0],data:e[3],indentLevel:0}}),{key:s,first:null,c(){t=Ce(),w(n.$$.fragment),this.first=t},m(a,i){x(a,t,i),b(n,a,i),r=!0},p(a,i){e=a;const o={};1&i&&(o.wsContextModel=e[0]),2&i&&(o.data=e[3]),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&$(t),k(n,a)}}}function Fc(s){let e,t,n=[],r=new Map,a=$e(s[1]);const i=o=>kt.fileIdToString(o[3].fileId);for(let o=0;o<a.length;o+=1){let c=xr(s,a,o),l=i(c);r.set(l,n[o]=wr(l,c))}return{c(){e=M("div");for(let o=0;o<n.length;o+=1)n[o].c();_(e,"class","files-container svelte-8hfqhl")},m(o,c){x(o,e,c);for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(e,null);t=!0},p(o,[c]){3&c&&(a=$e(o[1]),H(),n=Et(n,c,i,1,o,a,r,e,Ot,wr,null,xr),W())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&$(e);for(let c=0;c<n.length;c+=1)n[c].d()}}}function zc(s,e,t){let n,r=G,a=()=>(r(),r=Js(o,c=>t(2,n=c)),o);s.$$.on_destroy.push(()=>r());let i,{wsContextModel:o}=e;return a(),s.$$set=c=>{"wsContextModel"in c&&a(t(0,o=c.wsContextModel))},s.$$.update=()=>{4&s.$$.dirty&&t(1,i=n.sourceTree)},[o,i,n]}class Dc extends he{constructor(e){super(),ge(this,e,zc,Fc,me,{wsContextModel:0})}}function Uc(s){let e,t,n;return{c(){e=qe("svg"),t=qe("rect"),n=qe("path"),_(t,"width","16"),_(t,"height","16"),_(t,"transform","matrix(-1 0 0 -1 16 16)"),_(t,"fill","currentColor"),_(t,"fill-opacity","0.01"),_(n,"fill-rule","evenodd"),_(n,"clip-rule","evenodd"),_(n,"d","M13.7075 11.7333C13.7075 12.8236 12.8236 13.7075 11.7333 13.7075C10.643 13.7075 9.75909 12.8236 9.75909 11.7333C9.75909 10.643 10.643 9.75909 11.7333 9.75909C12.8236 9.75909 13.7075 10.643 13.7075 11.7333ZM11.7333 14.6675C13.3538 14.6675 14.6675 13.3538 14.6675 11.7333C14.6675 10.1128 13.3538 8.79909 11.7333 8.79909C10.1128 8.79909 8.79909 10.1128 8.79909 11.7333C8.79909 13.3538 10.1128 14.6675 11.7333 14.6675ZM9.79161 4.26647L13.3333 2.30721V6.22571L9.79161 4.26647ZM13.1852 7.24088C13.6829 7.51617 14.2933 7.15625 14.2933 6.58752V1.9454C14.2933 1.37665 13.6829 1.01676 13.1852 1.29207L8.98946 3.61313C8.47582 3.89729 8.47582 4.63564 8.98946 4.9198L13.1852 7.24088ZM7.14663 6.39988C7.14663 6.81225 6.81233 7.14654 6.39996 7.14654H2.1333C1.72093 7.14654 1.38664 6.81225 1.38664 6.39988V2.13324C1.38664 1.72087 1.72093 1.38657 2.1333 1.38657H6.39996C6.81233 1.38657 7.14663 1.72087 7.14663 2.13324V6.39988ZM6.18663 6.18654V2.34657H2.34664V6.18654H6.18663ZM1.66056 13.6606C1.47314 13.848 1.47314 14.152 1.66056 14.3394C1.84797 14.5269 2.15186 14.5269 2.33938 14.3394L4.26664 12.4121L6.19388 14.3394C6.38133 14.5268 6.68525 14.5268 6.8727 14.3394C7.06015 14.1519 7.06015 13.848 6.8727 13.6606L4.94546 11.7333L6.8727 9.80608C7.06015 9.61863 7.06015 9.31471 6.8727 9.12726C6.68525 8.9398 6.38133 8.9398 6.19388 9.12726L4.26664 11.0545L2.33938 9.12722C2.15186 8.93978 1.84797 8.93978 1.66056 9.12722C1.47314 9.31468 1.47314 9.61861 1.66056 9.80605L3.58781 11.7333L1.66056 13.6606Z"),_(n,"fill","currentColor"),_(e,"width","15"),_(e,"height","15"),_(e,"viewBox","0 0 16 16"),_(e,"fill","none"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(r,a){x(r,e,a),C(e,t),C(e,n)},p:G,i:G,o:G,d(r){r&&$(e)}}}class Vc extends he{constructor(e){super(),ge(this,e,null,Uc,me,{})}}const qc=s=>({}),br=s=>({}),Bc=s=>({}),kr=s=>({});function Jc(s){let e;const t=s[8]["header-left"],n=Fe(t,s,s[10],kr);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||1024&a)&&ze(n,t,r,r[10],e?Ue(t,r[10],a,Bc):De(r[10]),kr)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function Gc(s){let e,t,n,r=s[0]&&Sr(s),a=s[1]&&Cr(s);return{c(){r&&r.c(),e=Z(),a&&a.c(),t=Ce()},m(i,o){r&&r.m(i,o),x(i,e,o),a&&a.m(i,o),x(i,t,o),n=!0},p(i,o){i[0]?r?(r.p(i,o),1&o&&p(r,1)):(r=Sr(i),r.c(),p(r,1),r.m(e.parentNode,e)):r&&(H(),m(r,1,1,()=>{r=null}),W()),i[1]?a?(a.p(i,o),2&o&&p(a,1)):(a=Cr(i),a.c(),p(a,1),a.m(t.parentNode,t)):a&&(H(),m(a,1,1,()=>{a=null}),W())},i(i){n||(p(r),p(a),n=!0)},o(i){m(r),m(a),n=!1},d(i){i&&($(e),$(t)),r&&r.d(i),a&&a.d(i)}}}function Sr(s){let e,t,n;var r=s[0];return r&&(t=St(r,{})),{c(){e=M("div"),t&&w(t.$$.fragment),_(e,"class","icon-wrapper svelte-13uht7n")},m(a,i){x(a,e,i),t&&b(t,e,null),n=!0},p(a,i){if(1&i&&r!==(r=a[0])){if(t){H();const o=t;m(o.$$.fragment,1,0,()=>{k(o,1)}),W()}r?(t=St(r,{}),w(t.$$.fragment),p(t.$$.fragment,1),b(t,e,null)):t=null}},i(a){n||(t&&p(t.$$.fragment,a),n=!0)},o(a){t&&m(t.$$.fragment,a),n=!1},d(a){a&&$(e),t&&k(t)}}}function Cr(s){let e,t;return e=new de({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[Hc]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};1026&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Hc(s){let e;return{c(){e=F(s[1])},m(t,n){x(t,e,n)},p(t,n){2&n&&we(e,t[1])},d(t){t&&$(e)}}}function Tr(s){let e,t;const n=s[8].default,r=Fe(n,s,s[10],null);return{c(){e=M("div"),r&&r.c(),_(e,"class","settings-card-body")},m(a,i){x(a,e,i),r&&r.m(e,null),t=!0},p(a,i){r&&r.p&&(!t||1024&i)&&ze(r,n,a,a[10],t?Ue(n,a[10],i,null):De(a[10]),null)},i(a){t||(p(r,a),t=!0)},o(a){m(r,a),t=!1},d(a){a&&$(e),r&&r.d(a)}}}function Wc(s){let e,t,n,r,a,i,o,c,l,d,u;const h=[Gc,Jc],v=[];function y(T,A){return T[0]||T[1]?0:1}r=y(s),a=v[r]=h[r](s);const f=s[8]["header-right"],g=Fe(f,s,s[10],br);let S=s[5].default&&Tr(s),I=[{role:"button"},{class:s[3]},s[4]],N={};for(let T=0;T<I.length;T+=1)N=ke(N,I[T]);return{c(){e=M("div"),t=M("div"),n=M("div"),a.c(),i=Z(),o=M("div"),g&&g.c(),c=Z(),S&&S.c(),_(n,"class","settings-card-left svelte-13uht7n"),_(o,"class","settings-card-right svelte-13uht7n"),_(t,"class","settings-card-content svelte-13uht7n"),ts(e,N),xe(e,"clickable",s[2]),xe(e,"svelte-13uht7n",!0)},m(T,A){x(T,e,A),C(e,t),C(t,n),v[r].m(n,null),C(t,i),C(t,o),g&&g.m(o,null),C(e,c),S&&S.m(e,null),l=!0,d||(u=Ge(e,"click",s[9]),d=!0)},p(T,[A]){let P=r;r=y(T),r===P?v[r].p(T,A):(H(),m(v[P],1,1,()=>{v[P]=null}),W(),a=v[r],a?a.p(T,A):(a=v[r]=h[r](T),a.c()),p(a,1),a.m(n,null)),g&&g.p&&(!l||1024&A)&&ze(g,f,T,T[10],l?Ue(f,T[10],A,qc):De(T[10]),br),T[5].default?S?(S.p(T,A),32&A&&p(S,1)):(S=Tr(T),S.c(),p(S,1),S.m(e,null)):S&&(H(),m(S,1,1,()=>{S=null}),W()),ts(e,N=yt(I,[{role:"button"},(!l||8&A)&&{class:T[3]},16&A&&T[4]])),xe(e,"clickable",T[2]),xe(e,"svelte-13uht7n",!0)},i(T){l||(p(a),p(g,T),p(S),l=!0)},o(T){m(a),m(g,T),m(S),l=!1},d(T){T&&$(e),v[r].d(),g&&g.d(T),S&&S.d(),d=!1,u()}}}function Kc(s,e,t){let n,r,a;const i=["class","icon","title","isClickable"];let o=ns(e,i),{$$slots:c={},$$scope:l}=e;const d=Oa(c);let{class:u=""}=e,{icon:h}=e,{title:v}=e,{isClickable:y=!1}=e;return s.$$set=f=>{e=ke(ke({},e),Xe(f)),t(11,o=ns(e,i)),"class"in f&&t(6,u=f.class),"icon"in f&&t(0,h=f.icon),"title"in f&&t(1,v=f.title),"isClickable"in f&&t(2,y=f.isClickable),"$$scope"in f&&t(10,l=f.$$scope)},s.$$.update=()=>{t(7,{class:n,...r}=o,n,(t(4,r),t(11,o))),192&s.$$.dirty&&t(3,a=`settings-card ${u} ${n||""}`)},[h,v,y,a,r,d,u,n,c,function(f){Ia.call(this,s,f)},l]}let oi=class extends he{constructor(s){super(),ge(this,s,Kc,Wc,me,{class:6,icon:0,title:1,isClickable:2})}};function Yc(s){let e;return{c(){e=F("SOURCE FOLDERS")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function Xc(s){let e;return{c(){e=F("FILES")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function Qc(s){let e,t=s[2].toLocaleString()+"";return{c(){e=F(t)},m(n,r){x(n,e,r)},p(n,r){4&r&&t!==(t=n[2].toLocaleString()+"")&&we(e,t)},d(n){n&&$(e)}}}function el(s){let e,t,n,r,a,i,o,c,l,d,u,h,v,y;return n=new de({props:{size:1,weight:"medium",class:"context-section-header",$$slots:{default:[Yc]},$$scope:{ctx:s}}}),a=new Pc({props:{folders:s[0],onRemove:s[7],onAddMore:s[8]}}),l=new de({props:{size:1,weight:"medium",class:"context-section-header",$$slots:{default:[Xc]},$$scope:{ctx:s}}}),u=new de({props:{size:1,class:"file-count",$$slots:{default:[Qc]},$$scope:{ctx:s}}}),v=new Dc({props:{wsContextModel:s[3]}}),{c(){e=M("div"),t=M("div"),w(n.$$.fragment),r=Z(),w(a.$$.fragment),i=Z(),o=M("div"),c=M("div"),w(l.$$.fragment),d=Z(),w(u.$$.fragment),h=Z(),w(v.$$.fragment),_(c,"class","files-header svelte-qsnirf"),_(e,"class","context-list svelte-qsnirf")},m(f,g){x(f,e,g),C(e,t),b(n,t,null),C(t,r),b(a,t,null),C(e,i),C(e,o),C(o,c),b(l,c,null),C(c,d),b(u,c,null),C(o,h),b(v,o,null),y=!0},p(f,g){const S={};512&g&&(S.$$scope={dirty:g,ctx:f}),n.$set(S);const I={};1&g&&(I.folders=f[0]),a.$set(I);const N={};512&g&&(N.$$scope={dirty:g,ctx:f}),l.$set(N);const T={};516&g&&(T.$$scope={dirty:g,ctx:f}),u.$set(T)},i(f){y||(p(n.$$.fragment,f),p(a.$$.fragment,f),p(l.$$.fragment,f),p(u.$$.fragment,f),p(v.$$.fragment,f),y=!0)},o(f){m(n.$$.fragment,f),m(a.$$.fragment,f),m(l.$$.fragment,f),m(u.$$.fragment,f),m(v.$$.fragment,f),y=!1},d(f){f&&$(e),k(n),k(a),k(l),k(u),k(v)}}}function Mr(s){let e,t;return e=new xs({props:{title:"Refresh",variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[tl]},$$scope:{ctx:s}}}),e.$on("click",s[5]),e.$on("keyup",ao("Enter",s[6])),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};512&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function tl(s){let e,t;return e=new Hi({}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function nl(s){let e,t,n=s[1]===Es.done&&Mr(s);return{c(){e=M("div"),n&&n.c(),_(e,"slot","header-right")},m(r,a){x(r,e,a),n&&n.m(e,null),t=!0},p(r,a){r[1]===Es.done?n?(n.p(r,a),2&a&&p(n,1)):(n=Mr(r),n.c(),p(n,1),n.m(e,null)):n&&(H(),m(n,1,1,()=>{n=null}),W())},i(r){t||(p(n),t=!0)},o(r){m(n),t=!1},d(r){r&&$(e),n&&n.d()}}}function sl(s){let e,t,n,r;return e=new oi({props:{icon:Vc,title:"Context",$$slots:{"header-right":[nl],default:[el]},$$scope:{ctx:s}}}),e.$on("contextmenu",rl),{c(){w(e.$$.fragment)},m(a,i){b(e,a,i),t=!0,n||(r=Ge(window,"message",s[3].handleMessageFromExtension),n=!0)},p(a,[i]){const o={};519&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o)},i(a){t||(p(e.$$.fragment,a),t=!0)},o(a){m(e.$$.fragment,a),t=!1},d(a){k(e,a),n=!1,r()}}}const rl=s=>s.preventDefault();function al(s,e,t){let n,r,a,i,o=new kt(Me,new ro(Me.postMessage));return bt(s,o,c=>t(4,r=c)),s.$$.update=()=>{16&s.$$.dirty&&t(0,a=r.sourceFolders.sort((c,l)=>c.isWorkspaceFolder!==l.isWorkspaceFolder?c.isWorkspaceFolder?-1:1:c.fileId.folderRoot.localeCompare(l.fileId.folderRoot))),16&s.$$.dirty&&t(1,i=r.syncStatus),1&s.$$.dirty&&t(2,n=a.reduce((c,l)=>c+(l.trackedFileCount??0),0))},[a,i,n,o,r,()=>o.requestRefresh(),()=>o.requestRefresh(),c=>o.removeSourceFolder(c),()=>o.addMoreSourceFolders()]}class il extends he{constructor(e){super(),ge(this,e,al,sl,me,{})}}function ci(s){return function(e){switch(typeof e){case"object":return e!=null;case"function":return!0;default:return!1}}(s)&&"name"in s}function Ar(s){return ci(s)&&"component"in s}function ol(s){let e,t;return{c(){e=qe("svg"),t=qe("path"),_(t,"d","M5.5 1.75V3H10.5V1.75C10.5 1.625 10.375 1.5 10.25 1.5H5.75C5.59375 1.5 5.5 1.625 5.5 1.75ZM4 3V1.75C4 0.8125 4.78125 0 5.75 0H10.25C11.1875 0 12 0.8125 12 1.75V3H14C15.0938 3 16 3.90625 16 5V8.75V13C16 14.125 15.0938 15 14 15H2C0.875 15 0 14.125 0 13V8.75V5C0 3.90625 0.875 3 2 3H4ZM1.5 9.5V13C1.5 13.2812 1.71875 13.5 2 13.5H14C14.25 13.5 14.5 13.2812 14.5 13V9.5H10V10C10 10.5625 9.53125 11 9 11H7C6.4375 11 6 10.5625 6 10V9.5H1.5ZM6 8H10H14.5V5C14.5 4.75 14.25 4.5 14 4.5H11.25H4.75H2C1.71875 4.5 1.5 4.75 1.5 5V8H6Z"),_(t,"fill","currentColor"),_(e,"width","16"),_(e,"height","15"),_(e,"viewBox","0 0 16 15"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){x(n,e,r),C(e,t)},p:G,i:G,o:G,d(n){n&&$(e)}}}class li extends he{constructor(e){super(),ge(this,e,null,ol,me,{})}}const cl=s=>({item:1&s}),Nr=s=>({item:s[0]}),ll=s=>({}),Zr=s=>({});function Er(s){var l;let e,t,n,r,a;e=new de({props:{size:4,weight:"medium",color:"neutral",$$slots:{default:[dl]},$$scope:{ctx:s}}});let i=((l=s[0])==null?void 0:l.description)&&Or(s);const o=s[1].content,c=Fe(o,s,s[2],Nr);return{c(){w(e.$$.fragment),t=Z(),i&&i.c(),n=Z(),r=M("div"),c&&c.c(),_(r,"class","c-navigation__content-container svelte-z0ijuz")},m(d,u){b(e,d,u),x(d,t,u),i&&i.m(d,u),x(d,n,u),x(d,r,u),c&&c.m(r,null),a=!0},p(d,u){var v;const h={};5&u&&(h.$$scope={dirty:u,ctx:d}),e.$set(h),(v=d[0])!=null&&v.description?i?(i.p(d,u),1&u&&p(i,1)):(i=Or(d),i.c(),p(i,1),i.m(n.parentNode,n)):i&&(H(),m(i,1,1,()=>{i=null}),W()),c&&c.p&&(!a||5&u)&&ze(c,o,d,d[2],a?Ue(o,d[2],u,cl):De(d[2]),Nr)},i(d){a||(p(e.$$.fragment,d),p(i),p(c,d),a=!0)},o(d){m(e.$$.fragment,d),m(i),m(c,d),a=!1},d(d){d&&($(t),$(n),$(r)),k(e,d),i&&i.d(d),c&&c.d(d)}}}function dl(s){var r;let e,t,n=((r=s[0])==null?void 0:r.name)+"";return{c(){e=M("div"),t=F(n),_(e,"class","c-navigation__content-header svelte-z0ijuz")},m(a,i){x(a,e,i),C(e,t)},p(a,i){var o;1&i&&n!==(n=((o=a[0])==null?void 0:o.name)+"")&&we(t,n)},d(a){a&&$(e)}}}function Or(s){let e,t;return e=new de({props:{color:"secondary",size:1,weight:"light",$$slots:{default:[ul]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};5&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function ul(s){var r;let e,t,n=((r=s[0])==null?void 0:r.description)+"";return{c(){e=M("div"),t=F(n),_(e,"class","c-navigation__content-description svelte-z0ijuz")},m(a,i){x(a,e,i),C(e,t)},p(a,i){var o;1&i&&n!==(n=((o=a[0])==null?void 0:o.description)+"")&&we(t,n)},d(a){a&&$(e)}}}function pl(s){let e,t,n,r,a;const i=s[1].header,o=Fe(i,s,s[2],Zr);let c=s[0]!=null&&Er(s);return{c(){var l;e=M("div"),o&&o.c(),t=Z(),n=M("div"),c&&c.c(),_(e,"class","c-navigation__content svelte-z0ijuz"),_(e,"id",r=(l=s[0])==null?void 0:l.id)},m(l,d){x(l,e,d),o&&o.m(e,null),C(e,t),C(e,n),c&&c.m(n,null),a=!0},p(l,[d]){var u;o&&o.p&&(!a||4&d)&&ze(o,i,l,l[2],a?Ue(i,l[2],d,ll):De(l[2]),Zr),l[0]!=null?c?(c.p(l,d),1&d&&p(c,1)):(c=Er(l),c.c(),p(c,1),c.m(n,null)):c&&(H(),m(c,1,1,()=>{c=null}),W()),(!a||1&d&&r!==(r=(u=l[0])==null?void 0:u.id))&&_(e,"id",r)},i(l){a||(p(o,l),p(c),a=!0)},o(l){m(o,l),m(c),a=!1},d(l){l&&$(e),o&&o.d(l),c&&c.d()}}}function ml(s,e,t){let{$$slots:n={},$$scope:r}=e,{item:a}=e;return s.$$set=i=>{"item"in i&&t(0,a=i.item),"$$scope"in i&&t(2,r=i.$$scope)},[a,n,r]}class di extends he{constructor(e){super(),ge(this,e,ml,pl,me,{item:0})}}function fl(s,e){let t;function n({scrollTo:r,delay:a,options:i}){clearTimeout(t),r&&(t=setTimeout(()=>{s.scrollIntoView(i)},a))}return n(e),{update:n,destroy(){clearTimeout(t)}}}function Ir(s,e,t){const n=s.slice();return n[13]=e[t][0],n[14]=e[t][1],n}function Pr(s,e,t){const n=s.slice();return n[22]=e[t],n}const hl=s=>({item:32&s}),jr=s=>({slot:"content",item:s[22]}),gl=s=>({label:32&s,mode:4&s}),Rr=s=>({label:s[13],mode:s[2]}),vl=s=>({item:1&s}),Lr=s=>({item:s[0]});function Fr(s,e,t){const n=s.slice();return n[13]=e[t][0],n[14]=e[t][1],n}function zr(s,e,t){const n=s.slice();return n[17]=e[t],n}const $l=s=>({label:32&s,mode:4&s}),Dr=s=>({label:s[13],mode:s[2]}),yl=s=>({item:1&s,selectedId:2&s}),Ur=s=>({slot:"header",item:s[0],selectedId:s[1]}),_l=s=>({item:1&s,isSelected:3&s}),Vr=s=>{var e;return{slot:"content",item:s[0],isSelected:((e=s[0])==null?void 0:e.id)===s[1]}};function xl(s){let e,t,n;const r=s[10].header,a=Fe(r,s,s[12],Lr);let i=$e(s[5]),o=[];for(let l=0;l<i.length;l+=1)o[l]=Br(Ir(s,i,l));const c=l=>m(o[l],1,1,()=>{o[l]=null});return{c(){e=M("div"),a&&a.c(),t=Z();for(let l=0;l<o.length;l+=1)o[l].c();_(e,"class","c-navigation__flat svelte-n5ccbo")},m(l,d){x(l,e,d),a&&a.m(e,null),C(e,t);for(let u=0;u<o.length;u+=1)o[u]&&o[u].m(e,null);n=!0},p(l,d){if(a&&a.p&&(!n||4097&d)&&ze(a,r,l,l[12],n?Ue(r,l[12],d,vl):De(l[12]),Lr),4134&d){let u;for(i=$e(l[5]),u=0;u<i.length;u+=1){const h=Ir(l,i,u);o[u]?(o[u].p(h,d),p(o[u],1)):(o[u]=Br(h),o[u].c(),p(o[u],1),o[u].m(e,null))}for(H(),u=i.length;u<o.length;u+=1)c(u);W()}},i(l){if(!n){p(a,l);for(let d=0;d<i.length;d+=1)p(o[d]);n=!0}},o(l){m(a,l),o=o.filter(Boolean);for(let d=0;d<o.length;d+=1)m(o[d]);n=!1},d(l){l&&$(e),a&&a.d(l),sn(o,l)}}}function wl(s){let e,t;return e=new io({props:{initialWidth:200,expandedMinWidth:150,columnLayoutThreshold:0,showButton:s[3],minimized:!1,$$slots:{right:[Nl],left:[Tl]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};8&r&&(a.showButton=n[3]),4135&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function bl(s){let e,t,n,r,a,i,o=s[13]+"";return t=new li({}),{c(){e=M("span"),w(t.$$.fragment),n=Z(),r=M("span"),a=F(o),_(e,"class","c-navigation__head-icon")},m(c,l){x(c,e,l),b(t,e,null),x(c,n,l),x(c,r,l),C(r,a),i=!0},p(c,l){(!i||32&l)&&o!==(o=c[13]+"")&&we(a,o)},i(c){i||(p(t.$$.fragment,c),i=!0)},o(c){m(t.$$.fragment,c),i=!1},d(c){c&&($(e),$(n),$(r)),k(t)}}}function kl(s){let e;const t=s[10].content,n=Fe(t,s,s[12],jr);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||4128&a)&&ze(n,t,r,r[12],e?Ue(t,r[12],a,hl):De(r[12]),jr)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function qr(s){let e,t,n,r,a,i,o;return t=new di({props:{item:s[22],$$slots:{content:[kl]},$$scope:{ctx:s}}}),{c(){e=M("span"),w(t.$$.fragment),n=Z()},m(c,l){x(c,e,l),b(t,e,null),C(e,n),a=!0,i||(o=Ci(r=fl.call(null,e,{scrollTo:s[2]==="flat"&&s[22].id===s[1],delay:300,options:{behavior:"smooth"}})),i=!0)},p(c,l){s=c;const d={};32&l&&(d.item=s[22]),4128&l&&(d.$$scope={dirty:l,ctx:s}),t.$set(d),r&&jt(r.update)&&38&l&&r.update.call(null,{scrollTo:s[2]==="flat"&&s[22].id===s[1],delay:300,options:{behavior:"smooth"}})},i(c){a||(p(t.$$.fragment,c),a=!0)},o(c){m(t.$$.fragment,c),a=!1},d(c){c&&$(e),k(t),i=!1,o()}}}function Br(s){let e,t,n,r;const a=s[10].group,i=Fe(a,s,s[12],Rr),o=i||function(u){let h,v;return h=new de({props:{color:"secondary",size:2,weight:"medium",$$slots:{default:[bl]},$$scope:{ctx:u}}}),{c(){w(h.$$.fragment)},m(y,f){b(h,y,f),v=!0},p(y,f){const g={};4128&f&&(g.$$scope={dirty:f,ctx:y}),h.$set(g)},i(y){v||(p(h.$$.fragment,y),v=!0)},o(y){m(h.$$.fragment,y),v=!1},d(y){k(h,y)}}}(s);let c=$e(s[14]),l=[];for(let u=0;u<c.length;u+=1)l[u]=qr(Pr(s,c,u));const d=u=>m(l[u],1,1,()=>{l[u]=null});return{c(){e=M("div"),o&&o.c(),t=Z();for(let u=0;u<l.length;u+=1)l[u].c();n=Ce(),_(e,"class","c-navigation__head svelte-n5ccbo")},m(u,h){x(u,e,h),o&&o.m(e,null),x(u,t,h);for(let v=0;v<l.length;v+=1)l[v]&&l[v].m(u,h);x(u,n,h),r=!0},p(u,h){if(i?i.p&&(!r||4132&h)&&ze(i,a,u,u[12],r?Ue(a,u[12],h,gl):De(u[12]),Rr):o&&o.p&&(!r||32&h)&&o.p(u,r?h:-1),4134&h){let v;for(c=$e(u[14]),v=0;v<c.length;v+=1){const y=Pr(u,c,v);l[v]?(l[v].p(y,h),p(l[v],1)):(l[v]=qr(y),l[v].c(),p(l[v],1),l[v].m(n.parentNode,n))}for(H(),v=c.length;v<l.length;v+=1)d(v);W()}},i(u){if(!r){p(o,u);for(let h=0;h<c.length;h+=1)p(l[h]);r=!0}},o(u){m(o,u),l=l.filter(Boolean);for(let h=0;h<l.length;h+=1)m(l[h]);r=!1},d(u){u&&($(e),$(t),$(n)),o&&o.d(u),sn(l,u)}}}function Sl(s){let e,t=s[13]+"";return{c(){e=F(t)},m(n,r){x(n,e,r)},p(n,r){32&r&&t!==(t=n[13]+"")&&we(e,t)},d(n){n&&$(e)}}}function Cl(s){let e,t,n,r,a,i=s[17].name+"";var o=s[17].icon;return o&&(t=St(o,{})),{c(){e=M("span"),t&&w(t.$$.fragment),n=Z(),r=F(i),_(e,"class","c-navigation__head-icon")},m(c,l){x(c,e,l),t&&b(t,e,null),x(c,n,l),x(c,r,l),a=!0},p(c,l){if(32&l&&o!==(o=c[17].icon)){if(t){H();const d=t;m(d.$$.fragment,1,0,()=>{k(d,1)}),W()}o?(t=St(o,{}),w(t.$$.fragment),p(t.$$.fragment,1),b(t,e,null)):t=null}(!a||32&l)&&i!==(i=c[17].name+"")&&we(r,i)},i(c){a||(t&&p(t.$$.fragment,c),a=!0)},o(c){t&&m(t.$$.fragment,c),a=!1},d(c){c&&($(e),$(n),$(r)),t&&k(t)}}}function Jr(s){let e,t,n,r,a,i;function o(){return s[11](s[17])}return t=new de({props:{size:2,weight:"regular",color:"primary",$$slots:{default:[Cl]},$$scope:{ctx:s}}}),{c(){e=M("button"),w(t.$$.fragment),n=Z(),_(e,"class","c-navigation__item svelte-n5ccbo"),xe(e,"is-active",s[17].id===s[1])},m(c,l){x(c,e,l),b(t,e,null),C(e,n),r=!0,a||(i=Ge(e,"click",o),a=!0)},p(c,l){s=c;const d={};4128&l&&(d.$$scope={dirty:l,ctx:s}),t.$set(d),(!r||34&l)&&xe(e,"is-active",s[17].id===s[1])},i(c){r||(p(t.$$.fragment,c),r=!0)},o(c){m(t.$$.fragment,c),r=!1},d(c){c&&$(e),k(t),a=!1,i()}}}function Gr(s){let e,t,n,r,a;const i=s[10].group,o=Fe(i,s,s[12],Dr),c=o||function(h){let v,y,f,g,S;return y=new li({}),g=new de({props:{size:2,color:"primary",$$slots:{default:[Sl]},$$scope:{ctx:h}}}),{c(){v=M("div"),w(y.$$.fragment),f=Z(),w(g.$$.fragment),_(v,"class","c-navigation__head svelte-n5ccbo")},m(I,N){x(I,v,N),b(y,v,null),C(v,f),b(g,v,null),S=!0},p(I,N){const T={};4128&N&&(T.$$scope={dirty:N,ctx:I}),g.$set(T)},i(I){S||(p(y.$$.fragment,I),p(g.$$.fragment,I),S=!0)},o(I){m(y.$$.fragment,I),m(g.$$.fragment,I),S=!1},d(I){I&&$(v),k(y),k(g)}}}(s);let l=$e(s[14]),d=[];for(let h=0;h<l.length;h+=1)d[h]=Jr(zr(s,l,h));const u=h=>m(d[h],1,1,()=>{d[h]=null});return{c(){e=M("div"),c&&c.c(),t=Z(),n=M("div");for(let h=0;h<d.length;h+=1)d[h].c();r=Z(),_(n,"class","c-navigation__items svelte-n5ccbo"),_(e,"class","c-navigation__group")},m(h,v){x(h,e,v),c&&c.m(e,null),C(e,t),C(e,n);for(let y=0;y<d.length;y+=1)d[y]&&d[y].m(n,null);C(e,r),a=!0},p(h,v){if(o?o.p&&(!a||4132&v)&&ze(o,i,h,h[12],a?Ue(i,h[12],v,$l):De(h[12]),Dr):c&&c.p&&(!a||32&v)&&c.p(h,a?v:-1),98&v){let y;for(l=$e(h[14]),y=0;y<l.length;y+=1){const f=zr(h,l,y);d[y]?(d[y].p(f,v),p(d[y],1)):(d[y]=Jr(f),d[y].c(),p(d[y],1),d[y].m(n,null))}for(H(),y=l.length;y<d.length;y+=1)u(y);W()}},i(h){if(!a){p(c,h);for(let v=0;v<l.length;v+=1)p(d[v]);a=!0}},o(h){m(c,h),d=d.filter(Boolean);for(let v=0;v<d.length;v+=1)m(d[v]);a=!1},d(h){h&&$(e),c&&c.d(h),sn(d,h)}}}function Hr(s){let e,t,n=$e(s[5]),r=[];for(let i=0;i<n.length;i+=1)r[i]=Gr(Fr(s,n,i));const a=i=>m(r[i],1,1,()=>{r[i]=null});return{c(){for(let i=0;i<r.length;i+=1)r[i].c();e=Ce()},m(i,o){for(let c=0;c<r.length;c+=1)r[c]&&r[c].m(i,o);x(i,e,o),t=!0},p(i,o){if(4198&o){let c;for(n=$e(i[5]),c=0;c<n.length;c+=1){const l=Fr(i,n,c);r[c]?(r[c].p(l,o),p(r[c],1)):(r[c]=Gr(l),r[c].c(),p(r[c],1),r[c].m(e.parentNode,e))}for(H(),c=n.length;c<r.length;c+=1)a(c);W()}},i(i){if(!t){for(let o=0;o<n.length;o+=1)p(r[o]);t=!0}},o(i){r=r.filter(Boolean);for(let o=0;o<r.length;o+=1)m(r[o]);t=!1},d(i){i&&$(e),sn(r,i)}}}function Tl(s){let e,t,n=s[1],r=Hr(s);return{c(){e=M("nav"),r.c(),_(e,"class","c-navigation__nav svelte-n5ccbo"),_(e,"slot","left")},m(a,i){x(a,e,i),r.m(e,null),t=!0},p(a,i){2&i&&me(n,n=a[1])?(H(),m(r,1,1,G),W(),r=Hr(a),r.c(),p(r,1),r.m(e,null)):r.p(a,i)},i(a){t||(p(r),t=!0)},o(a){m(r),t=!1},d(a){a&&$(e),r.d(a)}}}function Ml(s){let e;const t=s[10].header,n=Fe(t,s,s[12],Ur);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||4099&a)&&ze(n,t,r,r[12],e?Ue(t,r[12],a,yl):De(r[12]),Ur)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function Wr(s){let e,t,n;const r=[s[0].props];var a=s[0].component;function i(o,c){let l={};for(let d=0;d<r.length;d+=1)l=ke(l,r[d]);return c!==void 0&&1&c&&(l=ke(l,yt(r,[sr(o[0].props)]))),{props:l}}return a&&(e=St(a,i(s))),{c(){e&&w(e.$$.fragment),t=Ce()},m(o,c){e&&b(e,o,c),x(o,t,c),n=!0},p(o,c){if(1&c&&a!==(a=o[0].component)){if(e){H();const l=e;m(l.$$.fragment,1,0,()=>{k(l,1)}),W()}a?(e=St(a,i(o,c)),w(e.$$.fragment),p(e.$$.fragment,1),b(e,t.parentNode,t)):e=null}else if(a){const l=1&c?yt(r,[sr(o[0].props)]):{};e.$set(l)}},i(o){n||(e&&p(e.$$.fragment,o),n=!0)},o(o){e&&m(e.$$.fragment,o),n=!1},d(o){o&&$(t),e&&k(e,o)}}}function Al(s){let e;const t=s[10].content,n=Fe(t,s,s[12],Vr),r=n||function(a){let i,o,c=Ar(a[0])&&Kr(a[0],a[2],a[1]),l=c&&Wr(a);return{c(){l&&l.c(),i=Ce()},m(d,u){l&&l.m(d,u),x(d,i,u),o=!0},p(d,u){7&u&&(c=Ar(d[0])&&Kr(d[0],d[2],d[1])),c?l?(l.p(d,u),7&u&&p(l,1)):(l=Wr(d),l.c(),p(l,1),l.m(i.parentNode,i)):l&&(H(),m(l,1,1,()=>{l=null}),W())},i(d){o||(p(l),o=!0)},o(d){m(l),o=!1},d(d){d&&$(i),l&&l.d(d)}}}(s);return{c(){r&&r.c()},m(a,i){r&&r.m(a,i),e=!0},p(a,i){n?n.p&&(!e||4099&i)&&ze(n,t,a,a[12],e?Ue(t,a[12],i,_l):De(a[12]),Vr):r&&r.p&&(!e||7&i)&&r.p(a,e?i:-1)},i(a){e||(p(r,a),e=!0)},o(a){m(r,a),e=!1},d(a){r&&r.d(a)}}}function Nl(s){let e,t;return e=new di({props:{item:s[0],slot:"right",$$slots:{content:[Al],header:[Ml]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};1&r&&(a.item=n[0]),4103&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Zl(s){let e,t,n,r,a;const i=[wl,xl],o=[];function c(l,d){return l[2]==="tree"?0:1}return t=c(s),n=o[t]=i[t](s),{c(){e=M("div"),n.c(),_(e,"class",r="c-navigation c-navigation--mode__"+s[2]+" "+s[4]+" svelte-n5ccbo")},m(l,d){x(l,e,d),o[t].m(e,null),a=!0},p(l,[d]){let u=t;t=c(l),t===u?o[t].p(l,d):(H(),m(o[u],1,1,()=>{o[u]=null}),W(),n=o[t],n?n.p(l,d):(n=o[t]=i[t](l),n.c()),p(n,1),n.m(e,null)),(!a||20&d&&r!==(r="c-navigation c-navigation--mode__"+l[2]+" "+l[4]+" svelte-n5ccbo"))&&_(e,"class",r)},i(l){a||(p(n),a=!0)},o(l){m(n),a=!1},d(l){l&&$(e),o[t].d()}}}function Xn(s,e,t,n,r,a){return{name:s,description:e,icon:t,id:n}}function Kr(s,e,t){return e!=="tree"||(s==null?void 0:s.id)===t}function El(s,e,t){let{$$slots:n={},$$scope:r}=e,{group:a="Workspace Settings"}=e,{items:i=[]}=e,{item:o}=e,{mode:c="tree"}=e,{selectedId:l}=e,{onNavigationChangeItem:d=f=>{}}=e,{showButton:u=!0}=e,{class:h=""}=e,v=new Map;function y(f){t(0,o=f),t(1,l=f==null?void 0:f.id)}return s.$$set=f=>{"group"in f&&t(7,a=f.group),"items"in f&&t(8,i=f.items),"item"in f&&t(0,o=f.item),"mode"in f&&t(2,c=f.mode),"selectedId"in f&&t(1,l=f.selectedId),"onNavigationChangeItem"in f&&t(9,d=f.onNavigationChangeItem),"showButton"in f&&t(3,u=f.showButton),"class"in f&&t(4,h=f.class),"$$scope"in f&&t(12,r=f.$$scope)},s.$$.update=()=>{259&s.$$.dirty&&(l?t(0,o=i.find(f=>(f==null?void 0:f.id)===l)):t(1,l=o==null?void 0:o.id)),384&s.$$.dirty&&t(5,v=i.reduce((f,g)=>{if(!g)return f;const S=g.group??a,I=f.get(S)??[];return I.push(g),f.set(S,I),f},new Map)),257&s.$$.dirty&&(o||t(0,o=i[0])),514&s.$$.dirty&&d(l)},[o,l,c,u,h,v,y,a,i,d,n,f=>y(f),r]}class Ol extends he{constructor(e){super(),ge(this,e,El,Zl,me,{group:7,items:8,item:0,mode:2,selectedId:1,onNavigationChangeItem:9,showButton:3,class:4})}}function Il(s){let e,t;return{c(){e=qe("svg"),t=qe("path"),_(t,"d","M3.13281 0.886719L5.97656 3.07422C6.14062 3.21094 6.25 3.40234 6.25 3.59375V5.07031L9.23047 8.05078C10.0234 7.66797 11.0078 7.80469 11.6641 8.46094L14.7266 11.5234C15.082 11.8516 15.082 12.4258 14.7266 12.7539L12.9766 14.5039C12.6484 14.8594 12.0742 14.8594 11.7461 14.5039L8.68359 11.4414C8.02734 10.7852 7.89062 9.77344 8.30078 8.98047L5.32031 6H3.81641C3.625 6 3.43359 5.91797 3.29688 5.75391L1.10938 2.91016C0.917969 2.63672 0.945312 2.28125 1.19141 2.03516L2.28516 0.941406C2.50391 0.722656 2.88672 0.695312 3.13281 0.886719ZM1.62891 11.0586L5.375 7.3125L6.30469 8.24219L2.55859 11.9883C2.39453 12.1523 2.3125 12.3711 2.3125 12.5898C2.3125 13.0547 2.69531 13.4375 3.16016 13.4375C3.37891 13.4375 3.59766 13.3555 3.76172 13.1914L7.17969 9.77344C7.15234 10.293 7.26172 10.8125 7.50781 11.3047L4.69141 14.1211C4.28125 14.5312 3.73438 14.75 3.16016 14.75C1.95703 14.75 1 13.793 1 12.5898C1 12.0156 1.21875 11.4688 1.62891 11.0586ZM13.6602 5.23438L12.9766 5.94531C12.6484 6.27344 12.2109 6.46484 11.7461 6.46484H11.0625C10.0781 6.46484 9.3125 5.67188 9.3125 4.71484V4.00391C9.3125 3.53906 9.47656 3.10156 9.80469 2.77344L10.5156 2.08984C8.875 2.14453 7.5625 3.48438 7.5625 5.125V5.15234L7.125 4.71484V3.59375C7.125 3.32031 7.04297 3.04688 6.90625 2.82812C7.67188 1.59766 9.03906 0.75 10.625 0.75C11.2812 0.75 11.9375 0.914062 12.5117 1.1875C12.7578 1.32422 12.7852 1.65234 12.5938 1.84375L10.7344 3.70312C10.6523 3.78516 10.625 3.89453 10.625 4.00391V4.6875C10.625 4.93359 10.8164 5.125 11.0625 5.125L11.7461 5.15234C11.8555 5.15234 11.9648 5.09766 12.0469 5.01562L13.9062 3.15625C14.0977 2.96484 14.4258 2.99219 14.5625 3.23828C14.8359 3.8125 15 4.46875 15 5.15234C15 6.60156 14.2617 7.91406 13.1406 8.70703L12.293 7.83203C12.2656 7.80469 12.2383 7.77734 12.2109 7.75C13.0586 7.23047 13.6328 6.30078 13.6602 5.23438Z"),_(t,"fill","currentColor"),_(e,"width","16"),_(e,"height","16"),_(e,"viewBox","0 0 16 16"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){x(n,e,r),C(e,t)},p:G,i:G,o:G,d(n){n&&$(e)}}}class Pl extends he{constructor(e){super(),ge(this,e,null,Il,me,{})}}function jl(s){let e,t;return{c(){e=qe("svg"),t=qe("path"),_(t,"d","M6.25 10.8125H12.375C12.5938 10.8125 12.8125 10.6211 12.8125 10.375V4.25H11.5C11.0078 4.25 10.625 3.86719 10.625 3.375V2.0625H6.25C6.00391 2.0625 5.8125 2.28125 5.8125 2.5V10.375C5.8125 10.6211 6.00391 10.8125 6.25 10.8125ZM12.375 12.125H6.25C5.26562 12.125 4.5 11.3594 4.5 10.375V2.5C4.5 1.54297 5.26562 0.75 6.25 0.75H10.7617C11.2266 0.75 11.6641 0.941406 11.9922 1.26953L13.6055 2.88281C13.9336 3.21094 14.125 3.64844 14.125 4.11328V10.375C14.125 11.3594 13.332 12.125 12.375 12.125ZM2.53125 3.375C2.88672 3.375 3.1875 3.67578 3.1875 4.03125V11.0312C3.1875 12.3711 4.25391 13.4375 5.59375 13.4375H10.8438C11.1992 13.4375 11.5 13.7383 11.5 14.0938C11.5 14.4766 11.1992 14.75 10.8438 14.75H5.59375C3.51562 14.75 1.875 13.1094 1.875 11.0312V4.03125C1.875 3.67578 2.14844 3.375 2.53125 3.375Z"),_(t,"fill","currentColor"),_(e,"width","16"),_(e,"height","16"),_(e,"viewBox","0 0 16 16"),_(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){x(n,e,r),C(e,t)},p:G,i:G,o:G,d(n){n&&$(e)}}}class Rl extends he{constructor(e){super(),ge(this,e,null,jl,me,{})}}const Ll=s=>({}),Yr=s=>({}),Fl=s=>({}),Xr=s=>({});function zl(s){let e;const t=s[8]["header-left"],n=Fe(t,s,s[10],Xr);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||1024&a)&&ze(n,t,r,r[10],e?Ue(t,r[10],a,Fl):De(r[10]),Xr)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function Dl(s){let e,t,n,r=s[0]&&Qr(s),a=s[1]&&ea(s);return{c(){r&&r.c(),e=Z(),a&&a.c(),t=Ce()},m(i,o){r&&r.m(i,o),x(i,e,o),a&&a.m(i,o),x(i,t,o),n=!0},p(i,o){i[0]?r?(r.p(i,o),1&o&&p(r,1)):(r=Qr(i),r.c(),p(r,1),r.m(e.parentNode,e)):r&&(H(),m(r,1,1,()=>{r=null}),W()),i[1]?a?(a.p(i,o),2&o&&p(a,1)):(a=ea(i),a.c(),p(a,1),a.m(t.parentNode,t)):a&&(H(),m(a,1,1,()=>{a=null}),W())},i(i){n||(p(r),p(a),n=!0)},o(i){m(r),m(a),n=!1},d(i){i&&($(e),$(t)),r&&r.d(i),a&&a.d(i)}}}function Qr(s){let e,t,n;var r=s[0];return r&&(t=St(r,{})),{c(){e=M("div"),t&&w(t.$$.fragment),_(e,"class","icon-wrapper svelte-13uht7n")},m(a,i){x(a,e,i),t&&b(t,e,null),n=!0},p(a,i){if(1&i&&r!==(r=a[0])){if(t){H();const o=t;m(o.$$.fragment,1,0,()=>{k(o,1)}),W()}r?(t=St(r,{}),w(t.$$.fragment),p(t.$$.fragment,1),b(t,e,null)):t=null}},i(a){n||(t&&p(t.$$.fragment,a),n=!0)},o(a){t&&m(t.$$.fragment,a),n=!1},d(a){a&&$(e),t&&k(t)}}}function ea(s){let e,t;return e=new de({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[Ul]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};1026&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Ul(s){let e;return{c(){e=F(s[1])},m(t,n){x(t,e,n)},p(t,n){2&n&&we(e,t[1])},d(t){t&&$(e)}}}function ta(s){let e,t;const n=s[8].default,r=Fe(n,s,s[10],null);return{c(){e=M("div"),r&&r.c(),_(e,"class","settings-card-body")},m(a,i){x(a,e,i),r&&r.m(e,null),t=!0},p(a,i){r&&r.p&&(!t||1024&i)&&ze(r,n,a,a[10],t?Ue(n,a[10],i,null):De(a[10]),null)},i(a){t||(p(r,a),t=!0)},o(a){m(r,a),t=!1},d(a){a&&$(e),r&&r.d(a)}}}function Vl(s){let e,t,n,r,a,i,o,c,l,d,u;const h=[Dl,zl],v=[];function y(T,A){return T[0]||T[1]?0:1}r=y(s),a=v[r]=h[r](s);const f=s[8]["header-right"],g=Fe(f,s,s[10],Yr);let S=s[5].default&&ta(s),I=[{role:"button"},{class:s[3]},s[4]],N={};for(let T=0;T<I.length;T+=1)N=ke(N,I[T]);return{c(){e=M("div"),t=M("div"),n=M("div"),a.c(),i=Z(),o=M("div"),g&&g.c(),c=Z(),S&&S.c(),_(n,"class","settings-card-left svelte-13uht7n"),_(o,"class","settings-card-right svelte-13uht7n"),_(t,"class","settings-card-content svelte-13uht7n"),ts(e,N),xe(e,"clickable",s[2]),xe(e,"svelte-13uht7n",!0)},m(T,A){x(T,e,A),C(e,t),C(t,n),v[r].m(n,null),C(t,i),C(t,o),g&&g.m(o,null),C(e,c),S&&S.m(e,null),l=!0,d||(u=Ge(e,"click",s[9]),d=!0)},p(T,[A]){let P=r;r=y(T),r===P?v[r].p(T,A):(H(),m(v[P],1,1,()=>{v[P]=null}),W(),a=v[r],a?a.p(T,A):(a=v[r]=h[r](T),a.c()),p(a,1),a.m(n,null)),g&&g.p&&(!l||1024&A)&&ze(g,f,T,T[10],l?Ue(f,T[10],A,Ll):De(T[10]),Yr),T[5].default?S?(S.p(T,A),32&A&&p(S,1)):(S=ta(T),S.c(),p(S,1),S.m(e,null)):S&&(H(),m(S,1,1,()=>{S=null}),W()),ts(e,N=yt(I,[{role:"button"},(!l||8&A)&&{class:T[3]},16&A&&T[4]])),xe(e,"clickable",T[2]),xe(e,"svelte-13uht7n",!0)},i(T){l||(p(a),p(g,T),p(S),l=!0)},o(T){m(a),m(g,T),m(S),l=!1},d(T){T&&$(e),v[r].d(),g&&g.d(T),S&&S.d(),d=!1,u()}}}function ql(s,e,t){let n,r,a;const i=["class","icon","title","isClickable"];let o=ns(e,i),{$$slots:c={},$$scope:l}=e;const d=Oa(c);let{class:u=""}=e,{icon:h}=e,{title:v}=e,{isClickable:y=!1}=e;return s.$$set=f=>{e=ke(ke({},e),Xe(f)),t(11,o=ns(e,i)),"class"in f&&t(6,u=f.class),"icon"in f&&t(0,h=f.icon),"title"in f&&t(1,v=f.title),"isClickable"in f&&t(2,y=f.isClickable),"$$scope"in f&&t(10,l=f.$$scope)},s.$$.update=()=>{t(7,{class:n,...r}=o,n,(t(4,r),t(11,o))),192&s.$$.dirty&&t(3,a=`settings-card ${u} ${n||""}`)},[h,v,y,a,r,d,u,n,c,function(f){Ia.call(this,s,f)},l]}class ws extends he{constructor(e){super(),ge(this,e,ql,Vl,me,{class:6,icon:0,title:1,isClickable:2})}}function Bl(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 17 16"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=ke(r,n[a]);return{c(){e=qe("svg"),t=new Un(!0),this.h()},l(a){e=Vn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=qn(e);t=Bn(i,!0),i.forEach($),this.h()},h(){t.a=null,lt(e,r)},m(a,i){Jn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M3.552 7.158a.568.568 0 0 1 .804 0l.702.702L6.23 6.688c.2-.2.511-.22.734-.06l.07.06a.568.568 0 0 1 0 .804L5.862 8.664l1.626 1.626 1.173-1.172c.2-.2.511-.22.733-.06l.071.06a.568.568 0 0 1 0 .804l-1.173 1.172.703.703c.2.2.22.51.06.733l-.06.07a.568.568 0 0 1-.804 0l-.041-.039-.812.813a3.226 3.226 0 0 1-4.043.421l-.08-.054-.959.96c-.2.2-.511.22-.733.06l-.071-.06a.568.568 0 0 1 0-.804l.96-.96-.054-.079a3.226 3.226 0 0 1 .294-3.91l.127-.133.811-.813-.038-.04a.567.567 0 0 1-.06-.734zm3.759-3.759a.568.568 0 0 1 .804 0l.038.04.815-.813a3.226 3.226 0 0 1 4.043-.421l.078.054.96-.96c.2-.2.511-.22.734-.06l.07.06a.568.568 0 0 1 0 .804l-.96.96.055.079a3.226 3.226 0 0 1-.295 3.91l-.126.133-.814.813.04.04c.201.2.221.511.06.734l-.06.07a.568.568 0 0 1-.804 0L7.31 4.204a.568.568 0 0 1 0-.805m2.39-.04-.884.884 3.093 3.093.884-.884A2.186 2.186 0 1 0 9.7 3.359M4.396 8.664l-.884.884a2.186 2.186 0 1 0 3.092 3.093l.884-.884z"/>',e)},p(a,[i]){lt(e,r=yt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 17 16"},1&i&&a[0]]))},i:G,o:G,d(a){a&&$(e)}}}function Jl(s,e,t){return s.$$set=n=>{t(0,e=ke(ke({},e),Xe(n)))},[e=Xe(e)]}class Gl extends he{constructor(e){super(),ge(this,e,Jl,Bl,me,{})}}function Hl(s){let e,t,n,r,a,i,o,c;return a=new Gt({props:{triggerOn:[oo.Hover],content:"Revoke Access",$$slots:{default:[Yl]},$$scope:{ctx:s}}}),o=new La.Root({props:{color:"success",size:1,variant:"soft",$$slots:{default:[Xl]},$$scope:{ctx:s}}}),{c(){e=M("div"),t=M("div"),n=M("div"),r=M("div"),w(a.$$.fragment),i=Z(),w(o.$$.fragment),_(r,"class","icon-button-wrapper svelte-js5lik"),xe(r,"active",s[3]),_(n,"class","connection-status svelte-js5lik"),_(t,"class","icon-container svelte-js5lik"),_(e,"class","status-controls svelte-js5lik")},m(l,d){x(l,e,d),C(e,t),C(t,n),C(n,r),b(a,r,null),C(t,i),b(o,t,null),c=!0},p(l,d){const u={};1027&d&&(u.$$scope={dirty:d,ctx:l}),a.$set(u),(!c||8&d)&&xe(r,"active",l[3]);const h={};1024&d&&(h.$$scope={dirty:d,ctx:l}),o.$set(h)},i(l){c||(p(a.$$.fragment,l),p(o.$$.fragment,l),c=!0)},o(l){m(a.$$.fragment,l),m(o.$$.fragment,l),c=!1},d(l){l&&$(e),k(a),k(o)}}}function Wl(s){let e,t;return e=new Ne({props:{variant:"ghost-block",color:s[2]?"neutral":"accent",size:1,$$slots:{default:[td]},$$scope:{ctx:s}}}),e.$on("click",s[4]),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};4&r&&(a.color=n[2]?"neutral":"accent"),1028&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Kl(s){let e,t;return e=new Gl({}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Yl(s){let e,t;return e=new xs({props:{color:"neutral",variant:"ghost",size:1,$$slots:{default:[Kl]},$$scope:{ctx:s}}}),e.$on("click",s[7]),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};1024&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Xl(s){let e;return{c(){e=F("Connected")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function Ql(s){let e;return{c(){e=M("span"),e.textContent="Connect"},m(t,n){x(t,e,n)},i:G,o:G,d(t){t&&$(e)}}}function ed(s){let e,t,n,r,a;return t=new Pa({props:{size:1,useCurrentColor:!0}}),{c(){e=M("div"),w(t.$$.fragment),n=Z(),r=M("span"),r.textContent="Cancel",_(e,"class","connect-button-spinner svelte-js5lik")},m(i,o){x(i,e,o),b(t,e,null),x(i,n,o),x(i,r,o),a=!0},i(i){a||(p(t.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),a=!1},d(i){i&&($(e),$(n),$(r)),k(t)}}}function td(s){let e,t,n,r;const a=[ed,Ql],i=[];function o(c,l){return c[2]?0:1}return t=o(s),n=i[t]=a[t](s),{c(){e=M("div"),n.c(),_(e,"class","connect-button-content svelte-js5lik")},m(c,l){x(c,e,l),i[t].m(e,null),r=!0},p(c,l){let d=t;t=o(c),t!==d&&(H(),m(i[d],1,1,()=>{i[d]=null}),W(),n=i[t],n||(n=i[t]=a[t](c),n.c()),p(n,1),n.m(e,null))},i(c){r||(p(n),r=!0)},o(c){m(n),r=!1},d(c){c&&$(e),i[t].d()}}}function nd(s){let e,t,n,r;const a=[Wl,Hl],i=[];function o(c,l){return!c[0].isConfigured&&c[0].authUrl?0:c[0].isConfigured?1:-1}return~(t=o(s))&&(n=i[t]=a[t](s)),{c(){e=M("div"),n&&n.c(),_(e,"slot","header-right")},m(c,l){x(c,e,l),~t&&i[t].m(e,null),r=!0},p(c,l){let d=t;t=o(c),t===d?~t&&i[t].p(c,l):(n&&(H(),m(i[d],1,1,()=>{i[d]=null}),W()),~t?(n=i[t],n?n.p(c,l):(n=i[t]=a[t](c),n.c()),p(n,1),n.m(e,null)):n=null)},i(c){r||(p(n),r=!0)},o(c){m(n),r=!1},d(c){c&&$(e),~t&&i[t].d()}}}function na(s){let e,t,n,r=s[0].statusMessage+"";return{c(){e=M("div"),t=F(r),_(e,"class",n="status-message "+s[0].statusType+" svelte-js5lik")},m(a,i){x(a,e,i),C(e,t)},p(a,i){1&i&&r!==(r=a[0].statusMessage+"")&&we(t,r),1&i&&n!==(n="status-message "+a[0].statusType+" svelte-js5lik")&&_(e,"class",n)},d(a){a&&$(e)}}}function sd(s){let e,t,n,r,a,i;t=new ws({props:{icon:s[0].icon,title:s[0].displayName,$$slots:{"header-right":[nd]},$$scope:{ctx:s}}});let o=s[0].showStatus&&na(s);return{c(){e=M("div"),w(t.$$.fragment),n=Z(),o&&o.c(),_(e,"class","config-wrapper"),_(e,"role","group"),_(e,"aria-label","Connection status controls")},m(c,l){x(c,e,l),b(t,e,null),C(e,n),o&&o.m(e,null),r=!0,a||(i=[Ge(e,"mouseenter",s[8]),Ge(e,"mouseleave",s[9])],a=!0)},p(c,[l]){const d={};1&l&&(d.icon=c[0].icon),1&l&&(d.title=c[0].displayName),1039&l&&(d.$$scope={dirty:l,ctx:c}),t.$set(d),c[0].showStatus?o?o.p(c,l):(o=na(c),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},i(c){r||(p(t.$$.fragment,c),r=!0)},o(c){m(t.$$.fragment,c),r=!1},d(c){c&&$(e),k(t),o&&o.d(),a=!1,Bs(i)}}}function rd(s,e,t){let{config:n}=e,{onAuthenticate:r}=e,{onRevokeAccess:a}=e,i=!1,o=null,c=!1;return s.$$set=l=>{"config"in l&&t(0,n=l.config),"onAuthenticate"in l&&t(5,r=l.onAuthenticate),"onRevokeAccess"in l&&t(1,a=l.onRevokeAccess)},s.$$.update=()=>{69&s.$$.dirty&&n.isConfigured&&i&&(t(2,i=!1),o&&(clearTimeout(o),t(6,o=null)))},[n,a,i,c,function(){if(i)t(2,i=!1),o&&(clearTimeout(o),t(6,o=null));else{t(2,i=!0);const l=n.authUrl||"";r(l),t(6,o=setTimeout(()=>{t(2,i=!1),t(6,o=null)},6e4))}},r,o,()=>a(n),()=>t(3,c=!0),()=>t(3,c=!1)]}class ad extends he{constructor(e){super(),ge(this,e,rd,sd,me,{config:0,onAuthenticate:5,onRevokeAccess:1})}}function id(s){let e;return{c(){e=F(s[0])},m(t,n){x(t,e,n)},p(t,n){1&n&&we(e,t[0])},d(t){t&&$(e)}}}function od(s){let e,t;const n=s[2].default,r=Fe(n,s,s[3],null);return{c(){e=M("div"),r&&r.c(),_(e,"class","category-content")},m(a,i){x(a,e,i),r&&r.m(e,null),t=!0},p(a,i){r&&r.p&&(!t||8&i)&&ze(r,n,a,a[3],t?Ue(n,a[3],i,null):De(a[3]),null)},i(a){t||(p(r,a),t=!0)},o(a){m(r,a),t=!1},d(a){a&&$(e),r&&r.d(a)}}}function cd(s){let e,t,n,r,a;return t=new Pa({props:{size:1}}),r=new de({props:{size:1,color:"secondary",$$slots:{default:[ld]},$$scope:{ctx:s}}}),{c(){e=M("div"),w(t.$$.fragment),n=Z(),w(r.$$.fragment),_(e,"class","loading-container svelte-2bsejd")},m(i,o){x(i,e,o),b(t,e,null),C(e,n),b(r,e,null),a=!0},p(i,o){const c={};8&o&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&$(e),k(t),k(r)}}}function ld(s){let e;return{c(){e=F("Loading...")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function dd(s){let e,t,n,r,a,i,o;n=new de({props:{size:1,color:"secondary",weight:"regular",$$slots:{default:[id]},$$scope:{ctx:s}}});const c=[cd,od],l=[];function d(u,h){return u[1]?0:1}return a=d(s),i=l[a]=c[a](s),{c(){e=M("div"),t=M("div"),w(n.$$.fragment),r=Z(),i.c(),_(t,"class","category-heading"),_(e,"class","category")},m(u,h){x(u,e,h),C(e,t),b(n,t,null),C(e,r),l[a].m(e,null),o=!0},p(u,[h]){const v={};9&h&&(v.$$scope={dirty:h,ctx:u}),n.$set(v);let y=a;a=d(u),a===y?l[a].p(u,h):(H(),m(l[y],1,1,()=>{l[y]=null}),W(),i=l[a],i?i.p(u,h):(i=l[a]=c[a](u),i.c()),p(i,1),i.m(e,null))},i(u){o||(p(n.$$.fragment,u),p(i),o=!0)},o(u){m(n.$$.fragment,u),m(i),o=!1},d(u){u&&$(e),k(n),l[a].d()}}}function ud(s,e,t){let{$$slots:n={},$$scope:r}=e,{title:a}=e,{loading:i=!1}=e;return s.$$set=o=>{"title"in o&&t(0,a=o.title),"loading"in o&&t(1,i=o.loading),"$$scope"in o&&t(3,r=o.$$scope)},[a,i,n,r]}class pd extends he{constructor(e){super(),ge(this,e,ud,dd,me,{title:0,loading:1})}}function sa(s,e,t){const n=s.slice();return n[4]=e[t],n}function ra(s){let e,t;return e=new ad({props:{config:s[4],onAuthenticate:s[2],onRevokeAccess:s[3]}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};2&r&&(a.config=n[4]),4&r&&(a.onAuthenticate=n[2]),8&r&&(a.onRevokeAccess=n[3]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function md(s){let e,t,n=$e(s[1]),r=[];for(let i=0;i<n.length;i+=1)r[i]=ra(sa(s,n,i));const a=i=>m(r[i],1,1,()=>{r[i]=null});return{c(){e=M("div");for(let i=0;i<r.length;i+=1)r[i].c();_(e,"class","tool-category-list svelte-on3wl5")},m(i,o){x(i,e,o);for(let c=0;c<r.length;c+=1)r[c]&&r[c].m(e,null);t=!0},p(i,o){if(14&o){let c;for(n=$e(i[1]),c=0;c<n.length;c+=1){const l=sa(i,n,c);r[c]?(r[c].p(l,o),p(r[c],1)):(r[c]=ra(l),r[c].c(),p(r[c],1),r[c].m(e,null))}for(H(),c=n.length;c<r.length;c+=1)a(c);W()}},i(i){if(!t){for(let o=0;o<n.length;o+=1)p(r[o]);t=!0}},o(i){r=r.filter(Boolean);for(let o=0;o<r.length;o+=1)m(r[o]);t=!1},d(i){i&&$(e),sn(r,i)}}}function fd(s){let e,t;return e=new pd({props:{title:s[0],loading:s[1].length===0,$$slots:{default:[md]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,[r]){const a={};1&r&&(a.title=n[0]),2&r&&(a.loading=n[1].length===0),142&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function hd(s,e,t){let{title:n}=e,{tools:r=[]}=e,{onAuthenticate:a}=e,{onRevokeAccess:i}=e;return s.$$set=o=>{"title"in o&&t(0,n=o.title),"tools"in o&&t(1,r=o.tools),"onAuthenticate"in o&&t(2,a=o.onAuthenticate),"onRevokeAccess"in o&&t(3,i=o.onRevokeAccess)},[n,r,a,i]}class gd extends he{constructor(e){super(),ge(this,e,hd,fd,me,{title:0,tools:1,onAuthenticate:2,onRevokeAccess:3})}}var pe,Ds;(function(s){s.assertEqual=e=>e,s.assertIs=function(e){},s.assertNever=function(e){throw new Error},s.arrayToEnum=e=>{const t={};for(const n of e)t[n]=n;return t},s.getValidEnumValues=e=>{const t=s.objectKeys(e).filter(r=>typeof e[e[r]]!="number"),n={};for(const r of t)n[r]=e[r];return s.objectValues(n)},s.objectValues=e=>s.objectKeys(e).map(function(t){return e[t]}),s.objectKeys=typeof Object.keys=="function"?e=>Object.keys(e):e=>{const t=[];for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},s.find=(e,t)=>{for(const n of e)if(t(n))return n},s.isInteger=typeof Number.isInteger=="function"?e=>Number.isInteger(e):e=>typeof e=="number"&&isFinite(e)&&Math.floor(e)===e,s.joinValues=function(e,t=" | "){return e.map(n=>typeof n=="string"?`'${n}'`:n).join(t)},s.jsonStringifyReplacer=(e,t)=>typeof t=="bigint"?t.toString():t})(pe||(pe={})),function(s){s.mergeShapes=(e,t)=>({...e,...t})}(Ds||(Ds={}));const D=pe.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),ht=s=>{switch(typeof s){case"undefined":return D.undefined;case"string":return D.string;case"number":return isNaN(s)?D.nan:D.number;case"boolean":return D.boolean;case"function":return D.function;case"bigint":return D.bigint;case"symbol":return D.symbol;case"object":return Array.isArray(s)?D.array:s===null?D.null:s.then&&typeof s.then=="function"&&s.catch&&typeof s.catch=="function"?D.promise:typeof Map<"u"&&s instanceof Map?D.map:typeof Set<"u"&&s instanceof Set?D.set:typeof Date<"u"&&s instanceof Date?D.date:D.object;default:return D.unknown}},O=pe.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class Je extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(a){return a.message},n={_errors:[]},r=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(r);else if(i.code==="invalid_return_type")r(i.returnTypeError);else if(i.code==="invalid_arguments")r(i.argumentsError);else if(i.path.length===0)n._errors.push(t(i));else{let o=n,c=0;for(;c<i.path.length;){const l=i.path[c];c===i.path.length-1?(o[l]=o[l]||{_errors:[]},o[l]._errors.push(t(i))):o[l]=o[l]||{_errors:[]},o=o[l],c++}}};return r(this),n}static assert(e){if(!(e instanceof Je))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,pe.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},n=[];for(const r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}}Je.create=s=>new Je(s);const en=(s,e)=>{let t;switch(s.code){case O.invalid_type:t=s.received===D.undefined?"Required":`Expected ${s.expected}, received ${s.received}`;break;case O.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(s.expected,pe.jsonStringifyReplacer)}`;break;case O.unrecognized_keys:t=`Unrecognized key(s) in object: ${pe.joinValues(s.keys,", ")}`;break;case O.invalid_union:t="Invalid input";break;case O.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${pe.joinValues(s.options)}`;break;case O.invalid_enum_value:t=`Invalid enum value. Expected ${pe.joinValues(s.options)}, received '${s.received}'`;break;case O.invalid_arguments:t="Invalid function arguments";break;case O.invalid_return_type:t="Invalid function return type";break;case O.invalid_date:t="Invalid date";break;case O.invalid_string:typeof s.validation=="object"?"includes"in s.validation?(t=`Invalid input: must include "${s.validation.includes}"`,typeof s.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${s.validation.position}`)):"startsWith"in s.validation?t=`Invalid input: must start with "${s.validation.startsWith}"`:"endsWith"in s.validation?t=`Invalid input: must end with "${s.validation.endsWith}"`:pe.assertNever(s.validation):t=s.validation!=="regex"?`Invalid ${s.validation}`:"Invalid";break;case O.too_small:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at least":"more than"} ${s.minimum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at least":"over"} ${s.minimum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${s.minimum}`:s.type==="date"?`Date must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(s.minimum))}`:"Invalid input";break;case O.too_big:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at most":"less than"} ${s.maximum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at most":"under"} ${s.maximum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="bigint"?`BigInt must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="date"?`Date must be ${s.exact?"exactly":s.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(s.maximum))}`:"Invalid input";break;case O.custom:t="Invalid input";break;case O.invalid_intersection_types:t="Intersection results could not be merged";break;case O.not_multiple_of:t=`Number must be a multiple of ${s.multipleOf}`;break;case O.not_finite:t="Number must be finite";break;default:t=e.defaultError,pe.assertNever(s)}return{message:t}};let ui=en;function ps(){return ui}const ms=s=>{const{data:e,path:t,errorMaps:n,issueData:r}=s,a=[...t,...r.path||[]],i={...r,path:a};if(r.message!==void 0)return{...r,path:a,message:r.message};let o="";const c=n.filter(l=>!!l).slice().reverse();for(const l of c)o=l(i,{data:e,defaultError:o}).message;return{...r,path:a,message:o}};function R(s,e){const t=ps(),n=ms({issueData:e,data:s.data,path:s.path,errorMaps:[s.common.contextualErrorMap,s.schemaErrorMap,t,t===en?void 0:en].filter(r=>!!r)});s.common.issues.push(n)}class je{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const r of t){if(r.status==="aborted")return te;r.status==="dirty"&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const n=[];for(const r of t){const a=await r.key,i=await r.value;n.push({key:a,value:i})}return je.mergeObjectSync(e,n)}static mergeObjectSync(e,t){const n={};for(const r of t){const{key:a,value:i}=r;if(a.status==="aborted"||i.status==="aborted")return te;a.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),a.value==="__proto__"||i.value===void 0&&!r.alwaysSet||(n[a.value]=i.value)}return{status:e.value,value:n}}}const te=Object.freeze({status:"aborted"}),fs=s=>({status:"dirty",value:s}),Le=s=>({status:"valid",value:s}),Us=s=>s.status==="aborted",Vs=s=>s.status==="dirty",Ft=s=>s.status==="valid",Nn=s=>typeof Promise<"u"&&s instanceof Promise;function hs(s,e,t,n){if(typeof e=="function"?s!==e||!n:!e.has(s))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(s)}function pi(s,e,t,n,r){if(typeof e=="function"?s!==e||!r:!e.has(s))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(s,t),t}var B,un,pn;typeof SuppressedError=="function"&&SuppressedError,function(s){s.errToObj=e=>typeof e=="string"?{message:e}:e||{},s.toString=e=>typeof e=="string"?e:e==null?void 0:e.message}(B||(B={}));class ut{constructor(e,t,n,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=r}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const aa=(s,e)=>{if(Ft(e))return{success:!0,data:e.value};if(!s.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new Je(s.common.issues);return this._error=t,this._error}}};function ae(s){if(!s)return{};const{errorMap:e,invalid_type_error:t,required_error:n,description:r}=s;if(e&&(t||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:r}:{errorMap:(a,i)=>{var o,c;const{message:l}=s;return a.code==="invalid_enum_value"?{message:l??i.defaultError}:i.data===void 0?{message:(o=l??n)!==null&&o!==void 0?o:i.defaultError}:a.code!=="invalid_type"?{message:i.defaultError}:{message:(c=l??t)!==null&&c!==void 0?c:i.defaultError}},description:r}}class le{get description(){return this._def.description}_getType(e){return ht(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:ht(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new je,ctx:{common:e.parent.common,data:e.data,parsedType:ht(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(Nn(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){var n;const r={common:{issues:[],async:(n=t==null?void 0:t.async)!==null&&n!==void 0&&n,contextualErrorMap:t==null?void 0:t.errorMap},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ht(e)},a=this._parseSync({data:e,path:r.path,parent:r});return aa(r,a)}"~validate"(e){var t,n;const r={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ht(e)};if(!this["~standard"].async)try{const a=this._parseSync({data:e,path:[],parent:r});return Ft(a)?{value:a.value}:{issues:r.common.issues}}catch(a){!((n=(t=a==null?void 0:a.message)===null||t===void 0?void 0:t.toLowerCase())===null||n===void 0)&&n.includes("encountered")&&(this["~standard"].async=!0),r.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:r}).then(a=>Ft(a)?{value:a.value}:{issues:r.common.issues})}async parseAsync(e,t){const n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){const n={common:{issues:[],contextualErrorMap:t==null?void 0:t.errorMap,async:!0},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ht(e)},r=this._parse({data:e,path:n.path,parent:n}),a=await(Nn(r)?r:Promise.resolve(r));return aa(n,a)}refine(e,t){const n=r=>typeof t=="string"||t===void 0?{message:t}:typeof t=="function"?t(r):t;return this._refinement((r,a)=>{const i=e(r),o=()=>a.addIssue({code:O.custom,...n(r)});return typeof Promise<"u"&&i instanceof Promise?i.then(c=>!!c||(o(),!1)):!!i||(o(),!1)})}refinement(e,t){return this._refinement((n,r)=>!!e(n)||(r.addIssue(typeof t=="function"?t(n,r):t),!1))}_refinement(e){return new rt({schema:this,typeName:Q.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return ct.create(this,this._def)}nullable(){return Zt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return it.create(this)}promise(){return nn.create(this,this._def)}or(e){return In.create([this,e],this._def)}and(e){return Pn.create(this,e,this._def)}transform(e){return new rt({...ae(this._def),schema:this,typeName:Q.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t=typeof e=="function"?e:()=>e;return new Fn({...ae(this._def),innerType:this,defaultValue:t,typeName:Q.ZodDefault})}brand(){return new Ws({typeName:Q.ZodBranded,type:this,...ae(this._def)})}catch(e){const t=typeof e=="function"?e:()=>e;return new zn({...ae(this._def),innerType:this,catchValue:t,typeName:Q.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return Hn.create(this,e)}readonly(){return Dn.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const vd=/^c[^\s-]{8,}$/i,$d=/^[0-9a-z]+$/,yd=/^[0-9A-HJKMNP-TV-Z]{26}$/i,_d=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,xd=/^[a-z0-9_-]{21}$/i,wd=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,bd=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,kd=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let Ns;const Sd=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Cd=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Td=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Md=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Ad=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Nd=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,mi="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Zd=new RegExp(`^${mi}$`);function fi(s){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return s.precision?e=`${e}\\.\\d{${s.precision}}`:s.precision==null&&(e=`${e}(\\.\\d+)?`),e}function hi(s){let e=`${mi}T${fi(s)}`;const t=[];return t.push(s.local?"Z?":"Z"),s.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function Ed(s,e){if(!wd.test(s))return!1;try{const[t]=s.split("."),n=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),r=JSON.parse(atob(n));return typeof r=="object"&&r!==null&&!(!r.typ||!r.alg)&&(!e||r.alg===e)}catch{return!1}}function Od(s,e){return!(e!=="v4"&&e||!Cd.test(s))||!(e!=="v6"&&e||!Md.test(s))}class st extends le{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==D.string){const i=this._getOrReturnCtx(e);return R(i,{code:O.invalid_type,expected:D.string,received:i.parsedType}),te}const t=new je;let n;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(n=this._getOrReturnCtx(e,n),R(n,{code:O.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="max")e.data.length>i.value&&(n=this._getOrReturnCtx(e,n),R(n,{code:O.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="length"){const o=e.data.length>i.value,c=e.data.length<i.value;(o||c)&&(n=this._getOrReturnCtx(e,n),o?R(n,{code:O.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):c&&R(n,{code:O.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if(i.kind==="email")kd.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"email",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="emoji")Ns||(Ns=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),Ns.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"emoji",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="uuid")_d.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"uuid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="nanoid")xd.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"nanoid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid")vd.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"cuid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid2")$d.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"cuid2",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="ulid")yd.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"ulid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),R(n,{validation:"url",code:O.invalid_string,message:i.message}),t.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"regex",code:O.invalid_string,message:i.message}),t.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(n=this._getOrReturnCtx(e,n),R(n,{code:O.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(n=this._getOrReturnCtx(e,n),R(n,{code:O.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(n=this._getOrReturnCtx(e,n),R(n,{code:O.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty()):i.kind==="datetime"?hi(i).test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{code:O.invalid_string,validation:"datetime",message:i.message}),t.dirty()):i.kind==="date"?Zd.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{code:O.invalid_string,validation:"date",message:i.message}),t.dirty()):i.kind==="time"?new RegExp(`^${fi(i)}$`).test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{code:O.invalid_string,validation:"time",message:i.message}),t.dirty()):i.kind==="duration"?bd.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"duration",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="ip"?(r=e.data,((a=i.version)!=="v4"&&a||!Sd.test(r))&&(a!=="v6"&&a||!Td.test(r))&&(n=this._getOrReturnCtx(e,n),R(n,{validation:"ip",code:O.invalid_string,message:i.message}),t.dirty())):i.kind==="jwt"?Ed(e.data,i.alg)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"jwt",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="cidr"?Od(e.data,i.version)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"cidr",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="base64"?Ad.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"base64",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="base64url"?Nd.test(e.data)||(n=this._getOrReturnCtx(e,n),R(n,{validation:"base64url",code:O.invalid_string,message:i.message}),t.dirty()):pe.assertNever(i);var r,a;return{status:t.value,value:e.data}}_regex(e,t,n){return this.refinement(r=>e.test(r),{validation:t,code:O.invalid_string,...B.errToObj(n)})}_addCheck(e){return new st({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...B.errToObj(e)})}url(e){return this._addCheck({kind:"url",...B.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...B.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...B.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...B.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...B.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...B.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...B.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...B.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...B.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...B.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...B.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...B.errToObj(e)})}datetime(e){var t,n;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!==null&&t!==void 0&&t,local:(n=e==null?void 0:e.local)!==null&&n!==void 0&&n,...B.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,...B.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...B.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...B.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...B.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...B.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...B.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...B.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...B.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...B.errToObj(t)})}nonempty(e){return this.min(1,B.errToObj(e))}trim(){return new st({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new st({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new st({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}function Id(s,e){const t=(s.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,r=t>n?t:n;return parseInt(s.toFixed(r).replace(".",""))%parseInt(e.toFixed(r).replace(".",""))/Math.pow(10,r)}st.create=s=>{var e;return new st({checks:[],typeName:Q.ZodString,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...ae(s)})};class Mt extends le{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==D.number){const r=this._getOrReturnCtx(e);return R(r,{code:O.invalid_type,expected:D.number,received:r.parsedType}),te}let t;const n=new je;for(const r of this._def.checks)r.kind==="int"?pe.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),R(t,{code:O.invalid_type,expected:"integer",received:"float",message:r.message}),n.dirty()):r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),R(t,{code:O.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),R(t,{code:O.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="multipleOf"?Id(e.data,r.value)!==0&&(t=this._getOrReturnCtx(e,t),R(t,{code:O.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):r.kind==="finite"?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),R(t,{code:O.not_finite,message:r.message}),n.dirty()):pe.assertNever(r);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,B.toString(t))}gt(e,t){return this.setLimit("min",e,!1,B.toString(t))}lte(e,t){return this.setLimit("max",e,!0,B.toString(t))}lt(e,t){return this.setLimit("max",e,!1,B.toString(t))}setLimit(e,t,n,r){return new Mt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:B.toString(r)}]})}_addCheck(e){return new Mt({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:B.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:B.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:B.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:B.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:B.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:B.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:B.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:B.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:B.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&pe.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(t===null||n.value>t)&&(t=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}}Mt.create=s=>new Mt({checks:[],typeName:Q.ZodNumber,coerce:(s==null?void 0:s.coerce)||!1,...ae(s)});class At extends le{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==D.bigint)return this._getInvalidInput(e);let t;const n=new je;for(const r of this._def.checks)r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),R(t,{code:O.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),R(t,{code:O.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="multipleOf"?e.data%r.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),R(t,{code:O.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):pe.assertNever(r);return{status:n.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return R(t,{code:O.invalid_type,expected:D.bigint,received:t.parsedType}),te}gte(e,t){return this.setLimit("min",e,!0,B.toString(t))}gt(e,t){return this.setLimit("min",e,!1,B.toString(t))}lte(e,t){return this.setLimit("max",e,!0,B.toString(t))}lt(e,t){return this.setLimit("max",e,!1,B.toString(t))}setLimit(e,t,n,r){return new At({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:B.toString(r)}]})}_addCheck(e){return new At({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:B.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:B.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:B.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:B.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:B.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}At.create=s=>{var e;return new At({checks:[],typeName:Q.ZodBigInt,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...ae(s)})};class Zn extends le{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==D.boolean){const t=this._getOrReturnCtx(e);return R(t,{code:O.invalid_type,expected:D.boolean,received:t.parsedType}),te}return Le(e.data)}}Zn.create=s=>new Zn({typeName:Q.ZodBoolean,coerce:(s==null?void 0:s.coerce)||!1,...ae(s)});class zt extends le{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==D.date){const r=this._getOrReturnCtx(e);return R(r,{code:O.invalid_type,expected:D.date,received:r.parsedType}),te}if(isNaN(e.data.getTime()))return R(this._getOrReturnCtx(e),{code:O.invalid_date}),te;const t=new je;let n;for(const r of this._def.checks)r.kind==="min"?e.data.getTime()<r.value&&(n=this._getOrReturnCtx(e,n),R(n,{code:O.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),t.dirty()):r.kind==="max"?e.data.getTime()>r.value&&(n=this._getOrReturnCtx(e,n),R(n,{code:O.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),t.dirty()):pe.assertNever(r);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new zt({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:B.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:B.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}zt.create=s=>new zt({checks:[],coerce:(s==null?void 0:s.coerce)||!1,typeName:Q.ZodDate,...ae(s)});class gs extends le{_parse(e){if(this._getType(e)!==D.symbol){const t=this._getOrReturnCtx(e);return R(t,{code:O.invalid_type,expected:D.symbol,received:t.parsedType}),te}return Le(e.data)}}gs.create=s=>new gs({typeName:Q.ZodSymbol,...ae(s)});class En extends le{_parse(e){if(this._getType(e)!==D.undefined){const t=this._getOrReturnCtx(e);return R(t,{code:O.invalid_type,expected:D.undefined,received:t.parsedType}),te}return Le(e.data)}}En.create=s=>new En({typeName:Q.ZodUndefined,...ae(s)});class On extends le{_parse(e){if(this._getType(e)!==D.null){const t=this._getOrReturnCtx(e);return R(t,{code:O.invalid_type,expected:D.null,received:t.parsedType}),te}return Le(e.data)}}On.create=s=>new On({typeName:Q.ZodNull,...ae(s)});class tn extends le{constructor(){super(...arguments),this._any=!0}_parse(e){return Le(e.data)}}tn.create=s=>new tn({typeName:Q.ZodAny,...ae(s)});class Pt extends le{constructor(){super(...arguments),this._unknown=!0}_parse(e){return Le(e.data)}}Pt.create=s=>new Pt({typeName:Q.ZodUnknown,...ae(s)});class $t extends le{_parse(e){const t=this._getOrReturnCtx(e);return R(t,{code:O.invalid_type,expected:D.never,received:t.parsedType}),te}}$t.create=s=>new $t({typeName:Q.ZodNever,...ae(s)});class vs extends le{_parse(e){if(this._getType(e)!==D.undefined){const t=this._getOrReturnCtx(e);return R(t,{code:O.invalid_type,expected:D.void,received:t.parsedType}),te}return Le(e.data)}}vs.create=s=>new vs({typeName:Q.ZodVoid,...ae(s)});class it extends le{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==D.array)return R(t,{code:O.invalid_type,expected:D.array,received:t.parsedType}),te;if(r.exactLength!==null){const i=t.data.length>r.exactLength.value,o=t.data.length<r.exactLength.value;(i||o)&&(R(t,{code:i?O.too_big:O.too_small,minimum:o?r.exactLength.value:void 0,maximum:i?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(r.minLength!==null&&t.data.length<r.minLength.value&&(R(t,{code:O.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),r.maxLength!==null&&t.data.length>r.maxLength.value&&(R(t,{code:O.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>r.type._parseAsync(new ut(t,i,t.path,o)))).then(i=>je.mergeArray(n,i));const a=[...t.data].map((i,o)=>r.type._parseSync(new ut(t,i,t.path,o)));return je.mergeArray(n,a)}get element(){return this._def.type}min(e,t){return new it({...this._def,minLength:{value:e,message:B.toString(t)}})}max(e,t){return new it({...this._def,maxLength:{value:e,message:B.toString(t)}})}length(e,t){return new it({...this._def,exactLength:{value:e,message:B.toString(t)}})}nonempty(e){return this.min(1,e)}}function Jt(s){if(s instanceof be){const e={};for(const t in s.shape){const n=s.shape[t];e[t]=ct.create(Jt(n))}return new be({...s._def,shape:()=>e})}return s instanceof it?new it({...s._def,type:Jt(s.element)}):s instanceof ct?ct.create(Jt(s.unwrap())):s instanceof Zt?Zt.create(Jt(s.unwrap())):s instanceof pt?pt.create(s.items.map(e=>Jt(e))):s}it.create=(s,e)=>new it({type:s,minLength:null,maxLength:null,exactLength:null,typeName:Q.ZodArray,...ae(e)});class be extends le{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=pe.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==D.object){const c=this._getOrReturnCtx(e);return R(c,{code:O.invalid_type,expected:D.object,received:c.parsedType}),te}const{status:t,ctx:n}=this._processInputParams(e),{shape:r,keys:a}=this._getCached(),i=[];if(!(this._def.catchall instanceof $t&&this._def.unknownKeys==="strip"))for(const c in n.data)a.includes(c)||i.push(c);const o=[];for(const c of a){const l=r[c],d=n.data[c];o.push({key:{status:"valid",value:c},value:l._parse(new ut(n,d,n.path,c)),alwaysSet:c in n.data})}if(this._def.catchall instanceof $t){const c=this._def.unknownKeys;if(c==="passthrough")for(const l of i)o.push({key:{status:"valid",value:l},value:{status:"valid",value:n.data[l]}});else if(c==="strict")i.length>0&&(R(n,{code:O.unrecognized_keys,keys:i}),t.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const l of i){const d=n.data[l];o.push({key:{status:"valid",value:l},value:c._parse(new ut(n,d,n.path,l)),alwaysSet:l in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const c=[];for(const l of o){const d=await l.key,u=await l.value;c.push({key:d,value:u,alwaysSet:l.alwaysSet})}return c}).then(c=>je.mergeObjectSync(t,c)):je.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return B.errToObj,new be({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,n)=>{var r,a,i,o;const c=(i=(a=(r=this._def).errorMap)===null||a===void 0?void 0:a.call(r,t,n).message)!==null&&i!==void 0?i:n.defaultError;return t.code==="unrecognized_keys"?{message:(o=B.errToObj(e).message)!==null&&o!==void 0?o:c}:{message:c}}}:{}})}strip(){return new be({...this._def,unknownKeys:"strip"})}passthrough(){return new be({...this._def,unknownKeys:"passthrough"})}extend(e){return new be({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new be({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:Q.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new be({...this._def,catchall:e})}pick(e){const t={};return pe.objectKeys(e).forEach(n=>{e[n]&&this.shape[n]&&(t[n]=this.shape[n])}),new be({...this._def,shape:()=>t})}omit(e){const t={};return pe.objectKeys(this.shape).forEach(n=>{e[n]||(t[n]=this.shape[n])}),new be({...this._def,shape:()=>t})}deepPartial(){return Jt(this)}partial(e){const t={};return pe.objectKeys(this.shape).forEach(n=>{const r=this.shape[n];e&&!e[n]?t[n]=r:t[n]=r.optional()}),new be({...this._def,shape:()=>t})}required(e){const t={};return pe.objectKeys(this.shape).forEach(n=>{if(e&&!e[n])t[n]=this.shape[n];else{let r=this.shape[n];for(;r instanceof ct;)r=r._def.innerType;t[n]=r}}),new be({...this._def,shape:()=>t})}keyof(){return gi(pe.objectKeys(this.shape))}}be.create=(s,e)=>new be({shape:()=>s,unknownKeys:"strip",catchall:$t.create(),typeName:Q.ZodObject,...ae(e)}),be.strictCreate=(s,e)=>new be({shape:()=>s,unknownKeys:"strict",catchall:$t.create(),typeName:Q.ZodObject,...ae(e)}),be.lazycreate=(s,e)=>new be({shape:s,unknownKeys:"strip",catchall:$t.create(),typeName:Q.ZodObject,...ae(e)});class In extends le{_parse(e){const{ctx:t}=this._processInputParams(e),n=this._def.options;if(t.common.async)return Promise.all(n.map(async r=>{const a={...t,common:{...t.common,issues:[]},parent:null};return{result:await r._parseAsync({data:t.data,path:t.path,parent:a}),ctx:a}})).then(function(r){for(const i of r)if(i.result.status==="valid")return i.result;for(const i of r)if(i.result.status==="dirty")return t.common.issues.push(...i.ctx.common.issues),i.result;const a=r.map(i=>new Je(i.ctx.common.issues));return R(t,{code:O.invalid_union,unionErrors:a}),te});{let r;const a=[];for(const o of n){const c={...t,common:{...t.common,issues:[]},parent:null},l=o._parseSync({data:t.data,path:t.path,parent:c});if(l.status==="valid")return l;l.status!=="dirty"||r||(r={result:l,ctx:c}),c.common.issues.length&&a.push(c.common.issues)}if(r)return t.common.issues.push(...r.ctx.common.issues),r.result;const i=a.map(o=>new Je(o));return R(t,{code:O.invalid_union,unionErrors:i}),te}}get options(){return this._def.options}}In.create=(s,e)=>new In({options:s,typeName:Q.ZodUnion,...ae(e)});const wt=s=>s instanceof jn?wt(s.schema):s instanceof rt?wt(s.innerType()):s instanceof Rn?[s.value]:s instanceof Nt?s.options:s instanceof Ln?pe.objectValues(s.enum):s instanceof Fn?wt(s._def.innerType):s instanceof En?[void 0]:s instanceof On?[null]:s instanceof ct?[void 0,...wt(s.unwrap())]:s instanceof Zt?[null,...wt(s.unwrap())]:s instanceof Ws||s instanceof Dn?wt(s.unwrap()):s instanceof zn?wt(s._def.innerType):[];class bs extends le{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==D.object)return R(t,{code:O.invalid_type,expected:D.object,received:t.parsedType}),te;const n=this.discriminator,r=t.data[n],a=this.optionsMap.get(r);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):(R(t,{code:O.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),te)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){const r=new Map;for(const a of t){const i=wt(a.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const o of i){if(r.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);r.set(o,a)}}return new bs({typeName:Q.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...ae(n)})}}function qs(s,e){const t=ht(s),n=ht(e);if(s===e)return{valid:!0,data:s};if(t===D.object&&n===D.object){const r=pe.objectKeys(e),a=pe.objectKeys(s).filter(o=>r.indexOf(o)!==-1),i={...s,...e};for(const o of a){const c=qs(s[o],e[o]);if(!c.valid)return{valid:!1};i[o]=c.data}return{valid:!0,data:i}}if(t===D.array&&n===D.array){if(s.length!==e.length)return{valid:!1};const r=[];for(let a=0;a<s.length;a++){const i=qs(s[a],e[a]);if(!i.valid)return{valid:!1};r.push(i.data)}return{valid:!0,data:r}}return t===D.date&&n===D.date&&+s==+e?{valid:!0,data:s}:{valid:!1}}class Pn extends le{_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=(a,i)=>{if(Us(a)||Us(i))return te;const o=qs(a.value,i.value);return o.valid?((Vs(a)||Vs(i))&&t.dirty(),{status:t.value,value:o.data}):(R(n,{code:O.invalid_intersection_types}),te)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([a,i])=>r(a,i)):r(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}Pn.create=(s,e,t)=>new Pn({left:s,right:e,typeName:Q.ZodIntersection,...ae(t)});class pt extends le{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==D.array)return R(n,{code:O.invalid_type,expected:D.array,received:n.parsedType}),te;if(n.data.length<this._def.items.length)return R(n,{code:O.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),te;!this._def.rest&&n.data.length>this._def.items.length&&(R(n,{code:O.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const r=[...n.data].map((a,i)=>{const o=this._def.items[i]||this._def.rest;return o?o._parse(new ut(n,a,n.path,i)):null}).filter(a=>!!a);return n.common.async?Promise.all(r).then(a=>je.mergeArray(t,a)):je.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new pt({...this._def,rest:e})}}pt.create=(s,e)=>{if(!Array.isArray(s))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new pt({items:s,typeName:Q.ZodTuple,rest:null,...ae(e)})};class ks extends le{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==D.object)return R(n,{code:O.invalid_type,expected:D.object,received:n.parsedType}),te;const r=[],a=this._def.keyType,i=this._def.valueType;for(const o in n.data)r.push({key:a._parse(new ut(n,o,n.path,o)),value:i._parse(new ut(n,n.data[o],n.path,o)),alwaysSet:o in n.data});return n.common.async?je.mergeObjectAsync(t,r):je.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,n){return new ks(t instanceof le?{keyType:e,valueType:t,typeName:Q.ZodRecord,...ae(n)}:{keyType:st.create(),valueType:e,typeName:Q.ZodRecord,...ae(t)})}}class $s extends le{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==D.map)return R(n,{code:O.invalid_type,expected:D.map,received:n.parsedType}),te;const r=this._def.keyType,a=this._def.valueType,i=[...n.data.entries()].map(([o,c],l)=>({key:r._parse(new ut(n,o,n.path,[l,"key"])),value:a._parse(new ut(n,c,n.path,[l,"value"]))}));if(n.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const c of i){const l=await c.key,d=await c.value;if(l.status==="aborted"||d.status==="aborted")return te;l.status!=="dirty"&&d.status!=="dirty"||t.dirty(),o.set(l.value,d.value)}return{status:t.value,value:o}})}{const o=new Map;for(const c of i){const l=c.key,d=c.value;if(l.status==="aborted"||d.status==="aborted")return te;l.status!=="dirty"&&d.status!=="dirty"||t.dirty(),o.set(l.value,d.value)}return{status:t.value,value:o}}}}$s.create=(s,e,t)=>new $s({valueType:e,keyType:s,typeName:Q.ZodMap,...ae(t)});class Dt extends le{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==D.set)return R(n,{code:O.invalid_type,expected:D.set,received:n.parsedType}),te;const r=this._def;r.minSize!==null&&n.data.size<r.minSize.value&&(R(n,{code:O.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),r.maxSize!==null&&n.data.size>r.maxSize.value&&(R(n,{code:O.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const a=this._def.valueType;function i(c){const l=new Set;for(const d of c){if(d.status==="aborted")return te;d.status==="dirty"&&t.dirty(),l.add(d.value)}return{status:t.value,value:l}}const o=[...n.data.values()].map((c,l)=>a._parse(new ut(n,c,n.path,l)));return n.common.async?Promise.all(o).then(c=>i(c)):i(o)}min(e,t){return new Dt({...this._def,minSize:{value:e,message:B.toString(t)}})}max(e,t){return new Dt({...this._def,maxSize:{value:e,message:B.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}Dt.create=(s,e)=>new Dt({valueType:s,minSize:null,maxSize:null,typeName:Q.ZodSet,...ae(e)});class Ht extends le{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==D.function)return R(t,{code:O.invalid_type,expected:D.function,received:t.parsedType}),te;function n(o,c){return ms({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,ps(),en].filter(l=>!!l),issueData:{code:O.invalid_arguments,argumentsError:c}})}function r(o,c){return ms({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,ps(),en].filter(l=>!!l),issueData:{code:O.invalid_return_type,returnTypeError:c}})}const a={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof nn){const o=this;return Le(async function(...c){const l=new Je([]),d=await o._def.args.parseAsync(c,a).catch(h=>{throw l.addIssue(n(c,h)),l}),u=await Reflect.apply(i,this,d);return await o._def.returns._def.type.parseAsync(u,a).catch(h=>{throw l.addIssue(r(u,h)),l})})}{const o=this;return Le(function(...c){const l=o._def.args.safeParse(c,a);if(!l.success)throw new Je([n(c,l.error)]);const d=Reflect.apply(i,this,l.data),u=o._def.returns.safeParse(d,a);if(!u.success)throw new Je([r(d,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new Ht({...this._def,args:pt.create(e).rest(Pt.create())})}returns(e){return new Ht({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new Ht({args:e||pt.create([]).rest(Pt.create()),returns:t||Pt.create(),typeName:Q.ZodFunction,...ae(n)})}}class jn extends le{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}jn.create=(s,e)=>new jn({getter:s,typeName:Q.ZodLazy,...ae(e)});class Rn extends le{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return R(t,{received:t.data,code:O.invalid_literal,expected:this._def.value}),te}return{status:"valid",value:e.data}}get value(){return this._def.value}}function gi(s,e){return new Nt({values:s,typeName:Q.ZodEnum,...ae(e)})}Rn.create=(s,e)=>new Rn({value:s,typeName:Q.ZodLiteral,...ae(e)});class Nt extends le{constructor(){super(...arguments),un.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),n=this._def.values;return R(t,{expected:pe.joinValues(n),received:t.parsedType,code:O.invalid_type}),te}if(hs(this,un)||pi(this,un,new Set(this._def.values)),!hs(this,un).has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return R(t,{received:t.data,code:O.invalid_enum_value,options:n}),te}return Le(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Nt.create(e,{...this._def,...t})}exclude(e,t=this._def){return Nt.create(this.options.filter(n=>!e.includes(n)),{...this._def,...t})}}un=new WeakMap,Nt.create=gi;class Ln extends le{constructor(){super(...arguments),pn.set(this,void 0)}_parse(e){const t=pe.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==D.string&&n.parsedType!==D.number){const r=pe.objectValues(t);return R(n,{expected:pe.joinValues(r),received:n.parsedType,code:O.invalid_type}),te}if(hs(this,pn)||pi(this,pn,new Set(pe.getValidEnumValues(this._def.values))),!hs(this,pn).has(e.data)){const r=pe.objectValues(t);return R(n,{received:n.data,code:O.invalid_enum_value,options:r}),te}return Le(e.data)}get enum(){return this._def.values}}pn=new WeakMap,Ln.create=(s,e)=>new Ln({values:s,typeName:Q.ZodNativeEnum,...ae(e)});class nn extends le{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==D.promise&&t.common.async===!1)return R(t,{code:O.invalid_type,expected:D.promise,received:t.parsedType}),te;const n=t.parsedType===D.promise?t.data:Promise.resolve(t.data);return Le(n.then(r=>this._def.type.parseAsync(r,{path:t.path,errorMap:t.common.contextualErrorMap})))}}nn.create=(s,e)=>new nn({type:s,typeName:Q.ZodPromise,...ae(e)});class rt extends le{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===Q.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=this._def.effect||null,a={addIssue:i=>{R(n,i),i.fatal?t.abort():t.dirty()},get path(){return n.path}};if(a.addIssue=a.addIssue.bind(a),r.type==="preprocess"){const i=r.transform(n.data,a);if(n.common.async)return Promise.resolve(i).then(async o=>{if(t.value==="aborted")return te;const c=await this._def.schema._parseAsync({data:o,path:n.path,parent:n});return c.status==="aborted"?te:c.status==="dirty"||t.value==="dirty"?fs(c.value):c});{if(t.value==="aborted")return te;const o=this._def.schema._parseSync({data:i,path:n.path,parent:n});return o.status==="aborted"?te:o.status==="dirty"||t.value==="dirty"?fs(o.value):o}}if(r.type==="refinement"){const i=o=>{const c=r.refinement(o,a);if(n.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(n.common.async===!1){const o=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return o.status==="aborted"?te:(o.status==="dirty"&&t.dirty(),i(o.value),{status:t.value,value:o.value})}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(o=>o.status==="aborted"?te:(o.status==="dirty"&&t.dirty(),i(o.value).then(()=>({status:t.value,value:o.value}))))}if(r.type==="transform"){if(n.common.async===!1){const i=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!Ft(i))return i;const o=r.transform(i.value,a);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:o}}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(i=>Ft(i)?Promise.resolve(r.transform(i.value,a)).then(o=>({status:t.value,value:o})):i)}pe.assertNever(r)}}rt.create=(s,e,t)=>new rt({schema:s,typeName:Q.ZodEffects,effect:e,...ae(t)}),rt.createWithPreprocess=(s,e,t)=>new rt({schema:e,effect:{type:"preprocess",transform:s},typeName:Q.ZodEffects,...ae(t)});class ct extends le{_parse(e){return this._getType(e)===D.undefined?Le(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ct.create=(s,e)=>new ct({innerType:s,typeName:Q.ZodOptional,...ae(e)});class Zt extends le{_parse(e){return this._getType(e)===D.null?Le(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Zt.create=(s,e)=>new Zt({innerType:s,typeName:Q.ZodNullable,...ae(e)});class Fn extends le{_parse(e){const{ctx:t}=this._processInputParams(e);let n=t.data;return t.parsedType===D.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}Fn.create=(s,e)=>new Fn({innerType:s,typeName:Q.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...ae(e)});class zn extends le{_parse(e){const{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return Nn(r)?r.then(a=>({status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new Je(n.common.issues)},input:n.data})})):{status:"valid",value:r.status==="valid"?r.value:this._def.catchValue({get error(){return new Je(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}zn.create=(s,e)=>new zn({innerType:s,typeName:Q.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...ae(e)});class ys extends le{_parse(e){if(this._getType(e)!==D.nan){const t=this._getOrReturnCtx(e);return R(t,{code:O.invalid_type,expected:D.nan,received:t.parsedType}),te}return{status:"valid",value:e.data}}}ys.create=s=>new ys({typeName:Q.ZodNaN,...ae(s)});const Pd=Symbol("zod_brand");class Ws extends le{_parse(e){const{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}}class Hn extends le{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const r=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?te:r.status==="dirty"?(t.dirty(),fs(r.value)):this._def.out._parseAsync({data:r.value,path:n.path,parent:n})})();{const r=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?te:r.status==="dirty"?(t.dirty(),{status:"dirty",value:r.value}):this._def.out._parseSync({data:r.value,path:n.path,parent:n})}}static create(e,t){return new Hn({in:e,out:t,typeName:Q.ZodPipeline})}}class Dn extends le{_parse(e){const t=this._def.innerType._parse(e),n=r=>(Ft(r)&&(r.value=Object.freeze(r.value)),r);return Nn(t)?t.then(r=>n(r)):n(t)}unwrap(){return this._def.innerType}}function ia(s,e={},t){return s?tn.create().superRefine((n,r)=>{var a,i;if(!s(n)){const o=typeof e=="function"?e(n):typeof e=="string"?{message:e}:e,c=(i=(a=o.fatal)!==null&&a!==void 0?a:t)===null||i===void 0||i,l=typeof o=="string"?{message:o}:o;r.addIssue({code:"custom",...l,fatal:c})}}):tn.create()}Dn.create=(s,e)=>new Dn({innerType:s,typeName:Q.ZodReadonly,...ae(e)});const jd={object:be.lazycreate};var Q;(function(s){s.ZodString="ZodString",s.ZodNumber="ZodNumber",s.ZodNaN="ZodNaN",s.ZodBigInt="ZodBigInt",s.ZodBoolean="ZodBoolean",s.ZodDate="ZodDate",s.ZodSymbol="ZodSymbol",s.ZodUndefined="ZodUndefined",s.ZodNull="ZodNull",s.ZodAny="ZodAny",s.ZodUnknown="ZodUnknown",s.ZodNever="ZodNever",s.ZodVoid="ZodVoid",s.ZodArray="ZodArray",s.ZodObject="ZodObject",s.ZodUnion="ZodUnion",s.ZodDiscriminatedUnion="ZodDiscriminatedUnion",s.ZodIntersection="ZodIntersection",s.ZodTuple="ZodTuple",s.ZodRecord="ZodRecord",s.ZodMap="ZodMap",s.ZodSet="ZodSet",s.ZodFunction="ZodFunction",s.ZodLazy="ZodLazy",s.ZodLiteral="ZodLiteral",s.ZodEnum="ZodEnum",s.ZodEffects="ZodEffects",s.ZodNativeEnum="ZodNativeEnum",s.ZodOptional="ZodOptional",s.ZodNullable="ZodNullable",s.ZodDefault="ZodDefault",s.ZodCatch="ZodCatch",s.ZodPromise="ZodPromise",s.ZodBranded="ZodBranded",s.ZodPipeline="ZodPipeline",s.ZodReadonly="ZodReadonly"})(Q||(Q={}));const oa=st.create,ca=Mt.create,Rd=ys.create,Ld=At.create,la=Zn.create,Fd=zt.create,zd=gs.create,Dd=En.create,Ud=On.create,Vd=tn.create,qd=Pt.create,Bd=$t.create,Jd=vs.create,Gd=it.create,Hd=be.create,Wd=be.strictCreate,Kd=In.create,Yd=bs.create,Xd=Pn.create,Qd=pt.create,eu=ks.create,tu=$s.create,nu=Dt.create,su=Ht.create,ru=jn.create,au=Rn.create,iu=Nt.create,ou=Ln.create,cu=nn.create,da=rt.create,lu=ct.create,du=Zt.create,uu=rt.createWithPreprocess,pu=Hn.create,mu={string:s=>st.create({...s,coerce:!0}),number:s=>Mt.create({...s,coerce:!0}),boolean:s=>Zn.create({...s,coerce:!0}),bigint:s=>At.create({...s,coerce:!0}),date:s=>zt.create({...s,coerce:!0})},fu=te;var _e=Object.freeze({__proto__:null,defaultErrorMap:en,setErrorMap:function(s){ui=s},getErrorMap:ps,makeIssue:ms,EMPTY_PATH:[],addIssueToContext:R,ParseStatus:je,INVALID:te,DIRTY:fs,OK:Le,isAborted:Us,isDirty:Vs,isValid:Ft,isAsync:Nn,get util(){return pe},get objectUtil(){return Ds},ZodParsedType:D,getParsedType:ht,ZodType:le,datetimeRegex:hi,ZodString:st,ZodNumber:Mt,ZodBigInt:At,ZodBoolean:Zn,ZodDate:zt,ZodSymbol:gs,ZodUndefined:En,ZodNull:On,ZodAny:tn,ZodUnknown:Pt,ZodNever:$t,ZodVoid:vs,ZodArray:it,ZodObject:be,ZodUnion:In,ZodDiscriminatedUnion:bs,ZodIntersection:Pn,ZodTuple:pt,ZodRecord:ks,ZodMap:$s,ZodSet:Dt,ZodFunction:Ht,ZodLazy:jn,ZodLiteral:Rn,ZodEnum:Nt,ZodNativeEnum:Ln,ZodPromise:nn,ZodEffects:rt,ZodTransformer:rt,ZodOptional:ct,ZodNullable:Zt,ZodDefault:Fn,ZodCatch:zn,ZodNaN:ys,BRAND:Pd,ZodBranded:Ws,ZodPipeline:Hn,ZodReadonly:Dn,custom:ia,Schema:le,ZodSchema:le,late:jd,get ZodFirstPartyTypeKind(){return Q},coerce:mu,any:Vd,array:Gd,bigint:Ld,boolean:la,date:Fd,discriminatedUnion:Yd,effect:da,enum:iu,function:su,instanceof:(s,e={message:`Input not instance of ${s.name}`})=>ia(t=>t instanceof s,e),intersection:Xd,lazy:ru,literal:au,map:tu,nan:Rd,nativeEnum:ou,never:Bd,null:Ud,nullable:du,number:ca,object:Hd,oboolean:()=>la().optional(),onumber:()=>ca().optional(),optional:lu,ostring:()=>oa().optional(),pipeline:pu,preprocess:uu,promise:cu,record:eu,set:nu,strictObject:Wd,string:oa,symbol:zd,transformer:da,tuple:Qd,undefined:Dd,union:Kd,unknown:qd,void:Jd,NEVER:fu,ZodIssueCode:O,quotelessJson:s=>JSON.stringify(s,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:Je});const Ye=_e.object({name:_e.string().optional(),title:_e.string().optional(),command:_e.string().optional(),args:_e.array(_e.union([_e.string(),_e.number(),_e.boolean()])).optional(),env:_e.record(_e.union([_e.string(),_e.number(),_e.boolean(),_e.null(),_e.undefined()])).optional()}).passthrough(),hu=_e.array(Ye),gu=_e.object({servers:_e.array(Ye)}).passthrough(),vu=_e.object({mcpServers:_e.array(Ye)}).passthrough(),$u=_e.object({servers:_e.record(Ye)}).passthrough(),yu=_e.object({mcpServers:_e.record(Ye)}).passthrough(),_u=_e.record(Ye),xu=Ye.refine(s=>s.command!==void 0,{message:"Server must have a 'command' property"}),vi=Symbol("MCPServerError");class Ee extends Error{constructor(e){super(e),this.name="MCPServerError",Object.setPrototypeOf(this,Ee.prototype)}}var Ea;Ea=vi;class Ks{constructor(e){ve(this,"servers",Ve([]));this.host=e,this.loadServersFromStorage()}handleMessageFromExtension(e){const t=e.data;if(t.type===se.getStoredMCPServersResponse){const n=t.data;return Array.isArray(n)&&this.servers.set(n),!0}return!1}async importServersFromJSON(e){return this.importFromJSON(e)}loadServersFromStorage(){try{this.host.postMessage({type:se.getStoredMCPServers})}catch(e){console.error("Failed to load MCP servers:",e),this.servers.set([])}}saveServers(e){try{this.host.postMessage({type:se.setStoredMCPServers,data:e})}catch(t){throw console.error("Failed to save MCP servers:",t),new Ee("Failed to save MCP servers")}}getServers(){return this.servers}addServer(e){this.checkExistingServerName(e.name),this.servers.update(t=>{const n=[...t,{...e,id:crypto.randomUUID()}];return this.saveServers(n),n})}addServers(e){for(const t of e)this.checkExistingServerName(t.name);this.servers.update(t=>{const n=[...t,...e.map(r=>({...r,id:crypto.randomUUID()}))];return this.saveServers(n),n})}checkExistingServerName(e,t){const n=mn(this.servers).find(r=>r.name===e);if(n&&(n==null?void 0:n.id)!==t)throw new Ee(`Server name '${e}' already exists`)}updateServer(e){this.checkExistingServerName(e.name,e.id),this.servers.update(t=>{const n=t.map(r=>r.id===e.id?e:r);return this.saveServers(n),n})}deleteServer(e){this.servers.update(t=>{const n=t.filter(r=>r.id!==e);return this.saveServers(n),n})}toggleDisabledServer(e){this.servers.update(t=>{const n=t.map(r=>r.id===e?{...r,disabled:!r.disabled}:r);return this.saveServers(n),n})}static convertServerToJSON(e){return JSON.stringify({mcpServers:{[e.name]:{command:e.command.split(" ")[0],args:e.command.split(" ").slice(1),env:e.env}}},null,2)}static parseServerValidationMessages(e){const t=new Map,n=new Map;e.forEach(a=>{var i;a.disabled?t.set(a.id,"MCP server has been manually disabled"):a.tools&&a.tools.length===0?t.set(a.id,"No tools are available for this MCP server"):a.disabledTools&&a.disabledTools.length===((i=a.tools)==null?void 0:i.length)?t.set(a.id,"All tools for this MCP server have validation errors: "+a.disabledTools.join(", ")):a.disabledTools&&a.disabledTools.length>0&&n.set(a.id,"MCP server has validation errors in the following tools which have been disabled: "+a.disabledTools.join(", "))});const r=this.parseDuplicateServerIds(e);return{errors:new Map([...t,...r]),warnings:n}}static parseDuplicateServerIds(e){const t=new Map;for(const r of e)t.has(r.name)||t.set(r.name,[]),t.get(r.name).push(r.id);const n=new Map;for(const[,r]of t)if(r.length>1)for(let a=1;a<r.length;a++)n.set(r[a],"MCP server is disabled due to duplicate server names");return n}parseServerConfigFromJSON(e){try{const t=JSON.parse(e),n=_e.union([hu.transform(r=>r.map(a=>this.normalizeServerConfig(a))),gu.transform(r=>r.servers.map(a=>this.normalizeServerConfig(a))),vu.transform(r=>r.mcpServers.map(a=>this.normalizeServerConfig(a))),$u.transform(r=>Object.entries(r.servers).map(([a,i])=>{const o=Ye.parse(i);return this.normalizeServerConfig({...o,name:o.name||a})})),yu.transform(r=>Object.entries(r.mcpServers).map(([a,i])=>{const o=Ye.parse(i);return this.normalizeServerConfig({...o,name:o.name||a})})),_u.transform(r=>{if(!Object.values(r).some(a=>{const i=Ye.safeParse(a);return i.success&&i.data.command!==void 0}))throw new Error("No command property found in any server config");return Object.entries(r).map(([a,i])=>{const o=Ye.parse(i);return this.normalizeServerConfig({...o,name:o.name||a})})}),xu.transform(r=>[this.normalizeServerConfig(r)])]).safeParse(t);if(n.success)return n.data;throw new Ee("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(t){throw t instanceof Ee?t:new Ee("Failed to parse MCP servers from JSON. Please check the format.")}}importFromJSON(e){try{const t=this.parseServerConfigFromJSON(e),n=mn(this.servers),r=new Set(n.map(a=>a.name));for(const a of t){if(!a.name)throw new Ee("All servers must have a name.");if(r.has(a.name))throw new Ee(`A server with the name '${a.name}' already exists.`);r.add(a.name)}return this.servers.update(a=>{const i=[...a,...t.map(o=>({...o,id:crypto.randomUUID()}))];return this.saveServers(i),i}),t.length}catch(t){throw t instanceof Ee?t:new Ee("Failed to import MCP servers from JSON. Please check the format.")}}normalizeServerConfig(e){try{const t=Ye.transform(n=>{const r=n.command||"",a=n.args?n.args.map(l=>String(l)):[];if(!r)throw new Error("Server must have a 'command' property");const i=a.length>0?`${r} ${a.join(" ")}`:r,o=n.name||n.title||(r?r.split(" ")[0]:""),c=n.env?Object.fromEntries(Object.entries(n.env).filter(([l,d])=>d!=null).map(([l,d])=>[l,String(d)])):void 0;return{name:o,command:i,arguments:"",useShellInterpolation:!0,env:Object.keys(c||{}).length>0?c:void 0}}).refine(n=>!!n.name,{message:"Server must have a name",path:["name"]}).refine(n=>!!n.command,{message:"Server must have a command",path:["command"]}).safeParse(e);if(!t.success)throw new Ee(t.error.message);return t.data}catch(t){throw t instanceof Error?new Ee(`Invalid server configuration: ${t.message}`):new Ee("Invalid server configuration")}}}ve(Ks,Ea,"MCPServerError");function wu(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=ke(r,n[a]);return{c(){e=qe("svg"),t=new Un(!0),this.h()},l(a){e=Vn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=qn(e);t=Bn(i,!0),i.forEach($),this.h()},h(){t.a=null,lt(e,r)},m(a,i){Jn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m170.5 51.6-19 28.4h145l-19-28.4c-1.5-2.2-4-3.6-6.7-3.6h-93.7c-2.7 0-5.2 1.3-6.7 3.6zm147-26.6 36.7 55H424c13.3 0 24 10.7 24 24s-10.7 24-24 24h-8v304c0 44.2-35.8 80-80 80H112c-44.2 0-80-35.8-80-80V128h-8c-13.3 0-24-10.7-24-24s10.7-24 24-24h69.8l36.7-55.1C140.9 9.4 158.4 0 177.1 0h93.7c18.7 0 36.2 9.4 46.6 24.9zM80 128v304c0 17.7 14.3 32 32 32h224c17.7 0 32-14.3 32-32V128zm80 64v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16"/>',e)},p(a,[i]){lt(e,r=yt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&i&&a[0]]))},i:G,o:G,d(a){a&&$(e)}}}function bu(s,e,t){return s.$$set=n=>{t(0,e=ke(ke({},e),Xe(n)))},[e=Xe(e)]}class $i extends he{constructor(e){super(),ge(this,e,bu,wu,me,{})}}function ua(s,e,t){const n=s.slice();return n[11]=e[t],n[12]=e,n[13]=t,n}function ku(s){let e;return{c(){e=F("Environment Variables")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function pa(s){let e,t,n=[],r=new Map,a=$e(s[0]);const i=o=>o[11].id;for(let o=0;o<a.length;o+=1){let c=ua(s,a,o),l=i(c);r.set(l,n[o]=ma(l,c))}return{c(){for(let o=0;o<n.length;o+=1)n[o].c();e=Ce()},m(o,c){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,c);x(o,e,c),t=!0},p(o,c){59&c&&(a=$e(o[0]),H(),n=Et(n,c,i,1,o,a,r,e.parentNode,Ot,ma,e,ua),W())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&$(e);for(let c=0;c<n.length;c+=1)n[c].d(o)}}}function Su(s){let e,t;return e=new $i({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p:G,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Cu(s){let e,t;return e=new Ne({props:{variant:"ghost",color:"neutral",type:"button",size:1,$$slots:{iconLeft:[Su]},$$scope:{ctx:s}}}),e.$on("focus",function(){jt(s[1])&&s[1].apply(this,arguments)}),e.$on("click",function(){return s[10](s[11])}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){s=n;const a={};16384&r&&(a.$$scope={dirty:r,ctx:s}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function ma(s,e){let t,n,r,a,i,o,c,l,d,u,h,v,y;function f(N){e[6](N,e[11])}let g={size:1,placeholder:"Name",class:"full-width"};function S(N){e[8](N,e[11])}e[11].key!==void 0&&(g.value=e[11].key),r=new Wt({props:g}),Oe.push(()=>Ie(r,"value",f)),r.$on("focus",function(){jt(e[1])&&e[1].apply(this,arguments)}),r.$on("change",function(){return e[7](e[11])});let I={size:1,placeholder:"Value",class:"full-width"};return e[11].value!==void 0&&(I.value=e[11].value),c=new Wt({props:I}),Oe.push(()=>Ie(c,"value",S)),c.$on("focus",function(){jt(e[1])&&e[1].apply(this,arguments)}),c.$on("change",function(){return e[9](e[11])}),h=new Gt({props:{content:"Remove",$$slots:{default:[Cu]},$$scope:{ctx:e}}}),{key:s,first:null,c(){t=M("tr"),n=M("td"),w(r.$$.fragment),i=Z(),o=M("td"),w(c.$$.fragment),d=Z(),u=M("td"),w(h.$$.fragment),v=Z(),_(n,"class","name-cell svelte-1mazg1z"),_(o,"class","value-cell svelte-1mazg1z"),_(u,"class","action-cell svelte-1mazg1z"),_(t,"class","env-var-row svelte-1mazg1z"),this.first=t},m(N,T){x(N,t,T),C(t,n),b(r,n,null),C(t,i),C(t,o),b(c,o,null),C(t,d),C(t,u),b(h,u,null),C(t,v),y=!0},p(N,T){e=N;const A={};!a&&1&T&&(a=!0,A.value=e[11].key,Pe(()=>a=!1)),r.$set(A);const P={};!l&&1&T&&(l=!0,P.value=e[11].value,Pe(()=>l=!1)),c.$set(P);const U={};16387&T&&(U.$$scope={dirty:T,ctx:e}),h.$set(U)},i(N){y||(p(r.$$.fragment,N),p(c.$$.fragment,N),p(h.$$.fragment,N),y=!0)},o(N){m(r.$$.fragment,N),m(c.$$.fragment,N),m(h.$$.fragment,N),y=!1},d(N){N&&$(t),k(r),k(c),k(h)}}}function Tu(s){let e;return{c(){e=F("Variable")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function Mu(s){let e,t;return e=new _s({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p:G,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Au(s){let e,t,n,r,a,i,o,c;e=new de({props:{size:1,weight:"medium",$$slots:{default:[ku]},$$scope:{ctx:s}}});let l=s[0].length>0&&pa(s);return o=new Ne({props:{size:1,variant:"soft",color:"neutral",type:"button",$$slots:{iconLeft:[Mu],default:[Tu]},$$scope:{ctx:s}}}),o.$on("click",s[2]),{c(){w(e.$$.fragment),t=Z(),n=M("table"),r=M("tbody"),l&&l.c(),a=Z(),i=M("div"),w(o.$$.fragment),_(n,"class","env-vars-table svelte-1mazg1z"),_(i,"class","new-var-button-container svelte-1mazg1z")},m(d,u){b(e,d,u),x(d,t,u),x(d,n,u),C(n,r),l&&l.m(r,null),x(d,a,u),x(d,i,u),b(o,i,null),c=!0},p(d,[u]){const h={};16384&u&&(h.$$scope={dirty:u,ctx:d}),e.$set(h),d[0].length>0?l?(l.p(d,u),1&u&&p(l,1)):(l=pa(d),l.c(),p(l,1),l.m(r,null)):l&&(H(),m(l,1,1,()=>{l=null}),W());const v={};16384&u&&(v.$$scope={dirty:u,ctx:d}),o.$set(v)},i(d){c||(p(e.$$.fragment,d),p(l),p(o.$$.fragment,d),c=!0)},o(d){m(e.$$.fragment,d),m(l),m(o.$$.fragment,d),c=!1},d(d){d&&($(t),$(n),$(a),$(i)),k(e,d),l&&l.d(),k(o)}}}function Nu(s,e,t){let{handleEnterEditMode:n}=e,{envVarEntries:r=[]}=e;function a(c){n(),t(0,r=r.filter(l=>l.id!==c))}function i(c,l){const d=r.findIndex(u=>u.id===c);d!==-1&&(t(0,r[d].key=l,r),t(0,r))}function o(c,l){const d=r.findIndex(u=>u.id===c);d!==-1&&(t(0,r[d].value=l,r),t(0,r))}return s.$$set=c=>{"handleEnterEditMode"in c&&t(1,n=c.handleEnterEditMode),"envVarEntries"in c&&t(0,r=c.envVarEntries)},[r,n,function(){n(),t(0,r=[...r,{id:crypto.randomUUID(),key:"",value:""}])},a,i,o,function(c,l){s.$$.not_equal(l.key,c)&&(l.key=c,t(0,r))},c=>i(c.id,c.key),function(c,l){s.$$.not_equal(l.value,c)&&(l.value=c,t(0,r))},c=>o(c.id,c.value),c=>a(c.id)]}class Zu extends he{constructor(e){super(),ge(this,e,Nu,Au,me,{handleEnterEditMode:1,envVarEntries:0})}}function Eu(s){let e,t,n,r,a,i,o,c,l,d,u,h,v,y,f,g,S,I,N,T,A,P,U,ne,V;i=new qi({}),c=new de({props:{color:"secondary",size:1,weight:"medium",$$slots:{default:[Iu]},$$scope:{ctx:s}}});const K=[ju,Pu],L=[];function J(Y,fe){return Y[0]==="addJson"?0:Y[0]==="add"||Y[0]==="edit"?1:-1}~(d=J(s))&&(u=L[d]=K[d](s));let ie=(s[0]==="add"||s[0]==="edit")&&fa(s);return g=new co({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[Vu],default:[Uu]},$$scope:{ctx:s}}}),N=new Ne({props:{size:1,variant:"ghost",color:"neutral",type:"button",$$slots:{default:[qu]},$$scope:{ctx:s}}}),N.$on("click",s[18]),A=new Ne({props:{size:1,variant:"solid",color:"accent",loading:s[2],type:"submit",disabled:s[14],$$slots:{default:[Hu]},$$scope:{ctx:s}}}),{c(){e=M("form"),t=M("div"),n=M("div"),r=M("div"),a=M("div"),w(i.$$.fragment),o=Z(),w(c.$$.fragment),l=Z(),u&&u.c(),h=Z(),ie&&ie.c(),v=Z(),y=M("div"),f=M("div"),w(g.$$.fragment),S=Z(),I=M("div"),w(N.$$.fragment),T=Z(),w(A.$$.fragment),_(a,"class","server-icon svelte-nlsbjs"),_(r,"class","server-title svelte-nlsbjs"),_(n,"class","server-header svelte-nlsbjs"),_(f,"class","error-container svelte-nlsbjs"),xe(f,"is-error",!!s[1]),_(I,"class","form-actions svelte-nlsbjs"),_(y,"class","form-actions-row svelte-nlsbjs"),_(t,"class","server-edit-form svelte-nlsbjs"),_(e,"class",P="c-mcp-server-card "+(s[0]==="add"||s[0]==="addJson"?"add-server-section":"server-item")+" svelte-nlsbjs")},m(Y,fe){x(Y,e,fe),C(e,t),C(t,n),C(n,r),C(r,a),b(i,a,null),C(r,o),b(c,r,null),C(t,l),~d&&L[d].m(t,null),C(t,h),ie&&ie.m(t,null),C(t,v),C(t,y),C(y,f),b(g,f,null),C(y,S),C(y,I),b(N,I,null),C(I,T),b(A,I,null),U=!0,ne||(V=Ge(e,"submit",Ti(s[17])),ne=!0)},p(Y,fe){const rn={};8192&fe[0]|32&fe[1]&&(rn.$$scope={dirty:fe,ctx:Y}),c.$set(rn);let Ut=d;d=J(Y),d===Ut?~d&&L[d].p(Y,fe):(u&&(H(),m(L[Ut],1,1,()=>{L[Ut]=null}),W()),~d?(u=L[d],u?u.p(Y,fe):(u=L[d]=K[d](Y),u.c()),p(u,1),u.m(t,h)):u=null),Y[0]==="add"||Y[0]==="edit"?ie?(ie.p(Y,fe),1&fe[0]&&p(ie,1)):(ie=fa(Y),ie.c(),p(ie,1),ie.m(t,v)):ie&&(H(),m(ie,1,1,()=>{ie=null}),W());const Qe={};2&fe[0]|32&fe[1]&&(Qe.$$scope={dirty:fe,ctx:Y}),g.$set(Qe),(!U||2&fe[0])&&xe(f,"is-error",!!Y[1]);const an={};32&fe[1]&&(an.$$scope={dirty:fe,ctx:Y}),N.$set(an);const Vt={};4&fe[0]&&(Vt.loading=Y[2]),16384&fe[0]&&(Vt.disabled=Y[14]),1&fe[0]|32&fe[1]&&(Vt.$$scope={dirty:fe,ctx:Y}),A.$set(Vt),(!U||1&fe[0]&&P!==(P="c-mcp-server-card "+(Y[0]==="add"||Y[0]==="addJson"?"add-server-section":"server-item")+" svelte-nlsbjs"))&&_(e,"class",P)},i(Y){U||(p(i.$$.fragment,Y),p(c.$$.fragment,Y),p(u),p(ie),p(g.$$.fragment,Y),p(N.$$.fragment,Y),p(A.$$.fragment,Y),U=!0)},o(Y){m(i.$$.fragment,Y),m(c.$$.fragment,Y),m(u),m(ie),m(g.$$.fragment,Y),m(N.$$.fragment,Y),m(A.$$.fragment,Y),U=!1},d(Y){Y&&$(e),k(i),k(c),~d&&L[d].d(),ie&&ie.d(),k(g),k(N),k(A),ne=!1,V()}}}function Ou(s){let e,t;return e=new ws({props:{$$slots:{"header-right":[up],"header-left":[ep]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};4344&r[0]|32&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Iu(s){let e;return{c(){e=F(s[13])},m(t,n){x(t,e,n)},p(t,n){8192&n[0]&&we(e,t[13])},d(t){t&&$(e)}}}function Pu(s){let e,t,n,r,a,i,o,c,l,d;function u(f){s[32](f)}let h={size:1,placeholder:"Enter a name for your MCP server (e.g., 'Server Memory')",$$slots:{label:[Lu]},$$scope:{ctx:s}};function v(f){s[33](f)}s[8]!==void 0&&(h.value=s[8]),n=new Wt({props:h}),Oe.push(()=>Ie(n,"value",u)),n.$on("focus",s[15]);let y={size:1,placeholder:"Enter the MCP command (e.g., 'npx -y @modelcontextprotocol/server-memory')",$$slots:{label:[zu]},$$scope:{ctx:s}};return s[9]!==void 0&&(y.value=s[9]),c=new Wt({props:y}),Oe.push(()=>Ie(c,"value",v)),c.$on("focus",s[15]),{c(){e=M("div"),t=M("div"),w(n.$$.fragment),a=Z(),i=M("div"),o=M("div"),w(c.$$.fragment),_(t,"class","input-field svelte-nlsbjs"),_(e,"class","form-row svelte-nlsbjs"),_(o,"class","input-field svelte-nlsbjs"),_(i,"class","form-row svelte-nlsbjs")},m(f,g){x(f,e,g),C(e,t),b(n,t,null),x(f,a,g),x(f,i,g),C(i,o),b(c,o,null),d=!0},p(f,g){const S={};32&g[1]&&(S.$$scope={dirty:g,ctx:f}),!r&&256&g[0]&&(r=!0,S.value=f[8],Pe(()=>r=!1)),n.$set(S);const I={};32&g[1]&&(I.$$scope={dirty:g,ctx:f}),!l&&512&g[0]&&(l=!0,I.value=f[9],Pe(()=>l=!1)),c.$set(I)},i(f){d||(p(n.$$.fragment,f),p(c.$$.fragment,f),d=!0)},o(f){m(n.$$.fragment,f),m(c.$$.fragment,f),d=!1},d(f){f&&($(e),$(a),$(i)),k(n),k(c)}}}function ju(s){let e,t,n,r,a,i,o,c,l;function d(h){s[31](h)}n=new de({props:{size:1,weight:"medium",$$slots:{default:[Du]},$$scope:{ctx:s}}});let u={size:1,placeholder:"Paste JSON here..."};return s[10]!==void 0&&(u.value=s[10]),o=new Ua({props:u}),Oe.push(()=>Ie(o,"value",d)),{c(){e=M("div"),t=M("div"),w(n.$$.fragment),r=Z(),a=M("div"),i=M("div"),w(o.$$.fragment),_(t,"class","input-field svelte-nlsbjs"),_(e,"class","form-row svelte-nlsbjs"),_(i,"class","input-field svelte-nlsbjs"),_(a,"class","form-row svelte-nlsbjs")},m(h,v){x(h,e,v),C(e,t),b(n,t,null),x(h,r,v),x(h,a,v),C(a,i),b(o,i,null),l=!0},p(h,v){const y={};32&v[1]&&(y.$$scope={dirty:v,ctx:h}),n.$set(y);const f={};!c&&1024&v[0]&&(c=!0,f.value=h[10],Pe(()=>c=!1)),o.$set(f)},i(h){l||(p(n.$$.fragment,h),p(o.$$.fragment,h),l=!0)},o(h){m(n.$$.fragment,h),m(o.$$.fragment,h),l=!1},d(h){h&&($(e),$(r),$(a)),k(n),k(o)}}}function Ru(s){let e;return{c(){e=F("Name")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function Lu(s){let e,t;return e=new de({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[Ru]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};32&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Fu(s){let e;return{c(){e=F("Command")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function zu(s){let e,t;return e=new de({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[Fu]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};32&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Du(s){let e;return{c(){e=F("Code Snippet")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function fa(s){let e,t,n;function r(i){s[34](i)}let a={handleEnterEditMode:s[15]};return s[11]!==void 0&&(a.envVarEntries=s[11]),e=new Zu({props:a}),Oe.push(()=>Ie(e,"envVarEntries",r)),{c(){w(e.$$.fragment)},m(i,o){b(e,i,o),n=!0},p(i,o){const c={};!t&&2048&o[0]&&(t=!0,c.envVarEntries=i[11],Pe(()=>t=!1)),e.$set(c)},i(i){n||(p(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){k(e,i)}}}function Uu(s){let e;return{c(){e=F(s[1])},m(t,n){x(t,e,n)},p(t,n){2&n[0]&&we(e,t[1])},d(t){t&&$(e)}}}function Vu(s){let e,t;return e=new Wi({props:{slot:"icon"}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p:G,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function qu(s){let e;return{c(){e=F("Cancel")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function Bu(s){let e;return{c(){e=F("Save")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function Ju(s){let e;return{c(){e=F("Add")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function Gu(s){let e;return{c(){e=F("Import")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function Hu(s){let e;function t(a,i){return a[0]==="addJson"?Gu:a[0]==="add"?Ju:a[0]==="edit"?Bu:void 0}let n=t(s),r=n&&n(s);return{c(){r&&r.c(),e=Ce()},m(a,i){r&&r.m(a,i),x(a,e,i)},p(a,i){n!==(n=t(a))&&(r&&r.d(1),r=n&&n(a),r&&(r.c(),r.m(e.parentNode,e)))},d(a){a&&$(e),r&&r.d(a)}}}function Wu(s){let e;return{c(){e=M("div"),_(e,"class","c-dot svelte-nlsbjs"),xe(e,"c-green",!s[6]),xe(e,"c-warning",!s[6]&&!!s[7]),xe(e,"c-red",!!s[6]),xe(e,"c-disabled",s[3].disabled)},m(t,n){x(t,e,n)},p(t,n){64&n[0]&&xe(e,"c-green",!t[6]),192&n[0]&&xe(e,"c-warning",!t[6]&&!!t[7]),64&n[0]&&xe(e,"c-red",!!t[6]),8&n[0]&&xe(e,"c-disabled",t[3].disabled)},d(t){t&&$(e)}}}function Ku(s){let e,t=s[3].name+"";return{c(){e=F(t)},m(n,r){x(n,e,r)},p(n,r){8&r[0]&&t!==(t=n[3].name+"")&&we(e,t)},d(n){n&&$(e)}}}function Yu(s){let e,t,n;return t=new de({props:{size:1,weight:"medium",$$slots:{default:[Ku]},$$scope:{ctx:s}}}),{c(){e=M("div"),w(t.$$.fragment),_(e,"class","server-name svelte-nlsbjs")},m(r,a){x(r,e,a),b(t,e,null),n=!0},p(r,a){const i={};8&a[0]|32&a[1]&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&$(e),k(t)}}}function Xu(s){let e,t=s[3].command+"";return{c(){e=F(t)},m(n,r){x(n,e,r)},p(n,r){8&r[0]&&t!==(t=n[3].command+"")&&we(e,t)},d(n){n&&$(e)}}}function Qu(s){let e,t,n;return t=new de({props:{color:"secondary",size:1,weight:"regular",$$slots:{default:[Xu]},$$scope:{ctx:s}}}),{c(){e=M("div"),w(t.$$.fragment),_(e,"class","command-text svelte-nlsbjs")},m(r,a){x(r,e,a),b(t,e,null),n=!0},p(r,a){const i={};8&a[0]|32&a[1]&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&$(e),k(t)}}}function ep(s){let e,t,n,r,a,i,o;return t=new Gt({props:{content:s[6]||s[7],$$slots:{default:[Wu]},$$scope:{ctx:s}}}),r=new Gt({props:{content:s[3].name,side:"top",align:"start",$$slots:{default:[Yu]},$$scope:{ctx:s}}}),i=new Gt({props:{content:s[3].command,side:"top",align:"start",$$slots:{default:[Qu]},$$scope:{ctx:s}}}),{c(){e=M("div"),w(t.$$.fragment),n=Z(),w(r.$$.fragment),a=Z(),w(i.$$.fragment),_(e,"slot","header-left"),_(e,"class","l-header svelte-nlsbjs")},m(c,l){x(c,e,l),b(t,e,null),C(e,n),b(r,e,null),C(e,a),b(i,e,null),o=!0},p(c,l){const d={};192&l[0]&&(d.content=c[6]||c[7]),200&l[0]|32&l[1]&&(d.$$scope={dirty:l,ctx:c}),t.$set(d);const u={};8&l[0]&&(u.content=c[3].name),8&l[0]|32&l[1]&&(u.$$scope={dirty:l,ctx:c}),r.$set(u);const h={};8&l[0]&&(h.content=c[3].command),8&l[0]|32&l[1]&&(h.$$scope={dirty:l,ctx:c}),i.$set(h)},i(c){o||(p(t.$$.fragment,c),p(r.$$.fragment,c),p(i.$$.fragment,c),o=!0)},o(c){m(t.$$.fragment,c),m(r.$$.fragment,c),m(i.$$.fragment,c),o=!1},d(c){c&&$(e),k(t),k(r),k(i)}}}function tp(s){let e,t;return e=new lo({}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function np(s){let e,t;return e=new xs({props:{size:1,variant:"ghost-block",color:"neutral",$$slots:{default:[tp]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};32&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function sp(s){let e;return{c(){e=F("Edit")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function rp(s){let e,t,n,r,a;return t=new uo({}),r=new de({props:{size:1,weight:"medium",$$slots:{default:[sp]},$$scope:{ctx:s}}}),{c(){e=M("div"),w(t.$$.fragment),n=Z(),w(r.$$.fragment),_(e,"class","status-controls-button svelte-nlsbjs")},m(i,o){x(i,e,o),b(t,e,null),C(e,n),b(r,e,null),a=!0},p(i,o){const c={};32&o[1]&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&$(e),k(t),k(r)}}}function ap(s){let e;return{c(){e=F("Copy JSON")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function ip(s){let e,t,n,r,a;return t=new eo({}),r=new de({props:{size:1,weight:"medium",$$slots:{default:[ap]},$$scope:{ctx:s}}}),{c(){e=M("div"),w(t.$$.fragment),n=Z(),w(r.$$.fragment),_(e,"class","status-controls-button svelte-nlsbjs")},m(i,o){x(i,e,o),b(t,e,null),C(e,n),b(r,e,null),a=!0},p(i,o){const c={};32&o[1]&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&$(e),k(t),k(r)}}}function op(s){let e;return{c(){e=F("Delete")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function cp(s){let e,t,n,r,a;return t=new $i({}),r=new de({props:{size:1,weight:"medium",$$slots:{default:[op]},$$scope:{ctx:s}}}),{c(){e=M("div"),w(t.$$.fragment),n=Z(),w(r.$$.fragment),_(e,"class","status-controls-button svelte-nlsbjs")},m(i,o){x(i,e,o),b(t,e,null),C(e,n),b(r,e,null),a=!0},p(i,o){const c={};32&o[1]&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&$(e),k(t),k(r)}}}function lp(s){let e,t,n,r,a,i;return e=new Ae.Item({props:{onSelect:s[15],$$slots:{default:[rp]},$$scope:{ctx:s}}}),n=new Ae.Item({props:{onSelect:s[28],$$slots:{default:[ip]},$$scope:{ctx:s}}}),a=new Ae.Item({props:{color:"error",onSelect:s[29],$$slots:{default:[cp]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=Z(),w(n.$$.fragment),r=Z(),w(a.$$.fragment)},m(o,c){b(e,o,c),x(o,t,c),b(n,o,c),x(o,r,c),b(a,o,c),i=!0},p(o,c){const l={};32&c[1]&&(l.$$scope={dirty:c,ctx:o}),e.$set(l);const d={};4096&c[0]&&(d.onSelect=o[28]),32&c[1]&&(d.$$scope={dirty:c,ctx:o}),n.$set(d);const u={};4120&c[0]&&(u.onSelect=o[29]),32&c[1]&&(u.$$scope={dirty:c,ctx:o}),a.$set(u)},i(o){i||(p(e.$$.fragment,o),p(n.$$.fragment,o),p(a.$$.fragment,o),i=!0)},o(o){m(e.$$.fragment,o),m(n.$$.fragment,o),m(a.$$.fragment,o),i=!1},d(o){o&&($(t),$(r)),k(e,o),k(n,o),k(a,o)}}}function dp(s){let e,t,n,r;return e=new Ae.Trigger({props:{$$slots:{default:[np]},$$scope:{ctx:s}}}),n=new Ae.Content({props:{side:"bottom",align:"end",$$slots:{default:[lp]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=Z(),w(n.$$.fragment)},m(a,i){b(e,a,i),x(a,t,i),b(n,a,i),r=!0},p(a,i){const o={};32&i[1]&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};4120&i[0]|32&i[1]&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&$(t),k(e,a),k(n,a)}}}function up(s){let e,t,n,r,a,i,o;function c(d){s[30](d)}n=new Ai({props:{size:1,checked:!s[3].disabled}}),n.$on("change",s[27]);let l={$$slots:{default:[dp]},$$scope:{ctx:s}};return s[12]!==void 0&&(l.requestClose=s[12]),a=new Ae.Root({props:l}),Oe.push(()=>Ie(a,"requestClose",c)),{c(){e=M("div"),t=M("div"),w(n.$$.fragment),r=Z(),w(a.$$.fragment),_(t,"class","status-controls svelte-nlsbjs"),_(e,"class","server-actions svelte-nlsbjs"),_(e,"slot","header-right")},m(d,u){x(d,e,u),C(e,t),b(n,t,null),C(t,r),b(a,t,null),o=!0},p(d,u){const h={};8&u[0]&&(h.checked=!d[3].disabled),n.$set(h);const v={};4120&u[0]|32&u[1]&&(v.$$scope={dirty:u,ctx:d}),!i&&4096&u[0]&&(i=!0,v.requestClose=d[12],Pe(()=>i=!1)),a.$set(v)},i(d){o||(p(n.$$.fragment,d),p(a.$$.fragment,d),o=!0)},o(d){m(n.$$.fragment,d),m(a.$$.fragment,d),o=!1},d(d){d&&$(e),k(n),k(a)}}}function pp(s){let e,t,n,r;const a=[Ou,Eu],i=[];function o(c,l){return c[0]==="view"&&c[3]?0:1}return e=o(s),t=i[e]=a[e](s),{c(){t.c(),n=Ce()},m(c,l){i[e].m(c,l),x(c,n,l),r=!0},p(c,l){let d=e;e=o(c),e===d?i[e].p(c,l):(H(),m(i[d],1,1,()=>{i[d]=null}),W(),t=i[e],t?t.p(c,l):(t=i[e]=a[e](c),t.c()),p(t,1),t.m(n.parentNode,n))},i(c){r||(p(t),r=!0)},o(c){m(t),r=!1},d(c){c&&$(n),i[e].d(c)}}}function mp({key:s,value:e}){return s.trim()&&e.trim()}function fp(s,e,t){let n,r,a,i,{server:o=null}=e,{onDelete:c}=e,{onAdd:l}=e,{onSave:d}=e,{onEdit:u}=e,{onToggleDisableServer:h}=e,{onJSONImport:v}=e,{onCancel:y}=e,{disabledText:f}=e,{warningText:g}=e,{mode:S="view"}=e,{mcpServerError:I=""}=e,N=(o==null?void 0:o.name)??"",T=(o==null?void 0:o.command)??"",A=(o==null?void 0:o.env)??{},P="",U=[];function ne(){t(11,U=Object.entries(A).map(([J,ie])=>({id:crypto.randomUUID(),key:J,value:ie})))}ne();let V=()=>{},{busy:K=!1}=e;function L(){if(o){const J=Ks.convertServerToJSON(o);navigator.clipboard.writeText(J)}}return s.$$set=J=>{"server"in J&&t(3,o=J.server),"onDelete"in J&&t(4,c=J.onDelete),"onAdd"in J&&t(19,l=J.onAdd),"onSave"in J&&t(20,d=J.onSave),"onEdit"in J&&t(21,u=J.onEdit),"onToggleDisableServer"in J&&t(5,h=J.onToggleDisableServer),"onJSONImport"in J&&t(22,v=J.onJSONImport),"onCancel"in J&&t(23,y=J.onCancel),"disabledText"in J&&t(6,f=J.disabledText),"warningText"in J&&t(7,g=J.warningText),"mode"in J&&t(0,S=J.mode),"mcpServerError"in J&&t(1,I=J.mcpServerError),"busy"in J&&t(2,K=J.busy)},s.$$.update=()=>{768&s.$$.dirty[0]&&N&&T&&t(1,I=""),769&s.$$.dirty[0]&&t(26,n=!(S!=="add"||N.trim()&&T.trim())),1025&s.$$.dirty[0]&&t(25,r=S==="addJson"&&!P.trim()),100663297&s.$$.dirty[0]&&t(14,a=n||S==="view"||r),1&s.$$.dirty[0]&&t(13,i=S==="add"||S==="addJson"?"New MCP Server":"Edit MCP Server")},[S,I,K,o,c,h,f,g,N,T,P,U,V,i,a,function(){o&&S==="view"&&(t(0,S="edit"),u(o),V())},L,async function(){t(1,I=""),t(2,K=!0);const J=U.filter(mp);A=Object.fromEntries(J.map(({key:Y,value:fe})=>[Y.trim(),fe.trim()])),ne();try{if(S==="add")await l({name:N.trim(),command:T.trim(),arguments:"",useShellInterpolation:!0,env:Object.keys(A).length>0?A:void 0});else if(S==="addJson"){try{JSON.parse(P)}catch(Y){const fe=Y instanceof Error?Y.message:String(Y);throw new Ee(`Invalid JSON format: ${fe}`)}await v(P)}else S==="edit"&&o&&await d({...o,name:N.trim(),command:T.trim(),arguments:"",env:Object.keys(A).length>0?A:void 0})}catch(Y){t(1,I=(ie=Y)!=null&&typeof ie=="object"&&(ie instanceof Ee||vi in(ie.constructor||ie))?Y.message:"Failed to save server"),console.warn(Y)}finally{t(2,K=!1)}var ie},function(){t(2,K=!1),t(1,I=""),y==null||y(),t(10,P=""),t(8,N=(o==null?void 0:o.name)??""),t(9,T=(o==null?void 0:o.command)??""),A=o!=null&&o.env?{...o.env}:{},ne()},l,d,u,v,y,ne,r,n,()=>{o&&h(o.id),V()},()=>{L(),V()},()=>{c(o.id),V()},function(J){V=J,t(12,V)},function(J){P=J,t(10,P)},function(J){N=J,t(8,N)},function(J){T=J,t(9,T)},function(J){U=J,t(11,U)}]}class yi extends he{constructor(e){super(),ge(this,e,fp,pp,me,{server:3,onDelete:4,onAdd:19,onSave:20,onEdit:21,onToggleDisableServer:5,onJSONImport:22,onCancel:23,disabledText:6,warningText:7,mode:0,mcpServerError:1,setLocalEnvVarFormState:24,busy:2},null,[-1,-1])}get setLocalEnvVarFormState(){return this.$$.ctx[24]}}function hp(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=ke(r,n[a]);return{c(){e=qe("svg"),t=new Un(!0),this.h()},l(a){e=Vn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=qn(e);t=Bn(i,!0),i.forEach($),this.h()},h(){t.a=null,lt(e,r)},m(a,i){Jn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M280 24c0-13.3-10.7-24-24-24s-24 10.7-24 24v270.1l-95-95c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9L239 369c9.4 9.4 24.6 9.4 33.9 0L409 233c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-95 95zM128.8 304H64c-35.3 0-64 28.7-64 64v80c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-80c0-35.3-28.7-64-64-64h-64.8l-48 48H448c8.8 0 16 7.2 16 16v80c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-80c0-8.8 7.2-16 16-16h112.8zM432 408a24 24 0 1 0-48 0 24 24 0 1 0 48 0"/>',e)},p(a,[i]){lt(e,r=yt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&a[0]]))},i:G,o:G,d(a){a&&$(e)}}}function gp(s,e,t){return s.$$set=n=>{t(0,e=ke(ke({},e),Xe(n)))},[e=Xe(e)]}class _i extends he{constructor(e){super(),ge(this,e,gp,hp,me,{})}}function ha(s,e,t){const n=s.slice();return n[24]=e[t],n}function vp(s){let e,t,n;return t=new Ni({}),{c(){e=M("div"),w(t.$$.fragment),_(e,"slot","iconLeft"),_(e,"class","search-icon")},m(r,a){x(r,e,a),b(t,e,null),n=!0},p:G,i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&$(e),k(t)}}}function $p(s){let e,t=s[24].label+"";return{c(){e=F(t)},m(n,r){x(n,e,r)},p:G,d(n){n&&$(e)}}}function yp(s){let e,t=s[24].description+"";return{c(){e=F(t)},m(n,r){x(n,e,r)},p:G,d(n){n&&$(e)}}}function ga(s){let e,t,n,r,a,i,o,c,l,d;function u(y){s[15](y)}function h(y){s[16](y)}let v={placeholder:"Enter your API key...",size:1,variant:"surface",type:"password"};return s[4]!==void 0&&(v.value=s[4]),s[5]!==void 0&&(v.textInput=s[5]),t=new Wt({props:v}),Oe.push(()=>Ie(t,"value",u)),Oe.push(()=>Ie(t,"textInput",h)),t.$on("keydown",function(...y){return s[17](s[24],...y)}),o=new Ne({props:{variant:"ghost-block",color:"accent",size:1,$$slots:{default:[_p]},$$scope:{ctx:s}}}),o.$on("click",function(){return s[18](s[24])}),l=new Ne({props:{variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[xp]},$$scope:{ctx:s}}}),l.$on("click",s[10]),{c(){e=M("div"),w(t.$$.fragment),a=Z(),i=M("div"),w(o.$$.fragment),c=Z(),w(l.$$.fragment),_(i,"class","api-key-actions svelte-nkd86k"),_(e,"class","api-key-input-container svelte-nkd86k")},m(y,f){x(y,e,f),b(t,e,null),C(e,a),C(e,i),b(o,i,null),C(i,c),b(l,i,null),d=!0},p(y,f){s=y;const g={};!n&&16&f&&(n=!0,g.value=s[4],Pe(()=>n=!1)),!r&&32&f&&(r=!0,g.textInput=s[5],Pe(()=>r=!1)),t.$set(g);const S={};134217728&f&&(S.$$scope={dirty:f,ctx:s}),o.$set(S);const I={};134217728&f&&(I.$$scope={dirty:f,ctx:s}),l.$set(I)},i(y){d||(p(t.$$.fragment,y),p(o.$$.fragment,y),p(l.$$.fragment,y),d=!0)},o(y){m(t.$$.fragment,y),m(o.$$.fragment,y),m(l.$$.fragment,y),d=!1},d(y){y&&$(e),k(t),k(o),k(l)}}}function _p(s){let e;return{c(){e=F("Install")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function xp(s){let e;return{c(){e=F("Cancel")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function wp(s){let e,t,n,r,a,i;n=new de({props:{size:1,weight:"medium",$$slots:{default:[$p]},$$scope:{ctx:s}}});let o=s[24].description&&function(l){let d,u;return d=new de({props:{size:1,color:"secondary",$$slots:{default:[yp]},$$scope:{ctx:l}}}),{c(){w(d.$$.fragment)},m(h,v){b(d,h,v),u=!0},p(h,v){const y={};134217728&v&&(y.$$scope={dirty:v,ctx:h}),d.$set(y)},i(h){u||(p(d.$$.fragment,h),u=!0)},o(h){m(d.$$.fragment,h),u=!1},d(h){k(d,h)}}}(s),c=s[3]===s[24].value&&ga(s);return{c(){e=M("div"),t=M("div"),w(n.$$.fragment),r=Z(),o&&o.c(),a=Z(),c&&c.c(),_(t,"class","mcp-service-title svelte-nkd86k"),_(e,"slot","header-left"),_(e,"class","mcp-service-info svelte-nkd86k")},m(l,d){x(l,e,d),C(e,t),b(n,t,null),C(e,r),o&&o.m(e,null),C(e,a),c&&c.m(e,null),i=!0},p(l,d){const u={};134217728&d&&(u.$$scope={dirty:d,ctx:l}),n.$set(u),l[24].description&&o.p(l,d),l[3]===l[24].value?c?(c.p(l,d),8&d&&p(c,1)):(c=ga(l),c.c(),p(c,1),c.m(e,null)):c&&(H(),m(c,1,1,()=>{c=null}),W())},i(l){i||(p(n.$$.fragment,l),p(o),p(c),i=!0)},o(l){m(n.$$.fragment,l),m(o),m(c),i=!1},d(l){l&&$(e),k(n),o&&o.d(),c&&c.d()}}}function bp(s){let e,t;return e=new Ne({props:{variant:"ghost-block",color:"accent",size:1,$$slots:{default:[Sp]},$$scope:{ctx:s}}}),e.$on("click",function(){return s[14](s[24])}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){s=n;const a={};134217728&r&&(a.$$scope={dirty:r,ctx:s}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function kp(s){let e,t,n;return t=new La.Root({props:{color:"success",size:1,variant:"soft",$$slots:{default:[Cp]},$$scope:{ctx:s}}}),{c(){e=M("div"),w(t.$$.fragment),_(e,"class","installed-indicator svelte-nkd86k")},m(r,a){x(r,e,a),b(t,e,null),n=!0},p(r,a){const i={};134217728&a&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&$(e),k(t)}}}function Sp(s){let e;return{c(){e=M("span"),e.textContent="+"},m(t,n){x(t,e,n)},p:G,d(t){t&&$(e)}}}function Cp(s){let e;return{c(){e=F("Installed")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function Tp(s){let e,t,n,r,a;function i(...d){return s[12](s[24],...d)}const o=[kp,bp],c=[];function l(d,u){return 1&u&&(t=null),t==null&&(t=!!d[0].some(i)),t?0:1}return n=l(s,-1),r=c[n]=o[n](s),{c(){e=M("div"),r.c(),_(e,"slot","header-right"),_(e,"class","mcp-service-actions svelte-nkd86k")},m(d,u){x(d,e,u),c[n].m(e,null),a=!0},p(d,u){let h=n;n=l(s=d,u),n===h?c[n].p(s,u):(H(),m(c[h],1,1,()=>{c[h]=null}),W(),r=c[n],r?r.p(s,u):(r=c[n]=o[n](s),r.c()),p(r,1),r.m(e,null))},i(d){a||(p(r),a=!0)},o(d){m(r),a=!1},d(d){d&&$(e),c[n].d()}}}function va(s){let e,t,n,r;return t=new ws({props:{$$slots:{"header-right":[Tp],"header-left":[wp]},$$scope:{ctx:s}}}),{c(){e=M("div"),w(t.$$.fragment),n=Z(),_(e,"class","mcp-service-item")},m(a,i){x(a,e,i),b(t,e,null),C(e,n),r=!0},p(a,i){const o={};134217785&i&&(o.$$scope={dirty:i,ctx:a}),t.$set(o)},i(a){r||(p(t.$$.fragment,a),r=!0)},o(a){m(t.$$.fragment,a),r=!1},d(a){a&&$(e),k(t)}}}function Mp(s){let e,t,n,r,a=s[6].length>9&&function(l){let d,u,h,v;function y(g){l[13](g)}let f={placeholder:"Search MCPs...",size:1,variant:"surface",$$slots:{iconLeft:[vp]},$$scope:{ctx:l}};return l[2]!==void 0&&(f.value=l[2]),u=new Wt({props:f}),Oe.push(()=>Ie(u,"value",y)),u.$on("input",l[7]),{c(){d=M("div"),w(u.$$.fragment),_(d,"class","mcp-search-container")},m(g,S){x(g,d,S),b(u,d,null),v=!0},p(g,S){const I={};134217728&S&&(I.$$scope={dirty:S,ctx:g}),!h&&4&S&&(h=!0,I.value=g[2],Pe(()=>h=!1)),u.$set(I)},i(g){v||(p(u.$$.fragment,g),v=!0)},o(g){m(u.$$.fragment,g),v=!1},d(g){g&&$(d),k(u)}}}(s),i=$e(s[6]),o=[];for(let l=0;l<i.length;l+=1)o[l]=va(ha(s,i,l));const c=l=>m(o[l],1,1,()=>{o[l]=null});return{c(){e=M("div"),a&&a.c(),t=Z(),n=M("div");for(let l=0;l<o.length;l+=1)o[l].c();_(n,"class","mcp-list-container svelte-nkd86k"),_(e,"class","mcp-install-content svelte-nkd86k")},m(l,d){x(l,e,d),a&&a.m(e,null),C(e,t),C(e,n);for(let u=0;u<o.length;u+=1)o[u]&&o[u].m(n,null);r=!0},p(l,d){if(l[6].length>9&&a.p(l,d),1913&d){let u;for(i=$e(l[6]),u=0;u<i.length;u+=1){const h=ha(l,i,u);o[u]?(o[u].p(h,d),p(o[u],1)):(o[u]=va(h),o[u].c(),p(o[u],1),o[u].m(n,null))}for(H(),u=i.length;u<o.length;u+=1)c(u);W()}},i(l){if(!r){p(a);for(let d=0;d<i.length;d+=1)p(o[d]);r=!0}},o(l){m(a),o=o.filter(Boolean);for(let d=0;d<o.length;d+=1)m(o[d]);r=!1},d(l){l&&$(e),a&&a.d(),sn(o,l)}}}function Ap(s){let e;return{c(){e=F("Easy MCP Installation")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function Np(s){let e,t,n,r,a;return t=new mo({}),r=new de({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[Ap]},$$scope:{ctx:s}}}),{c(){e=M("div"),w(t.$$.fragment),n=Z(),w(r.$$.fragment),_(e,"slot","header-left"),_(e,"class","mcp-install-left svelte-nkd86k")},m(i,o){x(i,e,o),b(t,e,null),C(e,n),b(r,e,null),a=!0},p(i,o){const c={};134217728&o&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&$(e),k(t),k(r)}}}function Zp(s){let e,t,n;return t=new ws({props:{$$slots:{"header-left":[Np]},$$scope:{ctx:s}}}),{c(){e=M("div"),w(t.$$.fragment),_(e,"slot","header"),_(e,"class","mcp-install-header svelte-nkd86k")},m(r,a){x(r,e,a),b(t,e,null),n=!0},p(r,a){const i={};134217728&a&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&$(e),k(t)}}}function Ep(s){let e,t,n,r;function a(o){s[19](o)}let i={$$slots:{header:[Zp],default:[Mp]},$$scope:{ctx:s}};return s[1]!==void 0&&(i.collapsed=s[1]),t=new po({props:i}),Oe.push(()=>Ie(t,"collapsed",a)),{c(){e=M("div"),w(t.$$.fragment),_(e,"class","mcp-install-wrapper svelte-nkd86k")},m(o,c){x(o,e,c),b(t,e,null),r=!0},p(o,[c]){const l={};134217789&c&&(l.$$scope={dirty:c,ctx:o}),!n&&2&c&&(n=!0,l.collapsed=o[1],Pe(()=>n=!1)),t.$set(l)},i(o){r||(p(t.$$.fragment,o),r=!0)},o(o){m(t.$$.fragment,o),r=!1},d(o){o&&$(e),k(t)}}}function Op(s,e){return s.value==="tavily"?`npx -y tavily-mcp@latest --TAVILY_API_KEY=${e}`:s.command}function Ip(s,e){switch(s.value){case"tavily":return{};case"exa-search":return{EXA_API_KEY:e};default:return{API_KEY:e}}}function Pp(s,e,t){let{onMCPServerAdd:n}=e,{servers:r=[]}=e;const a=[{value:"context7",label:"Context 7",description:"Package documentation",command:"npx -y @upstash/context7-mcp@latest"},{value:"playwright",label:"Playwright",description:"Browser automation",command:"npx -y @playwright/mcp@latest"},{value:"sequential-thinking",label:"Sequential thinking",description:"Think through complex problems step-by-step.",command:"npx -y @modelcontextprotocol/server-sequential-thinking"}];let i,o=!0,c="",l=null,d=null,u="";function h(g){l&&clearTimeout(l),l=setTimeout(()=>{(function(S){if(!S.trim())return;const I=S.toLowerCase();a.filter(N=>N.label.toLowerCase().includes(I)||N.description&&N.description.toLowerCase().includes(I))})(g)},300)}async function v(g){try{if(r.some(I=>I.name===g.label))return;const S={name:g.label,command:g.command,arguments:"",useShellInterpolation:!0,env:void 0};n&&n(S)}catch(S){console.error(`Failed to install ${g.label}:`,S)}}async function y(g){try{if(!u.trim())return void(i==null?void 0:i.focus());const S={name:g.label,command:Op(g,u.trim()),arguments:"",useShellInterpolation:!0,env:Ip(g,u.trim())};n&&n(S),t(3,d=null),t(4,u="")}catch(S){console.error(`Failed to install ${g.label}:`,S)}}function f(){t(3,d=null),t(4,u="")}return s.$$set=g=>{"onMCPServerAdd"in g&&t(11,n=g.onMCPServerAdd),"servers"in g&&t(0,r=g.servers)},[r,o,c,d,u,i,a,function(g){const S=g.target;t(2,c=S.value),h(c)},v,y,f,n,(g,S)=>S.name===g.label,function(g){c=g,t(2,c)},g=>v(g),function(g){u=g,t(4,u)},function(g){i=g,t(5,i)},(g,S)=>{S.key==="Enter"?y(g):S.key==="Escape"&&f()},g=>y(g),function(g){o=g,t(1,o)}]}class jp extends he{constructor(e){super(),ge(this,e,Pp,Ep,me,{onMCPServerAdd:11,servers:0})}}const Rp={mcpDocsURL:"https://docs.augmentcode.com/setup-augment/mcp"},Lp={mcpDocsURL:"https://docs.augmentcode.com/jetbrains/setup-augment/mcp"},Fp=Ii(),zp=new class{constructor(s){ve(this,"strings");let e={[ar.vscode]:{},[ar.jetbrains]:Lp};this.strings={...Rp,...e[s]}}get(s){return this.strings[s]}}(Fp.clientType);function $a(s,e,t){const n=s.slice();return n[23]=e[t],n}function Dp(s){let e;return{c(){e=M("div"),e.textContent="MCP",_(e,"class","section-heading-text")},m(t,n){x(t,e,n)},p:G,d(t){t&&$(e)}}}function ya(s,e){let t,n,r;return n=new yi({props:{mode:e[2]===e[23].id?"edit":"view",server:e[23],onAdd:e[8],onSave:e[9],onDelete:e[11],onToggleDisableServer:e[12],onEdit:e[7],onCancel:e[6],onJSONImport:e[10],disabledText:e[4].errors.get(e[23].id),warningText:e[4].warnings.get(e[23].id)}}),{key:s,first:null,c(){t=Ce(),w(n.$$.fragment),this.first=t},m(a,i){x(a,t,i),b(n,a,i),r=!0},p(a,i){e=a;const o={};5&i&&(o.mode=e[2]===e[23].id?"edit":"view"),1&i&&(o.server=e[23]),17&i&&(o.disabledText=e[4].errors.get(e[23].id)),17&i&&(o.warningText=e[4].warnings.get(e[23].id)),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&$(t),k(n,a)}}}function _a(s){let e,t;return e=new yi({props:{mode:s[3],onAdd:s[8],onSave:s[9],onDelete:s[11],onToggleDisableServer:s[12],onEdit:s[7],onCancel:s[6],onJSONImport:s[10]}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};8&r&&(a.mode=n[3]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Up(s){let e;return{c(){e=F("Add MCP")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function Vp(s){let e,t;return e=new _s({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p:G,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function xa(s){let e,t;return e=new Ne({props:{disabled:s[5],color:"neutral",variant:"soft",size:1,title:"Add MCP from JSON",$$slots:{iconLeft:[Bp],default:[qp]},$$scope:{ctx:s}}}),e.$on("click",s[21]),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};32&r&&(a.disabled=n[5]),67108864&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function qp(s){let e;return{c(){e=F("Import from JSON")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function Bp(s){let e,t;return e=new _i({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p:G,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Jp(s){let e,t,n,r,a,i,o,c,l,d,u,h,v,y,f,g,S,I,N=[],T=new Map;n=new de({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[Dp]},$$scope:{ctx:s}}}),u=new jp({props:{onMCPServerAdd:s[8],servers:s[0]}});let A=$e(s[0]);const P=V=>V[23].id;for(let V=0;V<A.length;V+=1){let K=$a(s,A,V),L=P(K);T.set(L,N[V]=ya(L,K))}let U=(s[3]==="add"||s[3]==="addJson")&&_a(s);g=new Ne({props:{disabled:s[5],color:"neutral",variant:"soft",size:1,$$slots:{iconLeft:[Vp],default:[Up]},$$scope:{ctx:s}}}),g.$on("click",s[20]);let ne=s[1]&&xa(s);return{c(){e=M("div"),t=M("div"),w(n.$$.fragment),r=Z(),a=M("div"),i=F(`Configure a new Model Context Protocol server to connect Augment to custom tools. Find out more
    about MCP `),o=M("a"),c=F("in the docs"),l=F("."),d=Z(),w(u.$$.fragment),h=Z();for(let V=0;V<N.length;V+=1)N[V].c();v=Z(),U&&U.c(),y=Z(),f=M("div"),w(g.$$.fragment),S=Z(),ne&&ne.c(),_(t,"class","section-heading svelte-1vnq4q3"),_(o,"href",s[13]),_(a,"class","description-text svelte-1vnq4q3"),_(e,"class","mcp-servers svelte-1vnq4q3"),_(f,"class","add-mcp-button-container svelte-1vnq4q3")},m(V,K){x(V,e,K),C(e,t),b(n,t,null),C(e,r),C(e,a),C(a,i),C(a,o),C(o,c),C(a,l),C(e,d),b(u,e,null),C(e,h);for(let L=0;L<N.length;L+=1)N[L]&&N[L].m(e,null);x(V,v,K),U&&U.m(V,K),x(V,y,K),x(V,f,K),b(g,f,null),C(f,S),ne&&ne.m(f,null),I=!0},p(V,[K]){const L={};67108864&K&&(L.$$scope={dirty:K,ctx:V}),n.$set(L);const J={};1&K&&(J.servers=V[0]),u.$set(J),8149&K&&(A=$e(V[0]),H(),N=Et(N,K,P,1,V,A,T,e,Ot,ya,null,$a),W()),V[3]==="add"||V[3]==="addJson"?U?(U.p(V,K),8&K&&p(U,1)):(U=_a(V),U.c(),p(U,1),U.m(y.parentNode,y)):U&&(H(),m(U,1,1,()=>{U=null}),W());const ie={};32&K&&(ie.disabled=V[5]),67108864&K&&(ie.$$scope={dirty:K,ctx:V}),g.$set(ie),V[1]?ne?(ne.p(V,K),2&K&&p(ne,1)):(ne=xa(V),ne.c(),p(ne,1),ne.m(f,null)):ne&&(H(),m(ne,1,1,()=>{ne=null}),W())},i(V){if(!I){p(n.$$.fragment,V),p(u.$$.fragment,V);for(let K=0;K<A.length;K+=1)p(N[K]);p(U),p(g.$$.fragment,V),p(ne),I=!0}},o(V){m(n.$$.fragment,V),m(u.$$.fragment,V);for(let K=0;K<N.length;K+=1)m(N[K]);m(U),m(g.$$.fragment,V),m(ne),I=!1},d(V){V&&($(e),$(v),$(y),$(f)),k(n),k(u);for(let K=0;K<N.length;K+=1)N[K].d();U&&U.d(V),k(g),ne&&ne.d()}}}function Gp(s,e,t){let n,r,{servers:a}=e,{onMCPServerAdd:i}=e,{onMCPServerSave:o}=e,{onMCPServerDelete:c}=e,{onMCPServerToggleDisable:l}=e,{onCancel:d}=e,{onMCPServerJSONImport:u}=e,{isMCPImportEnabled:h=!0}=e,v=null,y=null;function f(P){return async function(...U){const ne=await P(...U);return t(3,y=null),t(2,v=null),ne}}const g=f(i),S=f(o),I=f(u),N=f(c),T=f(l),A=zp.get("mcpDocsURL");return s.$$set=P=>{"servers"in P&&t(0,a=P.servers),"onMCPServerAdd"in P&&t(14,i=P.onMCPServerAdd),"onMCPServerSave"in P&&t(15,o=P.onMCPServerSave),"onMCPServerDelete"in P&&t(16,c=P.onMCPServerDelete),"onMCPServerToggleDisable"in P&&t(17,l=P.onMCPServerToggleDisable),"onCancel"in P&&t(18,d=P.onCancel),"onMCPServerJSONImport"in P&&t(19,u=P.onMCPServerJSONImport),"isMCPImportEnabled"in P&&t(1,h=P.isMCPImportEnabled)},s.$$.update=()=>{12&s.$$.dirty&&t(5,n=y==="add"||y==="addJson"||v!==null),1&s.$$.dirty&&t(4,r=Ks.parseServerValidationMessages(a))},[a,h,v,y,r,n,function(){t(2,v=null),t(3,y=null),d==null||d()},function(P){t(2,v=P.id)},g,S,I,N,T,A,i,o,c,l,d,u,()=>{t(3,y="add")},()=>{t(3,y="addJson")}]}class Hp extends he{constructor(e){super(),ge(this,e,Gp,Jp,me,{servers:0,onMCPServerAdd:14,onMCPServerSave:15,onMCPServerDelete:16,onMCPServerToggleDisable:17,onCancel:18,onMCPServerJSONImport:19,isMCPImportEnabled:1})}}function wa(s,e,t){const n=s.slice();return n[12]=e[t],n}function Wp(s){let e;return{c(){e=M("div"),e.textContent="Terminal",_(e,"class","section-heading-text")},m(t,n){x(t,e,n)},p:G,d(t){t&&$(e)}}}function Kp(s){let e;return{c(){e=F("Shell:")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function Yp(s){let e;return{c(){e=F("Select a shell")},m(t,n){x(t,e,n)},p:G,d(t){t&&$(e)}}}function Xp(s){let e;return{c(){e=F("No shells available")},m(t,n){x(t,e,n)},p:G,d(t){t&&$(e)}}}function Qp(s){let e,t,n,r,a=s[5].friendlyName+"",i=s[5].supportString+"";return{c(){e=F(a),t=F(`
            (`),n=F(i),r=F(")")},m(o,c){x(o,e,c),x(o,t,c),x(o,n,c),x(o,r,c)},p(o,c){32&c&&a!==(a=o[5].friendlyName+"")&&we(e,a),32&c&&i!==(i=o[5].supportString+"")&&we(n,i)},d(o){o&&($(e),$(t),$(n),$(r))}}}function em(s){let e;function t(a,i){return a[5]&&a[1].length>0?Qp:a[1].length===0?Xp:Yp}let n=t(s),r=n(s);return{c(){r.c(),e=Ce()},m(a,i){r.m(a,i),x(a,e,i)},p(a,i){n===(n=t(a))&&r?r.p(a,i):(r.d(1),r=n(a),r&&(r.c(),r.m(e.parentNode,e)))},d(a){a&&$(e),r.d(a)}}}function tm(s){let e,t;return e=new Ki({props:{slot:"iconRight"}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p:G,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function nm(s){let e,t;return e=new Ne({props:{size:1,variant:"outline",color:"neutral",disabled:s[1].length===0,$$slots:{iconRight:[tm],default:[em]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};2&r&&(a.disabled=n[1].length===0),32802&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function sm(s){let e,t;return e=new Ae.Label({props:{$$slots:{default:[am]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};32768&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function rm(s){let e,t,n=[],r=new Map,a=$e(s[1]);const i=o=>o[12].friendlyName;for(let o=0;o<a.length;o+=1){let c=wa(s,a,o),l=i(c);r.set(l,n[o]=ba(l,c))}return{c(){for(let o=0;o<n.length;o+=1)n[o].c();e=Ce()},m(o,c){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,c);x(o,e,c),t=!0},p(o,c){30&c&&(a=$e(o[1]),H(),n=Et(n,c,i,1,o,a,r,e.parentNode,Ot,ba,e,wa),W())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&$(e);for(let c=0;c<n.length;c+=1)n[c].d(o)}}}function am(s){let e;return{c(){e=F("No shells available")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function im(s){let e,t,n,r,a=s[12].friendlyName+"",i=s[12].supportString+"";return{c(){e=F(a),t=F(`
              (`),n=F(i),r=F(`)
            `)},m(o,c){x(o,e,c),x(o,t,c),x(o,n,c),x(o,r,c)},p(o,c){2&c&&a!==(a=o[12].friendlyName+"")&&we(e,a),2&c&&i!==(i=o[12].supportString+"")&&we(n,i)},d(o){o&&($(e),$(t),$(n),$(r))}}}function ba(s,e){let t,n,r;function a(){return e[8](e[12])}return n=new Ae.Item({props:{onSelect:a,highlight:e[2]===e[12].friendlyName,$$slots:{default:[im]},$$scope:{ctx:e}}}),{key:s,first:null,c(){t=Ce(),w(n.$$.fragment),this.first=t},m(i,o){x(i,t,o),b(n,i,o),r=!0},p(i,o){e=i;const c={};26&o&&(c.onSelect=a),6&o&&(c.highlight=e[2]===e[12].friendlyName),32770&o&&(c.$$scope={dirty:o,ctx:e}),n.$set(c)},i(i){r||(p(n.$$.fragment,i),r=!0)},o(i){m(n.$$.fragment,i),r=!1},d(i){i&&$(t),k(n,i)}}}function om(s){let e,t,n,r;const a=[rm,sm],i=[];function o(c,l){return c[1].length>0?0:1}return e=o(s),t=i[e]=a[e](s),{c(){t.c(),n=Ce()},m(c,l){i[e].m(c,l),x(c,n,l),r=!0},p(c,l){let d=e;e=o(c),e===d?i[e].p(c,l):(H(),m(i[d],1,1,()=>{i[d]=null}),W(),t=i[e],t?t.p(c,l):(t=i[e]=a[e](c),t.c()),p(t,1),t.m(n.parentNode,n))},i(c){r||(p(t),r=!0)},o(c){m(t),r=!1},d(c){c&&$(n),i[e].d(c)}}}function cm(s){let e,t,n,r;return e=new Ae.Trigger({props:{$$slots:{default:[nm]},$$scope:{ctx:s}}}),n=new Ae.Content({props:{side:"bottom",align:"start",$$slots:{default:[om]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=Z(),w(n.$$.fragment)},m(a,i){b(e,a,i),x(a,t,i),b(n,a,i),r=!0},p(a,i){const o={};32802&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};32798&i&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&$(t),k(e,a),k(n,a)}}}function lm(s){let e;return{c(){e=F("Start-up script: Code to run wherever a new terminal is opened")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function dm(s){let e,t,n,r,a,i,o,c,l,d,u,h,v,y,f;function g(T){s[9](T)}t=new de({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[Wp]},$$scope:{ctx:s}}}),a=new de({props:{size:1,$$slots:{default:[Kp]},$$scope:{ctx:s}}});let S={$$slots:{default:[cm]},$$scope:{ctx:s}};function I(T){s[10](T)}s[4]!==void 0&&(S.requestClose=s[4]),o=new Ae.Root({props:S}),Oe.push(()=>Ie(o,"requestClose",g)),u=new de({props:{size:1,$$slots:{default:[lm]},$$scope:{ctx:s}}});let N={placeholder:"Enter shell commands to run on terminal startup",resize:"vertical"};return s[0]!==void 0&&(N.value=s[0]),v=new Ua({props:N}),Oe.push(()=>Ie(v,"value",I)),v.$on("change",s[6]),{c(){e=M("div"),w(t.$$.fragment),n=Z(),r=M("div"),w(a.$$.fragment),i=Z(),w(o.$$.fragment),l=Z(),d=M("div"),w(u.$$.fragment),h=Z(),w(v.$$.fragment),_(r,"class","shell-selector svelte-dndd5n"),_(d,"class","startup-script-container svelte-dndd5n"),_(e,"class","terminal-settings svelte-dndd5n")},m(T,A){x(T,e,A),b(t,e,null),C(e,n),C(e,r),b(a,r,null),C(r,i),b(o,r,null),C(e,l),C(e,d),b(u,d,null),C(d,h),b(v,d,null),f=!0},p(T,[A]){const P={};32768&A&&(P.$$scope={dirty:A,ctx:T}),t.$set(P);const U={};32768&A&&(U.$$scope={dirty:A,ctx:T}),a.$set(U);const ne={};32830&A&&(ne.$$scope={dirty:A,ctx:T}),!c&&16&A&&(c=!0,ne.requestClose=T[4],Pe(()=>c=!1)),o.$set(ne);const V={};32768&A&&(V.$$scope={dirty:A,ctx:T}),u.$set(V);const K={};!y&&1&A&&(y=!0,K.value=T[0],Pe(()=>y=!1)),v.$set(K)},i(T){f||(p(t.$$.fragment,T),p(a.$$.fragment,T),p(o.$$.fragment,T),p(u.$$.fragment,T),p(v.$$.fragment,T),f=!0)},o(T){m(t.$$.fragment,T),m(a.$$.fragment,T),m(o.$$.fragment,T),m(u.$$.fragment,T),m(v.$$.fragment,T),f=!1},d(T){T&&$(e),k(t),k(a),k(o),k(u),k(v)}}}function um(s,e,t){let n,r,{supportedShells:a=[]}=e,{selectedShell:i}=e,{startupScript:o}=e,{onShellSelect:c}=e,{onStartupScriptChange:l}=e;return s.$$set=d=>{"supportedShells"in d&&t(1,a=d.supportedShells),"selectedShell"in d&&t(2,i=d.selectedShell),"startupScript"in d&&t(0,o=d.startupScript),"onShellSelect"in d&&t(3,c=d.onShellSelect),"onStartupScriptChange"in d&&t(7,l=d.onStartupScriptChange)},s.$$.update=()=>{var d;4&s.$$.dirty&&t(5,n=i?(d=i,a.find(u=>u.friendlyName===d)):void 0)},[o,a,i,c,r,n,function(d){const u=d.target;l(u.value)},l,d=>{c(d.friendlyName),r()},function(d){r=d,t(4,r)},function(d){o=d,t(0,o)}]}class pm extends he{constructor(e){super(),ge(this,e,um,dm,me,{supportedShells:1,selectedShell:2,startupScript:0,onShellSelect:3,onStartupScriptChange:7})}}function ka(s){let e,t;return e=new Hp({props:{servers:s[1],onMCPServerAdd:s[7],onMCPServerSave:s[8],onMCPServerDelete:s[9],onMCPServerToggleDisable:s[10],onMCPServerJSONImport:s[11],onCancel:s[12],isMCPImportEnabled:s[3]}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};2&r&&(a.servers=n[1]),128&r&&(a.onMCPServerAdd=n[7]),256&r&&(a.onMCPServerSave=n[8]),512&r&&(a.onMCPServerDelete=n[9]),1024&r&&(a.onMCPServerToggleDisable=n[10]),2048&r&&(a.onMCPServerJSONImport=n[11]),4096&r&&(a.onCancel=n[12]),8&r&&(a.isMCPImportEnabled=n[3]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Sa(s){let e,t;return e=new pm({props:{supportedShells:s[13],selectedShell:s[14],startupScript:s[15],onShellSelect:s[16],onStartupScriptChange:s[17]}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};8192&r&&(a.supportedShells=n[13]),16384&r&&(a.selectedShell=n[14]),32768&r&&(a.startupScript=n[15]),65536&r&&(a.onShellSelect=n[16]),131072&r&&(a.onStartupScriptChange=n[17]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function mm(s){let e,t,n,r,a;t=new gd({props:{title:"Services",tools:s[0],onAuthenticate:s[5],onRevokeAccess:s[6]}});let i=s[2]&&ka(s),o=s[4]&&Sa(s);return{c(){e=M("div"),w(t.$$.fragment),n=Z(),i&&i.c(),r=Z(),o&&o.c(),_(e,"class","c-settings-tools svelte-181yusq")},m(c,l){x(c,e,l),b(t,e,null),C(e,n),i&&i.m(e,null),C(e,r),o&&o.m(e,null),a=!0},p(c,[l]){const d={};1&l&&(d.tools=c[0]),32&l&&(d.onAuthenticate=c[5]),64&l&&(d.onRevokeAccess=c[6]),t.$set(d),c[2]?i?(i.p(c,l),4&l&&p(i,1)):(i=ka(c),i.c(),p(i,1),i.m(e,r)):i&&(H(),m(i,1,1,()=>{i=null}),W()),c[4]?o?(o.p(c,l),16&l&&p(o,1)):(o=Sa(c),o.c(),p(o,1),o.m(e,null)):o&&(H(),m(o,1,1,()=>{o=null}),W())},i(c){a||(p(t.$$.fragment,c),p(i),p(o),a=!0)},o(c){m(t.$$.fragment,c),m(i),m(o),a=!1},d(c){c&&$(e),k(t),i&&i.d(),o&&o.d()}}}function fm(s,e,t){let{tools:n=[]}=e,{servers:r=[]}=e,{isMCPEnabled:a=!0}=e,{isMCPImportEnabled:i=!0}=e,{isTerminalEnabled:o=!0}=e,{onAuthenticate:c}=e,{onRevokeAccess:l}=e,{onMCPServerAdd:d}=e,{onMCPServerSave:u}=e,{onMCPServerDelete:h}=e,{onMCPServerToggleDisable:v}=e,{onMCPServerJSONImport:y}=e,{onCancel:f}=e,{supportedShells:g=[]}=e,{selectedShell:S}=e,{startupScript:I}=e,{onShellSelect:N=()=>{}}=e,{onStartupScriptChange:T=()=>{}}=e;return s.$$set=A=>{"tools"in A&&t(0,n=A.tools),"servers"in A&&t(1,r=A.servers),"isMCPEnabled"in A&&t(2,a=A.isMCPEnabled),"isMCPImportEnabled"in A&&t(3,i=A.isMCPImportEnabled),"isTerminalEnabled"in A&&t(4,o=A.isTerminalEnabled),"onAuthenticate"in A&&t(5,c=A.onAuthenticate),"onRevokeAccess"in A&&t(6,l=A.onRevokeAccess),"onMCPServerAdd"in A&&t(7,d=A.onMCPServerAdd),"onMCPServerSave"in A&&t(8,u=A.onMCPServerSave),"onMCPServerDelete"in A&&t(9,h=A.onMCPServerDelete),"onMCPServerToggleDisable"in A&&t(10,v=A.onMCPServerToggleDisable),"onMCPServerJSONImport"in A&&t(11,y=A.onMCPServerJSONImport),"onCancel"in A&&t(12,f=A.onCancel),"supportedShells"in A&&t(13,g=A.supportedShells),"selectedShell"in A&&t(14,S=A.selectedShell),"startupScript"in A&&t(15,I=A.startupScript),"onShellSelect"in A&&t(16,N=A.onShellSelect),"onStartupScriptChange"in A&&t(17,T=A.onStartupScriptChange)},[n,r,a,i,o,c,l,d,u,h,v,y,f,g,S,I,N,T]}class hm extends he{constructor(e){super(),ge(this,e,fm,mm,me,{tools:0,servers:1,isMCPEnabled:2,isMCPImportEnabled:3,isTerminalEnabled:4,onAuthenticate:5,onRevokeAccess:6,onMCPServerAdd:7,onMCPServerSave:8,onMCPServerDelete:9,onMCPServerToggleDisable:10,onMCPServerJSONImport:11,onCancel:12,supportedShells:13,selectedShell:14,startupScript:15,onShellSelect:16,onStartupScriptChange:17})}}function gm(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=ke(r,n[a]);return{c(){e=qe("svg"),t=new Un(!0),this.h()},l(a){e=Vn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=qn(e);t=Bn(i,!0),i.forEach($),this.h()},h(){t.a=null,lt(e,r)},m(a,i){Jn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M63.2 379.3c-6.2-6.2-6.2-16.4 0-22.6l39.4-39.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 39.4-39.4c6.2-6.2 16.4-6.2 22.6 0l69.5 69.5c6.2 6.2 6.2 16.4 0 22.6L155.3 448.8c-6.2 6.2-16.4 6.2-22.6 0zm35.5 103.4c25 25 65.5 25 90.5 0l293.5-293.4c25-25 25-65.5 0-90.5l-69.4-69.5c-25-25-65.5-25-90.5 0L29.3 322.7c-25 25-25 65.5 0 90.5l69.5 69.5z"/>',e)},p(a,[i]){lt(e,r=yt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&a[0]]))},i:G,o:G,d(a){a&&$(e)}}}function vm(s,e,t){return s.$$set=n=>{t(0,e=ke(ke({},e),Xe(n)))},[e=Xe(e)]}class $m extends he{constructor(e){super(),ge(this,e,vm,gm,me,{})}}function ym(s){let e,t,n,r;function a(o){s[6](o)}let i={placeholder:"Add your guidelines for Augment Chat...",resize:"vertical",saveFunction:s[3]};return s[0]!==void 0&&(i.value=s[0]),t=new ho({props:i}),Oe.push(()=>Ie(t,"value",a)),t.$on("focus",s[7]),{c(){e=M("div"),w(t.$$.fragment),_(e,"class","c-user-guidelines-category__input svelte-10borzo")},m(o,c){x(o,e,c),b(t,e,null),r=!0},p(o,[c]){const l={};!n&&1&c&&(n=!0,l.value=o[0],Pe(()=>n=!1)),t.$set(l)},i(o){r||(p(t.$$.fragment,o),r=!0)},o(o){m(t.$$.fragment,o),r=!1},d(o){o&&$(e),k(t)}}}function _m(s,e,t){let n;const r=Mi();let{userGuidelines:a=""}=e,{userGuidelinesLengthLimit:i}=e,{updateUserGuideline:o=()=>!1}=e;const c=Ve(void 0);function l(){const d=a.trim();if(n!==d){if(!o(d))throw i&&d.length>i?`The user guideline must be less than ${i} character long`:"An error occurred updating the user";rr(c,n=d,n)}}return bt(s,c,d=>t(8,n=d)),ja(()=>{rr(c,n=a.trim(),n)}),Ra(()=>{l()}),s.$$set=d=>{"userGuidelines"in d&&t(0,a=d.userGuidelines),"userGuidelinesLengthLimit"in d&&t(4,i=d.userGuidelinesLengthLimit),"updateUserGuideline"in d&&t(5,o=d.updateUserGuideline)},[a,r,c,l,i,o,function(d){a=d,t(0,a)},d=>{r("focus",d)}]}class xi extends he{constructor(e){super(),ge(this,e,_m,ym,me,{userGuidelines:0,userGuidelinesLengthLimit:4,updateUserGuideline:5})}}class xm{constructor(e,t){ve(this,"_rulesFiles",Ve([]));ve(this,"_loading",Ve(!0));ve(this,"_extensionClient");this._host=e,this._msgBroker=t,this.requestRules();const n=new go;this._extensionClient=new vo(e,t,n)}handleMessageFromExtension(e){if(e.data&&e.data.type===se.getRulesListResponse)return this._rulesFiles.set(e.data.data),this._loading.set(!1),!0;if(e.data&&e.data.type===se.createRuleResponse&&this._extensionClient.reportAgentSessionEvent({eventName:Ms.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Yn.manuallyCreated,numFiles:1,source:""}}}),e.data&&e.data.type===se.autoImportRulesResponse&&e.data.data&&this._extensionClient.reportAgentSessionEvent({eventName:Ms.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Yn.auto,numFiles:e.data.data.importedRulesCount,source:e.data.data.source}}}),e.data&&e.data.type===se.triggerImportDialogResponse&&e.data.data){this.requestRules();const t=e.data.data.directoryOrFile==="directory"?Yn.selectedDirectory:Yn.selectedFile;return this._extensionClient.reportAgentSessionEvent({eventName:Ms.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:t,numFiles:e.data.data.importedRulesCount,source:""}}}),!0}return!1}requestRules(){this._loading.set(!0),this._host.postMessage({type:se.getRulesListRequest})}createRule(){this._host.postMessage({type:se.createRule})}openRule(e){e===Da?this._host.postMessage({type:se.openGuidelines,data:""}):this._host.postMessage({type:se.openRule,data:{path:e}})}updateRuleType(e,t,n){const r=to.updateAlwaysApplyFrontmatterKey(t,n);this._extensionClient.saveFile({repoRoot:"",pathName:e,content:r})}deleteRule(e){this._host.postMessage({type:se.deleteRule,data:{path:e}})}autoImportRules(){this._host.postMessage({type:se.autoImportRules})}selectFileToImport(){this._host.postMessage({type:se.triggerImportDialog})}importRule(e){this._host.postMessage({type:se.importFileRequest,data:{filename:e,autoImport:!0}})}importDirectoryContents(e){this._host.postMessage({type:se.importDirectoryRequest,data:{directoryPath:e}})}getRulesFiles(){return this._rulesFiles}getLoading(){return this._loading}}function Ca(s,e,t){const n=s.slice();return n[21]=e[t],n}function Ta(s,e,t){const n=s.slice();return n[24]=e[t],n}function wm(s){let e;return{c(){e=F("Rules")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function bm(s){let e;return{c(){e=F("Learn more")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function km(s){let e,t,n=[],r=new Map,a=$e(s[5]);const i=o=>o[24].path;for(let o=0;o<a.length;o+=1){let c=Ta(s,a,o),l=i(c);r.set(l,n[o]=Ma(l,c))}return{c(){for(let o=0;o<n.length;o+=1)n[o].c();e=Ce()},m(o,c){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,c);x(o,e,c),t=!0},p(o,c){1312&c&&(a=$e(o[5]),H(),n=Et(n,c,i,1,o,a,r,e.parentNode,Ot,Ma,e,Ta),W())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&$(e);for(let c=0;c<n.length;c+=1)n[c].d(o)}}}function Sm(s){let e,t,n;return t=new de({props:{size:1,color:"neutral",$$slots:{default:[Pm]},$$scope:{ctx:s}}}),{c(){e=M("div"),w(t.$$.fragment),_(e,"class","c-rules-list-empty svelte-11i1bw2")},m(r,a){x(r,e,a),b(t,e,null),n=!0},p(r,a){const i={};134217728&a&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&$(e),k(t)}}}function Cm(s){let e,t=s[24].path+"";return{c(){e=F(t)},m(n,r){x(n,e,r)},p(n,r){32&r&&t!==(t=n[24].path+"")&&we(e,t)},d(n){n&&$(e)}}}function Tm(s){let e,t,n,r,a,i,o;return t=new Zi({}),a=new de({props:{size:1,color:"neutral",$$slots:{default:[Cm]},$$scope:{ctx:s}}}),{c(){e=M("div"),w(t.$$.fragment),n=Z(),r=M("div"),w(a.$$.fragment),i=Z(),_(r,"class","c-rule-item-path svelte-11i1bw2"),_(e,"class","c-rule-item-info svelte-11i1bw2"),_(e,"slot","header-left")},m(c,l){x(c,e,l),b(t,e,null),C(e,n),C(e,r),b(a,r,null),C(e,i),o=!0},p(c,l){const d={};134217760&l&&(d.$$scope={dirty:l,ctx:c}),a.$set(d)},i(c){o||(p(t.$$.fragment,c),p(a.$$.fragment,c),o=!0)},o(c){m(t.$$.fragment,c),m(a.$$.fragment,c),o=!1},d(c){c&&$(e),k(t),k(a)}}}function Mm(s){let e,t;function n(...r){return s[13](s[24],...r)}return e=new $o({props:{onSave:n,alwaysApply:s[24].type===ir.ALWAYS_ATTACHED}}),{c(){w(e.$$.fragment)},m(r,a){b(e,r,a),t=!0},p(r,a){s=r;const i={};32&a&&(i.onSave=n),32&a&&(i.alwaysApply=s[24].type===ir.ALWAYS_ATTACHED),e.$set(i)},i(r){t||(p(e.$$.fragment,r),t=!0)},o(r){m(e.$$.fragment,r),t=!1},d(r){k(e,r)}}}function Am(s){let e,t;return e=new Gt({props:{content:"Workspace guidelines are always applied",$$slots:{default:[Zm]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};134217728&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Nm(s){let e;return{c(){e=F("Always")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function Zm(s){let e,t;return e=new Ne({props:{color:"accent",size:1,disabled:!0,$$slots:{default:[Nm]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};134217728&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Em(s){let e,t;return e=new Ei({props:{slot:"iconRight"}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p:G,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Om(s){let e,t;return e=new Yi({props:{slot:"iconRight"}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p:G,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Im(s){let e,t,n,r,a,i,o,c,l,d,u;const h=[Am,Mm],v=[];function y(f,g){return 32&g&&(n=null),n==null&&(n=!!f[10](f[24].path)),n?0:1}return r=y(s,-1),a=v[r]=h[r](s),o=new Ne({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$slots:{iconRight:[Em]},$$scope:{ctx:s}}}),o.$on("click",function(...f){return s[14](s[24],...f)}),l=new Ne({props:{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$slots:{iconRight:[Om]},$$scope:{ctx:s}}}),l.$on("click",function(...f){return s[15](s[24],...f)}),{c(){e=M("div"),t=M("div"),a.c(),i=Z(),w(o.$$.fragment),c=Z(),w(l.$$.fragment),d=Z(),_(t,"class","status-controls svelte-11i1bw2"),_(e,"class","server-actions"),_(e,"slot","header-right")},m(f,g){x(f,e,g),C(e,t),v[r].m(t,null),C(t,i),b(o,t,null),C(t,c),b(l,t,null),C(e,d),u=!0},p(f,g){let S=r;r=y(s=f,g),r===S?v[r].p(s,g):(H(),m(v[S],1,1,()=>{v[S]=null}),W(),a=v[r],a?a.p(s,g):(a=v[r]=h[r](s),a.c()),p(a,1),a.m(t,i));const I={};134217728&g&&(I.$$scope={dirty:g,ctx:s}),o.$set(I);const N={};134217728&g&&(N.$$scope={dirty:g,ctx:s}),l.$set(N)},i(f){u||(p(a),p(o.$$.fragment,f),p(l.$$.fragment,f),u=!0)},o(f){m(a),m(o.$$.fragment,f),m(l.$$.fragment,f),u=!1},d(f){f&&$(e),v[r].d(),k(o),k(l)}}}function Ma(s,e){let t,n,r;return n=new oi({props:{isClickable:!0,$$slots:{"header-right":[Im],"header-left":[Tm]},$$scope:{ctx:e}}}),n.$on("click",function(){return e[16](e[24])}),{key:s,first:null,c(){t=Ce(),w(n.$$.fragment),this.first=t},m(a,i){x(a,t,i),b(n,a,i),r=!0},p(a,i){e=a;const o={};134217760&i&&(o.$$scope={dirty:i,ctx:e}),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&$(t),k(n,a)}}}function Pm(s){let e;return{c(){e=F("No rules files found")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function jm(s){let e,t,n,r,a;return t=new _s({}),{c(){e=M("div"),w(t.$$.fragment),n=Z(),r=M("span"),r.textContent="Create new rule file",_(e,"class","c-rules-actions-button-content svelte-11i1bw2")},m(i,o){x(i,e,o),b(t,e,null),C(e,n),C(e,r),a=!0},p:G,i(i){a||(p(t.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),a=!1},d(i){i&&$(e),k(t)}}}function Rm(s){let e,t,n,r,a,i,o;return t=new _i({}),i=new yo({}),{c(){e=M("div"),w(t.$$.fragment),n=Z(),r=M("span"),r.textContent="Import rules",a=Z(),w(i.$$.fragment),_(e,"class","c-rules-actions-button-content svelte-11i1bw2")},m(c,l){x(c,e,l),b(t,e,null),C(e,n),C(e,r),C(e,a),b(i,e,null),o=!0},p:G,i(c){o||(p(t.$$.fragment,c),p(i.$$.fragment,c),o=!0)},o(c){m(t.$$.fragment,c),m(i.$$.fragment,c),o=!1},d(c){c&&$(e),k(t),k(i)}}}function Lm(s){let e,t;return e=new Ne({props:{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$slots:{default:[Rm]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};134217728&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Fm(s){let e,t=s[21].label+"";return{c(){e=F(t)},m(n,r){x(n,e,r)},p:G,d(n){n&&$(e)}}}function Aa(s,e){let t,n,r;return n=new Ae.Item({props:{onSelect:function(){return e[18](e[21])},$$slots:{default:[Fm]},$$scope:{ctx:e}}}),{key:s,first:null,c(){t=Ce(),w(n.$$.fragment),this.first=t},m(a,i){x(a,t,i),b(n,a,i),r=!0},p(a,i){e=a;const o={};134217728&i&&(o.$$scope={dirty:i,ctx:e}),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&$(t),k(n,a)}}}function Na(s){let e,t,n,r;return e=new Ae.Separator({}),n=new Ae.Label({props:{$$slots:{default:[zm]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=Z(),w(n.$$.fragment)},m(a,i){b(e,a,i),x(a,t,i),b(n,a,i),r=!0},p(a,i){const o={};134217792&i&&(o.$$scope={dirty:i,ctx:a}),n.$set(o)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&$(t),k(e,a),k(n,a)}}}function zm(s){let e,t=(s[6]!==void 0?s[11][s[6]].description:s[11][0])+"";return{c(){e=F(t)},m(n,r){x(n,e,r)},p(n,r){64&r&&t!==(t=(n[6]!==void 0?n[11][n[6]].description:n[11][0])+"")&&we(e,t)},d(n){n&&$(e)}}}function Dm(s){let e,t,n,r=[],a=new Map,i=$e(s[11]);const o=l=>l[21].id;for(let l=0;l<i.length;l+=1){let d=Ca(s,i,l),u=o(d);a.set(u,r[l]=Aa(u,d))}let c=s[6]!==void 0&&Na(s);return{c(){for(let l=0;l<r.length;l+=1)r[l].c();e=Z(),c&&c.c(),t=Ce()},m(l,d){for(let u=0;u<r.length;u+=1)r[u]&&r[u].m(l,d);x(l,e,d),c&&c.m(l,d),x(l,t,d),n=!0},p(l,d){6144&d&&(i=$e(l[11]),H(),r=Et(r,d,o,1,l,i,a,e.parentNode,Ot,Aa,e,Ca),W()),l[6]!==void 0?c?(c.p(l,d),64&d&&p(c,1)):(c=Na(l),c.c(),p(c,1),c.m(t.parentNode,t)):c&&(H(),m(c,1,1,()=>{c=null}),W())},i(l){if(!n){for(let d=0;d<i.length;d+=1)p(r[d]);p(c),n=!0}},o(l){for(let d=0;d<r.length;d+=1)m(r[d]);m(c),n=!1},d(l){l&&($(e),$(t));for(let d=0;d<r.length;d+=1)r[d].d(l);c&&c.d(l)}}}function Um(s){let e,t,n,r;return e=new Ae.Trigger({props:{$$slots:{default:[Lm]},$$scope:{ctx:s}}}),n=new Ae.Content({props:{align:"start",side:"bottom",$$slots:{default:[Dm]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment),t=Z(),w(n.$$.fragment)},m(a,i){b(e,a,i),x(a,t,i),b(n,a,i),r=!0},p(a,i){const o={};134217728&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};134217792&i&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&$(t),k(e,a),k(n,a)}}}function Vm(s){let e;return{c(){e=F("User Guidelines")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function qm(s){let e;return{c(){e=F("Learn more")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function Bm(s){let e,t,n,r,a,i,o,c,l,d,u,h,v,y,f,g,S,I,N,T,A,P,U,ne,V,K,L,J,ie,Y,fe,rn;n=new de({props:{class:"c-section-header",size:3,color:"primary",$$slots:{default:[wm]},$$scope:{ctx:s}}}),c=new de({props:{size:1,weight:"regular",$$slots:{default:[bm]},$$scope:{ctx:s}}});const Ut=[Sm,km],Qe=[];function an(oe,Se){return oe[5].length===0?0:1}function Vt(oe){s[19](oe)}function wi(oe){s[20](oe)}u=an(s),h=Qe[u]=Ut[u](s),f=new Ne({props:{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$slots:{default:[jm]},$$scope:{ctx:s}}}),f.$on("click",s[17]);let Ss={$$slots:{default:[Um]},$$scope:{ctx:s}};return s[4]!==void 0&&(Ss.requestClose=s[4]),s[3]!==void 0&&(Ss.focusedIndex=s[3]),S=new Ae.Root({props:Ss}),Oe.push(()=>Ie(S,"requestClose",Vt)),Oe.push(()=>Ie(S,"focusedIndex",wi)),P=new de({props:{class:"c-section-header",size:3,color:"primary",$$slots:{default:[Vm]},$$scope:{ctx:s}}}),L=new de({props:{size:1,weight:"regular",$$slots:{default:[qm]},$$scope:{ctx:s}}}),ie=new xi({props:{userGuidelines:s[0],userGuidelinesLengthLimit:s[1],updateUserGuideline:s[2]}}),{c(){e=M("div"),t=M("div"),w(n.$$.fragment),r=Z(),a=M("div"),i=F(`Rules are instructions for Augment Chat and Agent that can be applied automatically across all
      conversations or referenced in specific conversations using @mentions (e.g., @rule-file.md) `),o=M("a"),w(c.$$.fragment),l=Z(),d=M("div"),h.c(),v=Z(),y=M("div"),w(f.$$.fragment),g=Z(),w(S.$$.fragment),T=Z(),A=M("div"),w(P.$$.fragment),U=Z(),ne=M("div"),V=F(`User Guidelines allow you to control Augment's behavior through natural language instructions.
      These guidelines are applied globally to all Chat and Agent interactions. `),K=M("a"),w(L.$$.fragment),J=Z(),w(ie.$$.fragment),_(o,"href","https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"),_(o,"target","_blank"),_(d,"class","c-rules-list svelte-11i1bw2"),_(y,"class","c-rules-actions-container svelte-11i1bw2"),_(t,"class","c-rules-section svelte-11i1bw2"),_(K,"href","https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines"),_(K,"target","_blank"),_(A,"class","c-user-guidelines-section svelte-11i1bw2"),_(e,"class","c-rules-category svelte-11i1bw2")},m(oe,Se){x(oe,e,Se),C(e,t),b(n,t,null),C(t,r),C(t,a),C(a,i),C(a,o),b(c,o,null),C(t,l),C(t,d),Qe[u].m(d,null),C(t,v),C(t,y),b(f,y,null),C(y,g),b(S,y,null),C(e,T),C(e,A),b(P,A,null),C(A,U),C(A,ne),C(ne,V),C(ne,K),b(L,K,null),C(A,J),b(ie,A,null),Y=!0,fe||(rn=Ge(window,"message",s[7].onMessageFromExtension),fe=!0)},p(oe,[Se]){const Ys={};134217728&Se&&(Ys.$$scope={dirty:Se,ctx:oe}),n.$set(Ys);const Xs={};134217728&Se&&(Xs.$$scope={dirty:Se,ctx:oe}),c.$set(Xs);let Cs=u;u=an(oe),u===Cs?Qe[u].p(oe,Se):(H(),m(Qe[Cs],1,1,()=>{Qe[Cs]=null}),W(),h=Qe[u],h?h.p(oe,Se):(h=Qe[u]=Ut[u](oe),h.c()),p(h,1),h.m(d,null));const Qs={};134217728&Se&&(Qs.$$scope={dirty:Se,ctx:oe}),f.$set(Qs);const Wn={};134217792&Se&&(Wn.$$scope={dirty:Se,ctx:oe}),!I&&16&Se&&(I=!0,Wn.requestClose=oe[4],Pe(()=>I=!1)),!N&&8&Se&&(N=!0,Wn.focusedIndex=oe[3],Pe(()=>N=!1)),S.$set(Wn);const er={};134217728&Se&&(er.$$scope={dirty:Se,ctx:oe}),P.$set(er);const tr={};134217728&Se&&(tr.$$scope={dirty:Se,ctx:oe}),L.$set(tr);const Kn={};1&Se&&(Kn.userGuidelines=oe[0]),2&Se&&(Kn.userGuidelinesLengthLimit=oe[1]),4&Se&&(Kn.updateUserGuideline=oe[2]),ie.$set(Kn)},i(oe){Y||(p(n.$$.fragment,oe),p(c.$$.fragment,oe),p(h),p(f.$$.fragment,oe),p(S.$$.fragment,oe),p(P.$$.fragment,oe),p(L.$$.fragment,oe),p(ie.$$.fragment,oe),Y=!0)},o(oe){m(n.$$.fragment,oe),m(c.$$.fragment,oe),m(h),m(f.$$.fragment,oe),m(S.$$.fragment,oe),m(P.$$.fragment,oe),m(L.$$.fragment,oe),m(ie.$$.fragment,oe),Y=!1},d(oe){oe&&$(e),k(n),k(c),Qe[u].d(),k(f),k(S),k(P),k(L),k(ie),fe=!1,rn()}}}function Jm(s,e,t){let n,r,a=G,i=()=>(a(),a=Js(v,g=>t(6,r=g)),v);s.$$.on_destroy.push(()=>a());let{userGuidelines:o=""}=e,{userGuidelinesLengthLimit:c}=e,{updateUserGuideline:l=()=>!1}=e;const d=new Fa(Me),u=new xm(Me,d);d.registerConsumer(u);const h=u.getRulesFiles();bt(s,h,g=>t(5,n=g));let v;i();let y=()=>{};async function f(g){try{g.id==="select_file_or_directory"?u.selectFileToImport():g.id==="auto_import"&&u.autoImportRules()}catch(S){console.error("Failed to handle import select:",S)}y&&y()}return ja(()=>{u.requestRules()}),s.$$set=g=>{"userGuidelines"in g&&t(0,o=g.userGuidelines),"userGuidelinesLengthLimit"in g&&t(1,c=g.userGuidelinesLengthLimit),"updateUserGuideline"in g&&t(2,l=g.updateUserGuideline)},[o,c,l,v,y,n,r,d,u,h,function(g){return g===Da},[{label:"Auto import existing rules in the workspace",id:"auto_import",description:"Choose existing rules in your workspace to auto import to Augment."},{label:"Select file(s) or directory to import",id:"select_file_or_directory",description:"Select an existing directory or list of markdown files to import to Augment."}],f,(g,S)=>{u.updateRuleType(`${no}/${so}/${g.path}`,g.content,S)},(g,S)=>{S.stopPropagation(),u.openRule(g.path)},(g,S)=>{S.stopPropagation(),u.deleteRule(g.path)},g=>u.openRule(g.path),()=>u.createRule(),g=>f(g),function(g){y=g,t(4,y)},function(g){v=g,i(t(3,v))}]}class Gm extends he{constructor(e){super(),ge(this,e,Jm,Bm,me,{userGuidelines:0,userGuidelinesLengthLimit:1,updateUserGuideline:2})}}function Hm(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=ke(r,n[a]);return{c(){e=qe("svg"),t=new Un(!0),this.h()},l(a){e=Vn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=qn(e);t=Bn(i,!0),i.forEach($),this.h()},h(){t.a=null,lt(e,r)},m(a,i){Jn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M352 146.2 462 256 352 365.8V312c0-13.3-10.7-24-24-24H208v-64h120c13.3 0 24-10.7 24-24zM512 256c0-11.5-4.6-22.5-12.7-30.6L383.2 109.6c-8.7-8.7-20.5-13.6-32.8-13.6-25.6 0-46.4 20.8-46.4 46.4V176h-96c-26.5 0-48 21.5-48 48v64c0 26.5 21.5 48 48 48h96v33.6c0 25.6 20.8 46.4 46.4 46.4 12.3 0 24.1-4.9 32.8-13.6l116.1-115.8c8.1-8.1 12.7-19.1 12.7-30.6M168 80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88C39.4 32 0 71.4 0 120v272c0 48.6 39.4 88 88 88h80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88c-22.1 0-40-17.9-40-40V120c0-22.1 17.9-40 40-40z"/>',e)},p(a,[i]){lt(e,r=yt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&a[0]]))},i:G,o:G,d(a){a&&$(e)}}}function Wm(s,e,t){return s.$$set=n=>{t(0,e=ke(ke({},e),Xe(n)))},[e=Xe(e)]}class Km extends he{constructor(e){super(),ge(this,e,Wm,Hm,me,{})}}function Ym(s){let e;return{c(){e=F("Sign Out")},m(t,n){x(t,e,n)},d(t){t&&$(e)}}}function Xm(s){let e,t;return e=new Km({props:{slot:"iconLeft"}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p:G,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function Qm(s){let e,t;return e=new Ne({props:{loading:s[0],variant:"soft","data-testid":"sign-out-button",$$slots:{iconLeft:[Xm],default:[Ym]},$$scope:{ctx:s}}}),e.$on("click",s[1]),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,[r]){const a={};1&r&&(a.loading=n[0]),8&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function ef(s,e,t){let{onSignOut:n}=e,r=!1;return s.$$set=a=>{"onSignOut"in a&&t(2,n=a.onSignOut)},[r,function(){n(),t(0,r=!0)},n]}class tf extends he{constructor(e){super(),ge(this,e,ef,Qm,me,{onSignOut:2})}}function nf(s){let e,t;return e=new hm({props:{tools:s[6],onAuthenticate:s[18],onRevokeAccess:s[19],servers:s[7],onMCPServerAdd:s[24],onMCPServerSave:s[25],onMCPServerDelete:s[26],onMCPServerToggleDisable:s[27],onMCPServerJSONImport:s[28],isMCPEnabled:s[8]&&s[2].mcpServerList,isMCPImportEnabled:s[8]&&s[2].mcpServerImport,supportedShells:s[9].supportedShells,selectedShell:s[9].selectedShell,startupScript:s[9].startupScript,onShellSelect:s[20],onStartupScriptChange:s[21],isTerminalEnabled:s[2].terminal}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};64&r[0]&&(a.tools=n[6]),128&r[0]&&(a.servers=n[7]),260&r[0]&&(a.isMCPEnabled=n[8]&&n[2].mcpServerList),260&r[0]&&(a.isMCPImportEnabled=n[8]&&n[2].mcpServerImport),512&r[0]&&(a.supportedShells=n[9].supportedShells),512&r[0]&&(a.selectedShell=n[9].selectedShell),512&r[0]&&(a.startupScript=n[9].startupScript),4&r[0]&&(a.isTerminalEnabled=n[2].terminal),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function sf(s){let e,t;return e=new tf({props:{onSignOut:s[22]}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p:G,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function rf(s){let e,t,n,r;const a=[lf,cf],i=[];function o(c,l){return c[2].rules?0:1}return e=o(s),t=i[e]=a[e](s),{c(){t.c(),n=Ce()},m(c,l){i[e].m(c,l),x(c,n,l),r=!0},p(c,l){let d=e;e=o(c),e===d?i[e].p(c,l):(H(),m(i[d],1,1,()=>{i[d]=null}),W(),t=i[e],t?t.p(c,l):(t=i[e]=a[e](c),t.c()),p(t,1),t.m(n.parentNode,n))},i(c){r||(p(t),r=!0)},o(c){m(t),r=!1},d(c){c&&$(n),i[e].d(c)}}}function af(s){let e,t;return e=new il({}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p:G,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function of(s){return{c:G,m:G,p:G,i:G,o:G,d:G}}function cf(s){let e,t;return e=new xi({props:{userGuidelines:s[5],userGuidelinesLengthLimit:s[4],updateUserGuideline:s[17]}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};32&r[0]&&(a.userGuidelines=n[5]),16&r[0]&&(a.userGuidelinesLengthLimit=n[4]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function lf(s){let e,t;return e=new Gm({props:{userGuidelines:s[5],userGuidelinesLengthLimit:s[4],updateUserGuideline:s[17]}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};32&r[0]&&(a.userGuidelines=n[5]),16&r[0]&&(a.userGuidelinesLengthLimit=n[4]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function df(s){let e,t,n,r,a;const i=[of,af,rf,sf,nf],o=[];function c(l,d){var u,h,v;return 8&d[1]&&(t=null),t==null&&(t=!ci(l[34])),t?0:((u=l[34])==null?void 0:u.id)==="context"?1:((h=l[34])==null?void 0:h.id)==="guidelines"?2:((v=l[34])==null?void 0:v.id)==="account"?3:4}return n=c(s,[-1,-1]),r=o[n]=i[n](s),{c(){e=M("span"),r.c(),_(e,"slot","content")},m(l,d){x(l,e,d),o[n].m(e,null),a=!0},p(l,d){let u=n;n=c(l,d),n===u?o[n].p(l,d):(H(),m(o[u],1,1,()=>{o[u]=null}),W(),r=o[n],r?r.p(l,d):(r=o[n]=i[n](l),r.c()),p(r,1),r.m(e,null))},i(l){a||(p(r),a=!0)},o(l){m(r),a=!1},d(l){l&&$(e),o[n].d()}}}function uf(s){let e,t;return e=new Ol({props:{items:s[1],mode:"tree",class:"c-settings-navigation",selectedId:s[0],$$slots:{content:[df,({item:n})=>({34:n}),({item:n})=>[0,n?8:0]]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(n,r){b(e,n,r),t=!0},p(n,r){const a={};2&r[0]&&(a.items=n[1]),1&r[0]&&(a.selectedId=n[0]),1012&r[0]|24&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){k(e,n)}}}function pf(s){let e,t,n,r;return e=new fo.Root({props:{$$slots:{default:[uf]},$$scope:{ctx:s}}}),{c(){w(e.$$.fragment)},m(a,i){b(e,a,i),t=!0,n||(r=Ge(window,"message",s[11].onMessageFromExtension),n=!0)},p(a,i){const o={};1015&i[0]|16&i[1]&&(o.$$scope={dirty:i,ctx:a}),e.$set(o)},i(a){t||(p(e.$$.fragment,a),t=!0)},o(a){m(e.$$.fragment,a),t=!1},d(a){k(e,a),n=!1,r()}}}function mf(s,e,t){let n,r,a,i,o,c,l,d,u,h,v=G;s.$$.on_destroy.push(()=>v());const y=new wo(Me),f=new Ac(Me),g=new Nc(Me),S=new Fa(Me),I=new Qi,N=new _o(Me,S,I),T=y.getSettingsComponentSupported();bt(s,T,L=>t(2,o=L));const A=y.getEnableAgentMode();bt(s,A,L=>t(8,u=L)),S.registerConsumer(y),S.registerConsumer(f),S.registerConsumer(g);const P=g.getTerminalSettings();let U;bt(s,P,L=>t(9,h=L));const ne={handleMessageFromExtension:L=>!(!L.data||L.data.type!==se.navigateToSettingsSection)&&(L.data.data&&typeof L.data.data=="string"&&t(0,U=L.data.data),!0)};S.registerConsumer(ne);const V=y.getDisplayableTools();bt(s,V,L=>t(6,l=L));const K=y.getGuidelines();return bt(s,K,L=>t(23,c=L)),Ra(()=>{y.dispose()}),y.notifyLoaded(),Me.postMessage({type:se.getOrientationStatus}),Me.postMessage({type:se.settingsPanelLoaded}),s.$$.update=()=>{var L,J,ie;8388608&s.$$.dirty[0]&&t(5,n=(L=c.userGuidelines)==null?void 0:L.contents),8388608&s.$$.dirty[0]&&t(4,r=(J=c.userGuidelines)==null?void 0:J.lengthLimit),4&s.$$.dirty[0]&&t(1,i=[o.remoteTools?Xn("Tools","",Pl,"section-tools"):void 0,o.userGuidelines&&!o.rules?Xn("User Guidelines","Guidelines for Augment Chat to follow.",Oi,"guidelines"):void 0,o.rules?Xn("Rules and User Guidelines","",$m,"guidelines"):void 0,o.workspaceContext?{name:"Context",description:"",icon:Rl,id:"context"}:void 0,Xn("Account","Manage your Augment account settings.",Xi,"account")].filter(Boolean)),3&s.$$.dirty[0]&&i.length>1&&!U&&t(0,U=(ie=i[0])==null?void 0:ie.id)},t(3,a=f.getServers()),v(),v=Js(a,L=>t(7,d=L)),[U,i,o,a,r,n,l,d,u,h,f,S,T,A,P,V,K,function(L){const J=L.trim();return!(r&&J.length>r)&&(y.updateLocalUserGuidelines(J),Me.postMessage({type:se.updateUserGuidelines,data:L}),!0)},function(L){Me.postMessage({type:se.toolConfigStartOAuth,data:{authUrl:L}}),y.startPolling()},async function(L){await N.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${L.displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&Me.postMessage({type:se.toolConfigRevokeAccess,data:{toolId:L.identifier}})},function(L){g.updateSelectedShell(L)},function(L){g.updateStartupScript(L)},function(){Me.postMessage({type:se.signOut})},c,L=>f.addServer(L),L=>f.updateServer(L),L=>f.deleteServer(L),L=>f.toggleDisabledServer(L),L=>f.importServersFromJSON(L)]}class ff extends he{constructor(e){super(),ge(this,e,mf,pf,me,{},null,[-1,-1])}}(async function(){Me&&Me.initialize&&await Me.initialize(),new ff({target:document.getElementById("app")})})();
