var Sn=Object.defineProperty;var _n=(e,t,n)=>t in e?Sn(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var A=(e,t,n)=>_n(e,typeof t!="symbol"?t+"":t,n);import{R as ie}from"./chat-types-B-te1sXh.js";function Ie(e,t){return!(e===null||typeof e!="object"||!("$typeName"in e)||typeof e.$typeName!="string")&&(t===void 0||t.typeName===e.$typeName)}var c;function wn(){let e=0,t=0;for(let r=0;r<28;r+=7){let s=this.buf[this.pos++];if(e|=(127&s)<<r,!(128&s))return this.assertBounds(),[e,t]}let n=this.buf[this.pos++];if(e|=(15&n)<<28,t=(112&n)>>4,!(128&n))return this.assertBounds(),[e,t];for(let r=3;r<=31;r+=7){let s=this.buf[this.pos++];if(t|=(127&s)<<r,!(128&s))return this.assertBounds(),[e,t]}throw new Error("invalid varint")}function ue(e,t,n){for(let a=0;a<28;a+=7){const o=e>>>a,i=!(!(o>>>7)&&t==0),u=255&(i?128|o:o);if(n.push(u),!i)return}const r=e>>>28&15|(7&t)<<4,s=!!(t>>3);if(n.push(255&(s?128|r:r)),s){for(let a=3;a<31;a+=7){const o=t>>>a,i=!!(o>>>7),u=255&(i?128|o:o);if(n.push(u),!i)return}n.push(t>>>31&1)}}(function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"})(c||(c={}));const q=4294967296;function Me(e){const t=e[0]==="-";t&&(e=e.slice(1));const n=1e6;let r=0,s=0;function a(o,i){const u=Number(e.slice(o,i));s*=n,r=r*n+u,r>=q&&(s+=r/q|0,r%=q)}return a(-24,-18),a(-18,-12),a(-12,-6),a(-6),t?kt(r,s):Se(r,s)}function Ke(e,t){if({lo:e,hi:t}=function(u,l){return{lo:u>>>0,hi:l>>>0}}(e,t),t<=2097151)return String(q*t+e);const n=16777215&(e>>>24|t<<8),r=t>>16&65535;let s=(16777215&e)+6777216*n+6710656*r,a=n+8147497*r,o=2*r;const i=1e7;return s>=i&&(a+=Math.floor(s/i),s%=i),a>=i&&(o+=Math.floor(a/i),a%=i),o.toString()+Be(a)+Be(s)}function Se(e,t){return{lo:0|e,hi:0|t}}function kt(e,t){return t=~t,e?e=1+~e:t+=1,Se(e,t)}const Be=e=>{const t=String(e);return"0000000".slice(t.length)+t};function je(e,t){if(e>=0){for(;e>127;)t.push(127&e|128),e>>>=7;t.push(e)}else{for(let n=0;n<9;n++)t.push(127&e|128),e>>=7;t.push(1)}}function On(){let e=this.buf[this.pos++],t=127&e;if(!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<7,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<14,!(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<21,!(128&e))return this.assertBounds(),t;e=this.buf[this.pos++],t|=(15&e)<<28;for(let n=5;128&e&&n<10;n++)e=this.buf[this.pos++];if(128&e)throw new Error("invalid varint");return this.assertBounds(),t>>>0}var Xe={};const y=Rn();function Rn(){const e=new DataView(new ArrayBuffer(8));if(typeof BigInt=="function"&&typeof e.getBigInt64=="function"&&typeof e.getBigUint64=="function"&&typeof e.setBigInt64=="function"&&typeof e.setBigUint64=="function"&&(typeof process!="object"||typeof Xe!="object"||Xe.BUF_BIGINT_DISABLE!=="1")){const t=BigInt("-9223372036854775808"),n=BigInt("9223372036854775807"),r=BigInt("0"),s=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(a){const o=typeof a=="bigint"?a:BigInt(a);if(o>n||o<t)throw new Error(`invalid int64: ${a}`);return o},uParse(a){const o=typeof a=="bigint"?a:BigInt(a);if(o>s||o<r)throw new Error(`invalid uint64: ${a}`);return o},enc(a){return e.setBigInt64(0,this.parse(a),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},uEnc(a){return e.setBigInt64(0,this.uParse(a),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},dec:(a,o)=>(e.setInt32(0,a,!0),e.setInt32(4,o,!0),e.getBigInt64(0,!0)),uDec:(a,o)=>(e.setInt32(0,a,!0),e.setInt32(4,o,!0),e.getBigUint64(0,!0))}}return{zero:"0",supported:!1,parse:t=>(typeof t!="string"&&(t=t.toString()),We(t),t),uParse:t=>(typeof t!="string"&&(t=t.toString()),Je(t),t),enc:t=>(typeof t!="string"&&(t=t.toString()),We(t),Me(t)),uEnc:t=>(typeof t!="string"&&(t=t.toString()),Je(t),Me(t)),dec:(t,n)=>function(r,s){let a=Se(r,s);const o=2147483648&a.hi;o&&(a=kt(a.lo,a.hi));const i=Ke(a.lo,a.hi);return o?"-"+i:i}(t,n),uDec:(t,n)=>Ke(t,n)}}function We(e){if(!/^-?[0-9]+$/.test(e))throw new Error("invalid int64: "+e)}function Je(e){if(!/^[0-9]+$/.test(e))throw new Error("invalid uint64: "+e)}function L(e,t){switch(e){case c.STRING:return"";case c.BOOL:return!1;case c.DOUBLE:case c.FLOAT:return 0;case c.INT64:case c.UINT64:case c.SFIXED64:case c.FIXED64:case c.SINT64:return t?"0":y.zero;case c.BYTES:return new Uint8Array(0);default:return 0}}const O=Symbol.for("reflect unsafe local");function Ut(e,t){const n=e[t.localName].case;return n===void 0?n:t.fields.find(r=>r.localName===n)}function An(e,t){const n=t.localName;if(t.oneof)return e[t.oneof.localName].case===n;if(t.presence!=2)return e[n]!==void 0&&Object.prototype.hasOwnProperty.call(e,n);switch(t.fieldKind){case"list":return e[n].length>0;case"map":return Object.keys(e[n]).length>0;case"scalar":return!function(r,s){switch(r){case c.BOOL:return s===!1;case c.STRING:return s==="";case c.BYTES:return s instanceof Uint8Array&&!s.byteLength;default:return s==0}}(t.scalar,e[n]);case"enum":return e[n]!==t.enum.values[0].number}throw new Error("message field with implicit presence")}function V(e,t){return Object.prototype.hasOwnProperty.call(e,t)&&e[t]!==void 0}function Lt(e,t){if(t.oneof){const n=e[t.oneof.localName];return n.case===t.localName?n.value:void 0}return e[t.localName]}function $t(e,t,n){t.oneof?e[t.oneof.localName]={case:t.localName,value:n}:e[t.localName]=n}function D(e){return e!==null&&typeof e=="object"&&!Array.isArray(e)}function _e(e,t){var n,r,s,a;if(D(e)&&O in e&&"add"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const o=t,i=e.field();return o.listKind==i.listKind&&o.scalar===i.scalar&&((n=o.message)===null||n===void 0?void 0:n.typeName)===((r=i.message)===null||r===void 0?void 0:r.typeName)&&((s=o.enum)===null||s===void 0?void 0:s.typeName)===((a=i.enum)===null||a===void 0?void 0:a.typeName)}return!0}return!1}function we(e,t){var n,r,s,a;if(D(e)&&O in e&&"has"in e&&"field"in e&&typeof e.field=="function"){if(t!==void 0){const o=t,i=e.field();return o.mapKey===i.mapKey&&o.mapKind==i.mapKind&&o.scalar===i.scalar&&((n=o.message)===null||n===void 0?void 0:n.typeName)===((r=i.message)===null||r===void 0?void 0:r.typeName)&&((s=o.enum)===null||s===void 0?void 0:s.typeName)===((a=i.enum)===null||a===void 0?void 0:a.typeName)}return!0}return!1}function Oe(e,t){return D(e)&&O in e&&"desc"in e&&D(e.desc)&&e.desc.kind==="message"&&(t===void 0||e.desc.typeName==t.typeName)}function j(e){const t=e.fields[0];return Pt(e.typeName)&&t!==void 0&&t.fieldKind=="scalar"&&t.name=="value"&&t.number==1}function Pt(e){return e.startsWith("google.protobuf.")&&["DoubleValue","FloatValue","Int64Value","UInt64Value","Int32Value","UInt32Value","BoolValue","StringValue","BytesValue"].includes(e.substring(16))}const Dn=999,Fn=998,W=2;function _(e,t){if(Ie(t,e))return t;const n=function(r){let s;if(function(a){switch(a.file.edition){case Dn:return!1;case Fn:return!0;default:return a.fields.some(o=>o.presence!=W&&o.fieldKind!="message"&&!o.oneof)}}(r)){const a=qe.get(r);let o,i;if(a)({prototype:o,members:i}=a);else{o={},i=new Set;for(const u of r.members)u.kind!="oneof"&&(u.fieldKind!="scalar"&&u.fieldKind!="enum"||u.presence!=W&&(i.add(u),o[u.localName]=le(u)));qe.set(r,{prototype:o,members:i})}s=Object.create(o),s.$typeName=r.typeName;for(const u of r.members)if(!i.has(u)){if(u.kind=="field"&&(u.fieldKind=="message"||(u.fieldKind=="scalar"||u.fieldKind=="enum")&&u.presence!=W))continue;s[u.localName]=le(u)}}else{s={$typeName:r.typeName};for(const a of r.members)a.kind!="oneof"&&a.presence!=W||(s[a.localName]=le(a))}return s}(e);return t!==void 0&&function(r,s,a){for(const o of r.members){let i,u=a[o.localName];if(u!=null){if(o.kind=="oneof"){const l=Ut(a,o);if(!l)continue;i=l,u=Lt(a,l)}else i=o;switch(i.fieldKind){case"message":u=Re(i,u);break;case"scalar":u=Ct(i,u);break;case"list":u=Un(i,u);break;case"map":u=kn(i,u)}$t(s,i,u)}}}(e,n,t),n}function Ct(e,t){return e.scalar==c.BYTES?Ae(t):t}function kn(e,t){if(D(t)){if(e.scalar==c.BYTES)return Ze(t,Ae);if(e.mapKind=="message")return Ze(t,n=>Re(e,n))}return t}function Un(e,t){if(Array.isArray(t)){if(e.scalar==c.BYTES)return t.map(Ae);if(e.listKind=="message")return t.map(n=>Re(e,n))}return t}function Re(e,t){if(e.fieldKind=="message"&&!e.oneof&&j(e.message))return Ct(e.message.fields[0],t);if(D(t)){if(e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!=="google.protobuf.Value")return t;if(!Ie(t,e.message))return _(e.message,t)}return t}function Ae(e){return Array.isArray(e)?new Uint8Array(e):e}function Ze(e,t){const n={};for(const r of Object.entries(e))n[r[0]]=t(r[1]);return n}const Ln=Symbol(),qe=new WeakMap;function le(e){if(e.kind=="oneof")return{case:void 0};if(e.fieldKind=="list")return[];if(e.fieldKind=="map")return{};if(e.fieldKind=="message")return Ln;const t=e.getDefaultValue();return t!==void 0?e.fieldKind=="scalar"&&e.longAsString?t.toString():t:e.fieldKind=="scalar"?L(e.scalar,e.longAsString):e.enum.values[0].number}const $n=["FieldValueInvalidError","FieldListRangeError","ForeignFieldError"];class T extends Error{constructor(t,n,r="FieldValueInvalidError"){super(n),this.name=r,this.field=()=>t}}const ce=Symbol.for("@bufbuild/protobuf/text-encoding");function De(){if(globalThis[ce]==null){const e=new globalThis.TextEncoder,t=new globalThis.TextDecoder;globalThis[ce]={encodeUtf8:n=>e.encode(n),decodeUtf8:n=>t.decode(n),checkUtf8(n){try{return encodeURIComponent(n),!0}catch{return!1}}}}return globalThis[ce]}var N;(function(e){e[e.Varint=0]="Varint",e[e.Bit64=1]="Bit64",e[e.LengthDelimited=2]="LengthDelimited",e[e.StartGroup=3]="StartGroup",e[e.EndGroup=4]="EndGroup",e[e.Bit32=5]="Bit32"})(N||(N={}));const xt=34028234663852886e22,Gt=-34028234663852886e22,Yt=4294967295,Vt=2147483647,Mt=-2147483648;class Kt{constructor(t=De().encodeUtf8){this.encodeUtf8=t,this.stack=[],this.chunks=[],this.buf=[]}finish(){this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]);let t=0;for(let s=0;s<this.chunks.length;s++)t+=this.chunks[s].length;let n=new Uint8Array(t),r=0;for(let s=0;s<this.chunks.length;s++)n.set(this.chunks[s],r),r+=this.chunks[s].length;return this.chunks=[],n}fork(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}join(){let t=this.finish(),n=this.stack.pop();if(!n)throw new Error("invalid state, fork stack empty");return this.chunks=n.chunks,this.buf=n.buf,this.uint32(t.byteLength),this.raw(t)}tag(t,n){return this.uint32((t<<3|n)>>>0)}raw(t){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(t),this}uint32(t){for(He(t);t>127;)this.buf.push(127&t|128),t>>>=7;return this.buf.push(t),this}int32(t){return me(t),je(t,this.buf),this}bool(t){return this.buf.push(t?1:0),this}bytes(t){return this.uint32(t.byteLength),this.raw(t)}string(t){let n=this.encodeUtf8(t);return this.uint32(n.byteLength),this.raw(n)}float(t){(function(r){if(typeof r=="string"){const s=r;if(r=Number(r),Number.isNaN(r)&&s!=="NaN")throw new Error("invalid float32: "+s)}else if(typeof r!="number")throw new Error("invalid float32: "+typeof r);if(Number.isFinite(r)&&(r>xt||r<Gt))throw new Error("invalid float32: "+r)})(t);let n=new Uint8Array(4);return new DataView(n.buffer).setFloat32(0,t,!0),this.raw(n)}double(t){let n=new Uint8Array(8);return new DataView(n.buffer).setFloat64(0,t,!0),this.raw(n)}fixed32(t){He(t);let n=new Uint8Array(4);return new DataView(n.buffer).setUint32(0,t,!0),this.raw(n)}sfixed32(t){me(t);let n=new Uint8Array(4);return new DataView(n.buffer).setInt32(0,t,!0),this.raw(n)}sint32(t){return me(t),je(t=(t<<1^t>>31)>>>0,this.buf),this}sfixed64(t){let n=new Uint8Array(8),r=new DataView(n.buffer),s=y.enc(t);return r.setInt32(0,s.lo,!0),r.setInt32(4,s.hi,!0),this.raw(n)}fixed64(t){let n=new Uint8Array(8),r=new DataView(n.buffer),s=y.uEnc(t);return r.setInt32(0,s.lo,!0),r.setInt32(4,s.hi,!0),this.raw(n)}int64(t){let n=y.enc(t);return ue(n.lo,n.hi,this.buf),this}sint64(t){const n=y.enc(t),r=n.hi>>31;return ue(n.lo<<1^r,(n.hi<<1|n.lo>>>31)^r,this.buf),this}uint64(t){const n=y.uEnc(t);return ue(n.lo,n.hi,this.buf),this}}class Fe{constructor(t,n=De().decodeUtf8){this.decodeUtf8=n,this.varint64=wn,this.uint32=On,this.buf=t,this.len=t.length,this.pos=0,this.view=new DataView(t.buffer,t.byteOffset,t.byteLength)}tag(){let t=this.uint32(),n=t>>>3,r=7&t;if(n<=0||r<0||r>5)throw new Error("illegal tag: field no "+n+" wire type "+r);return[n,r]}skip(t,n){let r=this.pos;switch(t){case N.Varint:for(;128&this.buf[this.pos++];);break;case N.Bit64:this.pos+=4;case N.Bit32:this.pos+=4;break;case N.LengthDelimited:let s=this.uint32();this.pos+=s;break;case N.StartGroup:for(;;){const[a,o]=this.tag();if(o===N.EndGroup){if(n!==void 0&&a!==n)throw new Error("invalid end group tag");break}this.skip(o,a)}break;default:throw new Error("cant skip wire type "+t)}return this.assertBounds(),this.buf.subarray(r,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return 0|this.uint32()}sint32(){let t=this.uint32();return t>>>1^-(1&t)}int64(){return y.dec(...this.varint64())}uint64(){return y.uDec(...this.varint64())}sint64(){let[t,n]=this.varint64(),r=-(1&t);return t=(t>>>1|(1&n)<<31)^r,n=n>>>1^r,y.dec(t,n)}bool(){let[t,n]=this.varint64();return t!==0||n!==0}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return y.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return y.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let t=this.uint32(),n=this.pos;return this.pos+=t,this.assertBounds(),this.buf.subarray(n,n+t)}string(){return this.decodeUtf8(this.bytes())}}function me(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid int32: "+typeof e);if(!Number.isInteger(e)||e>Vt||e<Mt)throw new Error("invalid int32: "+e)}function He(e){if(typeof e=="string")e=Number(e);else if(typeof e!="number")throw new Error("invalid uint32: "+typeof e);if(!Number.isInteger(e)||e>Yt||e<0)throw new Error("invalid uint32: "+e)}function k(e,t){const n=e.fieldKind=="list"?_e(t,e):e.fieldKind=="map"?we(t,e):ke(e,t);if(n===!0)return;let r;switch(e.fieldKind){case"list":r=`expected ${Xt(e)}, got ${E(t)}`;break;case"map":r=`expected ${Wt(e)}, got ${E(t)}`;break;default:r=te(e,t,n)}return new T(e,r)}function ze(e,t,n){const r=ke(e,n);if(r!==!0)return new T(e,`list item #${t+1}: ${te(e,n,r)}`)}function ke(e,t){return e.scalar!==void 0?Bt(t,e.scalar):e.enum!==void 0?e.enum.open?Number.isInteger(t):e.enum.values.some(n=>n.number===t):Oe(t,e.message)}function Bt(e,t){switch(t){case c.DOUBLE:return typeof e=="number";case c.FLOAT:return typeof e=="number"&&(!(!Number.isNaN(e)&&Number.isFinite(e))||!(e>xt||e<Gt)||`${e.toFixed()} out of range`);case c.INT32:case c.SFIXED32:case c.SINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>Vt||e<Mt)||`${e.toFixed()} out of range`);case c.FIXED32:case c.UINT32:return!(typeof e!="number"||!Number.isInteger(e))&&(!(e>Yt||e<0)||`${e.toFixed()} out of range`);case c.BOOL:return typeof e=="boolean";case c.STRING:return typeof e=="string"&&(De().checkUtf8(e)||"invalid UTF8");case c.BYTES:return e instanceof Uint8Array;case c.INT64:case c.SFIXED64:case c.SINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return y.parse(e),!0}catch{return`${e} out of range`}return!1;case c.FIXED64:case c.UINT64:if(typeof e=="bigint"||typeof e=="number"||typeof e=="string"&&e.length>0)try{return y.uParse(e),!0}catch{return`${e} out of range`}return!1}}function te(e,t,n){return n=typeof n=="string"?`: ${n}`:`, got ${E(t)}`,e.scalar!==void 0?`expected ${function(r){switch(r){case c.STRING:return"string";case c.BOOL:return"boolean";case c.INT64:case c.SINT64:case c.SFIXED64:return"bigint (int64)";case c.UINT64:case c.FIXED64:return"bigint (uint64)";case c.BYTES:return"Uint8Array";case c.DOUBLE:return"number (float64)";case c.FLOAT:return"number (float32)";case c.FIXED32:case c.UINT32:return"number (uint32)";case c.INT32:case c.SFIXED32:case c.SINT32:return"number (int32)"}}(e.scalar)}`+n:e.enum!==void 0?`expected ${e.enum.toString()}`+n:`expected ${jt(e.message)}`+n}function E(e){switch(typeof e){case"object":return e===null?"null":e instanceof Uint8Array?`Uint8Array(${e.length})`:Array.isArray(e)?`Array(${e.length})`:_e(e)?Xt(e.field()):we(e)?Wt(e.field()):Oe(e)?jt(e.desc):Ie(e)?`message ${e.$typeName}`:"object";case"string":return e.length>30?"string":`"${e.split('"').join('\\"')}"`;case"boolean":case"number":return String(e);case"bigint":return String(e)+"n";default:return typeof e}}function jt(e){return`ReflectMessage (${e.typeName})`}function Xt(e){switch(e.listKind){case"message":return`ReflectList (${e.message.toString()})`;case"enum":return`ReflectList (${e.enum.toString()})`;case"scalar":return`ReflectList (${c[e.scalar]})`}}function Wt(e){switch(e.mapKind){case"message":return`ReflectMap (${c[e.mapKey]}, ${e.message.toString()})`;case"enum":return`ReflectMap (${c[e.mapKey]}, ${e.enum.toString()})`;case"scalar":return`ReflectMap (${c[e.mapKey]}, ${c[e.scalar]})`}}function I(e,t,n=!0){return new Jt(e,t,n)}class Jt{get sortedFields(){var t;return(t=this._sortedFields)!==null&&t!==void 0?t:this._sortedFields=this.desc.fields.concat().sort((n,r)=>n.number-r.number)}constructor(t,n,r=!0){this.lists=new Map,this.maps=new Map,this.check=r,this.desc=t,this.message=this[O]=n??_(t),this.fields=t.fields,this.oneofs=t.oneofs,this.members=t.members}findNumber(t){return this._fieldsByNumber||(this._fieldsByNumber=new Map(this.desc.fields.map(n=>[n.number,n]))),this._fieldsByNumber.get(t)}oneofCase(t){return G(this.message,t),Ut(this.message,t)}isSet(t){return G(this.message,t),An(this.message,t)}clear(t){G(this.message,t),function(n,r){const s=r.localName;if(r.oneof){const a=r.oneof.localName;n[a].case===s&&(n[a]={case:void 0})}else if(r.presence!=2)delete n[s];else switch(r.fieldKind){case"map":n[s]={};break;case"list":n[s]=[];break;case"enum":n[s]=r.enum.values[0].number;break;case"scalar":n[s]=L(r.scalar,r.longAsString)}}(this.message,t)}get(t){G(this.message,t);const n=Lt(this.message,t);switch(t.fieldKind){case"list":let r=this.lists.get(t);return r&&r[O]===n||this.lists.set(t,r=new Pn(t,n,this.check)),r;case"map":let s=this.maps.get(t);return s&&s[O]===n||this.maps.set(t,s=new Cn(t,n,this.check)),s;case"message":return Le(t,n,this.check);case"scalar":return n===void 0?L(t.scalar,!1):$e(t,n);case"enum":return n??t.enum.values[0].number}}set(t,n){if(G(this.message,t),this.check){const s=k(t,n);if(s)throw s}let r;r=t.fieldKind=="message"?Ue(t,n):we(n)||_e(n)?n[O]:Pe(t,n),$t(this.message,t,r)}getUnknown(){return this.message.$unknown}setUnknown(t){this.message.$unknown=t}}function G(e,t){if(t.parent.typeName!==e.$typeName)throw new T(t,`cannot use ${t.toString()} with message ${e.$typeName}`,"ForeignFieldError")}class Pn{field(){return this._field}get size(){return this._arr.length}constructor(t,n,r){this._field=t,this._arr=this[O]=n,this.check=r}get(t){const n=this._arr[t];return n===void 0?void 0:de(this._field,n,this.check)}set(t,n){if(t<0||t>=this._arr.length)throw new T(this._field,`list item #${t+1}: out of range`);if(this.check){const r=ze(this._field,t,n);if(r)throw r}this._arr[t]=Qe(this._field,n)}add(t){if(this.check){const n=ze(this._field,this._arr.length,t);if(n)throw n}this._arr.push(Qe(this._field,t))}clear(){this._arr.splice(0,this._arr.length)}[Symbol.iterator](){return this.values()}keys(){return this._arr.keys()}*values(){for(const t of this._arr)yield de(this._field,t,this.check)}*entries(){for(let t=0;t<this._arr.length;t++)yield[t,de(this._field,this._arr[t],this.check)]}}class Cn{constructor(t,n,r=!0){this.obj=this[O]=n??{},this.check=r,this._field=t}field(){return this._field}set(t,n){if(this.check){const r=function(s,a,o){const i=Bt(a,s.mapKey);if(i!==!0)return new T(s,`invalid map key: ${te({scalar:s.mapKey},a,i)}`);const u=ke(s,o);return u!==!0?new T(s,`map entry ${E(a)}: ${te(s,o,u)}`):void 0}(this._field,t,n);if(r)throw r}return this.obj[J(t)]=function(r,s){return r.mapKind=="message"?Ue(r,s):Pe(r,s)}(this._field,n),this}delete(t){const n=J(t),r=Object.prototype.hasOwnProperty.call(this.obj,n);return r&&delete this.obj[n],r}clear(){for(const t of Object.keys(this.obj))delete this.obj[t]}get(t){let n=this.obj[J(t)];return n!==void 0&&(n=pe(this._field,n,this.check)),n}has(t){return Object.prototype.hasOwnProperty.call(this.obj,J(t))}*keys(){for(const t of Object.keys(this.obj))yield et(t,this._field.mapKey)}*entries(){for(const t of Object.entries(this.obj))yield[et(t[0],this._field.mapKey),pe(this._field,t[1],this.check)]}[Symbol.iterator](){return this.entries()}get size(){return Object.keys(this.obj).length}*values(){for(const t of Object.values(this.obj))yield pe(this._field,t,this.check)}forEach(t,n){for(const r of this.entries())t.call(n,r[1],r[0],this)}}function Ue(e,t){return Oe(t)?Pt(t.message.$typeName)&&!e.oneof&&e.fieldKind=="message"?t.message.value:t.desc.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"?qt(t.message):t.message:t}function Le(e,t,n){return t!==void 0&&(j(e.message)&&!e.oneof&&e.fieldKind=="message"?t={$typeName:e.message.typeName,value:$e(e.message.fields[0],t)}:e.message.typeName=="google.protobuf.Struct"&&e.parent.typeName!="google.protobuf.Value"&&D(t)&&(t=Zt(t))),new Jt(e.message,t,n)}function Qe(e,t){return e.listKind=="message"?Ue(e,t):Pe(e,t)}function de(e,t,n){return e.listKind=="message"?Le(e,t,n):$e(e,t)}function pe(e,t,n){return e.mapKind=="message"?Le(e,t,n):t}function J(e){return typeof e=="string"||typeof e=="number"?e:String(e)}function et(e,t){switch(t){case c.STRING:return e;case c.INT32:case c.FIXED32:case c.UINT32:case c.SFIXED32:case c.SINT32:{const n=Number.parseInt(e);if(Number.isFinite(n))return n;break}case c.BOOL:switch(e){case"true":return!0;case"false":return!1}break;case c.UINT64:case c.FIXED64:try{return y.uParse(e)}catch{}break;default:try{return y.parse(e)}catch{}}return e}function $e(e,t){switch(e.scalar){case c.INT64:case c.SFIXED64:case c.SINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=y.parse(t));break;case c.FIXED64:case c.UINT64:"longAsString"in e&&e.longAsString&&typeof t=="string"&&(t=y.uParse(t))}return t}function Pe(e,t){switch(e.scalar){case c.INT64:case c.SFIXED64:case c.SINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=y.parse(t));break;case c.FIXED64:case c.UINT64:"longAsString"in e&&e.longAsString?t=String(t):typeof t!="string"&&typeof t!="number"||(t=y.uParse(t))}return t}function Zt(e){const t={$typeName:"google.protobuf.Struct",fields:{}};if(D(e))for(const[n,r]of Object.entries(e))t.fields[n]=zt(r);return t}function qt(e){const t={};for(const[n,r]of Object.entries(e.fields))t[n]=Ht(r);return t}function Ht(e){switch(e.kind.case){case"structValue":return qt(e.kind.value);case"listValue":return e.kind.value.values.map(Ht);case"nullValue":case void 0:return null;default:return e.kind.value}}function zt(e){const t={$typeName:"google.protobuf.Value",kind:{case:void 0}};switch(typeof e){case"number":t.kind={case:"numberValue",value:e};break;case"string":t.kind={case:"stringValue",value:e};break;case"boolean":t.kind={case:"boolValue",value:e};break;case"object":if(e===null)t.kind={case:"nullValue",value:0};else if(Array.isArray(e)){const n={$typeName:"google.protobuf.ListValue",values:[]};if(Array.isArray(e))for(const r of e)n.values.push(zt(r));t.kind={case:"listValue",value:n}}else t.kind={case:"structValue",value:Zt(e)}}return t}function Qt(e){const t=function(){if(!$){$=[];const u=en("std");for(let l=0;l<u.length;l++)$[u[l].charCodeAt(0)]=l;$[45]=u.indexOf("+"),$[95]=u.indexOf("/")}return $}();let n=3*e.length/4;e[e.length-2]=="="?n-=2:e[e.length-1]=="="&&(n-=1);let r,s=new Uint8Array(n),a=0,o=0,i=0;for(let u=0;u<e.length;u++){if(r=t[e.charCodeAt(u)],r===void 0)switch(e[u]){case"=":o=0;case`
`:case"\r":case"	":case" ":continue;default:throw Error("invalid base64 string")}switch(o){case 0:i=r,o=1;break;case 1:s[a++]=i<<2|(48&r)>>4,i=r,o=2;break;case 2:s[a++]=(15&i)<<4|(60&r)>>2,i=r,o=3;break;case 3:s[a++]=(3&i)<<6|r,o=0}}if(o==1)throw Error("invalid base64 string");return s.subarray(0,a)}let Z,tt,$;function en(e){return Z||(Z="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),tt=Z.slice(0,-2).concat("-","_")),e=="url"?tt:Z}function K(e){let t=!1;const n=[];for(let r=0;r<e.length;r++){let s=e.charAt(r);switch(s){case"_":t=!0;break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":n.push(s),t=!1;break;default:t&&(t=!1,s=s.toUpperCase()),n.push(s)}}return n.join("")}const xn=new Set(["constructor","toString","toJSON","valueOf"]);function B(e){return xn.has(e)?e+"$":e}function Ce(e){for(const t of e.field)V(t,"jsonName")||(t.jsonName=K(t.name));e.nestedType.forEach(Ce)}function Gn(e,t){switch(e){case c.STRING:return t;case c.BYTES:{const n=function(r){const s=[],a={tail:r,c:"",next(){return this.tail.length!=0&&(this.c=this.tail[0],this.tail=this.tail.substring(1),!0)},take(o){if(this.tail.length>=o){const i=this.tail.substring(0,o);return this.tail=this.tail.substring(o),i}return!1}};for(;a.next();)if(a.c==="\\"){if(a.next())switch(a.c){case"\\":s.push(a.c.charCodeAt(0));break;case"b":s.push(8);break;case"f":s.push(12);break;case"n":s.push(10);break;case"r":s.push(13);break;case"t":s.push(9);break;case"v":s.push(11);break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":{const o=a.c,i=a.take(2);if(i===!1)return!1;const u=parseInt(o+i,8);if(Number.isNaN(u))return!1;s.push(u);break}case"x":{const o=a.c,i=a.take(2);if(i===!1)return!1;const u=parseInt(o+i,16);if(Number.isNaN(u))return!1;s.push(u);break}case"u":{const o=a.c,i=a.take(4);if(i===!1)return!1;const u=parseInt(o+i,16);if(Number.isNaN(u))return!1;const l=new Uint8Array(4);new DataView(l.buffer).setInt32(0,u,!0),s.push(l[0],l[1],l[2],l[3]);break}case"U":{const o=a.c,i=a.take(8);if(i===!1)return!1;const u=y.uEnc(o+i),l=new Uint8Array(8),m=new DataView(l.buffer);m.setInt32(0,u.lo,!0),m.setInt32(4,u.hi,!0),s.push(l[0],l[1],l[2],l[3],l[4],l[5],l[6],l[7]);break}}}else s.push(a.c.charCodeAt(0));return new Uint8Array(s)}(t);if(n===!1)throw new Error(`cannot parse ${c[e]} default value: ${t}`);return n}case c.INT64:case c.SFIXED64:case c.SINT64:return y.parse(t);case c.UINT64:case c.FIXED64:return y.uParse(t);case c.DOUBLE:case c.FLOAT:switch(t){case"inf":return Number.POSITIVE_INFINITY;case"-inf":return Number.NEGATIVE_INFINITY;case"nan":return Number.NaN;default:return parseFloat(t)}case c.BOOL:return t==="true";case c.INT32:case c.UINT32:case c.SINT32:case c.FIXED32:case c.SFIXED32:return parseInt(t,10)}}function*he(e){switch(e.kind){case"file":for(const t of e.messages)yield t,yield*he(t);yield*e.enums,yield*e.services,yield*e.extensions;break;case"message":for(const t of e.nestedMessages)yield t,yield*he(t);yield*e.nestedEnums,yield*e.nestedExtensions}}function tn(...e){const t=function(){const n=new Map,r=new Map,s=new Map;return{kind:"registry",types:n,extendees:r,[Symbol.iterator]:()=>n.values(),get files(){return s.values()},addFile(a,o,i){if(s.set(a.proto.name,a),!o)for(const u of he(a))this.add(u);if(i)for(const u of a.dependencies)this.addFile(u,o,i)},add(a){if(a.kind=="extension"){let o=r.get(a.extendee.typeName);o||r.set(a.extendee.typeName,o=new Map),o.set(a.number,a)}n.set(a.typeName,a)},get:a=>n.get(a),getFile:a=>s.get(a),getMessage(a){const o=n.get(a);return(o==null?void 0:o.kind)=="message"?o:void 0},getEnum(a){const o=n.get(a);return(o==null?void 0:o.kind)=="enum"?o:void 0},getExtension(a){const o=n.get(a);return(o==null?void 0:o.kind)=="extension"?o:void 0},getExtensionFor(a,o){var i;return(i=r.get(a.typeName))===null||i===void 0?void 0:i.get(o)},getService(a){const o=n.get(a);return(o==null?void 0:o.kind)=="service"?o:void 0}}}();if(!e.length)return t;if("$typeName"in e[0]&&e[0].$typeName=="google.protobuf.FileDescriptorSet"){for(const n of e[0].file)at(n,t);return t}if("$typeName"in e[0]){let a=function(o){const i=[];for(const u of o.dependency){if(t.getFile(u)!=null||s.has(u))continue;const l=r(u);if(!l)throw new Error(`Unable to resolve ${u}, imported by ${o.name}`);"kind"in l?t.addFile(l,!1,!0):(s.add(l.name),i.push(l))}return i.concat(...i.map(a))};const n=e[0],r=e[1],s=new Set;for(const o of[n,...a(n)].reverse())at(o,t)}else for(const n of e)for(const r of n.files)t.addFile(r);return t}const Yn=998,Vn=999,Mn=9,H=10,Y=11,Kn=12,nt=14,ye=3,Bn=2,rt=1,jn=0,Xn=1,Wn=2,Jn=3,Zn=1,qn=2,Hn=1,nn={998:{fieldPresence:1,enumType:2,repeatedFieldEncoding:2,utf8Validation:3,messageEncoding:1,jsonFormat:2,enforceNamingStyle:2},999:{fieldPresence:2,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2},1e3:{fieldPresence:1,enumType:1,repeatedFieldEncoding:1,utf8Validation:2,messageEncoding:1,jsonFormat:1,enforceNamingStyle:2}};function at(e,t){var n,r;const s={kind:"file",proto:e,deprecated:(r=(n=e.options)===null||n===void 0?void 0:n.deprecated)!==null&&r!==void 0&&r,edition:er(e),name:e.name.replace(/\.proto$/,""),dependencies:tr(e,t),enums:[],messages:[],extensions:[],services:[],toString:()=>`file ${e.name}`},a=new Map,o={get:i=>a.get(i),add(i){var u;S(((u=i.proto.options)===null||u===void 0?void 0:u.mapEntry)===!0),a.set(i.typeName,i)}};for(const i of e.enumType)rn(i,s,void 0,t);for(const i of e.messageType)an(i,s,void 0,t,o);for(const i of e.service)zn(i,s,t);Ne(s,t);for(const i of a.values())Ee(i,t,o);for(const i of s.messages)Ee(i,t,o),Ne(i,t);t.addFile(s,!0)}function Ne(e,t){switch(e.kind){case"file":for(const n of e.proto.extension){const r=Te(n,e,t);e.extensions.push(r),t.add(r)}break;case"message":for(const n of e.proto.extension){const r=Te(n,e,t);e.nestedExtensions.push(r),t.add(r)}for(const n of e.nestedMessages)Ne(n,t)}}function Ee(e,t,n){const r=e.proto.oneofDecl.map(a=>function(o,i){return{kind:"oneof",proto:o,deprecated:!1,parent:i,fields:[],name:o.name,localName:B(K(o.name)),toString(){return`oneof ${i.typeName}.${this.name}`}}}(a,e)),s=new Set;for(const a of e.proto.field){const o=nr(a,r),i=Te(a,e,t,o,n);e.fields.push(i),e.field[i.localName]=i,o===void 0?e.members.push(i):(o.fields.push(i),s.has(o)||(s.add(o),e.members.push(o)))}for(const a of r.filter(o=>s.has(o)))e.oneofs.push(a);for(const a of e.nestedMessages)Ee(a,t,n)}function rn(e,t,n,r){var s,a,o,i,u;const l=function(d,p){const f=(g=d,(g.substring(0,1)+g.substring(1).replace(/[A-Z]/g,b=>"_"+b)).toLowerCase()+"_");var g;for(const b of p){if(!b.name.toLowerCase().startsWith(f))return;const h=b.name.substring(f.length);if(h.length==0||/^\d/.test(h))return}return f}(e.name,e.value),m={kind:"enum",proto:e,deprecated:(a=(s=e.options)===null||s===void 0?void 0:s.deprecated)!==null&&a!==void 0&&a,file:t,parent:n,open:!0,name:e.name,typeName:se(e,n,t),value:{},values:[],sharedPrefix:l,toString(){return`enum ${this.typeName}`}};m.open=function(d){var p;return Hn==x("enumType",{proto:d.proto,parent:(p=d.parent)!==null&&p!==void 0?p:d.file})}(m),r.add(m);for(const d of e.value){const p=d.name;m.values.push(m.value[d.number]={kind:"enum_value",proto:d,deprecated:(i=(o=e.options)===null||o===void 0?void 0:o.deprecated)!==null&&i!==void 0&&i,parent:m,name:p,localName:B(l==null?p:p.substring(l.length)),number:d.number,toString:()=>`enum value ${m.typeName}.${p}`})}((u=n==null?void 0:n.nestedEnums)!==null&&u!==void 0?u:t.enums).push(m)}function an(e,t,n,r,s){var a,o,i,u;const l={kind:"message",proto:e,deprecated:(o=(a=e.options)===null||a===void 0?void 0:a.deprecated)!==null&&o!==void 0&&o,file:t,parent:n,name:e.name,typeName:se(e,n,t),fields:[],field:{},oneofs:[],members:[],nestedEnums:[],nestedMessages:[],nestedExtensions:[],toString(){return`message ${this.typeName}`}};((i=e.options)===null||i===void 0?void 0:i.mapEntry)===!0?s.add(l):(((u=n==null?void 0:n.nestedMessages)!==null&&u!==void 0?u:t.messages).push(l),r.add(l));for(const m of e.enumType)rn(m,t,l,r);for(const m of e.nestedType)an(m,t,l,r,s)}function zn(e,t,n){var r,s;const a={kind:"service",proto:e,deprecated:(s=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&s!==void 0&&s,file:t,name:e.name,typeName:se(e,void 0,t),methods:[],method:{},toString(){return`service ${this.typeName}`}};t.services.push(a),n.add(a);for(const o of e.method){const i=Qn(o,a,n);a.methods.push(i),a.method[i.localName]=i}}function Qn(e,t,n){var r,s,a,o;let i;i=e.clientStreaming&&e.serverStreaming?"bidi_streaming":e.clientStreaming?"client_streaming":e.serverStreaming?"server_streaming":"unary";const u=n.getMessage(w(e.inputType)),l=n.getMessage(w(e.outputType));S(u,`invalid MethodDescriptorProto: input_type ${e.inputType} not found`),S(l,`invalid MethodDescriptorProto: output_type ${e.inputType} not found`);const m=e.name;return{kind:"rpc",proto:e,deprecated:(s=(r=e.options)===null||r===void 0?void 0:r.deprecated)!==null&&s!==void 0&&s,parent:t,name:m,localName:B(m.length?B(m[0].toLowerCase()+m.substring(1)):m),methodKind:i,input:u,output:l,idempotency:(o=(a=e.options)===null||a===void 0?void 0:a.idempotencyLevel)!==null&&o!==void 0?o:jn,toString:()=>`rpc ${t.typeName}.${m}`}}function Te(e,t,n,r,s){var a,o,i;const u=s===void 0,l={kind:"field",proto:e,deprecated:(o=(a=e.options)===null||a===void 0?void 0:a.deprecated)!==null&&o!==void 0&&o,name:e.name,number:e.number,scalar:void 0,message:void 0,enum:void 0,presence:rr(e,r,u,t),listKind:void 0,mapKind:void 0,mapKey:void 0,delimitedEncoding:void 0,packed:void 0,longAsString:!1,getDefaultValue:void 0};if(u){const f=t.kind=="file"?t:t.file,g=t.kind=="file"?void 0:t,b=se(e,g,f);l.kind="extension",l.file=f,l.parent=g,l.oneof=void 0,l.typeName=b,l.jsonName=`[${b}]`,l.toString=()=>`extension ${b}`;const h=n.getMessage(w(e.extendee));S(h,`invalid FieldDescriptorProto: extendee ${e.extendee} not found`),l.extendee=h}else{const f=t;S(f.kind=="message"),l.parent=f,l.oneof=r,l.localName=r?K(e.name):B(K(e.name)),l.jsonName=e.jsonName,l.toString=()=>`field ${f.typeName}.${e.name}`}const m=e.label,d=e.type,p=(i=e.options)===null||i===void 0?void 0:i.jstype;if(m===ye){const f=d==Y?s==null?void 0:s.get(w(e.typeName)):void 0;if(f){l.fieldKind="map";const{key:g,value:b}=function(h){const v=h.fields.find(R=>R.number===1),F=h.fields.find(R=>R.number===2);return S(v&&v.fieldKind=="scalar"&&v.scalar!=c.BYTES&&v.scalar!=c.FLOAT&&v.scalar!=c.DOUBLE&&F&&F.fieldKind!="list"&&F.fieldKind!="map"),{key:v,value:F}}(f);return l.mapKey=g.scalar,l.mapKind=b.fieldKind,l.message=b.message,l.delimitedEncoding=!1,l.enum=b.enum,l.scalar=b.scalar,l}switch(l.fieldKind="list",d){case Y:case H:l.listKind="message",l.message=n.getMessage(w(e.typeName)),S(l.message),l.delimitedEncoding=st(e,t);break;case nt:l.listKind="enum",l.enum=n.getEnum(w(e.typeName)),S(l.enum);break;default:l.listKind="scalar",l.scalar=d,l.longAsString=p==rt}return l.packed=function(g,b){if(g.label!=ye)return!1;switch(g.type){case Mn:case Kn:case H:case Y:return!1}const h=g.options;return h&&V(h,"packed")?h.packed:Zn==x("repeatedFieldEncoding",{proto:g,parent:b})}(e,t),l}switch(d){case Y:case H:l.fieldKind="message",l.message=n.getMessage(w(e.typeName)),S(l.message,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),l.delimitedEncoding=st(e,t),l.getDefaultValue=()=>{};break;case nt:{const f=n.getEnum(w(e.typeName));S(f!==void 0,`invalid FieldDescriptorProto: type_name ${e.typeName} not found`),l.fieldKind="enum",l.enum=n.getEnum(w(e.typeName)),l.getDefaultValue=()=>V(e,"defaultValue")?function(g,b){const h=g.values.find(v=>v.name===b);if(!h)throw new Error(`cannot parse ${g} default value: ${b}`);return h.number}(f,e.defaultValue):void 0;break}default:l.fieldKind="scalar",l.scalar=d,l.longAsString=p==rt,l.getDefaultValue=()=>V(e,"defaultValue")?Gn(d,e.defaultValue):void 0}return l}function er(e){switch(e.syntax){case"":case"proto2":return Yn;case"proto3":return Vn;case"editions":if(e.edition in nn)return e.edition;throw new Error(`${e.name}: unsupported edition`);default:throw new Error(`${e.name}: unsupported syntax "${e.syntax}"`)}}function tr(e,t){return e.dependency.map(n=>{const r=t.getFile(n);if(!r)throw new Error(`Cannot find ${n}, imported by ${e.name}`);return r})}function se(e,t,n){let r;return r=t?`${t.typeName}.${e.name}`:n.proto.package.length>0?`${n.proto.package}.${e.name}`:`${e.name}`,r}function w(e){return e.startsWith(".")?e.substring(1):e}function nr(e,t){if(!V(e,"oneofIndex")||e.proto3Optional)return;const n=t[e.oneofIndex];return S(n,`invalid FieldDescriptorProto: oneof #${e.oneofIndex} for field #${e.number} not found`),n}function rr(e,t,n,r){return e.label==Bn?Jn:e.label==ye?Wn:t||e.proto3Optional||e.type==Y||n?Xn:x("fieldPresence",{proto:e,parent:r})}function st(e,t){return e.type==H||qn==x("messageEncoding",{proto:e,parent:t})}function x(e,t){var n,r;const s=(n=t.proto.options)===null||n===void 0?void 0:n.features;if(s){const a=s[e];if(a!=0)return a}if("kind"in t){if(t.kind=="message")return x(e,(r=t.parent)!==null&&r!==void 0?r:t.file);const a=nn[t.edition];if(!a)throw new Error(`feature default for edition ${t.edition} not found`);return a[e]}return x(e,t.parent)}function S(e,t){if(!e)throw new Error(t)}function ar(e){const t=function(n){return Object.assign(Object.create({syntax:"",edition:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FileDescriptorProto",dependency:[],publicDependency:[],weakDependency:[],service:[],extension:[]},n),{messageType:n.messageType.map(sn),enumType:n.enumType.map(on)}))}(e);return t.messageType.forEach(Ce),tn(t,()=>{}).getFile(t.name)}function sn(e){var t,n,r,s,a,o,i,u;return{$typeName:"google.protobuf.DescriptorProto",name:e.name,field:(n=(t=e.field)===null||t===void 0?void 0:t.map(sr))!==null&&n!==void 0?n:[],extension:[],nestedType:(s=(r=e.nestedType)===null||r===void 0?void 0:r.map(sn))!==null&&s!==void 0?s:[],enumType:(o=(a=e.enumType)===null||a===void 0?void 0:a.map(on))!==null&&o!==void 0?o:[],extensionRange:(u=(i=e.extensionRange)===null||i===void 0?void 0:i.map(l=>Object.assign({$typeName:"google.protobuf.DescriptorProto.ExtensionRange"},l)))!==null&&u!==void 0?u:[],oneofDecl:[],reservedRange:[],reservedName:[]}}function sr(e){return Object.assign(Object.create({label:1,typeName:"",extendee:"",defaultValue:"",oneofIndex:0,jsonName:"",proto3Optional:!1}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldDescriptorProto"},e),{options:e.options?or(e.options):void 0}))}function or(e){var t,n,r;return Object.assign(Object.create({ctype:0,packed:!1,jstype:0,lazy:!1,unverifiedLazy:!1,deprecated:!1,weak:!1,debugRedact:!1,retention:0}),Object.assign(Object.assign({$typeName:"google.protobuf.FieldOptions"},e),{targets:(t=e.targets)!==null&&t!==void 0?t:[],editionDefaults:(r=(n=e.editionDefaults)===null||n===void 0?void 0:n.map(a=>Object.assign({$typeName:"google.protobuf.FieldOptions.EditionDefault"},a)))!==null&&r!==void 0?r:[],uninterpretedOption:[]}))}function on(e){return{$typeName:"google.protobuf.EnumDescriptorProto",name:e.name,reservedName:[],reservedRange:[],value:e.value.map(t=>Object.assign({$typeName:"google.protobuf.EnumValueDescriptorProto"},t))}}function X(e,t,...n){return n.reduce((r,s)=>r.nestedMessages[s],e.messages[t])}const ir=X(ar({name:"google/protobuf/descriptor.proto",package:"google.protobuf",messageType:[{name:"FileDescriptorSet",field:[{name:"file",number:1,type:11,label:3,typeName:".google.protobuf.FileDescriptorProto"}],extensionRange:[{start:536e6,end:536000001}]},{name:"FileDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"package",number:2,type:9,label:1},{name:"dependency",number:3,type:9,label:3},{name:"public_dependency",number:10,type:5,label:3},{name:"weak_dependency",number:11,type:5,label:3},{name:"message_type",number:4,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:5,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"service",number:6,type:11,label:3,typeName:".google.protobuf.ServiceDescriptorProto"},{name:"extension",number:7,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FileOptions"},{name:"source_code_info",number:9,type:11,label:1,typeName:".google.protobuf.SourceCodeInfo"},{name:"syntax",number:12,type:9,label:1},{name:"edition",number:14,type:14,label:1,typeName:".google.protobuf.Edition"}]},{name:"DescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"field",number:2,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"extension",number:6,type:11,label:3,typeName:".google.protobuf.FieldDescriptorProto"},{name:"nested_type",number:3,type:11,label:3,typeName:".google.protobuf.DescriptorProto"},{name:"enum_type",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto"},{name:"extension_range",number:5,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ExtensionRange"},{name:"oneof_decl",number:8,type:11,label:3,typeName:".google.protobuf.OneofDescriptorProto"},{name:"options",number:7,type:11,label:1,typeName:".google.protobuf.MessageOptions"},{name:"reserved_range",number:9,type:11,label:3,typeName:".google.protobuf.DescriptorProto.ReservedRange"},{name:"reserved_name",number:10,type:9,label:3}],nestedType:[{name:"ExtensionRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ExtensionRangeOptions"}]},{name:"ReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"ExtensionRangeOptions",field:[{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"},{name:"declaration",number:2,type:11,label:3,typeName:".google.protobuf.ExtensionRangeOptions.Declaration",options:{retention:2}},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"verification",number:3,type:14,label:1,typeName:".google.protobuf.ExtensionRangeOptions.VerificationState",defaultValue:"UNVERIFIED",options:{retention:2}}],nestedType:[{name:"Declaration",field:[{name:"number",number:1,type:5,label:1},{name:"full_name",number:2,type:9,label:1},{name:"type",number:3,type:9,label:1},{name:"reserved",number:5,type:8,label:1},{name:"repeated",number:6,type:8,label:1}]}],enumType:[{name:"VerificationState",value:[{name:"DECLARATION",number:0},{name:"UNVERIFIED",number:1}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:3,type:5,label:1},{name:"label",number:4,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Label"},{name:"type",number:5,type:14,label:1,typeName:".google.protobuf.FieldDescriptorProto.Type"},{name:"type_name",number:6,type:9,label:1},{name:"extendee",number:2,type:9,label:1},{name:"default_value",number:7,type:9,label:1},{name:"oneof_index",number:9,type:5,label:1},{name:"json_name",number:10,type:9,label:1},{name:"options",number:8,type:11,label:1,typeName:".google.protobuf.FieldOptions"},{name:"proto3_optional",number:17,type:8,label:1}],enumType:[{name:"Type",value:[{name:"TYPE_DOUBLE",number:1},{name:"TYPE_FLOAT",number:2},{name:"TYPE_INT64",number:3},{name:"TYPE_UINT64",number:4},{name:"TYPE_INT32",number:5},{name:"TYPE_FIXED64",number:6},{name:"TYPE_FIXED32",number:7},{name:"TYPE_BOOL",number:8},{name:"TYPE_STRING",number:9},{name:"TYPE_GROUP",number:10},{name:"TYPE_MESSAGE",number:11},{name:"TYPE_BYTES",number:12},{name:"TYPE_UINT32",number:13},{name:"TYPE_ENUM",number:14},{name:"TYPE_SFIXED32",number:15},{name:"TYPE_SFIXED64",number:16},{name:"TYPE_SINT32",number:17},{name:"TYPE_SINT64",number:18}]},{name:"Label",value:[{name:"LABEL_OPTIONAL",number:1},{name:"LABEL_REPEATED",number:3},{name:"LABEL_REQUIRED",number:2}]}]},{name:"OneofDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"options",number:2,type:11,label:1,typeName:".google.protobuf.OneofOptions"}]},{name:"EnumDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"value",number:2,type:11,label:3,typeName:".google.protobuf.EnumValueDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumOptions"},{name:"reserved_range",number:4,type:11,label:3,typeName:".google.protobuf.EnumDescriptorProto.EnumReservedRange"},{name:"reserved_name",number:5,type:9,label:3}],nestedType:[{name:"EnumReservedRange",field:[{name:"start",number:1,type:5,label:1},{name:"end",number:2,type:5,label:1}]}]},{name:"EnumValueDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"number",number:2,type:5,label:1},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.EnumValueOptions"}]},{name:"ServiceDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"method",number:2,type:11,label:3,typeName:".google.protobuf.MethodDescriptorProto"},{name:"options",number:3,type:11,label:1,typeName:".google.protobuf.ServiceOptions"}]},{name:"MethodDescriptorProto",field:[{name:"name",number:1,type:9,label:1},{name:"input_type",number:2,type:9,label:1},{name:"output_type",number:3,type:9,label:1},{name:"options",number:4,type:11,label:1,typeName:".google.protobuf.MethodOptions"},{name:"client_streaming",number:5,type:8,label:1,defaultValue:"false"},{name:"server_streaming",number:6,type:8,label:1,defaultValue:"false"}]},{name:"FileOptions",field:[{name:"java_package",number:1,type:9,label:1},{name:"java_outer_classname",number:8,type:9,label:1},{name:"java_multiple_files",number:10,type:8,label:1,defaultValue:"false"},{name:"java_generate_equals_and_hash",number:20,type:8,label:1,options:{deprecated:!0}},{name:"java_string_check_utf8",number:27,type:8,label:1,defaultValue:"false"},{name:"optimize_for",number:9,type:14,label:1,typeName:".google.protobuf.FileOptions.OptimizeMode",defaultValue:"SPEED"},{name:"go_package",number:11,type:9,label:1},{name:"cc_generic_services",number:16,type:8,label:1,defaultValue:"false"},{name:"java_generic_services",number:17,type:8,label:1,defaultValue:"false"},{name:"py_generic_services",number:18,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:23,type:8,label:1,defaultValue:"false"},{name:"cc_enable_arenas",number:31,type:8,label:1,defaultValue:"true"},{name:"objc_class_prefix",number:36,type:9,label:1},{name:"csharp_namespace",number:37,type:9,label:1},{name:"swift_prefix",number:39,type:9,label:1},{name:"php_class_prefix",number:40,type:9,label:1},{name:"php_namespace",number:41,type:9,label:1},{name:"php_metadata_namespace",number:44,type:9,label:1},{name:"ruby_package",number:45,type:9,label:1},{name:"features",number:50,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"OptimizeMode",value:[{name:"SPEED",number:1},{name:"CODE_SIZE",number:2},{name:"LITE_RUNTIME",number:3}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"MessageOptions",field:[{name:"message_set_wire_format",number:1,type:8,label:1,defaultValue:"false"},{name:"no_standard_descriptor_accessor",number:2,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"map_entry",number:7,type:8,label:1},{name:"deprecated_legacy_json_field_conflicts",number:11,type:8,label:1,options:{deprecated:!0}},{name:"features",number:12,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"FieldOptions",field:[{name:"ctype",number:1,type:14,label:1,typeName:".google.protobuf.FieldOptions.CType",defaultValue:"STRING"},{name:"packed",number:2,type:8,label:1},{name:"jstype",number:6,type:14,label:1,typeName:".google.protobuf.FieldOptions.JSType",defaultValue:"JS_NORMAL"},{name:"lazy",number:5,type:8,label:1,defaultValue:"false"},{name:"unverified_lazy",number:15,type:8,label:1,defaultValue:"false"},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"weak",number:10,type:8,label:1,defaultValue:"false"},{name:"debug_redact",number:16,type:8,label:1,defaultValue:"false"},{name:"retention",number:17,type:14,label:1,typeName:".google.protobuf.FieldOptions.OptionRetention"},{name:"targets",number:19,type:14,label:3,typeName:".google.protobuf.FieldOptions.OptionTargetType"},{name:"edition_defaults",number:20,type:11,label:3,typeName:".google.protobuf.FieldOptions.EditionDefault"},{name:"features",number:21,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"feature_support",number:22,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],nestedType:[{name:"EditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"value",number:2,type:9,label:1}]},{name:"FeatureSupport",field:[{name:"edition_introduced",number:1,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"edition_deprecated",number:2,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"deprecation_warning",number:3,type:9,label:1},{name:"edition_removed",number:4,type:14,label:1,typeName:".google.protobuf.Edition"}]}],enumType:[{name:"CType",value:[{name:"STRING",number:0},{name:"CORD",number:1},{name:"STRING_PIECE",number:2}]},{name:"JSType",value:[{name:"JS_NORMAL",number:0},{name:"JS_STRING",number:1},{name:"JS_NUMBER",number:2}]},{name:"OptionRetention",value:[{name:"RETENTION_UNKNOWN",number:0},{name:"RETENTION_RUNTIME",number:1},{name:"RETENTION_SOURCE",number:2}]},{name:"OptionTargetType",value:[{name:"TARGET_TYPE_UNKNOWN",number:0},{name:"TARGET_TYPE_FILE",number:1},{name:"TARGET_TYPE_EXTENSION_RANGE",number:2},{name:"TARGET_TYPE_MESSAGE",number:3},{name:"TARGET_TYPE_FIELD",number:4},{name:"TARGET_TYPE_ONEOF",number:5},{name:"TARGET_TYPE_ENUM",number:6},{name:"TARGET_TYPE_ENUM_ENTRY",number:7},{name:"TARGET_TYPE_SERVICE",number:8},{name:"TARGET_TYPE_METHOD",number:9}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"OneofOptions",field:[{name:"features",number:1,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumOptions",field:[{name:"allow_alias",number:2,type:8,label:1},{name:"deprecated",number:3,type:8,label:1,defaultValue:"false"},{name:"deprecated_legacy_json_field_conflicts",number:6,type:8,label:1,options:{deprecated:!0}},{name:"features",number:7,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"EnumValueOptions",field:[{name:"deprecated",number:1,type:8,label:1,defaultValue:"false"},{name:"features",number:2,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"debug_redact",number:3,type:8,label:1,defaultValue:"false"},{name:"feature_support",number:4,type:11,label:1,typeName:".google.protobuf.FieldOptions.FeatureSupport"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"ServiceOptions",field:[{name:"features",number:34,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],extensionRange:[{start:1e3,end:536870912}]},{name:"MethodOptions",field:[{name:"deprecated",number:33,type:8,label:1,defaultValue:"false"},{name:"idempotency_level",number:34,type:14,label:1,typeName:".google.protobuf.MethodOptions.IdempotencyLevel",defaultValue:"IDEMPOTENCY_UNKNOWN"},{name:"features",number:35,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"uninterpreted_option",number:999,type:11,label:3,typeName:".google.protobuf.UninterpretedOption"}],enumType:[{name:"IdempotencyLevel",value:[{name:"IDEMPOTENCY_UNKNOWN",number:0},{name:"NO_SIDE_EFFECTS",number:1},{name:"IDEMPOTENT",number:2}]}],extensionRange:[{start:1e3,end:536870912}]},{name:"UninterpretedOption",field:[{name:"name",number:2,type:11,label:3,typeName:".google.protobuf.UninterpretedOption.NamePart"},{name:"identifier_value",number:3,type:9,label:1},{name:"positive_int_value",number:4,type:4,label:1},{name:"negative_int_value",number:5,type:3,label:1},{name:"double_value",number:6,type:1,label:1},{name:"string_value",number:7,type:12,label:1},{name:"aggregate_value",number:8,type:9,label:1}],nestedType:[{name:"NamePart",field:[{name:"name_part",number:1,type:9,label:2},{name:"is_extension",number:2,type:8,label:2}]}]},{name:"FeatureSet",field:[{name:"field_presence",number:1,type:14,label:1,typeName:".google.protobuf.FeatureSet.FieldPresence",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPLICIT",edition:900},{value:"IMPLICIT",edition:999},{value:"EXPLICIT",edition:1e3}]}},{name:"enum_type",number:2,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnumType",options:{retention:1,targets:[6,1],editionDefaults:[{value:"CLOSED",edition:900},{value:"OPEN",edition:999}]}},{name:"repeated_field_encoding",number:3,type:14,label:1,typeName:".google.protobuf.FeatureSet.RepeatedFieldEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"EXPANDED",edition:900},{value:"PACKED",edition:999}]}},{name:"utf8_validation",number:4,type:14,label:1,typeName:".google.protobuf.FeatureSet.Utf8Validation",options:{retention:1,targets:[4,1],editionDefaults:[{value:"NONE",edition:900},{value:"VERIFY",edition:999}]}},{name:"message_encoding",number:5,type:14,label:1,typeName:".google.protobuf.FeatureSet.MessageEncoding",options:{retention:1,targets:[4,1],editionDefaults:[{value:"LENGTH_PREFIXED",edition:900}]}},{name:"json_format",number:6,type:14,label:1,typeName:".google.protobuf.FeatureSet.JsonFormat",options:{retention:1,targets:[3,6,1],editionDefaults:[{value:"LEGACY_BEST_EFFORT",edition:900},{value:"ALLOW",edition:999}]}},{name:"enforce_naming_style",number:7,type:14,label:1,typeName:".google.protobuf.FeatureSet.EnforceNamingStyle",options:{retention:2,targets:[1,2,3,4,5,6,7,8,9],editionDefaults:[{value:"STYLE_LEGACY",edition:900},{value:"STYLE2024",edition:1001}]}}],enumType:[{name:"FieldPresence",value:[{name:"FIELD_PRESENCE_UNKNOWN",number:0},{name:"EXPLICIT",number:1},{name:"IMPLICIT",number:2},{name:"LEGACY_REQUIRED",number:3}]},{name:"EnumType",value:[{name:"ENUM_TYPE_UNKNOWN",number:0},{name:"OPEN",number:1},{name:"CLOSED",number:2}]},{name:"RepeatedFieldEncoding",value:[{name:"REPEATED_FIELD_ENCODING_UNKNOWN",number:0},{name:"PACKED",number:1},{name:"EXPANDED",number:2}]},{name:"Utf8Validation",value:[{name:"UTF8_VALIDATION_UNKNOWN",number:0},{name:"VERIFY",number:2},{name:"NONE",number:3}]},{name:"MessageEncoding",value:[{name:"MESSAGE_ENCODING_UNKNOWN",number:0},{name:"LENGTH_PREFIXED",number:1},{name:"DELIMITED",number:2}]},{name:"JsonFormat",value:[{name:"JSON_FORMAT_UNKNOWN",number:0},{name:"ALLOW",number:1},{name:"LEGACY_BEST_EFFORT",number:2}]},{name:"EnforceNamingStyle",value:[{name:"ENFORCE_NAMING_STYLE_UNKNOWN",number:0},{name:"STYLE2024",number:1},{name:"STYLE_LEGACY",number:2}]}],extensionRange:[{start:1e3,end:9995},{start:9995,end:1e4},{start:1e4,end:10001}]},{name:"FeatureSetDefaults",field:[{name:"defaults",number:1,type:11,label:3,typeName:".google.protobuf.FeatureSetDefaults.FeatureSetEditionDefault"},{name:"minimum_edition",number:4,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"maximum_edition",number:5,type:14,label:1,typeName:".google.protobuf.Edition"}],nestedType:[{name:"FeatureSetEditionDefault",field:[{name:"edition",number:3,type:14,label:1,typeName:".google.protobuf.Edition"},{name:"overridable_features",number:4,type:11,label:1,typeName:".google.protobuf.FeatureSet"},{name:"fixed_features",number:5,type:11,label:1,typeName:".google.protobuf.FeatureSet"}]}]},{name:"SourceCodeInfo",field:[{name:"location",number:1,type:11,label:3,typeName:".google.protobuf.SourceCodeInfo.Location"}],nestedType:[{name:"Location",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"span",number:2,type:5,label:3,options:{packed:!0}},{name:"leading_comments",number:3,type:9,label:1},{name:"trailing_comments",number:4,type:9,label:1},{name:"leading_detached_comments",number:6,type:9,label:3}]}],extensionRange:[{start:536e6,end:536000001}]},{name:"GeneratedCodeInfo",field:[{name:"annotation",number:1,type:11,label:3,typeName:".google.protobuf.GeneratedCodeInfo.Annotation"}],nestedType:[{name:"Annotation",field:[{name:"path",number:1,type:5,label:3,options:{packed:!0}},{name:"source_file",number:2,type:9,label:1},{name:"begin",number:3,type:5,label:1},{name:"end",number:4,type:5,label:1},{name:"semantic",number:5,type:14,label:1,typeName:".google.protobuf.GeneratedCodeInfo.Annotation.Semantic"}],enumType:[{name:"Semantic",value:[{name:"NONE",number:0},{name:"SET",number:1},{name:"ALIAS",number:2}]}]}]}],enumType:[{name:"Edition",value:[{name:"EDITION_UNKNOWN",number:0},{name:"EDITION_LEGACY",number:900},{name:"EDITION_PROTO2",number:998},{name:"EDITION_PROTO3",number:999},{name:"EDITION_2023",number:1e3},{name:"EDITION_2024",number:1001},{name:"EDITION_1_TEST_ONLY",number:1},{name:"EDITION_2_TEST_ONLY",number:2},{name:"EDITION_99997_TEST_ONLY",number:99997},{name:"EDITION_99998_TEST_ONLY",number:99998},{name:"EDITION_99999_TEST_ONLY",number:99999},{name:"EDITION_MAX",number:2147483647}]}]}),1);var ot,it,ut,lt,ct,mt,dt,pt,ft,gt,bt,ht,yt,Nt,Et,Tt,vt,It;(function(e){e[e.DECLARATION=0]="DECLARATION",e[e.UNVERIFIED=1]="UNVERIFIED"})(ot||(ot={})),function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.GROUP=10]="GROUP",e[e.MESSAGE=11]="MESSAGE",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.ENUM=14]="ENUM",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"}(it||(it={})),function(e){e[e.OPTIONAL=1]="OPTIONAL",e[e.REPEATED=3]="REPEATED",e[e.REQUIRED=2]="REQUIRED"}(ut||(ut={})),function(e){e[e.SPEED=1]="SPEED",e[e.CODE_SIZE=2]="CODE_SIZE",e[e.LITE_RUNTIME=3]="LITE_RUNTIME"}(lt||(lt={})),function(e){e[e.STRING=0]="STRING",e[e.CORD=1]="CORD",e[e.STRING_PIECE=2]="STRING_PIECE"}(ct||(ct={})),function(e){e[e.JS_NORMAL=0]="JS_NORMAL",e[e.JS_STRING=1]="JS_STRING",e[e.JS_NUMBER=2]="JS_NUMBER"}(mt||(mt={})),function(e){e[e.RETENTION_UNKNOWN=0]="RETENTION_UNKNOWN",e[e.RETENTION_RUNTIME=1]="RETENTION_RUNTIME",e[e.RETENTION_SOURCE=2]="RETENTION_SOURCE"}(dt||(dt={})),function(e){e[e.TARGET_TYPE_UNKNOWN=0]="TARGET_TYPE_UNKNOWN",e[e.TARGET_TYPE_FILE=1]="TARGET_TYPE_FILE",e[e.TARGET_TYPE_EXTENSION_RANGE=2]="TARGET_TYPE_EXTENSION_RANGE",e[e.TARGET_TYPE_MESSAGE=3]="TARGET_TYPE_MESSAGE",e[e.TARGET_TYPE_FIELD=4]="TARGET_TYPE_FIELD",e[e.TARGET_TYPE_ONEOF=5]="TARGET_TYPE_ONEOF",e[e.TARGET_TYPE_ENUM=6]="TARGET_TYPE_ENUM",e[e.TARGET_TYPE_ENUM_ENTRY=7]="TARGET_TYPE_ENUM_ENTRY",e[e.TARGET_TYPE_SERVICE=8]="TARGET_TYPE_SERVICE",e[e.TARGET_TYPE_METHOD=9]="TARGET_TYPE_METHOD"}(pt||(pt={})),function(e){e[e.IDEMPOTENCY_UNKNOWN=0]="IDEMPOTENCY_UNKNOWN",e[e.NO_SIDE_EFFECTS=1]="NO_SIDE_EFFECTS",e[e.IDEMPOTENT=2]="IDEMPOTENT"}(ft||(ft={})),function(e){e[e.FIELD_PRESENCE_UNKNOWN=0]="FIELD_PRESENCE_UNKNOWN",e[e.EXPLICIT=1]="EXPLICIT",e[e.IMPLICIT=2]="IMPLICIT",e[e.LEGACY_REQUIRED=3]="LEGACY_REQUIRED"}(gt||(gt={})),function(e){e[e.ENUM_TYPE_UNKNOWN=0]="ENUM_TYPE_UNKNOWN",e[e.OPEN=1]="OPEN",e[e.CLOSED=2]="CLOSED"}(bt||(bt={})),function(e){e[e.REPEATED_FIELD_ENCODING_UNKNOWN=0]="REPEATED_FIELD_ENCODING_UNKNOWN",e[e.PACKED=1]="PACKED",e[e.EXPANDED=2]="EXPANDED"}(ht||(ht={})),function(e){e[e.UTF8_VALIDATION_UNKNOWN=0]="UTF8_VALIDATION_UNKNOWN",e[e.VERIFY=2]="VERIFY",e[e.NONE=3]="NONE"}(yt||(yt={})),function(e){e[e.MESSAGE_ENCODING_UNKNOWN=0]="MESSAGE_ENCODING_UNKNOWN",e[e.LENGTH_PREFIXED=1]="LENGTH_PREFIXED",e[e.DELIMITED=2]="DELIMITED"}(Nt||(Nt={})),function(e){e[e.JSON_FORMAT_UNKNOWN=0]="JSON_FORMAT_UNKNOWN",e[e.ALLOW=1]="ALLOW",e[e.LEGACY_BEST_EFFORT=2]="LEGACY_BEST_EFFORT"}(Et||(Et={})),function(e){e[e.ENFORCE_NAMING_STYLE_UNKNOWN=0]="ENFORCE_NAMING_STYLE_UNKNOWN",e[e.STYLE2024=1]="STYLE2024",e[e.STYLE_LEGACY=2]="STYLE_LEGACY"}(Tt||(Tt={})),function(e){e[e.NONE=0]="NONE",e[e.SET=1]="SET",e[e.ALIAS=2]="ALIAS"}(vt||(vt={})),function(e){e[e.EDITION_UNKNOWN=0]="EDITION_UNKNOWN",e[e.EDITION_LEGACY=900]="EDITION_LEGACY",e[e.EDITION_PROTO2=998]="EDITION_PROTO2",e[e.EDITION_PROTO3=999]="EDITION_PROTO3",e[e.EDITION_2023=1e3]="EDITION_2023",e[e.EDITION_2024=1001]="EDITION_2024",e[e.EDITION_1_TEST_ONLY=1]="EDITION_1_TEST_ONLY",e[e.EDITION_2_TEST_ONLY=2]="EDITION_2_TEST_ONLY",e[e.EDITION_99997_TEST_ONLY=99997]="EDITION_99997_TEST_ONLY",e[e.EDITION_99998_TEST_ONLY=99998]="EDITION_99998_TEST_ONLY",e[e.EDITION_99999_TEST_ONLY=99999]="EDITION_99999_TEST_ONLY",e[e.EDITION_MAX=2147483647]="EDITION_MAX"}(It||(It={}));const St={readUnknownFields:!0};function un(e,t,n){const r=I(e,void 0,!1);return ln(r,new Fe(t),function(s){return s?Object.assign(Object.assign({},St),s):St}(n),!1,t.byteLength),r.message}function ln(e,t,n,r,s){var a;const o=r?t.len:t.pos+s;let i,u;const l=(a=e.getUnknown())!==null&&a!==void 0?a:[];for(;t.pos<o&&([i,u]=t.tag(),!r||u!=N.EndGroup);){const m=e.findNumber(i);if(m)cn(e,t,m,u,n);else{const d=t.skip(u,i);n.readUnknownFields&&l.push({no:i,wireType:u,data:d})}}if(r&&(u!=N.EndGroup||i!==s))throw new Error("invalid end group tag");l.length>0&&e.setUnknown(l)}function cn(e,t,n,r,s){switch(n.fieldKind){case"scalar":e.set(n,P(t,n.scalar));break;case"enum":e.set(n,P(t,c.INT32));break;case"message":e.set(n,fe(t,s,n,e.get(n)));break;case"list":(function(a,o,i,u){var l;const m=i.field();if(m.listKind==="message")return void i.add(fe(a,u,m));const d=(l=m.scalar)!==null&&l!==void 0?l:c.INT32;if(!(o==N.LengthDelimited&&d!=c.STRING&&d!=c.BYTES))return void i.add(P(a,d));const f=a.uint32()+a.pos;for(;a.pos<f;)i.add(P(a,d))})(t,r,e.get(n),s);break;case"map":(function(a,o,i){const u=o.field();let l,m;const d=a.pos+a.uint32();for(;a.pos<d;){const[p]=a.tag();switch(p){case 1:l=P(a,u.mapKey);break;case 2:switch(u.mapKind){case"scalar":m=P(a,u.scalar);break;case"enum":m=a.int32();break;case"message":m=fe(a,i,u)}}}if(l===void 0&&(l=L(u.mapKey,!1)),m===void 0)switch(u.mapKind){case"scalar":m=L(u.scalar,!1);break;case"enum":m=u.enum.values[0].number;break;case"message":m=I(u.message,void 0,!1)}o.set(l,m)})(t,e.get(n),s)}}function fe(e,t,n,r){const s=n.delimitedEncoding,a=r??I(n.message,void 0,!1);return ln(a,e,t,s,s?n.number:e.uint32()),a}function P(e,t){switch(t){case c.STRING:return e.string();case c.BOOL:return e.bool();case c.DOUBLE:return e.double();case c.FLOAT:return e.float();case c.INT32:return e.int32();case c.INT64:return e.int64();case c.UINT64:return e.uint64();case c.FIXED64:return e.fixed64();case c.BYTES:return e.bytes();case c.FIXED32:return e.fixed32();case c.SFIXED32:return e.sfixed32();case c.SFIXED64:return e.sfixed64();case c.SINT64:return e.sint64();case c.UINT32:return e.uint32();case c.SINT32:return e.sint32()}}function xe(e,t){const n=un(ir,Qt(e));return n.messageType.forEach(Ce),n.dependency=[],tn(n,r=>{}).getFile(n.name)}const ur=X(xe("Chlnb29nbGUvcHJvdG9idWYvYW55LnByb3RvEg9nb29nbGUucHJvdG9idWYiJgoDQW55EhAKCHR5cGVfdXJsGAEgASgJEg0KBXZhbHVlGAIgASgMQnYKE2NvbS5nb29nbGUucHJvdG9idWZCCEFueVByb3RvUAFaLGdvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL2FueXBiogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),0),lr=3,_t={writeUnknownFields:!0};function cr(e,t,n){return ne(new Kt,function(r){return r?Object.assign(Object.assign({},_t),r):_t}(n),I(e,t)).finish()}function ne(e,t,n){var r;for(const s of n.sortedFields)if(n.isSet(s))mn(e,t,n,s);else if(s.presence==lr)throw new Error(`cannot encode ${s} to binary: required field not set`);if(t.writeUnknownFields)for(const{no:s,wireType:a,data:o}of(r=n.getUnknown())!==null&&r!==void 0?r:[])e.tag(s,a).raw(o);return e}function mn(e,t,n,r){var s;switch(r.fieldKind){case"scalar":case"enum":re(e,n.desc.typeName,r.name,(s=r.scalar)!==null&&s!==void 0?s:c.INT32,r.number,n.get(r));break;case"list":(function(a,o,i,u){var l;if(i.listKind=="message"){for(const d of u)wt(a,o,i,d);return}const m=(l=i.scalar)!==null&&l!==void 0?l:c.INT32;if(i.packed){if(!u.size)return;a.tag(i.number,N.LengthDelimited).fork();for(const d of u)dn(a,i.parent.typeName,i.name,m,d);return void a.join()}for(const d of u)re(a,i.parent.typeName,i.name,m,i.number,d)})(e,t,r,n.get(r));break;case"message":wt(e,t,r,n.get(r));break;case"map":for(const[a,o]of n.get(r))mr(e,t,r,a,o)}}function re(e,t,n,r,s,a){dn(e.tag(s,function(o){switch(o){case c.BYTES:case c.STRING:return N.LengthDelimited;case c.DOUBLE:case c.FIXED64:case c.SFIXED64:return N.Bit64;case c.FIXED32:case c.SFIXED32:case c.FLOAT:return N.Bit32;default:return N.Varint}}(r)),t,n,r,a)}function wt(e,t,n,r){n.delimitedEncoding?ne(e.tag(n.number,N.StartGroup),t,r).tag(n.number,N.EndGroup):ne(e.tag(n.number,N.LengthDelimited).fork(),t,r).join()}function mr(e,t,n,r,s){var a;switch(e.tag(n.number,N.LengthDelimited).fork(),re(e,n.parent.typeName,n.name,n.mapKey,1,r),n.mapKind){case"scalar":case"enum":re(e,n.parent.typeName,n.name,(a=n.scalar)!==null&&a!==void 0?a:c.INT32,2,s);break;case"message":ne(e.tag(2,N.LengthDelimited).fork(),t,s).join()}e.join()}function dn(e,t,n,r,s){try{switch(r){case c.STRING:e.string(s);break;case c.BOOL:e.bool(s);break;case c.DOUBLE:e.double(s);break;case c.FLOAT:e.float(s);break;case c.INT32:e.int32(s);break;case c.INT64:e.int64(s);break;case c.UINT64:e.uint64(s);break;case c.FIXED64:e.fixed64(s);break;case c.BYTES:e.bytes(s);break;case c.FIXED32:e.fixed32(s);break;case c.SFIXED32:e.sfixed32(s);break;case c.SFIXED64:e.sfixed64(s);break;case c.SINT64:e.sint64(s);break;case c.UINT32:e.uint32(s);break;case c.SINT32:e.sint32(s)}}catch(a){throw a instanceof Error?new Error(`cannot encode field ${t}.${n} to binary: ${a.message}`):a}}function dr(e,t){if(e.typeUrl==="")return;const n=t.kind=="message"?t:t.getMessage(Ot(e.typeUrl));return n&&function(r,s){return r.typeUrl!==""&&(typeof s=="string"?s:s.typeName)===Ot(r.typeUrl)}(e,n)?un(n,e.value):void 0}function Ot(e){const t=e.lastIndexOf("/"),n=t>=0?e.substring(t+1):e;if(!n.length)throw new Error(`invalid type url: ${e}`);return n}const Ge=xe("Chxnb29nbGUvcHJvdG9idWYvc3RydWN0LnByb3RvEg9nb29nbGUucHJvdG9idWYihAEKBlN0cnVjdBIzCgZmaWVsZHMYASADKAsyIy5nb29nbGUucHJvdG9idWYuU3RydWN0LkZpZWxkc0VudHJ5GkUKC0ZpZWxkc0VudHJ5EgsKA2tleRgBIAEoCRIlCgV2YWx1ZRgCIAEoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZToCOAEi6gEKBVZhbHVlEjAKCm51bGxfdmFsdWUYASABKA4yGi5nb29nbGUucHJvdG9idWYuTnVsbFZhbHVlSAASFgoMbnVtYmVyX3ZhbHVlGAIgASgBSAASFgoMc3RyaW5nX3ZhbHVlGAMgASgJSAASFAoKYm9vbF92YWx1ZRgEIAEoCEgAEi8KDHN0cnVjdF92YWx1ZRgFIAEoCzIXLmdvb2dsZS5wcm90b2J1Zi5TdHJ1Y3RIABIwCgpsaXN0X3ZhbHVlGAYgASgLMhouZ29vZ2xlLnByb3RvYnVmLkxpc3RWYWx1ZUgAQgYKBGtpbmQiMwoJTGlzdFZhbHVlEiYKBnZhbHVlcxgBIAMoCzIWLmdvb2dsZS5wcm90b2J1Zi5WYWx1ZSobCglOdWxsVmFsdWUSDgoKTlVMTF9WQUxVRRAAQn8KE2NvbS5nb29nbGUucHJvdG9idWZCC1N0cnVjdFByb3RvUAFaL2dvb2dsZS5nb2xhbmcub3JnL3Byb3RvYnVmL3R5cGVzL2tub3duL3N0cnVjdHBi+AEBogIDR1BCqgIeR29vZ2xlLlByb3RvYnVmLldlbGxLbm93blR5cGVzYgZwcm90bzM"),pr=X(Ge,0),pn=X(Ge,1),fr=X(Ge,2);var ve;function gr(e,t){fn(t,e);const n=function(o,i){if(o===void 0)return[];if(i.fieldKind==="enum"||i.fieldKind==="scalar"){for(let u=o.length-1;u>=0;--u)if(o[u].no==i.number)return[o[u]];return[]}return o.filter(u=>u.no===i.number)}(e.$unknown,t),[r,s,a]=oe(t);for(const o of n)cn(r,new Fe(o.data),s,o.wireType,{readUnknownFields:!0});return a()}function br(e,t,n){var r;fn(t,e);const s=((r=e.$unknown)!==null&&r!==void 0?r:[]).filter(l=>l.no!==t.number),[a,o]=oe(t,n),i=new Kt;mn(i,{writeUnknownFields:!0},a,o);const u=new Fe(i.finish());for(;u.pos<u.len;){const[l,m]=u.tag(),d=u.skip(m,l);s.push({no:l,wireType:m,data:d})}e.$unknown=s}function oe(e,t){const n=e.typeName,r=Object.assign(Object.assign({},e),{kind:"field",parent:e.extendee,localName:n}),s=Object.assign(Object.assign({},e.extendee),{fields:[r],members:[r],oneofs:[]}),a=_(s,t!==void 0?{[n]:t}:void 0);return[I(s,a),r,()=>{const o=a[n];if(o===void 0){const i=e.message;return j(i)?L(i.fields[0].scalar,i.fields[0].longAsString):_(i)}return o}]}function fn(e,t){if(e.extendee.typeName!=t.$typeName)throw new Error(`extension ${e.typeName} can only be applied to message ${e.extendee.typeName}`)}(function(e){e[e.NULL_VALUE=0]="NULL_VALUE"})(ve||(ve={}));const hr=3,yr=2,Rt={alwaysEmitImplicit:!1,enumAsInteger:!1,useProtoFieldName:!1};function Nr(e,t,n){return M(I(e,t),function(r){return r?Object.assign(Object.assign({},Rt),r):Rt}(n))}function M(e,t){var n;const r=function(a,o){if(a.desc.typeName.startsWith("google.protobuf.")){switch(a.desc.typeName){case"google.protobuf.Any":return function(u,l){if(u.typeUrl==="")return{};const{registry:m}=l;let d,p;if(m&&(d=dr(u,m),d&&(p=m.getMessage(d.$typeName))),!p||!d)throw new Error(`cannot encode message ${u.$typeName} to JSON: "${u.typeUrl}" is not in the type registry`);let f=M(I(p,d),l);return(p.typeName.startsWith("google.protobuf.")||f===null||Array.isArray(f)||typeof f!="object")&&(f={value:f}),f["@type"]=u.typeUrl,f}(a.message,o);case"google.protobuf.Timestamp":return function(u){const l=1e3*Number(u.seconds);if(l<Date.parse("0001-01-01T00:00:00Z")||l>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot encode message ${u.$typeName} to JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);if(u.nanos<0)throw new Error(`cannot encode message ${u.$typeName} to JSON: nanos must not be negative`);let m="Z";if(u.nanos>0){const d=(u.nanos+1e9).toString().substring(1);m=d.substring(3)==="000000"?"."+d.substring(0,3)+"Z":d.substring(6)==="000"?"."+d.substring(0,6)+"Z":"."+d+"Z"}return new Date(l).toISOString().replace(".000Z",m)}(a.message);case"google.protobuf.Duration":return function(u){if(Number(u.seconds)>315576e6||Number(u.seconds)<-315576e6)throw new Error(`cannot encode message ${u.$typeName} to JSON: value out of range`);let l=u.seconds.toString();if(u.nanos!==0){let m=Math.abs(u.nanos).toString();m="0".repeat(9-m.length)+m,m.substring(3)==="000000"?m=m.substring(0,3):m.substring(6)==="000"&&(m=m.substring(0,6)),l+="."+m,u.nanos<0&&Number(u.seconds)==0&&(l="-"+l)}return l+"s"}(a.message);case"google.protobuf.FieldMask":return(i=a.message).paths.map(u=>{if(u.match(/_[0-9]?_/g)||u.match(/[A-Z]/g))throw new Error(`cannot encode message ${i.$typeName} to JSON: lowerCamelCase of path name "`+u+'" is irreversible');return K(u)}).join(",");case"google.protobuf.Struct":return gn(a.message);case"google.protobuf.Value":return Ye(a.message);case"google.protobuf.ListValue":return bn(a.message);default:if(j(a.desc)){const u=a.desc.fields[0];return z(u,a.get(u))}return}var i}}(e,t);if(r!==void 0)return r;const s={};for(const a of e.sortedFields){if(!e.isSet(a)){if(a.presence==hr)throw new Error(`cannot encode ${a} to JSON: required field not set`);if(!t.alwaysEmitImplicit||a.presence!==yr)continue}const o=At(a,e.get(a),t);o!==void 0&&(s[Er(a,t)]=o)}if(t.registry){const a=new Set;for(const{no:o}of(n=e.getUnknown())!==null&&n!==void 0?n:[])if(!a.has(o)){a.add(o);const i=t.registry.getExtensionFor(e.desc,o);if(!i)continue;const u=gr(e.message,i),[l,m]=oe(i,u),d=At(m,l.get(m),t);d!==void 0&&(s[i.jsonName]=d)}}return s}function At(e,t,n){switch(e.fieldKind){case"scalar":return z(e,t);case"message":return M(t,n);case"enum":return ge(e.enum,t,n.enumAsInteger);case"list":return function(r,s){const a=r.field(),o=[];switch(a.listKind){case"scalar":for(const i of r)o.push(z(a,i));break;case"enum":for(const i of r)o.push(ge(a.enum,i,s.enumAsInteger));break;case"message":for(const i of r)o.push(M(i,s))}return s.alwaysEmitImplicit||o.length>0?o:void 0}(t,n);case"map":return function(r,s){const a=r.field(),o={};switch(a.mapKind){case"scalar":for(const[i,u]of r)o[i]=z(a,u);break;case"message":for(const[i,u]of r)o[i]=M(u,s);break;case"enum":for(const[i,u]of r)o[i]=ge(a.enum,u,s.enumAsInteger)}return s.alwaysEmitImplicit||r.size>0?o:void 0}(t,n)}}function ge(e,t,n){var r;if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: expected number, got ${E(t)}`);if(e.typeName=="google.protobuf.NullValue")return null;if(n)return t;const s=e.value[t];return(r=s==null?void 0:s.name)!==null&&r!==void 0?r:t}function z(e,t){var n,r,s,a,o,i;switch(e.scalar){case c.INT32:case c.SFIXED32:case c.SINT32:case c.FIXED32:case c.UINT32:if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: ${(n=k(e,t))===null||n===void 0?void 0:n.message}`);return t;case c.FLOAT:case c.DOUBLE:if(typeof t!="number")throw new Error(`cannot encode ${e} to JSON: ${(r=k(e,t))===null||r===void 0?void 0:r.message}`);return Number.isNaN(t)?"NaN":t===Number.POSITIVE_INFINITY?"Infinity":t===Number.NEGATIVE_INFINITY?"-Infinity":t;case c.STRING:if(typeof t!="string")throw new Error(`cannot encode ${e} to JSON: ${(s=k(e,t))===null||s===void 0?void 0:s.message}`);return t;case c.BOOL:if(typeof t!="boolean")throw new Error(`cannot encode ${e} to JSON: ${(a=k(e,t))===null||a===void 0?void 0:a.message}`);return t;case c.UINT64:case c.FIXED64:case c.INT64:case c.SFIXED64:case c.SINT64:if(typeof t!="bigint"&&typeof t!="string")throw new Error(`cannot encode ${e} to JSON: ${(o=k(e,t))===null||o===void 0?void 0:o.message}`);return t.toString();case c.BYTES:if(t instanceof Uint8Array)return function(u,l="std"){const m=en(l),d=l=="std";let p,f="",g=0,b=0;for(let h=0;h<u.length;h++)switch(p=u[h],g){case 0:f+=m[p>>2],b=(3&p)<<4,g=1;break;case 1:f+=m[b|p>>4],b=(15&p)<<2,g=2;break;case 2:f+=m[b|p>>6],f+=m[63&p],g=0}return g&&(f+=m[b],d&&(f+="=",g==1&&(f+="="))),f}(t);throw new Error(`cannot encode ${e} to JSON: ${(i=k(e,t))===null||i===void 0?void 0:i.message}`)}}function Er(e,t){return t.useProtoFieldName?e.name:e.jsonName}function gn(e){const t={};for(const[n,r]of Object.entries(e.fields))t[n]=Ye(r);return t}function Ye(e){switch(e.kind.case){case"nullValue":return null;case"numberValue":if(!Number.isFinite(e.kind.value))throw new Error(`${e.$typeName} cannot be NaN or Infinity`);return e.kind.value;case"boolValue":case"stringValue":return e.kind.value;case"structValue":return gn(e.kind.value);case"listValue":return bn(e.kind.value);default:throw new Error(`${e.$typeName} must have a value`)}}function bn(e){return e.values.map(Ye)}const Dt={ignoreUnknownFields:!1};function Tr(e,t,n){const r=I(e);try{C(r,t,function(a){return a?Object.assign(Object.assign({},Dt),a):Dt}(n))}catch(a){throw(s=a)instanceof Error&&$n.includes(s.name)&&"field"in s&&typeof s.field=="function"?new Error(`cannot decode ${a.field()} from JSON: ${a.message}`,{cause:a}):a}var s;return r.message}function C(e,t,n){var r;if(function(o,i,u){if(!o.desc.typeName.startsWith("google.protobuf."))return!1;switch(o.desc.typeName){case"google.protobuf.Any":return function(l,m,d){var p;if(m===null||Array.isArray(m)||typeof m!="object")throw new Error(`cannot decode message ${l.$typeName} from JSON: expected object but got ${E(m)}`);if(Object.keys(m).length==0)return;const f=m["@type"];if(typeof f!="string"||f=="")throw new Error(`cannot decode message ${l.$typeName} from JSON: "@type" is empty`);const g=f.includes("/")?f.substring(f.lastIndexOf("/")+1):f;if(!g.length)throw new Error(`cannot decode message ${l.$typeName} from JSON: "@type" is invalid`);const b=(p=d.registry)===null||p===void 0?void 0:p.getMessage(g);if(!b)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${f} is not in the type registry`);const h=I(b);if(g.startsWith("google.protobuf.")&&Object.prototype.hasOwnProperty.call(m,"value"))C(h,m.value,d);else{const v=Object.assign({},m);delete v["@type"],C(h,v,d)}(function(v,F,R){let In=!1;R||(R=_(ur),In=!0),R.value=cr(v,F),R.typeUrl=`type.googleapis.com/${F.$typeName}`})(h.desc,h.message,l)}(o.message,i,u),!0;case"google.protobuf.Timestamp":return function(l,m){if(typeof m!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${E(m)}`);const d=m.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:\.([0-9]{1,9}))?(?:Z|([+-][0-9][0-9]:[0-9][0-9]))$/);if(!d)throw new Error(`cannot decode message ${l.$typeName} from JSON: invalid RFC 3339 string`);const p=Date.parse(d[1]+"-"+d[2]+"-"+d[3]+"T"+d[4]+":"+d[5]+":"+d[6]+(d[8]?d[8]:"Z"));if(Number.isNaN(p))throw new Error(`cannot decode message ${l.$typeName} from JSON: invalid RFC 3339 string`);if(p<Date.parse("0001-01-01T00:00:00Z")||p>Date.parse("9999-12-31T23:59:59Z"))throw new Error(`cannot decode message ${l.$typeName} from JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive`);l.seconds=y.parse(p/1e3),l.nanos=0,d[7]&&(l.nanos=parseInt("1"+d[7]+"0".repeat(9-d[7].length))-1e9)}(o.message,i),!0;case"google.protobuf.Duration":return function(l,m){if(typeof m!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${E(m)}`);const d=m.match(/^(-?[0-9]+)(?:\.([0-9]+))?s/);if(d===null)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${E(m)}`);const p=Number(d[1]);if(p>315576e6||p<-315576e6)throw new Error(`cannot decode message ${l.$typeName} from JSON: ${E(m)}`);if(l.seconds=y.parse(p),typeof d[2]!="string")return;const f=d[2]+"0".repeat(9-d[2].length);l.nanos=parseInt(f),(p<0||Object.is(p,-0))&&(l.nanos=-l.nanos)}(o.message,i),!0;case"google.protobuf.FieldMask":return function(l,m){if(typeof m!="string")throw new Error(`cannot decode message ${l.$typeName} from JSON: ${E(m)}`);if(m==="")return;function d(p){if(p.includes("_"))throw new Error(`cannot decode message ${l.$typeName} from JSON: path names must be lowerCamelCase`);const f=p.replace(/[A-Z]/g,g=>"_"+g.toLowerCase());return f[0]==="_"?f.substring(1):f}l.paths=m.split(",").map(d)}(o.message,i),!0;case"google.protobuf.Struct":return yn(o.message,i),!0;case"google.protobuf.Value":return Ve(o.message,i),!0;case"google.protobuf.ListValue":return Nn(o.message,i),!0;default:if(j(o.desc)){const l=o.desc.fields[0];return i===null?o.clear(l):o.set(l,ee(l,i,!0)),!0}return!1}}(e,t,n))return;if(t==null||Array.isArray(t)||typeof t!="object")throw new Error(`cannot decode ${e.desc} from JSON: ${E(t)}`);const s=new Map,a=new Map;for(const o of e.desc.fields)a.set(o.name,o).set(o.jsonName,o);for(const[o,i]of Object.entries(t)){const u=a.get(o);if(u){if(u.oneof){if(i===null&&u.fieldKind=="scalar")continue;const l=s.get(u.oneof);if(l!==void 0)throw new T(u.oneof,`oneof set multiple times by ${l.name} and ${u.name}`);s.set(u.oneof,u)}Ft(e,u,i,n)}else{let l;if(o.startsWith("[")&&o.endsWith("]")&&(l=(r=n.registry)===null||r===void 0?void 0:r.getExtension(o.substring(1,o.length-1)))&&l.extendee.typeName===e.desc.typeName){const[m,d,p]=oe(l);Ft(m,d,i,n),br(e.message,l,p())}if(!l&&!n.ignoreUnknownFields)throw new Error(`cannot decode ${e.desc} from JSON: key "${o}" is unknown`)}}}function Ft(e,t,n,r){switch(t.fieldKind){case"scalar":(function(s,a,o){const i=ee(a,o,!1);i===ae?s.clear(a):s.set(a,i)})(e,t,n);break;case"enum":(function(s,a,o,i){const u=be(a.enum,o,i.ignoreUnknownFields,!1);u===ae?s.clear(a):u!==Q&&s.set(a,u)})(e,t,n,r);break;case"message":(function(s,a,o,i){if(o===null&&a.message.typeName!="google.protobuf.Value")return void s.clear(a);const u=s.isSet(a)?s.get(a):I(a.message);C(u,o,i),s.set(a,u)})(e,t,n,r);break;case"list":(function(s,a,o){if(a===null)return;const i=s.field();if(!Array.isArray(a))throw new T(i,"expected Array, got "+E(a));for(const u of a){if(u===null)throw new T(i,"list item must not be null");switch(i.listKind){case"message":const l=I(i.message);C(l,u,o),s.add(l);break;case"enum":const m=be(i.enum,u,o.ignoreUnknownFields,!0);m!==Q&&s.add(m);break;case"scalar":s.add(ee(i,u,!0))}}})(e.get(t),n,r);break;case"map":(function(s,a,o){if(a===null)return;const i=s.field();if(typeof a!="object"||Array.isArray(a))throw new T(i,"expected object, got "+E(a));for(const[u,l]of Object.entries(a)){if(l===null)throw new T(i,"map value must not be null");let m;switch(i.mapKind){case"message":const p=I(i.message);C(p,l,o),m=p;break;case"enum":if(m=be(i.enum,l,o.ignoreUnknownFields,!0),m===Q)return;break;case"scalar":m=ee(i,l,!0)}const d=vr(i.mapKey,u);s.set(d,m)}})(e.get(t),n,r)}}const Q=Symbol();function be(e,t,n,r){if(t===null)return e.typeName=="google.protobuf.NullValue"?0:r?e.values[0].number:ae;switch(typeof t){case"number":if(Number.isInteger(t))return t;break;case"string":const s=e.values.find(a=>a.name===t);if(s!==void 0)return s.number;if(n)return Q}throw new Error(`cannot decode ${e} from JSON: ${E(t)}`)}const ae=Symbol();function ee(e,t,n){if(t===null)return n?L(e.scalar,!1):ae;switch(e.scalar){case c.DOUBLE:case c.FLOAT:if(t==="NaN")return NaN;if(t==="Infinity")return Number.POSITIVE_INFINITY;if(t==="-Infinity")return Number.NEGATIVE_INFINITY;if(typeof t=="number"){if(Number.isNaN(t))throw new T(e,"unexpected NaN number");if(!Number.isFinite(t))throw new T(e,"unexpected infinite number");break}if(typeof t=="string"){if(t===""||t.trim().length!==t.length)break;const r=Number(t);if(!Number.isFinite(r))break;return r}break;case c.INT32:case c.FIXED32:case c.SFIXED32:case c.SINT32:case c.UINT32:return hn(t);case c.BYTES:if(typeof t=="string"){if(t==="")return new Uint8Array(0);try{return Qt(t)}catch(r){const s=r instanceof Error?r.message:String(r);throw new T(e,s)}}}return t}function vr(e,t){switch(e){case c.BOOL:switch(t){case"true":return!0;case"false":return!1}return t;case c.INT32:case c.FIXED32:case c.UINT32:case c.SFIXED32:case c.SINT32:return hn(t);default:return t}}function hn(e){if(typeof e=="string"){if(e===""||e.trim().length!==e.length)return e;const t=Number(e);return Number.isNaN(t)?e:t}return e}function yn(e,t){if(typeof t!="object"||t==null||Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${E(t)}`);for(const[n,r]of Object.entries(t)){const s=_(pn);Ve(s,r),e.fields[n]=s}}function Ve(e,t){switch(typeof t){case"number":e.kind={case:"numberValue",value:t};break;case"string":e.kind={case:"stringValue",value:t};break;case"boolean":e.kind={case:"boolValue",value:t};break;case"object":if(t===null)e.kind={case:"nullValue",value:ve.NULL_VALUE};else if(Array.isArray(t)){const n=_(fr);Nn(n,t),e.kind={case:"listValue",value:n}}else{const n=_(pr);yn(n,t),e.kind={case:"structValue",value:n}}break;default:throw new Error(`cannot decode message ${e.$typeName} from JSON ${E(t)}`)}return e}function Nn(e,t){if(!Array.isArray(t))throw new Error(`cannot decode message ${e.$typeName} from JSON ${E(t)}`);for(const n of t){const r=_(pn);Ve(r,n),e.values.push(r)}}class Ir{constructor(t){A(this,"pendingRequests",new Map);A(this,"cleanup");A(this,"serviceRegistries",new Set);this.target=t,this.cleanup=this.target.onReceiveMessage(this.handleMessage.bind(this))}addServiceRegistry(t){this.serviceRegistries.add(t)}removeServiceRegistry(t){this.serviceRegistries.delete(t)}handleMessage(t){if(!t||typeof t!="object"||!this.isGrpcMessageLike(t))return;const n=t;n.type==="com.augmentcode.client.rpc.request"?this.handleRequest(n):n.type==="com.augmentcode.client.rpc.response"&&this.handleResponse(n)}isGrpcMessageLike(t){return"type"in t&&t.type==="com.augmentcode.client.rpc.request"||t.type==="com.augmentcode.client.rpc.response"}async handleRequest(t){for(const n of this.serviceRegistries)if(n.canHandle(t))try{return void await n.handleRequest(t,r=>{this.target.sendMessage(r)})}catch(r){Array.from(this.serviceRegistries).indexOf(n)===this.serviceRegistries.size-1&&this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:r instanceof Error?r.message:String(r)})}this.target.sendMessage({type:"com.augmentcode.client.rpc.response",id:t.id,methodLocalName:t.methodLocalName,serviceTypeName:t.serviceTypeName,data:"",error:`No handlers registered for service: ${t.serviceTypeName}`})}handleResponse(t){const n=this.pendingRequests.get(t.id);if(n)if(this.pendingRequests.delete(t.id),clearTimeout(n.timeout),t.error)n.reject(new Error(`gRPC server error for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${t.error}`));else try{if(!t.data&&t.data!==null&&t.data!=="")throw new Error(`gRPC response missing data field for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id})`);n.resolve(t)}catch(r){const s=r instanceof Error?r.message:String(r);n.reject(new Error(`Failed to process gRPC response for ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}): ${s}`))}}sendRequest(t,n){return new Promise((r,s)=>{let a;n&&(a=setTimeout(()=>{this.pendingRequests.delete(t.id),s(new Error(`gRPC request timed out after ${n}ms: ${t.serviceTypeName}.${t.methodLocalName} (ID: ${t.id}). This may indicate that the server is not responding or the message routing is broken.`))},n)),this.pendingRequests.set(t.id,{resolve:r,reject:s,timeout:a}),this.target.sendMessage(t)})}async unary(t,n,r,s,a,o){const i=crypto.randomUUID(),u=t.localName,l=t.parent.typeName;if(!l)throw new Error("Service name is required for unary calls");const m=a?Nr(t.input,_(t.input,a)):{};if(n!=null&&n.aborted)throw new Error(`gRPC request aborted before sending: ${l}.${u} (ID: ${i})`);let d;n&&(d=()=>{const f=this.pendingRequests.get(i);f&&(this.pendingRequests.delete(i),clearTimeout(f.timeout),f.reject(new Error(`gRPC request aborted during execution: ${l}.${u} (ID: ${i})`)))},n.addEventListener("abort",d));const p=await this.sendRequest({type:"com.augmentcode.client.rpc.request",id:i,methodLocalName:u,serviceTypeName:l,data:m,timeout:r},r);return n&&d&&n.removeEventListener("abort",d),{stream:!1,method:t,service:t.parent,header:new Headers(s),message:Tr(t.output,p.data),trailer:new Headers}}stream(t,n,r,s,a,o){throw new Error("Streaming is not supported by this transport")}dispose(){this.cleanup();for(const{timeout:t}of this.pendingRequests.values())clearTimeout(t);this.pendingRequests.clear(),this.serviceRegistries.clear()}}A(Ir,"PROTOCOL_NAME","com.augmentcode.client.rpc");async function Br(e){const t=await crypto.subtle.digest("SHA-256",e);return Array.from(new Uint8Array(t)).map(n=>n.toString(16).padStart(2,"0")).join("")}var Sr=(e=>(e.chat="chat",e))(Sr||{}),_r=(e=>(e.chatMentionFolder="chat-mention-folder",e.chatMentionFile="chat-mention-file",e.chatMentionExternalSource="chat-mention-external-source",e.chatClearContext="chat-clear-context",e.chatRestoreDefaultContext="chat-restore-default-context",e.chatUseActionFind="chat-use-action-find",e.chatUseActionExplain="chat-use-action-explain",e.chatUseActionFix="chat-use-action-autofix",e.chatUseActionWriteTest="chat-use-action-write-test",e.chatNewConversation="chat-new-conversation",e.chatNewAutofixConversation="chat-new-autofix-conversation",e.chatEditConversationName="chat-edit-conversation-name",e.chatFailedSmartPasteResolveFile="chat-failed-smart-paste-resolve-file",e.chatPrecomputeSmartPaste="chat-precompute-smart-paste",e.chatSmartPaste="chat-smart-paste",e.chatCodeblockCopy="chat-codeblock-copy",e.chatCodeblockCreate="chat-codeblock-create",e.chatCodeblockGoToFile="chat-codeblock-go-to-file",e.chatCodespanGoToFile="chat-codespan-go-to-file",e.chatCodespanGoToSymbol="chat-codespan-go-to-symbol",e.chatMermaidblockInitialize="chat-mermaidblock-initialize",e.chatMermaidblockToggle="chat-mermaidblock-toggle",e.chatMermaidblockInteract="chat-mermaidblock-interact",e.chatMermaidBlockError="chat-mermaidblock-error",e.chatUseSuggestedQuestion="chat-use-suggested-question",e.chatDisplaySuggestedQuestions="chat-display-suggested-questions",e.setWorkspaceGuidelines="chat-set-workspace-guidelines",e.clearWorkspaceGuidelines="chat-clear-workspace-guidelines",e.setUserGuidelines="chat-set-user-guidelines",e.clearUserGuidelines="chat-clear-user-guidelines",e))(_r||{});function jr(e){return e.replace(/^data:.*?;base64,/,"")}async function Xr(e){return new Promise((t,n)=>{const r=new FileReader;r.onload=s=>{var a;return t((a=s.target)==null?void 0:a.result)},r.onerror=n,r.readAsDataURL(e)})}async function Wr(e){return e.length<1e4?Promise.resolve(function(t){const n=atob(t);return Uint8Array.from(n,r=>r.codePointAt(0)||0)}(e)):new Promise((t,n)=>{const r=new Worker(URL.createObjectURL(new Blob([`
            self.onmessage = function(e) {
              try {
                const base64 = e.data;
                const binString = atob(base64);
                const bytes = new Uint8Array(binString.length);
                for (let i = 0; i < binString.length; i++) {
                  bytes[i] = binString.charCodeAt(i);
                }
                self.postMessage(bytes, [bytes.buffer]);
              } catch (error) {
                self.postMessage({ error: error.message });
              }
            };
            `],{type:"application/javascript"})));r.onmessage=function(s){s.data.error?n(new Error(s.data.error)):t(s.data),r.terminate()},r.onerror=function(s){n(s.error),r.terminate()},r.postMessage(e)})}function wr(e,t,...n){if(n.length>0)throw new Error;return e.services[t]}const Jr=wr(xe("Ci5jbGllbnRzL3NpZGVjYXIvbGlicy9wcm90b3MvdGVzdF9zZXJ2aWNlLnByb3RvEgR0ZXN0IhoKC1Rlc3RSZXF1ZXN0EgsKA2ZvbxgBIAEoCSIeCgxUZXN0UmVzcG9uc2USDgoGcmVzdWx0GAEgASgJMngKC1Rlc3RTZXJ2aWNlEjMKClRlc3RNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2USNAoLRXJyb3JNZXRob2QSES50ZXN0LlRlc3RSZXF1ZXN0GhIudGVzdC5UZXN0UmVzcG9uc2ViBnByb3RvMw"),0);var Or=(e=>(e.getEditListRequest="agent-get-edit-list-request",e.getEditListResponse="agent-get-edit-list-response",e.getEditChangesByRequestIdRequest="agent-get-edit-changes-by-request-id-request",e.getEditChangesByRequestIdResponse="agent-get-edit-changes-by-request-id-response",e.setCurrentConversation="agent-set-current-conversation",e.migrateConversationId="agent-migrate-conversation-id",e.revertToTimestamp="revert-to-timestamp",e.chatAgentEditAcceptAll="chat-agent-edit-accept-all",e.reportAgentSessionEvent="report-agent-session-event",e.reportAgentRequestEvent="report-agent-request-event",e.chatReviewAgentFile="chat-review-agent-file",e.getAgentEditContentsByRequestId="get-agent-edit-contents-by-request-id",e.getAgentEditContentsByRequestIdResponse="get-agent-edit-contents-by-request-id-response",e.checkHasEverUsedAgent="check-has-ever-used-agent",e.checkHasEverUsedAgentResponse="check-has-ever-used-agent-response",e.setHasEverUsedAgent="set-has-ever-used-agent",e.checkHasEverUsedRemoteAgent="check-has-ever-used-remote-agent",e.checkHasEverUsedRemoteAgentResponse="check-has-ever-used-remote-agent-response",e.setHasEverUsedRemoteAgent="set-has-ever-used-remote-agent",e))(Or||{}),Rr=(e=>(e.closeAllToolProcesses="close-all-tool-processes",e.getToolIdentifierRequest="get-tool-identifier-request",e.getToolIdentifierResponse="get-tool-identifier-response",e))(Rr||{}),Ar=(e=>(e.getHydratedTaskRequest="get-hydrated-task-request",e.getHydratedTaskResponse="get-hydrated-task-response",e.setCurrentRootTaskUuid="set-current-root-task-uuid",e.createTaskRequest="create-task-request",e.createTaskResponse="create-task-response",e.updateTaskRequest="update-task-request",e.updateTaskResponse="update-task-response",e.updateHydratedTaskRequest="update-hydrated-task-request",e.updateHydratedTaskResponse="update-hydrated-task-response",e))(Ar||{});function Zr(e){return e.rootPath+"/"+e.relPath}var Dr=(e=>(e.longRunning="longRunning",e.running="running",e.done="done",e))(Dr||{}),Fr=(e=>(e.initializing="initializing",e.enabled="enabled",e.disabled="disabled",e.partial="partial",e))(Fr||{});class U{static hasFrontmatter(t){return this.frontmatterRegex.test(t)}static extractFrontmatter(t){const n=t.match(this.frontmatterRegex);return n&&n[1]?n[1]:null}static extractContent(t){return t.replace(this.frontmatterRegex,"")}static parseBoolean(t,n,r=!0){const s=this.extractFrontmatter(t);if(s){const a=new RegExp(`${n}\\s*:\\s*(true|false)`,"i"),o=s.match(a);if(o&&o[1])return o[1].toLowerCase()==="true"}return r}static parseString(t,n,r=""){const s=this.extractFrontmatter(t);if(s){const a=new RegExp(`${n}\\s*:\\s*["']?([^"'
]*)["']?`,"i"),o=s.match(a);if(o&&o[1])return o[1].trim()}return r}static updateFrontmatter(t,n,r){const s=t.match(this.frontmatterRegex),a=typeof r!="string"||/^(true|false)$/.test(r.toLowerCase())?String(r):`"${r}"`;if(s){const o=s[1],i=new RegExp(`(${n}\\s*:\\s*)([^\\n]*)`,"i");if(o.match(i)){const u=o.replace(i,`$1${a}`);return t.replace(this.frontmatterRegex,`---
${u}---
`)}{const u=`${o.endsWith(`
`)?o:o+`
`}${n}: ${a}
`;return t.replace(this.frontmatterRegex,`---
${u}---
`)}}return`---
${n}: ${a}
---

${t}`}static createFrontmatter(t,n){let r=t;this.hasFrontmatter(r)&&(r=this.extractContent(r));for(const[s,a]of Object.entries(n))r=this.updateFrontmatter(r,s,a);return r}}A(U,"frontmatterRegex",/^---\s*\n([\s\S]*?)\n---\s*\n/);class kr{static parseRuleFile(t,n){const r=U.parseBoolean(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,!1),s=U.extractContent(t);return{type:r?ie.ALWAYS_ATTACHED:ie.MANUAL,path:n,content:s}}static formatRuleFileForMarkdown(t){return U.updateFrontmatter(t.content,this.ALWAYS_APPLY_FRONTMATTER_KEY,t.type===ie.ALWAYS_ATTACHED)}static getAlwaysApplyFrontmatterKey(t){return U.parseBoolean(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,!1)}static extractContent(t){return U.extractContent(t)}static updateAlwaysApplyFrontmatterKey(t,n){return U.updateFrontmatter(t,this.ALWAYS_APPLY_FRONTMATTER_KEY,n)}}A(kr,"ALWAYS_APPLY_FRONTMATTER_KEY","alwaysApply");const qr=".augment",Hr="rules",zr=".augment-guidelines";var Ur=(e=>(e[e.unspecified=0]="unspecified",e[e.userGuidelines=1]="userGuidelines",e[e.augmentGuidelines=2]="augmentGuidelines",e[e.rules=3]="rules",e))(Ur||{}),Lr=(e=>(e[e.unspecified=0]="unspecified",e[e.manuallyCreated=1]="manuallyCreated",e[e.auto=2]="auto",e[e.selectedDirectory=3]="selectedDirectory",e[e.selectedFile=4]="selectedFile",e))(Lr||{});function $r(e){return e===void 0?{num_lines:-1,num_chars:-1}:{num_lines:e.split(`
`).length,num_chars:e.length}}class En{constructor(){A(this,"tracingData",{flags:{},nums:{},string_stats:{},request_ids:{}})}setFlag(t,n=!0){this.tracingData.flags[t]={value:n,timestamp:new Date().toISOString()}}getFlag(t){const n=this.tracingData.flags[t];return n==null?void 0:n.value}setNum(t,n){this.tracingData.nums[t]={value:n,timestamp:new Date().toISOString()}}getNum(t){const n=this.tracingData.nums[t];return n==null?void 0:n.value}setStringStats(t,n){this.tracingData.string_stats[t]={value:$r(n),timestamp:new Date().toISOString()}}setRequestId(t,n){this.tracingData.request_ids[t]={value:n,timestamp:new Date().toISOString()}}}var Pr=(e=>(e[e.unspecified=0]="unspecified",e[e.classify_and_distill=1]="classify_and_distill",e[e.orientation=2]="orientation",e))(Pr||{}),Cr=(e=>(e.start="start",e.end="end",e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noMemoryData="noMemoryData",e.agenticTurnHasRememberToolCall="agenticTurnHasRememberToolCall",e.emptyMemory="emptyMemory",e.removeUserExchangeMemoryFailed="removeUserExchangeMemoryFailed",e))(Cr||{});class Tn extends En{constructor(){super()}static create(){return new Tn}}var xr=(e=>(e.openedAgentConversation="opened-agent-conversation",e.revertCheckpoint="revert-checkpoint",e.agentInterruption="agent-interruption",e.sentUserMessage="sent-user-message",e.rememberToolCall="remember-tool-call",e.openedMemoriesFile="opened-memories-file",e.initialOrientation="initial-orientation",e.classifyAndDistill="classify-and-distill",e.flushMemories="flush-memories",e.vsCodeTerminalShellIntegrationNotAvailable="vs-code-terminal-shell-integration-not-available",e.vsCodeTerminalReadingApproximateOutput="vs-code-terminal-reading-approximate-output",e.vsCodeTerminalTimedOutWaitingForNoopCommand="vs-code-terminal-timed-out-waiting-for-noop-command",e.vsCodeTerminalFailedToUseShellIntegration="vs-code-terminal-failed-to-use-shell-integration",e.vsCodeTerminalLastCommandIsSameAsCurrent="vs-code-terminal-last-command-is-same-as-current",e.vsCodeTerminalPollingDeterminedProcessIsDone="vs-code-terminal-polling-determined-process-is-done",e.vsCodeTerminalFailedToReadOutput="vs-code-terminal-failed-to-read-output",e.vsCodeTerminalBuggyOutput="vs-code-terminal-buggy-output",e.vsCodeTerminalBuggyExecutionEvents="vs-code-terminal-buggy-execution-events",e.vsCodeTerminalUnsupportedVSCodeShell="vs-code-terminal-unsupported-vscode-shell",e.vsCodeTerminalFailedToFindGitBash="vs-code-terminal-failed-to-find-git-bash",e.vsCodeTerminalFailedToFindPowerShell="vs-code-terminal-failed-to-find-powershell",e.vsCodeTerminalNoSupportedShellsFound="vs-code-terminal-no-supported-shells-found",e.vsCodeTerminalSettingsChanged="vs-code-terminal-settings-changed",e.vsCodeTerminalWaitTimeout="vs-code-terminal-wait-timeout",e.vsCodeTerminalErrorLoadingSettings="vs-code-terminal-error-loading-settings",e.vsCodeTerminalErrorCheckingForShellUpdates="vs-code-terminal-error-checking-for-shell-updates",e.vsCodeTerminalErrorCleaningUpTempDir="vs-code-terminal-error-cleaning-up-temp-dir",e.vsCodeTerminalErrorInitializingShells="vs-code-terminal-error-initializing-shells",e.vsCodeTerminalErrorCheckingShellCapability="vs-code-terminal-error-checking-shell-capability",e.vsCodeTerminalErrorCreatingZshEnvironment="vs-code-terminal-error-creating-zsh-environment",e.vsCodeTerminalMissedStartEvent="vs-code-terminal-missed-start-event",e.vsCodeTerminalReadStreamTimeoutWhenProcessIsComplete="vs-code-terminal-read-stream-timeout-when-process-is-complete",e.chatHistoryTruncated="chat-history-truncated",e.enhancedPrompt="enhanced-prompt",e.memoriesMove="memories-move",e.rulesImported="rules-imported",e))(xr||{}),Gr=(e=>(e.sentUserMessage="sent-user-message",e))(Gr||{}),Yr=(e=>(e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.start="start",e.end="end",e.noPendingUserMessage="noPendingUserMessage",e.startSendSilentExchange="startSendSilentExchange",e.sendSilentExchangeRequestId="sendSilentExchangeRequestId",e.sendSilentExchangeResponseStats="sendSilentExchangeResponseStats",e.noRequestId="noRequestId",e.conversationChanged="conversationChanged",e.explanationStats="explanationStats",e.contentStats="contentStats",e.invalidResponse="invalidResponse",e.worthRemembering="worthRemembering",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noLastUserExchangeRequestId="noLastUserExchangeRequestId",e))(Yr||{});class vn extends En{constructor(){super()}static create(){return new vn}}var Vr=(e=>(e.remoteAgentSetup="remote-agent-setup",e.setupScript="setup-script",e.sshInteraction="ssh-interaction",e.notificationBell="notification-bell",e.diffPanel="diff-panel",e.setupPageOpened="setup-page-opened",e.githubAPIFailure="github-api-failure",e.remoteAgentCreated="remote-agent-created",e.changesApplied="changes-applied",e.createdPR="created-pr",e))(Vr||{});export{xr as A,_r as C,Tn as F,Ur as M,kr as R,Ir as S,Ar as T,Sr as W,qr as a,Hr as b,_ as c,Or as d,Rr as e,un as f,jr as g,Wr as h,Jr as i,Vr as j,Gr as k,Dr as l,Fr as m,Lr as n,zr as o,Zr as p,vn as q,Xr as r,Br as s,Yr as t,Cr as u,Pr as v};
