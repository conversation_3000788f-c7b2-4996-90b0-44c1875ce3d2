var e=(T=>(T[T.agentUnspecified=0]="agentUnspecified",T[T.agentPending=5]="agentPending",T[T.agentStarting=1]="agentStarting",T[T.agentRunning=2]="agentRunning",T[T.agentIdle=3]="agentIdle",T[T.agentFailed=4]="agentFailed",T))(e||{}),_=(T=>(T[T.workspaceUnspecified=0]="workspaceUnspecified",T[T.workspaceRunning=1]="workspaceRunning",T[T.workspacePausing=2]="workspacePausing",T[T.workspacePaused=3]="workspacePaused",T[T.workspaceResuming=4]="workspaceResuming",T))(_||{}),n=(T=>(T[T.AGENT_HISTORY_UPDATE_TYPE_UNSPECIFIED=0]="AGENT_HISTORY_UPDATE_TYPE_UNSPECIFIED",T[T.AGENT_HISTORY_EXCHANGE=1]="AGENT_HISTORY_EXCHANGE",T[T.AGENT_HISTORY_EXCHANGE_UPDATE=2]="AGENT_HISTORY_EXCHANGE_UPDATE",T[T.AGENT_HISTORY_AGENT_STATUS=3]="AGENT_HISTORY_AGENT_STATUS",T))(n||{}),A=(T=>(T[T.added=0]="added",T[T.deleted=1]="deleted",T[T.modified=2]="modified",T[T.renamed=3]="renamed",T))(A||{}),a=(T=>(T[T.unknown=0]="unknown",T[T.running=1]="running",T[T.success=2]="success",T[T.failure=3]="failure",T[T.skipped=4]="skipped",T))(a||{}),N=(T=>(T[T.AGENT_LIST_UPDATE_TYPE_UNSPECIFIED=0]="AGENT_LIST_UPDATE_TYPE_UNSPECIFIED",T[T.AGENT_LIST_AGENT_ADDED=1]="AGENT_LIST_AGENT_ADDED",T[T.AGENT_LIST_AGENT_UPDATED=2]="AGENT_LIST_AGENT_UPDATED",T[T.AGENT_LIST_AGENT_DELETED=3]="AGENT_LIST_AGENT_DELETED",T[T.AGENT_LIST_ALL_AGENTS=4]="AGENT_LIST_ALL_AGENTS",T))(N||{});export{n as A,A as F,_ as R,e as a,a as b,N as c};
