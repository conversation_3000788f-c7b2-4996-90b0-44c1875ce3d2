const f={},p=function(h,c,i){let a=Promise.resolve();if(c&&c.length>0){const n=document.getElementsByTagName("link"),e=document.querySelector("meta[property=csp-nonce]"),u=(e==null?void 0:e.nonce)||(e==null?void 0:e.getAttribute("nonce"));a=Promise.allSettled(c.map(t=>{if(t=function(o,l){return new URL(o,l).href}(t,i),t in f)return;f[t]=!0;const s=t.endsWith(".css"),m=s?'[rel="stylesheet"]':"";if(i)for(let o=n.length-1;o>=0;o--){const l=n[o];if(l.href===t&&(!s||l.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${t}"]${m}`))return;const r=document.createElement("link");return r.rel=s?"stylesheet":"modulepreload",s||(r.as="script"),r.crossOrigin="",r.href=t,u&&r.setAttribute("nonce",u),document.head.appendChild(r),s?new Promise((o,l)=>{r.addEventListener("load",o),r.addEventListener("error",()=>l(new Error(`Unable to preload CSS for ${t}`)))}):void 0}))}function d(n){const e=new Event("vite:preloadError",{cancelable:!0});if(e.payload=n,window.dispatchEvent(e),!e.defaultPrevented)throw n}return a.then(n=>{for(const e of n||[])e.status==="rejected"&&d(e.reason);return h().catch(d)})};export{p as _};
