import{S as O,i as R,s as B,J as v2,e as A,q as j1,t as F,r as M1,u as $,h as S,Y as A3,a as x1,j as z2,_ as _1,V as z,I as H,c as o,W as D1,X as r1,f,$ as $1,a0 as A1,a1 as F1,g as v3,C as D,D as T,a8 as d1,a9 as T1,F as I,aa as w2,an as P2,a3 as Z2,ag as D2,b as m,L as Z,n as _,ac as N,aA as F3,ah as I1,ai as L2,U as T2,ad as k1,M as I2,a6 as V2,a5 as U2,A as G2,K as W2,am as y2,a4 as q2,O as K2}from"./SpinnerAugment-Cx9dt_ox.js";import{V as H2}from"./VSCodeCodicon-B3px2_jp.js";import{o as i3}from"./keypress-DD1aQVr0.js";import{I as J2,n as m1,A as k2,k as X2,a as b1,j as N1,l as Y2,m as S3}from"./IconFilePath-B4JAagx1.js";import{e as j3,W as Q2}from"./BaseButton-BqzdgpkK.js";import{S as r3,C as M3}from"./next-edit-types-904A5ehg.js";import{e as e0}from"./toggleHighContrast-CwIv4U26.js";import{r as n0}from"./resize-observer-DdAtcrRr.js";import{a as t0,b as E3}from"./index-8X-F_Twk.js";import"./index-BxQII05L.js";import{r as o0}from"./LanguageIcon-FVMxq7uD.js";import{a as G1,b as b2,_ as s1,c as O3,i as W1,d as i0}from"./isObjectLike-BWVRxMGM.js";const Q1={duration:300,easing:"ease-out"},r0=(n,e=Q1)=>{const t=n.querySelector("summary"),a=n.querySelector(".c-detail__content");if(!t||!a)return;e={...Q1,...e};const i=/^(tb|vertical)/.test(getComputedStyle(a).writingMode);let r=!1;const l=d=>{r=!0,d&&(n.open=!0),n.dispatchEvent(new CustomEvent(d?"open-start":"close-start",{detail:n}));const C=a[i?"clientWidth":"clientHeight"],g=a.animate({blockSize:d?["0",`${C}px`]:[`${C}px`,"0"]},e);g.oncancel=g.onfinish=g.onremove=()=>{n.dispatchEvent(new CustomEvent(d?"open-end":"close-end",{detail:n})),d||(n.open=!1),r=!1}},s=new MutationObserver(d=>{for(const C of d)if(C.type==="attributes"&&C.attributeName==="open"){if(r)return;n.open&&l(!0)}});s.observe(n,{attributes:!0});const c=d=>{d.preventDefault(),r||l(!n.open)},u=i3("Enter",c);return t.addEventListener("click",c),t.addEventListener("keypress",u),{destroy(){s.disconnect(),t.removeEventListener("click",c),t.removeEventListener("keypress",u)},update(d=Q1){e={...e,...d}}}},a0=n=>({}),R3=n=>({}),s0=n=>({}),B3=n=>({});function l0(n){let e,t,a,i,r,l;const s=n[11].summary,c=_1(s,n,n[10],R3),u=n[11].default,d=_1(u,n,n[10],null);let C=[n[7],{class:r="c-detail "+n[2]}],g={};for(let h=0;h<C.length;h+=1)g=x1(g,C[h]);return{c(){e=z("div"),t=z("div"),c&&c.c(),a=H(),i=z("div"),d&&d.c(),o(t,"class","c-detail__summary svelte-hwtbr4"),o(i,"class","c-detail__content svelte-hwtbr4"),D1(e,g),r1(e,"svelte-hwtbr4",!0)},m(h,p){A(h,e,p),f(e,t),c&&c.m(t,null),f(e,a),f(e,i),d&&d.m(i,null),l=!0},p(h,p){c&&c.p&&(!l||1024&p)&&$1(c,s,h,h[10],l?F1(s,h[10],p,a0):A1(h[10]),R3),d&&d.p&&(!l||1024&p)&&$1(d,u,h,h[10],l?F1(u,h[10],p,null):A1(h[10]),null),D1(e,g=v3(C,[128&p&&h[7],(!l||4&p&&r!==(r="c-detail "+h[2]))&&{class:r}])),r1(e,"svelte-hwtbr4",!0)},i(h){l||($(c,h),$(d,h),l=!0)},o(h){F(c,h),F(d,h),l=!1},d(h){h&&S(e),c&&c.d(h),d&&d.d(h)}}}function c0(n){let e,t,a,i,r,l,s,c,u,d,C;a=new H2({props:{icon:"chevron-down",class:"c-detail__chevron"}});const g=n[11].summary,h=_1(g,n,n[10],B3),p=n[11].default,L=_1(p,n,n[10],null);let w=[n[7],{style:s="--au-detail-duration: "+n[4].duration+"ms"},{class:c="c-detail "+n[2]}],b={};for(let y=0;y<w.length;y+=1)b=x1(b,w[y]);return{c(){e=z("details"),t=z("summary"),D(a.$$.fragment),i=H(),h&&h.c(),r=H(),l=z("div"),L&&L.c(),o(t,"class","c-detail__summary svelte-hwtbr4"),o(l,"class","c-detail__content svelte-hwtbr4"),D1(e,b),r1(e,"c-detail__rotate",n[3]),r1(e,"svelte-hwtbr4",!0)},m(y,v){A(y,e,v),f(e,t),T(a,t,null),f(t,i),h&&h.m(t,null),f(e,r),f(e,l),L&&L.m(l,null),e.open=n[0],u=!0,d||(C=[d1(e,"toggle",n[12]),T1(r0.call(null,e,n[4])),d1(e,"close-start",n[6]("close")),d1(e,"open-start",n[6]("open")),d1(e,"close-end",n[5]("close")),d1(e,"open-end",n[5]("open"))],d=!0)},p(y,v){h&&h.p&&(!u||1024&v)&&$1(h,g,y,y[10],u?F1(g,y[10],v,s0):A1(y[10]),B3),L&&L.p&&(!u||1024&v)&&$1(L,p,y,y[10],u?F1(p,y[10],v,null):A1(y[10]),null),D1(e,b=v3(w,[128&v&&y[7],{style:s},(!u||4&v&&c!==(c="c-detail "+y[2]))&&{class:c}])),1&v&&(e.open=y[0]),r1(e,"c-detail__rotate",y[3]),r1(e,"svelte-hwtbr4",!0)},i(y){u||($(a.$$.fragment,y),$(h,y),$(L,y),u=!0)},o(y){F(a.$$.fragment,y),F(h,y),F(L,y),u=!1},d(y){y&&S(e),I(a),h&&h.d(y),L&&L.d(y),d=!1,w2(C)}}}function u0(n){let e,t,a,i;const r=[c0,l0],l=[];function s(c,u){return c[1]?0:1}return e=s(n),t=l[e]=r[e](n),{c(){t.c(),a=v2()},m(c,u){l[e].m(c,u),A(c,a,u),i=!0},p(c,[u]){let d=e;e=s(c),e===d?l[e].p(c,u):(j1(),F(l[d],1,1,()=>{l[d]=null}),M1(),t=l[e],t?t.p(c,u):(t=l[e]=r[e](c),t.c()),$(t,1),t.m(a.parentNode,a))},i(c){i||($(t),i=!0)},o(c){F(t),i=!1},d(c){c&&S(a),l[e].d(c)}}}function d0(n,e,t){const a=["open","duration","expandable","onChangeOpen","class"];let i=A3(e,a),{$$slots:r={},$$scope:l}=e,{open:s=!0}=e,{duration:c=300}=e,{expandable:u=!0}=e,{onChangeOpen:d}=e,{class:C=""}=e;const g=typeof c=="number"?{duration:c}:c;let h=s;return n.$$set=p=>{e=x1(x1({},e),z2(p)),t(7,i=A3(e,a)),"open"in p&&t(0,s=p.open),"duration"in p&&t(8,c=p.duration),"expandable"in p&&t(1,u=p.expandable),"onChangeOpen"in p&&t(9,d=p.onChangeOpen),"class"in p&&t(2,C=p.class),"$$scope"in p&&t(10,l=p.$$scope)},n.$$.update=()=>{1&n.$$.dirty&&t(3,h=s)},[s,u,C,h,g,p=>()=>{t(3,h=p!=="close"),d==null||d(p==="open")},p=>()=>{t(3,h=p==="open")},i,c,d,l,r,function(){s=this.open,t(0,s)}]}class C0 extends O{constructor(e){super(),R(this,e,d0,u0,B,{open:0,duration:8,expandable:1,onChangeOpen:9,class:2})}}function h0(n){let e,t,a,i,r;return t=new J2({props:{filepath:n[0].relPath,onCodeAction:m1,value:n[3]}}),i=new k2({props:{actions:n[2],onAction:n[1],value:n[0]}}),{c(){e=z("div"),D(t.$$.fragment),a=H(),D(i.$$.fragment),o(e,"class","c-code-roll-item-header svelte-3zqetr")},m(l,s){A(l,e,s),T(t,e,null),f(e,a),T(i,e,null),r=!0},p(l,[s]){const c={};1&s&&(c.filepath=l[0].relPath),8&s&&(c.value=l[3]),t.$set(c);const u={};4&s&&(u.actions=l[2]),2&s&&(u.onAction=l[1]),1&s&&(u.value=l[0]),i.$set(u)},i(l){r||($(t.$$.fragment,l),$(i.$$.fragment,l),r=!0)},o(l){F(t.$$.fragment,l),F(i.$$.fragment,l),r=!1},d(l){l&&S(e),I(t),I(i)}}}function g0(n,e,t){let{filepath:a}=e,{onFileAction:i=m1}=e,{fileActions:r=[]}=e,{suggestions:l}=e;return n.$$set=s=>{"filepath"in s&&t(0,a=s.filepath),"onFileAction"in s&&t(1,i=s.onFileAction),"fileActions"in s&&t(2,r=s.fileActions),"suggestions"in s&&t(3,l=s.suggestions)},[a,i,r,l]}class f0 extends O{constructor(e){super(),R(this,e,g0,h0,B,{filepath:0,onFileAction:1,fileActions:2,suggestions:3})}}function N3(n,e,t,a){const i=n.split(""),r=e.toSorted(({lineRange:{start:l}},{lineRange:{start:s}})=>s-l);for(const l of r){const s=[l.result.charStart,t(l)],c=a(l);c&&s.push(...c.split("")),i.splice(...s)}return i.join("")}const z3=((n=0)=>()=>Date.now()+"-"+n++)(),p0=(n,e,t,a,i,r)=>{var u,d,C,g;if(!n||!t)return[];t=function(h,p){return N3(h,p,L=>L.result.suggestedCode.split("").length,L=>L.result.existingCode)}(t,e.filter(h=>h.state===r3.accepted));const l=function(h,p){return N3(h,p,L=>L.result.charEnd-L.result.charStart,L=>L.result.suggestedCode)}(t,e);(d=(u=n.getModel())==null?void 0:u.original)==null||d.dispose(),(g=(C=n.getModel())==null?void 0:C.modified)==null||g.dispose();const s=r.editor.createModel(t,a,r.Uri.parse("file://"+i+`#${z3()}`)),c=r.editor.createModel(l,a,r.Uri.parse("file://"+i+`#${z3()}`));return n.setModel({original:s,modified:c}),[s,c]};function m0(n){var e;return`${n.requestId}#${(e=n.result)==null?void 0:e.suggestionId}`}function P3(n){return n.map(m0).join(":")}function Z3(n){const e=n.toSorted((i,r)=>i.start-r.start),t=[];let a=e.shift();for(;e.length;){const i=e.shift();i.start<=a.end+1?a.end=Math.max(a.end,i.end):(t.push(a),a=i)}return t.push(a),t}function D3(n){return n.reduce((e,t)=>e+=t.end-t.start+1,0)}function T3(n){return n.reduce((e,t,a)=>a===0?e:e+=t.start-n[a-1].end-1,0)}function a3(n){var a;let e,t;if(n.modifiedEndLineNumber===0)t=n.originalEndLineNumber-n.originalStartLineNumber+1,e=n.originalStartLineNumber-1;else if((a=n.charChanges)!=null&&a.length){const i=Z3(n.charChanges.map(d=>({start:d.originalStartLineNumber,end:d.originalEndLineNumber}))),r=Z3(n.charChanges.map(d=>({start:d.modifiedStartLineNumber,end:d.modifiedEndLineNumber}))),l=D3(i),s=D3(r),c=T3(i),u=T3(r);t=l+s+Math.max(c,u),e=n.modifiedStartLineNumber-1}else{if(n.originalEndLineNumber!==0)throw new Error("Unexpected line change");t=n.modifiedEndLineNumber-n.modifiedStartLineNumber+1,e=n.modifiedStartLineNumber-1}return{lineCount:t,afterLineNumber:e}}function v0(...n){return n.reduce((e,t)=>({...w0(e,t),charChanges:[...e.charChanges??[],...t.charChanges??[]]}))}function w0(...n){return n.reduce((e,t)=>({originalStartLineNumber:Math.min(e.originalStartLineNumber,t.originalStartLineNumber),originalEndLineNumber:Math.max(e.originalEndLineNumber,t.originalEndLineNumber),modifiedStartLineNumber:Math.min(e.modifiedStartLineNumber,t.modifiedStartLineNumber),modifiedEndLineNumber:Math.max(e.modifiedEndLineNumber,t.modifiedEndLineNumber)}))}function L0(n,e){if(e.originalStartLineNumber===n.lineRange.start+1)return!0;const t=Math.min(e.originalStartLineNumber,e.modifiedStartLineNumber),a=Math.max(e.originalEndLineNumber,e.modifiedEndLineNumber);return t>=n.lineRange.start&&t<=n.lineRange.stop||a>=n.lineRange.start&&a<=n.lineRange.stop||t<=n.lineRange.start&&a>=n.lineRange.stop}function s3(n,e){const t=new Map,a=n.toSorted(({lineRange:{start:i}},{lineRange:{start:r}})=>i-r);e:for(const i of e.toSorted(({modifiedStartLineNumber:r,originalStartLineNumber:l},{modifiedStartLineNumber:s,originalStartLineNumber:c})=>r-s||l-c))for(const r of a)if(L0(r,i)){const l=t.get(r);t.set(r,l?v0(l,i):i);continue e}return t}function y0(n,e){let t,a,i=e;const r=()=>i.editor.getModifiedEditor(),l=()=>{const{afterLineNumber:s}=i,c=r();if(s===void 0)return void c.changeViewZones(d=>{t&&c&&a&&d.removeZone(a)});const u={...i,afterLineNumber:s,domNode:n,suppressMouseDown:!0};c==null||c.changeViewZones(d=>{t&&a&&d.removeZone(a),a=d.addZone(u),t=u})};return l(),{update:s=>{i=s,l()},destroy:()=>{const s=r();s.changeViewZones(c=>{if(t&&s&&a)try{c.removeZone(a)}catch(u){if(u instanceof Error){if(u.message.includes("Cannot read properties of null (reading 'removeChild')"))return}else console.warn(`Failed to remove view zone: ${u}`)}})}}}const x2=Symbol("code-roll-selection-context");function k0(){let n=D2(x2);return n||(n=b0({})),n}function b0(n){return P2(x2,Z2(n))}function I3(n){return n.activeSuggestion??n.selectedSuggestion??n.nextSuggestion}function T8(n){return n.activeSuggestion?"active":n.selectedSuggestion?"select":"next"}const e3={scrollContainer:document.documentElement,scrollIntoView:{behavior:"smooth",block:"nearest"},scrollDelayMS:100,useSmartBlockAlignment:!0,doScroll:!0};function x0(n,e=e3){let t,a=Object.assign({},e3,e);function i(r){let{doScroll:l,scrollIntoView:s,scrollDelayMS:c,useSmartBlockAlignment:u,scrollContainer:d}=Object.assign({},a,r);l&&(u&&n.getBoundingClientRect().height>((d==null?void 0:d.getBoundingClientRect().height)??1/0)&&(s=Object.assign({},s,{block:"start"})),t=setTimeout(()=>{const C=n.getBoundingClientRect();if(C.bottom===0&&C.top===0&&C.height===0&&C.width===0)return;const g=function(h,p,L){const w=h.getBoundingClientRect(),b=p.getBoundingClientRect();if(L==="nearest")if(w.bottom>b.bottom)L="end";else{if(!(w.top<b.top))return p.scrollTop;L="start"}return w.height>b.height||L==="start"?p.scrollTop+w.top:L==="end"?p.scrollTop+w.bottom-b.height:p.scrollTop+w.top-(b.height-w.height)/2}(n,d,(s==null?void 0:s.block)??e3.scrollIntoView.block);d.scrollTo({top:g,behavior:s==null?void 0:s.behavior})},c))}return i(a),{update:i,destroy(){clearTimeout(t)}}}function l3(n,e,t){return e.activeSuggestion?b1(e.activeSuggestion,n)?t?"select":"active":"none":e.selectedSuggestion?b1(e.selectedSuggestion,n)?t?"active":"select":"none":e.nextSuggestion&&b1(e.nextSuggestion,n)?"next":"none"}function V3(n){return["",`--augment-code-roll-selection-background: var(--augment-code-roll-item-background-${n})`,`--augment-code-roll-selection-color: var(--augment-code-roll-item-color-${n})`,"--augment-code-roll-selection-border: var(--augment-code-roll-selection-background)",""].join(";")}const X=Symbol.for("@ts-pattern/matcher"),_0=Symbol.for("@ts-pattern/isVariadic"),V1="@ts-pattern/anonymous-select-key",c3=n=>!!(n&&typeof n=="object"),P1=n=>n&&!!n[X],K=(n,e,t)=>{if(P1(n)){const a=n[X](),{matched:i,selections:r}=a.match(e);return i&&r&&Object.keys(r).forEach(l=>t(l,r[l])),i}if(c3(n)){if(!c3(e))return!1;if(Array.isArray(n)){if(!Array.isArray(e))return!1;let a=[],i=[],r=[];for(const l of n.keys()){const s=n[l];P1(s)&&s[_0]?r.push(s):r.length?i.push(s):a.push(s)}if(r.length){if(r.length>1)throw new Error("Pattern error: Using `...P.array(...)` several times in a single pattern is not allowed.");if(e.length<a.length+i.length)return!1;const l=e.slice(0,a.length),s=i.length===0?[]:e.slice(-i.length),c=e.slice(a.length,i.length===0?1/0:-i.length);return a.every((u,d)=>K(u,l[d],t))&&i.every((u,d)=>K(u,s[d],t))&&(r.length===0||K(r[0],c,t))}return n.length===e.length&&n.every((l,s)=>K(l,e[s],t))}return Reflect.ownKeys(n).every(a=>{const i=n[a];return(a in e||P1(r=i)&&r[X]().matcherType==="optional")&&K(i,e[a],t);var r})}return Object.is(e,n)},a1=n=>{var e,t,a;return c3(n)?P1(n)?(e=(t=(a=n[X]()).getSelectionKeys)==null?void 0:t.call(a))!=null?e:[]:Array.isArray(n)?S1(n,a1):S1(Object.values(n),a1):[]},S1=(n,e)=>n.reduce((t,a)=>t.concat(e(a)),[]);function U(n){return Object.assign(n,{optional:()=>$0(n),and:e=>E(n,e),or:e=>A0(n,e),select:e=>e===void 0?U3(n):U3(e,n)})}function $0(n){return U({[X]:()=>({match:e=>{let t={};const a=(i,r)=>{t[i]=r};return e===void 0?(a1(n).forEach(i=>a(i,void 0)),{matched:!0,selections:t}):{matched:K(n,e,a),selections:t}},getSelectionKeys:()=>a1(n),matcherType:"optional"})})}function E(...n){return U({[X]:()=>({match:e=>{let t={};const a=(i,r)=>{t[i]=r};return{matched:n.every(i=>K(i,e,a)),selections:t}},getSelectionKeys:()=>S1(n,a1),matcherType:"and"})})}function A0(...n){return U({[X]:()=>({match:e=>{let t={};const a=(i,r)=>{t[i]=r};return S1(n,a1).forEach(i=>a(i,void 0)),{matched:n.some(i=>K(i,e,a)),selections:t}},getSelectionKeys:()=>S1(n,a1),matcherType:"or"})})}function x(n){return{[X]:()=>({match:e=>({matched:!!n(e)})})}}function U3(...n){const e=typeof n[0]=="string"?n[0]:void 0,t=n.length===2?n[1]:typeof n[0]=="string"?void 0:n[0];return U({[X]:()=>({match:a=>{let i={[e??V1]:a};return{matched:t===void 0||K(t,a,(r,l)=>{i[r]=l}),selections:i}},getSelectionKeys:()=>[e??V1].concat(t===void 0?[]:a1(t))})})}function W(n){return typeof n=="number"}function n1(n){return typeof n=="string"}function t1(n){return typeof n=="bigint"}U(x(function(n){return!0}));const o1=n=>Object.assign(U(n),{startsWith:e=>{return o1(E(n,(t=e,x(a=>n1(a)&&a.startsWith(t)))));var t},endsWith:e=>{return o1(E(n,(t=e,x(a=>n1(a)&&a.endsWith(t)))));var t},minLength:e=>o1(E(n,(t=>x(a=>n1(a)&&a.length>=t))(e))),length:e=>o1(E(n,(t=>x(a=>n1(a)&&a.length===t))(e))),maxLength:e=>o1(E(n,(t=>x(a=>n1(a)&&a.length<=t))(e))),includes:e=>{return o1(E(n,(t=e,x(a=>n1(a)&&a.includes(t)))));var t},regex:e=>{return o1(E(n,(t=e,x(a=>n1(a)&&!!a.match(t)))));var t}});o1(x(n1));const q=n=>Object.assign(U(n),{between:(e,t)=>q(E(n,((a,i)=>x(r=>W(r)&&a<=r&&i>=r))(e,t))),lt:e=>q(E(n,(t=>x(a=>W(a)&&a<t))(e))),gt:e=>q(E(n,(t=>x(a=>W(a)&&a>t))(e))),lte:e=>q(E(n,(t=>x(a=>W(a)&&a<=t))(e))),gte:e=>q(E(n,(t=>x(a=>W(a)&&a>=t))(e))),int:()=>q(E(n,x(e=>W(e)&&Number.isInteger(e)))),finite:()=>q(E(n,x(e=>W(e)&&Number.isFinite(e)))),positive:()=>q(E(n,x(e=>W(e)&&e>0))),negative:()=>q(E(n,x(e=>W(e)&&e<0)))});q(x(W));const i1=n=>Object.assign(U(n),{between:(e,t)=>i1(E(n,((a,i)=>x(r=>t1(r)&&a<=r&&i>=r))(e,t))),lt:e=>i1(E(n,(t=>x(a=>t1(a)&&a<t))(e))),gt:e=>i1(E(n,(t=>x(a=>t1(a)&&a>t))(e))),lte:e=>i1(E(n,(t=>x(a=>t1(a)&&a<=t))(e))),gte:e=>i1(E(n,(t=>x(a=>t1(a)&&a>=t))(e))),positive:()=>i1(E(n,x(e=>t1(e)&&e>0))),negative:()=>i1(E(n,x(e=>t1(e)&&e<0)))});i1(x(t1)),U(x(function(n){return typeof n=="boolean"})),U(x(function(n){return typeof n=="symbol"})),U(x(function(n){return n==null})),U(x(function(n){return n!=null}));class F0 extends Error{constructor(e){let t;try{t=JSON.stringify(e)}catch{t=e}super(`Pattern matching error: no pattern matches value ${t}`),this.input=void 0,this.input=e}}const u3={matched:!1,value:void 0};class U1{constructor(e,t){this.input=void 0,this.state=void 0,this.input=e,this.state=t}with(...e){if(this.state.matched)return this;const t=e[e.length-1],a=[e[0]];let i;e.length===3&&typeof e[1]=="function"?i=e[1]:e.length>2&&a.push(...e.slice(1,e.length-1));let r=!1,l={};const s=(u,d)=>{r=!0,l[u]=d},c=!a.some(u=>K(u,this.input,s))||i&&!i(this.input)?u3:{matched:!0,value:t(r?V1 in l?l[V1]:l:this.input,this.input)};return new U1(this.input,c)}when(e,t){if(this.state.matched)return this;const a=!!e(this.input);return new U1(this.input,a?{matched:!0,value:t(this.input,this.input)}:u3)}otherwise(e){return this.state.matched?this.state.value:e(this.input)}exhaustive(){if(this.state.matched)return this.state.value;throw new F0(this.input)}run(){return this.exhaustive()}returnType(){return this}}function S0(n){let e,t,a,i,r,l,s,c,u,d,C,g;return{c(){e=m("svg"),t=m("title"),a=Z("nextedit_addition_light"),i=m("g"),r=m("g"),l=m("path"),s=m("rect"),u=m("g"),d=m("rect"),C=m("rect"),o(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(l,"id","Combined-Shape"),o(l,"fill-rule","nonzero"),o(s,"id","Rectangle"),o(s,"opacity","0.005"),o(s,"x","0"),o(s,"y","0"),o(s,"width","16"),o(s,"height","16"),o(s,"rx","2"),o(r,"id","Pencil_Base"),o(r,"fill",c=n[0]?n[1]:"#0A84FF"),o(d,"id","Rectangle"),o(d,"x","2.25"),o(d,"y","0"),o(d,"width","1.5"),o(d,"height","6"),o(d,"rx","0.75"),o(C,"id","Rectangle-Copy"),o(C,"transform","translate(3, 3) rotate(90) translate(-3, -3)"),o(C,"x","2.25"),o(C,"y","1.13686838e-13"),o(C,"width","1.5"),o(C,"height","6"),o(C,"rx","0.75"),o(u,"id","Group"),o(u,"transform","translate(9, 1)"),o(u,"fill",g=n[0]?n[1]:"#34C759"),o(i,"id","nextedit_addition_light"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(e,"width","16"),o(e,"height","16"),o(e,"viewBox","0 0 16 16"),o(e,"version","1.1"),o(e,"xmlns","http://www.w3.org/2000/svg"),o(e,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(h,p){A(h,e,p),f(e,t),f(t,a),f(e,i),f(i,r),f(r,l),f(r,s),f(i,u),f(u,d),f(u,C)},p(h,[p]){3&p&&c!==(c=h[0]?h[1]:"#0A84FF")&&o(r,"fill",c),3&p&&g!==(g=h[0]?h[1]:"#34C759")&&o(u,"fill",g)},i:_,o:_,d(h){h&&S(e)}}}function j0(n,e,t){let{mask:a=!1}=e,{maskColor:i="white"}=e;return n.$$set=r=>{"mask"in r&&t(0,a=r.mask),"maskColor"in r&&t(1,i=r.maskColor)},[a,i]}class M0 extends O{constructor(e){super(),R(this,e,j0,S0,B,{mask:0,maskColor:1})}}function E0(n){let e,t,a,i,r,l,s,c,u,d,C,g;return{c(){e=m("svg"),t=m("title"),a=Z("nextedit_addition_dark"),i=m("g"),r=m("g"),l=m("path"),s=m("rect"),u=m("g"),d=m("rect"),C=m("rect"),o(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(l,"id","Combined-Shape"),o(l,"fill-rule","nonzero"),o(s,"id","Rectangle"),o(s,"opacity","0.005"),o(s,"x","0"),o(s,"y","0"),o(s,"width","16"),o(s,"height","16"),o(s,"rx","2"),o(r,"id","Pencil_Base"),o(r,"fill",c=n[0]?n[1]:"#168AFF"),o(d,"id","Rectangle"),o(d,"x","2.25"),o(d,"y","0"),o(d,"width","1.5"),o(d,"height","6"),o(d,"rx","0.75"),o(C,"id","Rectangle-Copy"),o(C,"transform","translate(3, 3) rotate(90) translate(-3, -3)"),o(C,"x","2.25"),o(C,"y","1.13686838e-13"),o(C,"width","1.5"),o(C,"height","6"),o(C,"rx","0.75"),o(u,"id","Group"),o(u,"transform","translate(9, 1)"),o(u,"fill",g=n[0]?n[1]:"#30D158"),o(i,"id","nextedit_addition_dark"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(e,"width","16"),o(e,"height","16"),o(e,"viewBox","0 0 16 16"),o(e,"version","1.1"),o(e,"xmlns","http://www.w3.org/2000/svg"),o(e,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(h,p){A(h,e,p),f(e,t),f(t,a),f(e,i),f(i,r),f(r,l),f(r,s),f(i,u),f(u,d),f(u,C)},p(h,[p]){3&p&&c!==(c=h[0]?h[1]:"#168AFF")&&o(r,"fill",c),3&p&&g!==(g=h[0]?h[1]:"#30D158")&&o(u,"fill",g)},i:_,o:_,d(h){h&&S(e)}}}function O0(n,e,t){let{mask:a=!1}=e,{maskColor:i="white"}=e;return n.$$set=r=>{"mask"in r&&t(0,a=r.mask),"maskColor"in r&&t(1,i=r.maskColor)},[a,i]}class R0 extends O{constructor(e){super(),R(this,e,O0,E0,B,{mask:0,maskColor:1})}}function B0(n){let e,t,a,i,r,l,s,c,u,d,C;return{c(){e=m("svg"),t=m("title"),a=Z("nextedit_deletion_light"),i=m("g"),r=m("g"),l=m("path"),s=m("rect"),u=m("g"),d=m("rect"),o(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(l,"id","Combined-Shape"),o(l,"fill-rule","nonzero"),o(s,"id","Rectangle"),o(s,"opacity","0.005"),o(s,"x","0"),o(s,"y","0"),o(s,"width","16"),o(s,"height","16"),o(s,"rx","2"),o(r,"id","Pencil_Base"),o(r,"fill",c=n[0]?n[1]:"#0A84FF"),o(d,"id","Rectangle-Copy"),o(d,"transform","translate(3, 0.75) rotate(90) translate(-3, -0.75)"),o(d,"x","2.25"),o(d,"y","-2.25"),o(d,"width","1.5"),o(d,"height","6"),o(d,"rx","0.75"),o(u,"id","Group"),o(u,"transform","translate(9, 3.25)"),o(u,"fill",C=n[0]?n[1]:"#FF5D4E"),o(i,"id","nextedit_deletion_light"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(e,"width","16"),o(e,"height","16"),o(e,"viewBox","0 0 16 16"),o(e,"version","1.1"),o(e,"xmlns","http://www.w3.org/2000/svg"),o(e,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(g,h){A(g,e,h),f(e,t),f(t,a),f(e,i),f(i,r),f(r,l),f(r,s),f(i,u),f(u,d)},p(g,[h]){3&h&&c!==(c=g[0]?g[1]:"#0A84FF")&&o(r,"fill",c),3&h&&C!==(C=g[0]?g[1]:"#FF5D4E")&&o(u,"fill",C)},i:_,o:_,d(g){g&&S(e)}}}function N0(n,e,t){let{mask:a=!1}=e,{maskColor:i="white"}=e;return n.$$set=r=>{"mask"in r&&t(0,a=r.mask),"maskColor"in r&&t(1,i=r.maskColor)},[a,i]}class z0 extends O{constructor(e){super(),R(this,e,N0,B0,B,{mask:0,maskColor:1})}}function P0(n){let e,t,a,i,r,l,s,c,u,d,C;return{c(){e=m("svg"),t=m("title"),a=Z("nextedit_deletion_dark"),i=m("g"),r=m("g"),l=m("path"),s=m("rect"),u=m("g"),d=m("rect"),o(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(l,"id","Combined-Shape"),o(l,"fill-rule","nonzero"),o(s,"id","Rectangle"),o(s,"opacity","0.005"),o(s,"x","0"),o(s,"y","0"),o(s,"width","16"),o(s,"height","16"),o(s,"rx","2"),o(r,"id","Pencil_Base"),o(r,"fill",c=n[0]?n[1]:"#168AFF"),o(d,"id","Rectangle-Copy"),o(d,"transform","translate(3, 0.75) rotate(90) translate(-3, -0.75)"),o(d,"x","2.25"),o(d,"y","-2.25"),o(d,"width","1.5"),o(d,"height","6"),o(d,"rx","0.75"),o(u,"id","Group"),o(u,"transform","translate(9, 3.25)"),o(u,"fill",C=n[0]?n[1]:"#FF7E72"),o(i,"id","nextedit_deletion_dark"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(e,"width","16"),o(e,"height","16"),o(e,"viewBox","0 0 16 16"),o(e,"version","1.1"),o(e,"xmlns","http://www.w3.org/2000/svg"),o(e,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(g,h){A(g,e,h),f(e,t),f(t,a),f(e,i),f(i,r),f(r,l),f(r,s),f(i,u),f(u,d)},p(g,[h]){3&h&&c!==(c=g[0]?g[1]:"#168AFF")&&o(r,"fill",c),3&h&&C!==(C=g[0]?g[1]:"#FF7E72")&&o(u,"fill",C)},i:_,o:_,d(g){g&&S(e)}}}function Z0(n,e,t){let{mask:a=!1}=e,{maskColor:i="white"}=e;return n.$$set=r=>{"mask"in r&&t(0,a=r.mask),"maskColor"in r&&t(1,i=r.maskColor)},[a,i]}class D0 extends O{constructor(e){super(),R(this,e,Z0,P0,B,{mask:0,maskColor:1})}}function T0(n){let e,t,a,i,r,l,s,c,u,d,C,g,h,p,L;return{c(){e=m("svg"),t=m("title"),a=Z("nextedit_change_light"),i=m("g"),r=m("g"),l=m("path"),s=m("rect"),u=m("g"),d=m("g"),C=m("rect"),g=m("rect"),h=m("g"),p=m("path"),o(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(l,"id","Combined-Shape"),o(l,"fill-rule","nonzero"),o(s,"id","Rectangle"),o(s,"opacity","0.005"),o(s,"x","0"),o(s,"y","0"),o(s,"width","16"),o(s,"height","16"),o(s,"rx","2"),o(r,"id","Pencil_Base"),o(r,"fill",c=n[0]?n[1]:"#0A84FF"),o(C,"id","Rectangle-Copy"),o(C,"transform","translate(2, 0.5) rotate(90) translate(-2, -0.5)"),o(C,"x","1.5"),o(C,"y","-1.5"),o(C,"width","1"),o(C,"height","4"),o(C,"rx","0.5"),o(g,"id","Rectangle-Copy"),o(g,"transform","translate(2, 4.5) rotate(90) translate(-2, -4.5)"),o(g,"x","1.5"),o(g,"y","2.5"),o(g,"width","1"),o(g,"height","4"),o(g,"rx","0.5"),o(d,"id","Group"),o(d,"transform","translate(0, 1.5)"),o(p,"d","M2,-1.5 C2.27614237,-1.5 2.5,-1.27614237 2.5,-1 L2.5,2 C2.5,2.27614237 2.27614237,2.5 2,2.5 C1.72385763,2.5 1.5,2.27614237 1.5,2 L1.5,-1 C1.5,-1.27614237 1.72385763,-1.5 2,-1.5 Z"),o(p,"id","Rectangle-Copy"),o(p,"transform","translate(2, 0.5) rotate(90) translate(-2, -0.5)"),o(h,"id","Group"),o(h,"transform","translate(2, 2) rotate(90) translate(-2, -2)translate(0, 1.5)"),o(u,"id","Group-2"),o(u,"transform","translate(10.5, 1.5)"),o(u,"fill",L=n[0]?n[1]:"#F4A414"),o(i,"id","nextedit_change_light"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(e,"width","16"),o(e,"height","16"),o(e,"viewBox","0 0 16 16"),o(e,"version","1.1"),o(e,"xmlns","http://www.w3.org/2000/svg"),o(e,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(w,b){A(w,e,b),f(e,t),f(t,a),f(e,i),f(i,r),f(r,l),f(r,s),f(i,u),f(u,d),f(d,C),f(d,g),f(u,h),f(h,p)},p(w,[b]){3&b&&c!==(c=w[0]?w[1]:"#0A84FF")&&o(r,"fill",c),3&b&&L!==(L=w[0]?w[1]:"#F4A414")&&o(u,"fill",L)},i:_,o:_,d(w){w&&S(e)}}}function I0(n,e,t){let{mask:a=!1}=e,{maskColor:i="white"}=e;return n.$$set=r=>{"mask"in r&&t(0,a=r.mask),"maskColor"in r&&t(1,i=r.maskColor)},[a,i]}class G3 extends O{constructor(e){super(),R(this,e,I0,T0,B,{mask:0,maskColor:1})}}function V0(n){let e,t,a,i,r,l,s,c,u,d,C,g,h,p,L;return{c(){e=m("svg"),t=m("title"),a=Z("nextedit_change_dark"),i=m("g"),r=m("g"),l=m("path"),s=m("rect"),u=m("g"),d=m("g"),C=m("rect"),g=m("rect"),h=m("g"),p=m("path"),o(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(l,"id","Combined-Shape"),o(l,"fill-rule","nonzero"),o(s,"id","Rectangle"),o(s,"opacity","0.005"),o(s,"x","0"),o(s,"y","0"),o(s,"width","16"),o(s,"height","16"),o(s,"rx","2"),o(r,"id","Pencil_Base"),o(r,"fill",c=n[0]?n[1]:"#168AFF"),o(C,"id","Rectangle-Copy"),o(C,"transform","translate(2, 0.5) rotate(90) translate(-2, -0.5)"),o(C,"x","1.5"),o(C,"y","-1.5"),o(C,"width","1"),o(C,"height","4"),o(C,"rx","0.5"),o(g,"id","Rectangle-Copy"),o(g,"transform","translate(2, 4.5) rotate(90) translate(-2, -4.5)"),o(g,"x","1.5"),o(g,"y","2.5"),o(g,"width","1"),o(g,"height","4"),o(g,"rx","0.5"),o(d,"id","Group"),o(d,"transform","translate(0, 1.5)"),o(p,"d","M2,-1.5 C2.27614237,-1.5 2.5,-1.27614237 2.5,-1 L2.5,2 C2.5,2.27614237 2.27614237,2.5 2,2.5 C1.72385763,2.5 1.5,2.27614237 1.5,2 L1.5,-1 C1.5,-1.27614237 1.72385763,-1.5 2,-1.5 Z"),o(p,"id","Rectangle-Copy"),o(p,"transform","translate(2, 0.5) rotate(90) translate(-2, -0.5)"),o(h,"id","Group"),o(h,"transform","translate(2, 2) rotate(90) translate(-2, -2)translate(0, 1.5)"),o(u,"id","Group-2"),o(u,"transform","translate(10.5, 1.5)"),o(u,"fill",L=n[0]?n[1]:"#FFC255"),o(i,"id","nextedit_change_dark"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(e,"width","16"),o(e,"height","16"),o(e,"viewBox","0 0 16 16"),o(e,"version","1.1"),o(e,"xmlns","http://www.w3.org/2000/svg"),o(e,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(w,b){A(w,e,b),f(e,t),f(t,a),f(e,i),f(i,r),f(r,l),f(r,s),f(i,u),f(u,d),f(d,C),f(d,g),f(u,h),f(h,p)},p(w,[b]){3&b&&c!==(c=w[0]?w[1]:"#168AFF")&&o(r,"fill",c),3&b&&L!==(L=w[0]?w[1]:"#FFC255")&&o(u,"fill",L)},i:_,o:_,d(w){w&&S(e)}}}function U0(n,e,t){let{mask:a=!1}=e,{maskColor:i="white"}=e;return n.$$set=r=>{"mask"in r&&t(0,a=r.mask),"maskColor"in r&&t(1,i=r.maskColor)},[a,i]}class W3 extends O{constructor(e){super(),R(this,e,U0,V0,B,{mask:0,maskColor:1})}}function G0(n){let e,t,a,i,r,l,s,c,u,d,C;return{c(){e=m("svg"),t=m("title"),a=Z("nextedit_applied_light"),i=m("g"),r=m("g"),l=m("path"),c=m("g"),u=m("path"),d=m("rect"),o(l,"d","M2.68994141,6.32666016 C3.01578776,6.32666016 3.26074219,6.2047526 3.42480469,5.9609375 L6.55908203,1.30566406 C6.61832682,1.21907552 6.66162109,1.13191732 6.68896484,1.04418945 C6.71630859,0.956461589 6.72998047,0.872721354 6.72998047,0.79296875 C6.72998047,0.567382812 6.65079753,0.37882487 6.49243164,0.227294922 C6.33406576,0.075764974 6.13867188,0 5.90625,0 C5.74902344,0 5.61572266,0.0313313802 5.50634766,0.0939941406 C5.39697266,0.156656901 5.29329427,0.263183594 5.1953125,0.413574219 L2.67626953,4.34765625 L1.42871094,2.91210938 C1.26692708,2.72753906 1.06184896,2.63525391 0.813476562,2.63525391 C0.578776042,2.63525391 0.384521484,2.71101888 0.230712891,2.86254883 C0.0769042969,3.01407878 0,3.20377604 0,3.43164063 C0,3.53417969 0.0165201823,3.62988281 0.0495605469,3.71875 C0.0826009115,3.80761719 0.143554688,3.90218099 0.232421875,4.00244141 L1.98925781,6.01904297 C2.16927083,6.22412109 2.40283203,6.32666016 2.68994141,6.32666016 Z"),o(l,"id","Path"),o(r,"id","􀆅"),o(r,"transform","translate(8.5216, 0.8311)"),o(r,"fill",s=n[0]?n[1]:"#34C759"),o(r,"fill-rule","nonzero"),o(u,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(u,"id","Combined-Shape"),o(u,"fill-rule","nonzero"),o(d,"id","Rectangle"),o(d,"opacity","0.005"),o(d,"x","0"),o(d,"y","0"),o(d,"width","16"),o(d,"height","16"),o(c,"id","Pencil_Base"),o(c,"fill",C=n[0]?n[1]:"#000000"),N(c,"opacity",n[0]?"1":"0.2"),o(i,"id","nextedit_applied_light"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(e,"width","16"),o(e,"height","16"),o(e,"viewBox","0 0 16 16"),o(e,"version","1.1"),o(e,"xmlns","http://www.w3.org/2000/svg"),o(e,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(g,h){A(g,e,h),f(e,t),f(t,a),f(e,i),f(i,r),f(r,l),f(i,c),f(c,u),f(c,d)},p(g,[h]){3&h&&s!==(s=g[0]?g[1]:"#34C759")&&o(r,"fill",s),3&h&&C!==(C=g[0]?g[1]:"#000000")&&o(c,"fill",C),1&h&&N(c,"opacity",g[0]?"1":"0.2")},i:_,o:_,d(g){g&&S(e)}}}function W0(n,e,t){let{mask:a=!1}=e,{maskColor:i="white"}=e;return n.$$set=r=>{"mask"in r&&t(0,a=r.mask),"maskColor"in r&&t(1,i=r.maskColor)},[a,i]}class q0 extends O{constructor(e){super(),R(this,e,W0,G0,B,{mask:0,maskColor:1})}}function K0(n){let e,t,a,i,r,l,s,c,u,d,C;return{c(){e=m("svg"),t=m("title"),a=Z("nextedit_applied_dark"),i=m("g"),r=m("g"),l=m("path"),c=m("g"),u=m("path"),d=m("rect"),o(l,"d","M2.68994141,6.32666016 C3.01578776,6.32666016 3.26074219,6.2047526 3.42480469,5.9609375 L6.55908203,1.30566406 C6.61832682,1.21907552 6.66162109,1.13191732 6.68896484,1.04418945 C6.71630859,0.956461589 6.72998047,0.872721354 6.72998047,0.79296875 C6.72998047,0.567382812 6.65079753,0.37882487 6.49243164,0.227294922 C6.33406576,0.075764974 6.13867188,0 5.90625,0 C5.74902344,0 5.61572266,0.0313313802 5.50634766,0.0939941406 C5.39697266,0.156656901 5.29329427,0.263183594 5.1953125,0.413574219 L2.67626953,4.34765625 L1.42871094,2.91210938 C1.26692708,2.72753906 1.06184896,2.63525391 0.813476562,2.63525391 C0.578776042,2.63525391 0.384521484,2.71101888 0.230712891,2.86254883 C0.0769042969,3.01407878 0,3.20377604 0,3.43164063 C0,3.53417969 0.0165201823,3.62988281 0.0495605469,3.71875 C0.0826009115,3.80761719 0.143554688,3.90218099 0.232421875,4.00244141 L1.98925781,6.01904297 C2.16927083,6.22412109 2.40283203,6.32666016 2.68994141,6.32666016 Z"),o(l,"id","Path"),o(r,"id","􀆅"),o(r,"transform","translate(8.5167, 0.8311)"),o(r,"fill",s=n[0]?n[1]:"#30D158"),o(r,"fill-rule","nonzero"),o(u,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(u,"id","Combined-Shape"),o(u,"fill-rule","nonzero"),o(d,"id","Rectangle"),o(d,"opacity","0.005"),o(d,"x","0"),o(d,"y","0"),o(d,"width","16"),o(d,"height","16"),o(c,"id","Pencil_Base"),o(c,"fill",C=n[0]?n[1]:"#FFFFFF"),N(c,"opacity",n[0]?"1":"0.4"),o(i,"id","nextedit_applied_dark"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(e,"width","16"),o(e,"height","16"),o(e,"viewBox","0 0 16 16"),o(e,"version","1.1"),o(e,"xmlns","http://www.w3.org/2000/svg"),o(e,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(g,h){A(g,e,h),f(e,t),f(t,a),f(e,i),f(i,r),f(r,l),f(i,c),f(c,u),f(c,d)},p(g,[h]){3&h&&s!==(s=g[0]?g[1]:"#30D158")&&o(r,"fill",s),3&h&&C!==(C=g[0]?g[1]:"#FFFFFF")&&o(c,"fill",C),1&h&&N(c,"opacity",g[0]?"1":"0.4")},i:_,o:_,d(g){g&&S(e)}}}function H0(n,e,t){let{mask:a=!1}=e,{maskColor:i="white"}=e;return n.$$set=r=>{"mask"in r&&t(0,a=r.mask),"maskColor"in r&&t(1,i=r.maskColor)},[a,i]}class J0 extends O{constructor(e){super(),R(this,e,H0,K0,B,{mask:0,maskColor:1})}}function X0(n){let e,t,a,i,r,l,s,c,u,d,C,g;return{c(){e=m("svg"),t=m("title"),a=Z("nextedit_rejected_light"),i=m("g"),r=m("g"),l=m("path"),s=m("rect"),u=m("g"),d=m("rect"),C=m("rect"),o(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(l,"id","Combined-Shape"),o(l,"fill-rule","nonzero"),o(s,"id","Rectangle"),o(s,"opacity","0.005"),o(s,"x","0"),o(s,"y","0"),o(s,"width","16"),o(s,"height","16"),o(s,"rx","2"),o(r,"id","Pencil_Base"),o(r,"fill",c=n[0]?n[1]:"#0A84FF"),o(d,"id","Rectangle"),o(d,"x","2.25"),o(d,"y","0"),o(d,"width","1.5"),o(d,"height","6"),o(d,"rx","0.75"),o(C,"id","Rectangle-Copy"),o(C,"transform","translate(3, 3) rotate(90) translate(-3, -3)"),o(C,"x","2.25"),o(C,"y","1.13686838e-13"),o(C,"width","1.5"),o(C,"height","6"),o(C,"rx","0.75"),o(u,"id","Group"),o(u,"transform","translate(12, 4) rotate(45) translate(-12, -4)translate(9, 1)"),o(u,"fill",g=n[0]?n[1]:"#FF5D4E"),o(i,"id","nextedit_rejected_light"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(e,"width","16"),o(e,"height","16"),o(e,"viewBox","0 0 16 16"),o(e,"version","1.1"),o(e,"xmlns","http://www.w3.org/2000/svg"),o(e,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(h,p){A(h,e,p),f(e,t),f(t,a),f(e,i),f(i,r),f(r,l),f(r,s),f(i,u),f(u,d),f(u,C)},p(h,[p]){3&p&&c!==(c=h[0]?h[1]:"#0A84FF")&&o(r,"fill",c),3&p&&g!==(g=h[0]?h[1]:"#FF5D4E")&&o(u,"fill",g)},i:_,o:_,d(h){h&&S(e)}}}function Y0(n,e,t){let{mask:a=!0}=e,{maskColor:i="white"}=e;return n.$$set=r=>{"mask"in r&&t(0,a=r.mask),"maskColor"in r&&t(1,i=r.maskColor)},[a,i]}class Q0 extends O{constructor(e){super(),R(this,e,Y0,X0,B,{mask:0,maskColor:1})}}function e4(n){let e,t,a,i,r,l,s,c,u,d,C,g;return{c(){e=m("svg"),t=m("title"),a=Z("nextedit_rejected_dark"),i=m("g"),r=m("g"),l=m("path"),s=m("rect"),u=m("g"),d=m("rect"),C=m("rect"),o(l,"d","M3.07557525,3.27946831 C3.10738379,3.27258798 3.13664209,3.26682472 3.16597818,3.26160513 C3.19407786,3.25661079 3.22181021,3.25217747 3.24959807,3.24822758 C3.3431507,3.23490837 3.43787348,3.22705558 3.53270619,3.22474499 C3.54619312,3.22441336 3.56021661,3.22418981 3.57424082,3.22408741 L3.59202055,3.22402251 C3.61600759,3.22402251 3.63999463,3.22437692 3.66397314,3.22508575 C3.69176119,3.22590043 3.72012236,3.22722855 3.74845755,3.22905289 C3.77692744,3.23089046 3.80498198,3.23319023 3.83299719,3.23597733 C3.86236278,3.23889105 3.89230728,3.24242516 3.92218997,3.24651769 C3.95842477,3.25149198 3.99379267,3.25714552 4.02904516,3.2635852 C4.04457753,3.26641925 4.06056799,3.26950351 4.07653203,3.27274998 C4.1217801,3.28195855 4.16647313,3.29238022 4.21089814,3.30408537 C4.22093231,3.3067264 4.23153789,3.30959531 4.24212737,3.31253756 C4.27196202,3.32083528 4.30106886,3.32952376 4.33003598,3.33877116 C4.35855924,3.347869 4.38751122,3.35771229 4.41630528,3.3681193 C4.42116985,3.36987869 4.42551008,3.37146263 4.42984665,3.3730594 C4.4761162,3.39008583 4.52241276,3.4087674 4.56821184,3.42893807 C4.59406406,3.44033198 4.61917606,3.45191971 4.64412424,3.46396063 C4.67111495,3.47697976 4.69839649,3.4907848 4.72546291,3.50513959 C4.75890801,3.52288219 4.79178851,3.54132453 4.82431475,3.56059431 C4.8374698,3.56838641 4.85073285,3.5764165 4.86393439,3.58458539 C4.89491851,3.60376145 4.92539479,3.6235868 4.95550936,3.64416832 C4.9772823,3.65904443 4.99913454,3.67451232 5.02078256,3.69038541 C5.03998798,3.70447076 5.05881967,3.71870909 5.07748715,3.73325923 C5.10440445,3.75423289 5.13126725,3.7760983 5.15775949,3.79862613 C5.1821715,3.81939236 5.20595148,3.84042939 5.22940861,3.86201411 C5.24512436,3.87647694 5.26059993,3.89109333 5.27592752,3.90595256 C5.28442786,3.91418351 5.29385225,3.92345739 5.30321896,3.9328241 L10.2031018,8.83270693 C10.255475,8.88508012 10.3065885,8.93859789 10.3564099,8.99321224 L10.2031018,8.83270693 C10.2748395,8.90444467 10.344214,8.97832987 10.4111413,9.05423915 C10.4223877,9.06699478 10.4335715,9.07981507 10.4446856,9.092692 C10.7663645,9.46539004 11.0297601,9.88553066 11.2252237,10.3388957 L11.6780206,11.3880225 L12.548286,13.4076516 C12.7467158,13.8678966 12.5344727,14.4018581 12.0742277,14.6002879 C11.9977866,14.6332447 11.9179446,14.6552159 11.836969,14.6662015 L11.7149387,14.6744406 C11.592625,14.6744406 11.4703113,14.6497231 11.3556497,14.6002879 L11.2340206,14.5480225 L9.33602055,13.7300225 L8.28689372,13.2772256 C7.83352871,13.081762 7.41338809,12.8183665 7.04069004,12.4966876 L7.0022372,12.4631433 C6.98177889,12.4451057 6.9614676,12.4268903 6.94130575,12.4084989 L7.04069004,12.4966876 C6.95122931,12.4194733 6.86450207,12.3389008 6.78070498,12.2551038 L1.88082214,7.35522092 C0.935753358,6.41015213 0.935753358,4.87789288 1.88082214,3.9328241 L1.90902055,3.90502251 L2.01192506,3.8109306 C2.19120357,3.65606766 2.38780913,3.5318516 2.59488381,3.4382824 C2.62872186,3.42311621 2.65522016,3.41182111 2.68187195,3.40102033 C2.76025666,3.36925866 2.83986347,3.34180278 2.92043821,3.31861145 L3.07557525,3.27946831 Z M9.58610551,9.95149698 L7.89951995,11.6381324 C8.10279642,11.805046 8.32371441,11.9494547 8.55841217,12.068738 L8.76594574,12.166096 L10.2570206,12.8090225 L10.7570206,12.3090225 L10.114094,10.8179477 C9.97930356,10.5053101 9.80144069,10.2137385 9.58610551,9.95149698 Z"),o(l,"id","Combined-Shape"),o(l,"fill-rule","nonzero"),o(s,"id","Rectangle"),o(s,"opacity","0.005"),o(s,"x","0"),o(s,"y","0"),o(s,"width","16"),o(s,"height","16"),o(s,"rx","2"),o(r,"id","Pencil_Base"),o(r,"fill",c=n[0]?n[1]:"#168AFF"),o(d,"id","Rectangle"),o(d,"x","2.25"),o(d,"y","0"),o(d,"width","1.5"),o(d,"height","6"),o(d,"rx","0.75"),o(C,"id","Rectangle-Copy"),o(C,"transform","translate(3, 3) rotate(90) translate(-3, -3)"),o(C,"x","2.25"),o(C,"y","1.13686838e-13"),o(C,"width","1.5"),o(C,"height","6"),o(C,"rx","0.75"),o(u,"id","Group"),o(u,"transform","translate(12, 4) rotate(45) translate(-12, -4)translate(9, 1)"),o(u,"fill",g=n[0]?n[1]:"#FF7E72"),o(i,"id","nextedit_rejected_dark"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(e,"width","16"),o(e,"height","16"),o(e,"viewBox","0 0 16 16"),o(e,"version","1.1"),o(e,"xmlns","http://www.w3.org/2000/svg"),o(e,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(h,p){A(h,e,p),f(e,t),f(t,a),f(e,i),f(i,r),f(r,l),f(r,s),f(i,u),f(u,d),f(u,C)},p(h,[p]){3&p&&c!==(c=h[0]?h[1]:"#168AFF")&&o(r,"fill",c),3&p&&g!==(g=h[0]?h[1]:"#FF7E72")&&o(u,"fill",g)},i:_,o:_,d(h){h&&S(e)}}}function n4(n,e,t){let{mask:a=!0}=e,{maskColor:i="white"}=e;return n.$$set=r=>{"mask"in r&&t(0,a=r.mask),"maskColor"in r&&t(1,i=r.maskColor)},[a,i]}class t4 extends O{constructor(e){super(),R(this,e,n4,e4,B,{mask:0,maskColor:1})}}function o4(n){let e,t,a,i,r,l,s,c,u,d,C;return{c(){e=m("svg"),t=m("title"),a=Z("Option 2_light"),i=m("g"),r=m("path"),s=m("g"),c=m("path"),d=m("path"),o(r,"d","M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z"),o(r,"id","Path"),o(r,"fill",l=n[0]?n[1]:"#007AFF"),o(c,"d","M4.81096943,2.24362978 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03645345,8.60325107 L2.24362978,4.81096943 L4.81096943,2.24362978 Z M2.40551485,0.605057322 C2.66486762,0.603004601 2.92459099,0.65617863 3.16550077,0.764612139 L0.782130647,3.14739928 C0.671050497,2.89397491 0.616248167,2.62929203 0.618310403,2.36812709 C0.621891891,1.91456136 0.798131359,1.47507103 1.13660119,1.13660119 C1.48731739,0.785884996 1.94585368,0.608695442 2.40551485,0.605057322 Z"),o(c,"id","Combined-Shape"),o(c,"stroke",u=n[0]?n[1]:"#007AFF"),o(c,"stroke-width","1.21"),o(d,"d","M10.506,8.164 L11.3762654,10.1836291 C11.5746952,10.6438741 11.3624521,11.1778356 10.9022072,11.3762654 C10.6728839,11.4751357 10.4129523,11.4751357 10.1836291,11.3762654 L8.164,10.506 L10.506,8.164 Z M4.13119841,0.708801589 L9.03108125,5.60868442 C9.11487834,5.69248152 9.19545077,5.77920876 9.27266509,5.86866949 L5.86866949,9.27266509 C5.77920876,9.19545077 5.69248152,9.11487834 5.60868442,9.03108125 L0.708801589,4.13119841 C-0.236267196,3.18612962 -0.236267196,1.65387038 0.708801589,0.708801589 C1.65387038,-0.236267196 3.18612962,-0.236267196 4.13119841,0.708801589 Z"),o(d,"id","Combined-Shape"),o(d,"fill",C=n[0]?n[1]:"#007AFF"),o(s,"id","Pencil"),o(s,"transform","translate(0.172, 2.224)"),o(i,"id","Option-2_light"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(e,"width","16"),o(e,"height","16"),o(e,"viewBox","0 0 16 16"),o(e,"version","1.1"),o(e,"xmlns","http://www.w3.org/2000/svg"),o(e,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(g,h){A(g,e,h),f(e,t),f(t,a),f(e,i),f(i,r),f(i,s),f(s,c),f(s,d)},p(g,[h]){3&h&&l!==(l=g[0]?g[1]:"#007AFF")&&o(r,"fill",l),3&h&&u!==(u=g[0]?g[1]:"#007AFF")&&o(c,"stroke",u),3&h&&C!==(C=g[0]?g[1]:"#007AFF")&&o(d,"fill",C)},i:_,o:_,d(g){g&&S(e)}}}function i4(n,e,t){let{mask:a=!1}=e,{maskColor:i="white"}=e;return n.$$set=r=>{"mask"in r&&t(0,a=r.mask),"maskColor"in r&&t(1,i=r.maskColor)},[a,i]}class r4 extends O{constructor(e){super(),R(this,e,i4,o4,B,{mask:0,maskColor:1})}}function a4(n){let e,t,a,i,r,l,s,c,u,d,C;return{c(){e=m("svg"),t=m("title"),a=Z("Option 2_dark"),i=m("g"),r=m("path"),s=m("g"),c=m("path"),d=m("path"),o(r,"d","M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z"),o(r,"id","Path"),o(r,"fill",l=n[0]?n[1]:"#BF5AF2"),o(c,"d","M4.81096943,2.24362978 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03645345,8.60325107 L2.24362978,4.81096943 L4.81096943,2.24362978 Z M2.40551485,0.605057322 C2.66486762,0.603004601 2.92459099,0.65617863 3.16550077,0.764612139 L0.782130647,3.14739928 C0.671050497,2.89397491 0.616248167,2.62929203 0.618310403,2.36812709 C0.621891891,1.91456136 0.798131359,1.47507103 1.13660119,1.13660119 C1.48731739,0.785884996 1.94585368,0.608695442 2.40551485,0.605057322 Z"),o(c,"id","Combined-Shape"),o(c,"stroke",u=n[0]?n[1]:"#389BFF"),o(c,"stroke-width","1.21"),o(d,"d","M10.506,8.164 L11.3762654,10.1836291 C11.5746952,10.6438741 11.3624521,11.1778356 10.9022072,11.3762654 C10.6728839,11.4751357 10.4129523,11.4751357 10.1836291,11.3762654 L8.164,10.506 L10.506,8.164 Z M4.13119841,0.708801589 L9.03108125,5.60868442 C9.11487834,5.69248152 9.19545077,5.77920876 9.27266509,5.86866949 L5.86866949,9.27266509 C5.77920876,9.19545077 5.69248152,9.11487834 5.60868442,9.03108125 L0.708801589,4.13119841 C-0.236267196,3.18612962 -0.236267196,1.65387038 0.708801589,0.708801589 C1.65387038,-0.236267196 3.18612962,-0.236267196 4.13119841,0.708801589 Z"),o(d,"id","Combined-Shape"),o(d,"fill",C=n[0]?n[1]:"#389BFF"),o(s,"id","Pencil"),o(s,"transform","translate(0.172, 2.224)"),o(i,"id","Option-2_dark"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(e,"width","16"),o(e,"height","16"),o(e,"viewBox","0 0 16 16"),o(e,"version","1.1"),o(e,"xmlns","http://www.w3.org/2000/svg"),o(e,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(g,h){A(g,e,h),f(e,t),f(t,a),f(e,i),f(i,r),f(i,s),f(s,c),f(s,d)},p(g,[h]){3&h&&l!==(l=g[0]?g[1]:"#BF5AF2")&&o(r,"fill",l),3&h&&u!==(u=g[0]?g[1]:"#389BFF")&&o(c,"stroke",u),3&h&&C!==(C=g[0]?g[1]:"#389BFF")&&o(d,"fill",C)},i:_,o:_,d(g){g&&S(e)}}}function s4(n,e,t){let{mask:a=!1}=e,{maskColor:i="white"}=e;return n.$$set=r=>{"mask"in r&&t(0,a=r.mask),"maskColor"in r&&t(1,i=r.maskColor)},[a,i]}class l4 extends O{constructor(e){super(),R(this,e,s4,a4,B,{mask:0,maskColor:1})}}function c4(n){let e,t,a,i,r,l,s,c,u;return{c(){e=m("svg"),t=m("title"),a=Z("Option 2_Inactive_light"),i=m("g"),r=m("path"),s=m("g"),c=m("path"),o(r,"d","M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z"),o(r,"id","Path"),o(r,"fill",l=n[0]?n[1]:"#BF5AF2"),o(c,"d","M2.42,0.605 C2.88449901,0.605 3.34899801,0.782200397 3.70339881,1.13660119 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03648403,8.60328164 L1.13660119,3.70339881 C0.782200397,3.34899801 0.605,2.88449901 0.605,2.42 C0.605,1.95550099 0.782200397,1.49100199 1.13660119,1.13660119 C1.49100199,0.782200397 1.95550099,0.605 2.42,0.605 Z"),o(c,"id","Combined-Shape"),o(s,"id","Pencil"),o(s,"transform","translate(0.172, 2.224)"),o(s,"stroke",u=n[0]?n[1]:"#007AFF"),o(s,"stroke-width","1.21"),o(i,"id","Option-2_Inactive_light"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(e,"width","16"),o(e,"height","16"),o(e,"viewBox","0 0 16 16"),o(e,"version","1.1"),o(e,"xmlns","http://www.w3.org/2000/svg"),o(e,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(d,C){A(d,e,C),f(e,t),f(t,a),f(e,i),f(i,r),f(i,s),f(s,c)},p(d,[C]){3&C&&l!==(l=d[0]?d[1]:"#BF5AF2")&&o(r,"fill",l),3&C&&u!==(u=d[0]?d[1]:"#007AFF")&&o(s,"stroke",u)},i:_,o:_,d(d){d&&S(e)}}}function u4(n,e,t){let{mask:a=!1}=e,{maskColor:i="white"}=e;return n.$$set=r=>{"mask"in r&&t(0,a=r.mask),"maskColor"in r&&t(1,i=r.maskColor)},[a,i]}class d4 extends O{constructor(e){super(),R(this,e,u4,c4,B,{mask:0,maskColor:1})}}function C4(n){let e,t,a,i,r,l,s,c,u;return{c(){e=m("svg"),t=m("title"),a=Z("Option 2_inactive_dark"),i=m("g"),r=m("path"),s=m("g"),c=m("path"),o(r,"d","M11.0070258,6 C11.1334895,6 11.2318501,5.90866511 11.2529274,5.76814988 C11.5409836,3.95550351 11.8641686,3.52693208 13.7751756,3.2529274 C13.9156909,3.23185012 14,3.13348946 14,3 C14,2.8735363 13.9156909,2.77517564 13.7751756,2.75409836 C11.8571429,2.48009368 11.618267,2.07259953 11.2529274,0.217798595 C11.2248244,0.0913348946 11.1334895,4.88498131e-15 11.0070258,4.88498131e-15 C10.8735363,4.88498131e-15 10.7751756,0.0913348946 10.7540984,0.224824356 C10.4660422,2.07259953 10.1498829,2.48009368 8.23887588,2.75409836 C8.09836066,2.78220141 8.00702576,2.8735363 8.00702576,3 C8.00702576,3.13348946 8.09836066,3.23185012 8.23887588,3.2529274 C10.1569087,3.52693208 10.4028103,3.92740047 10.7540984,5.77517564 C10.7822014,5.91569087 10.8805621,6 11.0070258,6 Z"),o(r,"id","Path"),o(r,"fill",l=n[0]?n[1]:"#BF5AF2"),o(c,"d","M2.42,0.605 C2.88449901,0.605 3.34899801,0.782200397 3.70339881,1.13660119 L8.60328164,6.03648403 C8.98206926,6.41527164 9.28555422,6.86248408 9.4976383,7.35439917 L10.8207006,10.4231551 L7.35439917,9.4976383 C6.86248408,9.28555422 6.41527164,8.98206926 6.03648403,8.60328164 L1.13660119,3.70339881 C0.782200397,3.34899801 0.605,2.88449901 0.605,2.42 C0.605,1.95550099 0.782200397,1.49100199 1.13660119,1.13660119 C1.49100199,0.782200397 1.95550099,0.605 2.42,0.605 Z"),o(c,"id","Combined-Shape"),o(s,"id","Pencil"),o(s,"transform","translate(0.172, 2.224)"),o(s,"stroke",u=n[0]?n[1]:"#389BFF"),o(s,"stroke-width","1.21"),o(i,"id","Option-2_inactive_dark"),o(i,"stroke","none"),o(i,"stroke-width","1"),o(i,"fill","none"),o(i,"fill-rule","evenodd"),o(e,"width","16"),o(e,"height","16"),o(e,"viewBox","0 0 16 16"),o(e,"version","1.1"),o(e,"xmlns","http://www.w3.org/2000/svg"),o(e,"xmlns:xlink","http://www.w3.org/1999/xlink")},m(d,C){A(d,e,C),f(e,t),f(t,a),f(e,i),f(i,r),f(i,s),f(s,c)},p(d,[C]){3&C&&l!==(l=d[0]?d[1]:"#BF5AF2")&&o(r,"fill",l),3&C&&u!==(u=d[0]?d[1]:"#389BFF")&&o(s,"stroke",u)},i:_,o:_,d(d){d&&S(e)}}}function h4(n,e,t){let{mask:a=!1}=e,{maskColor:i="white"}=e;return n.$$set=r=>{"mask"in r&&t(0,a=r.mask),"maskColor"in r&&t(1,i=r.maskColor)},[a,i]}class g4 extends O{constructor(e){super(),R(this,e,h4,C4,B,{mask:0,maskColor:1})}}function f4(n){let e,t,a,i;var r=n[2];function l(u,d){return{props:{mask:u[0],maskColor:u[1]}}}r&&(t=F3(r,l(n)));const s=n[9].default,c=_1(s,n,n[8],null);return{c(){e=z("span"),t&&D(t.$$.fragment),a=H(),c&&c.c(),o(e,"class","c-pencil-icon svelte-6fsamc")},m(u,d){A(u,e,d),t&&T(t,e,null),f(e,a),c&&c.m(e,null),i=!0},p(u,[d]){if(4&d&&r!==(r=u[2])){if(t){j1();const C=t;F(C.$$.fragment,1,0,()=>{I(C,1)}),M1()}r?(t=F3(r,l(u)),D(t.$$.fragment),$(t.$$.fragment,1),T(t,e,a)):t=null}else if(r){const C={};1&d&&(C.mask=u[0]),2&d&&(C.maskColor=u[1]),t.$set(C)}c&&c.p&&(!i||256&d)&&$1(c,s,u,u[8],i?F1(s,u[8],d,null):A1(u[8]),null)},i(u){i||(t&&$(t.$$.fragment,u),$(c,u),i=!0)},o(u){t&&F(t.$$.fragment,u),F(c,u),i=!1},d(u){u&&S(e),t&&I(t),c&&c.d(u)}}}function p4(n,e,t){let a,i,r;I1(n,L2,p=>t(7,r=p));let{$$slots:l={},$$scope:s}=e;const c={insertion:{light:M0,dark:R0},deletion:{light:z0,dark:D0},modification:{light:G3,dark:W3},noop:{light:G3,dark:W3},active:{light:r4,dark:l4},inactive:{light:d4,dark:g4},accepted:{light:q0,dark:J0},rejected:{light:Q0,dark:t4}};let u,{mask:d=!1}=e,{maskColor:C="currentColor"}=e,{suggestion:g}=e,{themeCategory:h}=e;return n.$$set=p=>{"mask"in p&&t(0,d=p.mask),"maskColor"in p&&t(1,C=p.maskColor),"suggestion"in p&&t(4,g=p.suggestion),"themeCategory"in p&&t(3,h=p.themeCategory),"$$scope"in p&&t(8,s=p.$$scope)},n.$$.update=()=>{136&n.$$.dirty&&(r!=null&&r.category)&&t(3,h??(h=r.category)),16&n.$$.dirty&&t(5,u=function(p){return new U1(p,u3)}(g).with({state:r3.stale},{state:r3.accepted},()=>"accepted").otherwise(({changeType:p})=>p)),32&n.$$.dirty&&t(6,a=c[u]??c.active),72&n.$$.dirty&&t(2,i=a[h??T2.light])},[d,C,i,h,g,u,a,r,s,l]}class m4 extends O{constructor(e){super(),R(this,e,p4,f4,B,{mask:0,maskColor:1,suggestion:4,themeCategory:3})}}function v4(n){let e,t,a,i,r,l,s,c,u,d,C,g,h,p,L,w,b,y,v,Y,C1,Q=n[1].result.changeDescription+"";return c=new m4({props:{mask:n[11]!=="none",suggestion:n[1]}}),g=new k2({props:{compact:!0,actions:n[2],onAction:n[3],value:n[1]}}),{c(){e=z("div"),a=H(),i=z("div"),r=z("div"),l=z("div"),s=z("button"),D(c.$$.fragment),u=H(),d=Z(Q),C=H(),D(g.$$.fragment),h=H(),p=z("div"),L=z("div"),o(e,"class","c-code-roll-suggestion-window__view-zone"),N(e,"--augment-code-roll-left-alignment",n[9]+"px"),o(s,"class","c-code-roll-suggestion-window__item-title-text svelte-1ci0na9"),o(s,"tabindex","0"),o(l,"class","c-code-roll-suggestion-window__item-title svelte-1ci0na9"),N(l,"height",n[6]+"px"),o(L,"class","c-code-roll-suggestion-window__window svelte-1ci0na9"),N(L,"height",n[10]+"px"),o(p,"class","c-code-roll-suggestion-window__border svelte-1ci0na9"),o(r,"class","c-code-roll-suggestion-window__item svelte-1ci0na9"),o(r,"style",w=V3(l3(n[1],n[7],!1))),N(i,"--augment-code-roll-left-alignment",n[9]+"px"),N(i,"--augment-code-roll-suggestion-window-line-height",n[6]+"px"),N(i,"top",n[8]+"px"),o(i,"class","c-code-roll-suggestion-window svelte-1ci0na9"),o(i,"data-result-id",y=`${n[1].result.suggestionId}:${n[1].requestId}`)},m(P,j){A(P,e,j),A(P,a,j),A(P,i,j),f(i,r),f(r,l),f(l,s),T(c,s,null),f(s,u),f(s,d),f(l,C),T(g,l,null),f(r,h),f(r,p),f(p,L),v=!0,Y||(C1=[T1(t=y0.call(null,e,{editor:n[0],afterLineNumber:n[4],heightInPx:n[6],onDomNodeTop:n[16]})),d1(s,"keydown",function(){k1(i3("Enter",N1(n[3],"active",n[1])))&&i3("Enter",N1(n[3],"active",n[1])).apply(this,arguments)}),d1(s,"click",function(){k1(N1(n[3],"active",n[1]))&&N1(n[3],"active",n[1]).apply(this,arguments)}),T1(b=x0.call(null,r,{scrollContainer:n[5],doScroll:b1(n[1],I3(n[7])),scrollIntoView:{behavior:"smooth",block:"center"},useSmartBlockAlignment:!0}))],Y=!0)},p(P,[j]){n=P,(!v||512&j)&&N(e,"--augment-code-roll-left-alignment",n[9]+"px"),t&&k1(t.update)&&337&j&&t.update.call(null,{editor:n[0],afterLineNumber:n[4],heightInPx:n[6],onDomNodeTop:n[16]});const e1={};2048&j&&(e1.mask=n[11]!=="none"),2&j&&(e1.suggestion=n[1]),c.$set(e1),(!v||2&j)&&Q!==(Q=n[1].result.changeDescription+"")&&I2(d,Q);const l1={};4&j&&(l1.actions=n[2]),8&j&&(l1.onAction=n[3]),2&j&&(l1.value=n[1]),g.$set(l1),(!v||64&j)&&N(l,"height",n[6]+"px"),(!v||1024&j)&&N(L,"height",n[10]+"px"),(!v||130&j&&w!==(w=V3(l3(n[1],n[7],!1))))&&o(r,"style",w),b&&k1(b.update)&&162&j&&b.update.call(null,{scrollContainer:n[5],doScroll:b1(n[1],I3(n[7])),scrollIntoView:{behavior:"smooth",block:"center"},useSmartBlockAlignment:!0}),(!v||512&j)&&N(i,"--augment-code-roll-left-alignment",n[9]+"px"),(!v||64&j)&&N(i,"--augment-code-roll-suggestion-window-line-height",n[6]+"px"),(!v||256&j)&&N(i,"top",n[8]+"px"),(!v||2&j&&y!==(y=`${n[1].result.suggestionId}:${n[1].requestId}`))&&o(i,"data-result-id",y)},i(P){v||($(c.$$.fragment,P),$(g.$$.fragment,P),v=!0)},o(P){F(c.$$.fragment,P),F(g.$$.fragment,P),v=!1},d(P){P&&(S(e),S(a),S(i)),I(c),I(g),Y=!1,w2(C1)}}}function w4(n,e,t){let a,i,r,l,s,c,u,{diffEditor:d}=e,{suggestion:C}=e,{codeActions:g=[]}=e,{onCodeAction:h}=e,{afterLineNumber:p}=e,{lineCount:L=0}=e,{scrollContainer:w}=e,b=0;const y=k0();return I1(n,y,v=>t(7,u=v)),n.$$set=v=>{"diffEditor"in v&&t(0,d=v.diffEditor),"suggestion"in v&&t(1,C=v.suggestion),"codeActions"in v&&t(2,g=v.codeActions),"onCodeAction"in v&&t(3,h=v.onCodeAction),"afterLineNumber"in v&&t(4,p=v.afterLineNumber),"lineCount"in v&&t(13,L=v.lineCount),"scrollContainer"in v&&t(5,w=v.scrollContainer)},n.$$.update=()=>{130&n.$$.dirty&&t(11,a=l3(C,u,!1)),1&n.$$.dirty&&t(6,i=d.getModifiedEditor().getOption(e0.EditorOption.lineHeight)),8256&n.$$.dirty&&t(10,r=L*i),2&n.$$.dirty&&t(15,l=C.changeType===M3.insertion||C.changeType===M3.modification),40976&n.$$.dirty&&t(14,s=l?p+L:p),16384&n.$$.dirty&&t(9,c=s>999?0:5)},[d,C,g,h,p,w,i,u,b,c,r,a,y,L,s,l,function(v){t(8,b=v)}]}class L4 extends O{constructor(e){super(),R(this,e,w4,v4,B,{diffEditor:0,suggestion:1,codeActions:2,onCodeAction:3,afterLineNumber:4,lineCount:13,scrollContainer:5})}}function q3(n,e,t){const a=n.slice();return a[29]=e[t][0],a[30]=e[t][1],a}function y4(n){let e,t,a=j3(s3(n[2],n[8])),i=[];for(let l=0;l<a.length;l+=1)i[l]=K3(q3(n,a,l));const r=l=>F(i[l],1,1,()=>{i[l]=null});return{c(){for(let l=0;l<i.length;l+=1)i[l].c();e=v2()},m(l,s){for(let c=0;c<i.length;c+=1)i[c]&&i[c].m(l,s);A(l,e,s),t=!0},p(l,s){if(380&s[0]){let c;for(a=j3(s3(l[2],l[8])),c=0;c<a.length;c+=1){const u=q3(l,a,c);i[c]?(i[c].p(u,s),$(i[c],1)):(i[c]=K3(u),i[c].c(),$(i[c],1),i[c].m(e.parentNode,e))}for(j1(),c=a.length;c<i.length;c+=1)r(c);M1()}},i(l){if(!t){for(let s=0;s<a.length;s+=1)$(i[s]);t=!0}},o(l){i=i.filter(Boolean);for(let s=0;s<i.length;s+=1)F(i[s]);t=!1},d(l){l&&S(e),W2(i,l)}}}function k4(n){let e,t,a;return t=new y2({}),{c(){e=z("div"),D(t.$$.fragment),o(e,"class","c-diff-view__loading")},m(i,r){A(i,e,r),T(t,e,null),a=!0},p:_,i(i){a||($(t.$$.fragment,i),a=!0)},o(i){F(t.$$.fragment,i),a=!1},d(i){i&&S(e),I(t)}}}function K3(n){let e,t;const a=[{diffEditor:n[6]},{suggestion:n[29]},{codeActions:n[3]},{onCodeAction:n[4]},a3(n[30]),{scrollContainer:n[5]}];let i={};for(let r=0;r<a.length;r+=1)i=x1(i,a[r]);return e=new L4({props:i}),{c(){D(e.$$.fragment)},m(r,l){T(e,r,l),t=!0},p(r,l){const s=380&l[0]?v3(a,[64&l[0]&&{diffEditor:r[6]},260&l[0]&&{suggestion:r[29]},8&l[0]&&{codeActions:r[3]},16&l[0]&&{onCodeAction:r[4]},260&l[0]&&q2(a3(r[30])),32&l[0]&&{scrollContainer:r[5]}]):{};e.$set(s)},i(r){t||($(e.$$.fragment,r),t=!0)},o(r){F(e.$$.fragment,r),t=!1},d(r){I(e,r)}}}function b4(n){let e,t,a,i,r,l,s,c,u;const d=[k4,y4],C=[];function g(h,p){return h[1]||!h[6]?0:1}return r=g(n),l=C[r]=d[r](n),{c(){e=z("div"),t=z("div"),i=H(),l.c(),o(t,"class","c-diff-view svelte-syu9kz"),N(t,"display",n[1]?"none":"flex"),o(e,"class","c-diff-view__container svelte-syu9kz"),N(e,"--augment-codeblock-min-height",n[0]+"px"),r1(e,"has-top-decorations",n[9])},m(h,p){A(h,e,p),f(e,t),n[21](t),f(e,i),C[r].m(e,null),s=!0,c||(u=T1(a=n0.call(null,t,{onResize:n[20]})),c=!0)},p(h,p){a&&k1(a.update)&&64&p[0]&&a.update.call(null,{onResize:h[20]}),2&p[0]&&N(t,"display",h[1]?"none":"flex");let L=r;r=g(h),r===L?C[r].p(h,p):(j1(),F(C[L],1,1,()=>{C[L]=null}),M1(),l=C[r],l?l.p(h,p):(l=C[r]=d[r](h),l.c()),$(l,1),l.m(e,null)),(!s||1&p[0])&&N(e,"--augment-codeblock-min-height",h[0]+"px"),(!s||512&p[0])&&r1(e,"has-top-decorations",h[9])},i(h){s||($(l),s=!0)},o(h){F(l),s=!1},d(h){h&&S(e),n[21](null),C[r].d(),c=!1,u()}}}function x4(n,e,t){let a,i,r;I1(n,L2,k=>t(19,r=k));let{height:l=500}=e,{language:s}=e,{originalCode:c}=e,{suggestions:u}=e,{codeActions:d=[]}=e,{onCodeAction:C=m1}=e,{path:g}=e,{busy:h=!0}=e,{expanded:p=!1}=e,{scrollContainer:L}=e,{options:w={enableSplitViewResizing:!1,automaticLayout:!0,readOnly:!0,overviewRulerLanes:0,lineHeight:20,renderLineHighlight:"none",contextmenu:!1,renderSideBySide:!1,renderIndicators:!0,renderMarginRevertIcon:!1,originalEditable:!1,diffCodeLens:!1,renderOverviewRuler:!1,ignoreTrimWhitespace:!1,maxComputationTime:3e3,scrollBeyondLastColumn:0,scrollBeyondLastLine:!1,scrollPredominantAxis:!1,scrollbar:{alwaysConsumeMouseWheel:!1,vertical:"hidden",horizontal:"hidden"},cursorSurroundingLines:0,cursorSurroundingLinesStyle:"all",hideUnchangedRegions:{enabled:!p,revealLineCount:5,minimumLineCount:3,contextLineCount:5},lineNumbers:String,hover:{enabled:!1}}}=e;const b=t0.getContext().monaco;I1(n,b,k=>t(18,i=k));let y,v,Y,C1=P3(u),Q="",P=[],j=document.createElement("div");j.classList.add("c-diff-view__editor");let e1=[],l1=!1;function y3(k){const V=function(L1,{enabled:E1=!0,revealLineCount:c1=5,minimumLineCount:B2=3,contextLineCount:X1=5}={enabled:!0,revealLineCount:5,minimumLineCount:3,contextLineCount:5},b3,O1){const N2={lines:0,decorations:0,hasTopDecorations:!1};if(!L1.length||!E1)return N2;let R1=0,x3=!1;const Y1=[...L1].sort((y1,B1)=>y1.lineRange.start-B1.lineRange.start);let G=1;for(let y1=0,B1=1;y1<Y1.length;y1++,B1++){const J=Y1[y1],_3=Y1[B1];if(G+=Math.min(J.lineRange.start+1,c1),G+=O1!=null&&O1.get(J)?a3(O1.get(J)).lineCount:X2(J),J.lineRange.start-c1>1?(x3=!0,R1++):J.lineRange.start-c1==1&&G++,_3){const $3=_3.lineRange.start-J.lineRange.start;$3>X1+c1?(R1++,G+=X1,G+=c1):G+=$3}else J.lineRange.stop<b3?(R1++,G+=Math.min(b3-J.lineRange.stop,c1)):(G+=X1,G+=c1)}return{lines:Math.max(G,B2),decorations:R1,hasTopDecorations:x3}}(u,w.hideUnchangedRegions,Y2(c),e1.length>0?s3(u,e1):void 0);t(0,l=p&&k?k.getModifiedEditor().getContentHeight():V.lines*(w.lineHeight??20)+24*V.decorations),t(9,l1=V.hasTopDecorations)}function k3(){var k,V,L1,E1;(V=(k=v==null?void 0:v.getModel())==null?void 0:k.original)==null||V.dispose(),(E1=(L1=v==null?void 0:v.getModel())==null?void 0:L1.modified)==null||E1.dispose(),i&&(v||(t(6,v=i.editor.createDiffEditor(j,a)),P.push(v)),v.onDidDispose(()=>t(6,v=void 0)),P.push(...p0(v,u,c,s,g,i)),y3(v),Y==null||Y.dispose(),Y=v.onDidUpdateDiff(()=>{t(1,h=!1),t(8,e1=(v==null?void 0:v.getLineChanges())??[]),y3(v)}))}return V2(()=>{y.appendChild(j),k3()}),U2(()=>{P.forEach(k=>{var V;return(V=k==null?void 0:k.dispose)==null?void 0:V.call(k)}),j.remove()}),n.$$set=k=>{"height"in k&&t(0,l=k.height),"language"in k&&t(11,s=k.language),"originalCode"in k&&t(12,c=k.originalCode),"suggestions"in k&&t(2,u=k.suggestions),"codeActions"in k&&t(3,d=k.codeActions),"onCodeAction"in k&&t(4,C=k.onCodeAction),"path"in k&&t(13,g=k.path),"busy"in k&&t(1,h=k.busy),"expanded"in k&&t(14,p=k.expanded),"scrollContainer"in k&&t(5,L=k.scrollContainer),"options"in k&&t(15,w=k.options)},n.$$.update=()=>{if(557056&n.$$.dirty[0]&&(a={...w,theme:E3(r==null?void 0:r.category,r==null?void 0:r.intensity)}),16448&n.$$.dirty[0]&&(v==null||v.updateOptions({hideUnchangedRegions:{enabled:!p,revealLineCount:5,minimumLineCount:3,contextLineCount:5}})),786496&n.$$.dirty[0]){const k=r,V=E3(k==null?void 0:k.category,k==null?void 0:k.intensity);i==null||i.editor.setTheme(V),v==null||v.getModifiedEditor().updateOptions({theme:V}),v==null||v.getOriginalEditor().updateOptions({theme:V}),v==null||v.layout()}if(200708&n.$$.dirty[0]){const k=P3(u);C1===k&&c===Q||(t(16,C1=k),t(17,Q=c),k3())}},[l,h,u,d,C,L,v,y,e1,l1,b,s,c,g,p,w,C1,Q,i,r,()=>v==null?void 0:v.layout(),function(k){G2[k?"unshift":"push"](()=>{y=k,t(7,y)})}]}class _4 extends O{constructor(e){super(),R(this,e,x4,b4,B,{height:0,language:11,originalCode:12,suggestions:2,codeActions:3,onCodeAction:4,path:13,busy:1,expanded:14,scrollContainer:5,options:15},null,[-1,-1])}}function $4(n){let e,t,a;return t=new y2({}),{c(){e=z("div"),D(t.$$.fragment),o(e,"class","c-code-roll-item__loading svelte-1i59d33")},m(i,r){A(i,e,r),T(t,e,null),a=!0},p:_,i(i){a||($(t.$$.fragment,i),a=!0)},o(i){F(t.$$.fragment,i),a=!1},d(i){i&&S(e),I(t)}}}function A4(n){let e,t;return e=new _4({props:{onCodeAction:n[5],codeActions:n[6],suggestions:n[0],originalCode:n[2],language:n[4],path:n[1].relPath,scrollContainer:n[7]}}),{c(){D(e.$$.fragment)},m(a,i){T(e,a,i),t=!0},p(a,i){const r={};32&i&&(r.onCodeAction=a[5]),64&i&&(r.codeActions=a[6]),1&i&&(r.suggestions=a[0]),4&i&&(r.originalCode=a[2]),16&i&&(r.language=a[4]),2&i&&(r.path=a[1].relPath),128&i&&(r.scrollContainer=a[7]),e.$set(r)},i(a){t||($(e.$$.fragment,a),t=!0)},o(a){F(e.$$.fragment,a),t=!1},d(a){I(e,a)}}}function F4(n){let e,t,a,i;const r=[A4,$4],l=[];function s(c,u){return c[3]?0:1}return t=s(n),a=l[t]=r[t](n),{c(){e=z("div"),a.c(),o(e,"class","c-code-roll-item-diff svelte-1i59d33")},m(c,u){A(c,e,u),l[t].m(e,null),i=!0},p(c,[u]){let d=t;t=s(c),t===d?l[t].p(c,u):(j1(),F(l[d],1,1,()=>{l[d]=null}),M1(),a=l[t],a?a.p(c,u):(a=l[t]=r[t](c),a.c()),$(a,1),a.m(e,null))},i(c){i||($(a),i=!0)},o(c){F(a),i=!1},d(c){c&&S(e),l[t].d()}}}function S4(n,e,t){let{suggestions:a}=e,{filepath:i}=e,{originalCode:r}=e,{loaded:l=!1}=e,{language:s=o0(i.relPath)}=e,{onCodeAction:c=m1}=e,{codeActions:u=[]}=e,{scrollContainer:d}=e;return n.$$set=C=>{"suggestions"in C&&t(0,a=C.suggestions),"filepath"in C&&t(1,i=C.filepath),"originalCode"in C&&t(2,r=C.originalCode),"loaded"in C&&t(3,l=C.loaded),"language"in C&&t(4,s=C.language),"onCodeAction"in C&&t(5,c=C.onCodeAction),"codeActions"in C&&t(6,u=C.codeActions),"scrollContainer"in C&&t(7,d=C.scrollContainer)},[a,i,r,l,s,c,u,d]}class j4 extends O{constructor(e){super(),R(this,e,S4,F4,B,{suggestions:0,filepath:1,originalCode:2,loaded:3,language:4,onCodeAction:5,codeActions:6,scrollContainer:7})}}var M4=function(){this.__data__=[],this.size=0},_2=function(n,e){return n===e||n!=n&&e!=e},E4=_2,q1=function(n,e){for(var t=n.length;t--;)if(E4(n[t][0],e))return t;return-1},O4=q1,R4=Array.prototype.splice,B4=function(n){var e=this.__data__,t=O4(e,n);return!(t<0)&&(t==e.length-1?e.pop():R4.call(e,t,1),--this.size,!0)},N4=q1,z4=function(n){var e=this.__data__,t=N4(e,n);return t<0?void 0:e[t][1]},P4=q1,Z4=function(n){return P4(this.__data__,n)>-1},D4=q1,T4=function(n,e){var t=this.__data__,a=D4(t,n);return a<0?(++this.size,t.push([n,e])):t[a][1]=e,this},I4=M4,V4=B4,U4=z4,G4=Z4,W4=T4;function h1(n){var e=-1,t=n==null?0:n.length;for(this.clear();++e<t;){var a=n[e];this.set(a[0],a[1])}}h1.prototype.clear=I4,h1.prototype.delete=V4,h1.prototype.get=U4,h1.prototype.has=G4,h1.prototype.set=W4;var K1=h1,q4=K1,K4=function(){this.__data__=new q4,this.size=0},H4=function(n){var e=this.__data__,t=e.delete(n);return this.size=e.size,t},J4=function(n){return this.__data__.get(n)},X4=function(n){return this.__data__.has(n)},Y4=G1,Q4=b2,H3,$2=function(n){if(!Q4(n))return!1;var e=Y4(n);return e=="[object Function]"||e=="[object GeneratorFunction]"||e=="[object AsyncFunction]"||e=="[object Proxy]"},n3=s1["__core-js_shared__"],J3=(H3=/[^.]+$/.exec(n3&&n3.keys&&n3.keys.IE_PROTO||""))?"Symbol(src)_1."+H3:"",e5=function(n){return!!J3&&J3 in n},n5=Function.prototype.toString,A2=function(n){if(n!=null){try{return n5.call(n)}catch{}try{return n+""}catch{}}return""},t5=$2,o5=e5,i5=b2,r5=A2,a5=/^\[object .+?Constructor\]$/,s5=Function.prototype,l5=Object.prototype,c5=s5.toString,u5=l5.hasOwnProperty,d5=RegExp("^"+c5.call(u5).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),C5=function(n,e){return n==null?void 0:n[e]},h5=function(n){return!(!i5(n)||o5(n))&&(t5(n)?d5:a5).test(r5(n))},g5=C5,v1=function(n,e){var t=g5(n,e);return h5(t)?t:void 0},w3=v1(s1,"Map"),H1=v1(Object,"create"),X3=H1,f5=function(){this.__data__=X3?X3(null):{},this.size=0},p5=function(n){var e=this.has(n)&&delete this.__data__[n];return this.size-=e?1:0,e},m5=H1,v5=Object.prototype.hasOwnProperty,w5=function(n){var e=this.__data__;if(m5){var t=e[n];return t==="__lodash_hash_undefined__"?void 0:t}return v5.call(e,n)?e[n]:void 0},L5=H1,y5=Object.prototype.hasOwnProperty,k5=function(n){var e=this.__data__;return L5?e[n]!==void 0:y5.call(e,n)},b5=H1,x5=function(n,e){var t=this.__data__;return this.size+=this.has(n)?0:1,t[n]=b5&&e===void 0?"__lodash_hash_undefined__":e,this},_5=f5,$5=p5,A5=w5,F5=k5,S5=x5;function g1(n){var e=-1,t=n==null?0:n.length;for(this.clear();++e<t;){var a=n[e];this.set(a[0],a[1])}}g1.prototype.clear=_5,g1.prototype.delete=$5,g1.prototype.get=A5,g1.prototype.has=F5,g1.prototype.set=S5;var Y3=g1,j5=K1,M5=w3,E5=function(n){var e=typeof n;return e=="string"||e=="number"||e=="symbol"||e=="boolean"?n!=="__proto__":n===null},J1=function(n,e){var t=n.__data__;return E5(e)?t[typeof e=="string"?"string":"hash"]:t.map},O5=J1,R5=function(n){var e=O5(this,n).delete(n);return this.size-=e?1:0,e},B5=J1,N5=function(n){return B5(this,n).get(n)},z5=J1,P5=function(n){return z5(this,n).has(n)},Z5=J1,D5=function(n,e){var t=Z5(this,n),a=t.size;return t.set(n,e),this.size+=t.size==a?0:1,this},T5=function(){this.size=0,this.__data__={hash:new Y3,map:new(M5||j5),string:new Y3}},I5=R5,V5=N5,U5=P5,G5=D5;function f1(n){var e=-1,t=n==null?0:n.length;for(this.clear();++e<t;){var a=n[e];this.set(a[0],a[1])}}f1.prototype.clear=T5,f1.prototype.delete=I5,f1.prototype.get=V5,f1.prototype.has=U5,f1.prototype.set=G5;var F2=f1,W5=K1,q5=w3,K5=F2,H5=function(n,e){var t=this.__data__;if(t instanceof W5){var a=t.__data__;if(!q5||a.length<199)return a.push([n,e]),this.size=++t.size,this;t=this.__data__=new K5(a)}return t.set(n,e),this.size=t.size,this},J5=K1,X5=K4,Y5=H4,Q5=J4,e9=X4,n9=H5;function p1(n){var e=this.__data__=new J5(n);this.size=e.size}p1.prototype.clear=X5,p1.prototype.delete=Y5,p1.prototype.get=Q5,p1.prototype.has=e9,p1.prototype.set=n9;var t9=p1,o9=F2,i9=function(n){return this.__data__.set(n,"__lodash_hash_undefined__"),this},r9=function(n){return this.__data__.has(n)};function Z1(n){var e=-1,t=n==null?0:n.length;for(this.__data__=new o9;++e<t;)this.add(n[e])}Z1.prototype.add=Z1.prototype.push=i9,Z1.prototype.has=r9;var a9=function(n,e){return n.has(e)},s9=Z1,l9=function(n,e){for(var t=-1,a=n==null?0:n.length;++t<a;)if(e(n[t],t,n))return!0;return!1},c9=a9,S2=function(n,e,t,a,i,r){var l=1&t,s=n.length,c=e.length;if(s!=c&&!(l&&c>s))return!1;var u=r.get(n),d=r.get(e);if(u&&d)return u==e&&d==n;var C=-1,g=!0,h=2&t?new s9:void 0;for(r.set(n,e),r.set(e,n);++C<s;){var p=n[C],L=e[C];if(a)var w=l?a(L,p,C,e,n,r):a(p,L,C,n,e,r);if(w!==void 0){if(w)continue;g=!1;break}if(h){if(!l9(e,function(b,y){if(!c9(h,y)&&(p===b||i(p,b,t,a,r)))return h.push(y)})){g=!1;break}}else if(p!==L&&!i(p,L,t,a,r)){g=!1;break}}return r.delete(n),r.delete(e),g},u9=function(n){var e=-1,t=Array(n.size);return n.forEach(function(a,i){t[++e]=[i,a]}),t},Q3=s1.Uint8Array,d9=_2,C9=S2,h9=u9,g9=function(n){var e=-1,t=Array(n.size);return n.forEach(function(a){t[++e]=a}),t},e2=O3?O3.prototype:void 0,t3=e2?e2.valueOf:void 0,f9=function(n,e,t,a,i,r,l){switch(t){case"[object DataView]":if(n.byteLength!=e.byteLength||n.byteOffset!=e.byteOffset)return!1;n=n.buffer,e=e.buffer;case"[object ArrayBuffer]":return!(n.byteLength!=e.byteLength||!r(new Q3(n),new Q3(e)));case"[object Boolean]":case"[object Date]":case"[object Number]":return d9(+n,+e);case"[object Error]":return n.name==e.name&&n.message==e.message;case"[object RegExp]":case"[object String]":return n==e+"";case"[object Map]":var s=h9;case"[object Set]":var c=1&a;if(s||(s=g9),n.size!=e.size&&!c)return!1;var u=l.get(n);if(u)return u==e;a|=2,l.set(n,e);var d=C9(s(n),s(e),a,i,r,l);return l.delete(n),d;case"[object Symbol]":if(t3)return t3.call(n)==t3.call(e)}return!1},p9=function(n,e){for(var t=-1,a=e.length,i=n.length;++t<a;)n[i+t]=e[t];return n},L3=Array.isArray,m9=p9,v9=L3,w9=function(n,e,t){var a=e(n);return v9(n)?a:m9(a,t(n))},L9=function(n,e){for(var t=-1,a=n==null?0:n.length,i=0,r=[];++t<a;){var l=n[t];e(l,t,n)&&(r[i++]=l)}return r},y9=function(){return[]},k9=Object.prototype.propertyIsEnumerable,n2=Object.getOwnPropertySymbols,b9=n2?function(n){return n==null?[]:(n=Object(n),L9(n2(n),function(e){return k9.call(n,e)}))}:y9,x9=function(n,e){for(var t=-1,a=Array(n);++t<n;)a[t]=e(t);return a},_9=G1,$9=W1,t2=function(n){return $9(n)&&_9(n)=="[object Arguments]"},A9=W1,j2=Object.prototype,F9=j2.hasOwnProperty,S9=j2.propertyIsEnumerable,j9=t2(function(){return arguments}())?t2:function(n){return A9(n)&&F9.call(n,"callee")&&!S9.call(n,"callee")},d3={exports:{}},M9=function(){return!1};(function(n,e){var t=s1,a=M9,i=e&&!e.nodeType&&e,r=i&&n&&!n.nodeType&&n,l=r&&r.exports===i?t.Buffer:void 0,s=(l?l.isBuffer:void 0)||a;n.exports=s})(d3,d3.exports);var M2=d3.exports,E9=/^(?:0|[1-9]\d*)$/,O9=function(n,e){var t=typeof n;return!!(e=e??9007199254740991)&&(t=="number"||t!="symbol"&&E9.test(n))&&n>-1&&n%1==0&&n<e},E2=function(n){return typeof n=="number"&&n>-1&&n%1==0&&n<=9007199254740991},R9=G1,B9=E2,N9=W1,M={};M["[object Float32Array]"]=M["[object Float64Array]"]=M["[object Int8Array]"]=M["[object Int16Array]"]=M["[object Int32Array]"]=M["[object Uint8Array]"]=M["[object Uint8ClampedArray]"]=M["[object Uint16Array]"]=M["[object Uint32Array]"]=!0,M["[object Arguments]"]=M["[object Array]"]=M["[object ArrayBuffer]"]=M["[object Boolean]"]=M["[object DataView]"]=M["[object Date]"]=M["[object Error]"]=M["[object Function]"]=M["[object Map]"]=M["[object Number]"]=M["[object Object]"]=M["[object RegExp]"]=M["[object Set]"]=M["[object String]"]=M["[object WeakMap]"]=!1;var z9=function(n){return N9(n)&&B9(n.length)&&!!M[R9(n)]},P9=function(n){return function(e){return n(e)}},C3={exports:{}};(function(n,e){var t=i0,a=e&&!e.nodeType&&e,i=a&&n&&!n.nodeType&&n,r=i&&i.exports===a&&t.process,l=function(){try{var s=i&&i.require&&i.require("util").types;return s||r&&r.binding&&r.binding("util")}catch{}}();n.exports=l})(C3,C3.exports);var o2=C3.exports,Z9=z9,D9=P9,i2=o2&&o2.isTypedArray,O2=i2?D9(i2):Z9,T9=x9,I9=j9,V9=L3,U9=M2,G9=O9,W9=O2,q9=Object.prototype.hasOwnProperty,K9=function(n,e){var t=V9(n),a=!t&&I9(n),i=!t&&!a&&U9(n),r=!t&&!a&&!i&&W9(n),l=t||a||i||r,s=l?T9(n.length,String):[],c=s.length;for(var u in n)!e&&!q9.call(n,u)||l&&(u=="length"||i&&(u=="offset"||u=="parent")||r&&(u=="buffer"||u=="byteLength"||u=="byteOffset")||G9(u,c))||s.push(u);return s},H9=Object.prototype,J9=function(n){var e=n&&n.constructor;return n===(typeof e=="function"&&e.prototype||H9)},X9=function(n,e){return function(t){return n(e(t))}}(Object.keys,Object),Y9=J9,Q9=X9,e8=Object.prototype.hasOwnProperty,n8=function(n){if(!Y9(n))return Q9(n);var e=[];for(var t in Object(n))e8.call(n,t)&&t!="constructor"&&e.push(t);return e},t8=$2,o8=E2,i8=K9,r8=n8,a8=function(n){return n!=null&&o8(n.length)&&!t8(n)},s8=w9,l8=b9,c8=function(n){return a8(n)?i8(n):r8(n)},r2=function(n){return s8(n,c8,l8)},u8=Object.prototype.hasOwnProperty,d8=function(n,e,t,a,i,r){var l=1&t,s=r2(n),c=s.length;if(c!=r2(e).length&&!l)return!1;for(var u=c;u--;){var d=s[u];if(!(l?d in e:u8.call(e,d)))return!1}var C=r.get(n),g=r.get(e);if(C&&g)return C==e&&g==n;var h=!0;r.set(n,e),r.set(e,n);for(var p=l;++u<c;){var L=n[d=s[u]],w=e[d];if(a)var b=l?a(w,L,d,e,n,r):a(L,w,d,n,e,r);if(!(b===void 0?L===w||i(L,w,t,a,r):b)){h=!1;break}p||(p=d=="constructor")}if(h&&!p){var y=n.constructor,v=e.constructor;y==v||!("constructor"in n)||!("constructor"in e)||typeof y=="function"&&y instanceof y&&typeof v=="function"&&v instanceof v||(h=!1)}return r.delete(n),r.delete(e),h},h3=v1(s1,"DataView"),g3=w3,f3=v1(s1,"Promise"),p3=v1(s1,"Set"),m3=v1(s1,"WeakMap"),R2=G1,w1=A2,a2="[object Map]",s2="[object Promise]",l2="[object Set]",c2="[object WeakMap]",u2="[object DataView]",C8=w1(h3),h8=w1(g3),g8=w1(f3),f8=w1(p3),p8=w1(m3),u1=R2;(h3&&u1(new h3(new ArrayBuffer(1)))!=u2||g3&&u1(new g3)!=a2||f3&&u1(f3.resolve())!=s2||p3&&u1(new p3)!=l2||m3&&u1(new m3)!=c2)&&(u1=function(n){var e=R2(n),t=e=="[object Object]"?n.constructor:void 0,a=t?w1(t):"";if(a)switch(a){case C8:return u2;case h8:return a2;case g8:return s2;case f8:return l2;case p8:return c2}return e});var o3=t9,m8=S2,v8=f9,w8=d8,d2=u1,C2=L3,h2=M2,L8=O2,g2="[object Arguments]",f2="[object Array]",z1="[object Object]",p2=Object.prototype.hasOwnProperty,y8=function(n,e,t,a,i,r){var l=C2(n),s=C2(e),c=l?f2:d2(n),u=s?f2:d2(e),d=(c=c==g2?z1:c)==z1,C=(u=u==g2?z1:u)==z1,g=c==u;if(g&&h2(n)){if(!h2(e))return!1;l=!0,d=!1}if(g&&!d)return r||(r=new o3),l||L8(n)?m8(n,e,t,a,i,r):v8(n,e,c,t,a,i,r);if(!(1&t)){var h=d&&p2.call(n,"__wrapped__"),p=C&&p2.call(e,"__wrapped__");if(h||p){var L=h?n.value():n,w=p?e.value():e;return r||(r=new o3),i(L,w,t,a,r)}}return!!g&&(r||(r=new o3),w8(n,e,t,a,i,r))},m2=W1,k8=function n(e,t,a,i,r){return e===t||(e==null||t==null||!m2(e)&&!m2(t)?e!=e&&t!=t:y8(e,t,a,i,n,r))},b8=k8;const x8=K2(function(n,e){return b8(n,e)});function _8(n){let e,t;return e=new j4({props:{codeActions:n[5],loaded:n[9],suggestions:n[0],filepath:n[1],originalCode:n[8],onCodeAction:n[3],scrollContainer:n[7]}}),{c(){D(e.$$.fragment)},m(a,i){T(e,a,i),t=!0},p(a,i){const r={};32&i&&(r.codeActions=a[5]),512&i&&(r.loaded=a[9]),1&i&&(r.suggestions=a[0]),2&i&&(r.filepath=a[1]),256&i&&(r.originalCode=a[8]),8&i&&(r.onCodeAction=a[3]),128&i&&(r.scrollContainer=a[7]),e.$set(r)},i(a){t||($(e.$$.fragment,a),t=!0)},o(a){F(e.$$.fragment,a),t=!1},d(a){I(e,a)}}}function $8(n){let e,t;return e=new f0({props:{filepath:n[1],onFileAction:n[2],fileActions:n[4],slot:"summary",suggestions:n[0]}}),{c(){D(e.$$.fragment)},m(a,i){T(e,a,i),t=!0},p(a,i){const r={};2&i&&(r.filepath=a[1]),4&i&&(r.onFileAction=a[2]),16&i&&(r.fileActions=a[4]),1&i&&(r.suggestions=a[0]),e.$set(r)},i(a){t||($(e.$$.fragment,a),t=!0)},o(a){F(e.$$.fragment,a),t=!1},d(a){I(e,a)}}}function A8(n){let e,t;return e=new C0({props:{open:!0,class:"c-code-roll-item ",expandable:n[6],$$slots:{summary:[$8],default:[_8]},$$scope:{ctx:n}}}),{c(){D(e.$$.fragment)},m(a,i){T(e,a,i),t=!0},p(a,[i]){const r={};64&i&&(r.expandable=a[6]),17343&i&&(r.$$scope={dirty:i,ctx:a}),e.$set(r)},i(a){t||($(e.$$.fragment,a),t=!0)},o(a){F(e.$$.fragment,a),t=!1},d(a){I(e,a)}}}function F8(n,e,t){let{suggestions:a}=e,{filepath:i}=e,{readFile:r}=e,{onFileAction:l=m1}=e,{onCodeAction:s=m1}=e,{fileActions:c=[]}=e,{codeActions:u=[]}=e,{expandable:d=!0}=e,{scrollContainer:C}=e,g="",h=!1,p=null,L=null;return n.$$set=w=>{"suggestions"in w&&t(0,a=w.suggestions),"filepath"in w&&t(1,i=w.filepath),"readFile"in w&&t(10,r=w.readFile),"onFileAction"in w&&t(2,l=w.onFileAction),"onCodeAction"in w&&t(3,s=w.onCodeAction),"fileActions"in w&&t(4,c=w.fileActions),"codeActions"in w&&t(5,u=w.codeActions),"expandable"in w&&t(6,d=w.expandable),"scrollContainer"in w&&t(7,C=w.scrollContainer)},n.$$.update=()=>{6147&n.$$.dirty&&(x8(a,p)&&L!==null&&S3(i,L)||(L!==null&&S3(i,L)||t(9,h=!1),t(12,L=i),t(11,p=a),async function(w){t(8,g=await r(w)),t(9,h=!0)}(i)))},[a,i,l,s,c,u,d,C,g,h,r,p,L]}class I8 extends O{constructor(e){super(),R(this,e,F8,A8,B,{suggestions:0,filepath:1,readFile:10,onFileAction:2,onCodeAction:3,fileActions:4,codeActions:5,expandable:6,scrollContainer:7})}}function V8(n){return async function(e){const t=await n.send({type:Q2.readFileRequest,data:{pathName:e}});if("error"in t.data)throw new Error(t.data.error);return t.data.content}}export{I8 as C,C0 as D,m4 as P,x0 as a,I3 as b,V3 as c,T8 as d,b0 as e,V8 as f,k0 as g,l3 as s};
