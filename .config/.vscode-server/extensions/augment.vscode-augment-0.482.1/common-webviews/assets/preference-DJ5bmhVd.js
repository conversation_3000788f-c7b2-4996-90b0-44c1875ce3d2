import{S as T,i as V,s as X,V as f,I as b,c,X as E,e as I,f as r,a8 as L,n as j,h as S,aa as $e,L as se,M as ie,a7 as ue,ad as ge,C as O,D as P,q as re,t as D,r as ce,u as B,F as z,ao as ve,a6 as be,A as ee,B as te,E as ne,J as ye}from"./SpinnerAugment-Cx9dt_ox.js";import{h as H,W as Q}from"./BaseButton-BqzdgpkK.js";import{aq as le}from"./AugmentMessage-kCRDis1x.js";import{C as we,S as ke}from"./folder-CEjIF7oG.js";import{M as Ce}from"./TextTooltipAugment-DTMpOwfF.js";import{s as xe}from"./check-BrrMO4vE.js";import{M as qe}from"./index-8X-F_Twk.js";import"./CalloutAugment-BFrX0piu.js";import"./Content-BiWRcmeV.js";import"./globals-D0QH3NT1.js";import"./lodash-Drc0SN5U.js";import"./types-8LwCBeyq.js";import"./chat-types-B-te1sXh.js";import"./file-paths-BcSg4gks.js";import"./diff-utils-C7XQLqYW.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-BxQII05L.js";import"./keypress-DD1aQVr0.js";import"./folder-opened-CX_GXeEO.js";import"./await_block-H61A9-v_.js";import"./CollapseButtonAugment-D3vAw6HE.js";import"./IconButtonAugment-BjDqXmYl.js";import"./ButtonAugment-DhtPLzGu.js";import"./expand-CURYX9ur.js";import"./index-CGnj6T3o.js";import"./CardAugment-RumqAz-v.js";import"./MaterialIcon-8-Z76Y2_.js";import"./CopyButton-CugjC8Pf.js";import"./magnifying-glass-Fv6Gz5Ea.js";import"./github-7gPAsyj4.js";import"./open-in-new-window-C_TwPNdv.js";import"./ellipsis-Cm0UKVWz.js";import"./IconFilePath-B4JAagx1.js";import"./LanguageIcon-FVMxq7uD.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-D-BqE8vd.js";import"./chevron-down-DYf4hfS2.js";import"./mcp-logo-DslCzNpc.js";import"./terminal-BjJSzToG.js";import"./pen-to-square-CZwCjcp0.js";import"./utils-DJhaageo.js";import"./types-DDm27S8B.js";import"./augment-logo-DdgjewTP.js";import"./types-CGlLNakm.js";import"./isObjectLike-BWVRxMGM.js";import"./TextAreaAugment-DEYj-_0J.js";function pe(s){let t,n;return{c(){t=f("div"),n=se(s[1]),c(t,"class","header svelte-1894wv4")},m(e,i){I(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&S(t)}}}function Ae(s){let t,n,e,i,o,l,p,h,d,u,A,y,$,x,k,w,m,M,q=s[1]&&pe(s);return{c(){t=f("div"),q&&q.c(),n=b(),e=f("div"),i=f("button"),i.textContent="A",o=b(),l=f("button"),l.textContent="A",p=b(),h=f("button"),h.textContent="A",d=b(),u=f("button"),u.textContent="=",A=b(),y=f("button"),y.textContent="B",$=b(),x=f("button"),x.textContent="B",k=b(),w=f("button"),w.textContent="B",c(i,"type","button"),c(i,"class","button large svelte-1894wv4"),E(i,"highlighted",s[0]==="A3"),c(l,"type","button"),c(l,"class","button medium svelte-1894wv4"),E(l,"highlighted",s[0]==="A2"),c(h,"type","button"),c(h,"class","button small svelte-1894wv4"),E(h,"highlighted",s[0]==="A1"),c(u,"type","button"),c(u,"class","button equal svelte-1894wv4"),E(u,"highlighted",s[0]==="="),c(y,"type","button"),c(y,"class","button small svelte-1894wv4"),E(y,"highlighted",s[0]==="B1"),c(x,"type","button"),c(x,"class","button medium svelte-1894wv4"),E(x,"highlighted",s[0]==="B2"),c(w,"type","button"),c(w,"class","button large svelte-1894wv4"),E(w,"highlighted",s[0]==="B3"),c(e,"class","buttons svelte-1894wv4"),c(t,"class","container svelte-1894wv4")},m(g,R){I(g,t,R),q&&q.m(t,null),r(t,n),r(t,e),r(e,i),r(e,o),r(e,l),r(e,p),r(e,h),r(e,d),r(e,u),r(e,A),r(e,y),r(e,$),r(e,x),r(e,k),r(e,w),m||(M=[L(i,"click",s[3]),L(l,"click",s[4]),L(h,"click",s[5]),L(u,"click",s[6]),L(y,"click",s[7]),L(x,"click",s[8]),L(w,"click",s[9])],m=!0)},p(g,[R]){g[1]?q?q.p(g,R):(q=pe(g),q.c(),q.m(t,n)):q&&(q.d(1),q=null),1&R&&E(i,"highlighted",g[0]==="A3"),1&R&&E(l,"highlighted",g[0]==="A2"),1&R&&E(h,"highlighted",g[0]==="A1"),1&R&&E(u,"highlighted",g[0]==="="),1&R&&E(y,"highlighted",g[0]==="B1"),1&R&&E(x,"highlighted",g[0]==="B2"),1&R&&E(w,"highlighted",g[0]==="B3")},i:j,o:j,d(g){g&&S(t),q&&q.d(),m=!1,$e(M)}}}function Be(s,t,n){let{selected:e=null}=t,{question:i=null}=t;function o(l){n(0,e=l)}return s.$$set=l=>{"selected"in l&&n(0,e=l.selected),"question"in l&&n(1,i=l.question)},[e,i,o,()=>o("A3"),()=>o("A2"),()=>o("A1"),()=>o("="),()=>o("B1"),()=>o("B2"),()=>o("B3")]}class ae extends T{constructor(t){super(),V(this,t,Be,Ae,X,{selected:0,question:1})}}function de(s){let t,n;return{c(){t=f("div"),n=se(s[1]),c(t,"class","question svelte-1i0f73l")},m(e,i){I(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&S(t)}}}function De(s){let t,n,e,i,o,l=s[1]&&de(s);return{c(){t=f("div"),l&&l.c(),n=b(),e=f("textarea"),c(e,"class","input svelte-1i0f73l"),c(e,"placeholder",s[2]),c(e,"rows","3"),c(t,"class","container svelte-1i0f73l")},m(p,h){I(p,t,h),l&&l.m(t,null),r(t,n),r(t,e),ue(e,s[0]),i||(o=L(e,"input",s[3]),i=!0)},p(p,[h]){p[1]?l?l.p(p,h):(l=de(p),l.c(),l.m(t,n)):l&&(l.d(1),l=null),4&h&&c(e,"placeholder",p[2]),1&h&&ue(e,p[0])},i:j,o:j,d(p){p&&S(t),l&&l.d(),i=!1,o()}}}function Me(s,t,n){let{value:e=""}=t,{question:i=null}=t,{placeholder:o=""}=t;return s.$$set=l=>{"value"in l&&n(0,e=l.value),"question"in l&&n(1,i=l.question),"placeholder"in l&&n(2,o=l.placeholder)},[e,i,o,function(){e=this.value,n(0,e)}]}class Re extends T{constructor(t){super(),V(this,t,Me,De,X,{value:0,question:1,placeholder:2})}}function Ie(s){let t,n,e,i;return{c(){t=f("button"),n=se(s[0]),c(t,"class","button svelte-2k5n")},m(o,l){I(o,t,l),r(t,n),e||(i=L(t,"click",function(){ge(s[1])&&s[1].apply(this,arguments)}),e=!0)},p(o,[l]){s=o,1&l&&ie(n,s[0])},i:j,o:j,d(o){o&&S(t),e=!1,i()}}}function Se(s,t,n){let{label:e="Submit"}=t,{onClick:i}=t;return s.$$set=o=>{"label"in o&&n(0,e=o.label),"onClick"in o&&n(1,i=o.onClick)},[e,i]}class We extends T{constructor(t){super(),V(this,t,Se,Ie,X,{label:0,onClick:1})}}function me(s){let t,n;return{c(){t=f("div"),n=se(s[1])},m(e,i){I(e,t,i),r(t,n)},p(e,i){2&i&&ie(n,e[1])},d(e){e&&S(t)}}}function _e(s){let t,n,e,i,o,l,p,h,d=s[1]&&me(s);return{c(){t=f("div"),d&&d.c(),n=b(),e=f("label"),i=f("input"),o=b(),l=f("span"),c(i,"type","checkbox"),c(i,"class","svelte-n0uy88"),c(l,"class","svelte-n0uy88"),c(e,"class","custom-checkbox svelte-n0uy88"),c(t,"class","container svelte-n0uy88")},m(u,A){I(u,t,A),d&&d.m(t,null),r(t,n),r(t,e),r(e,i),i.checked=s[0],r(e,o),r(e,l),p||(h=L(i,"change",s[2]),p=!0)},p(u,[A]){u[1]?d?d.p(u,A):(d=me(u),d.c(),d.m(t,n)):d&&(d.d(1),d=null),1&A&&(i.checked=u[0])},i:j,o:j,d(u){u&&S(t),d&&d.d(),p=!1,h()}}}function Ee(s,t,n){let{isChecked:e=!1}=t,{question:i=null}=t;return s.$$set=o=>{"isChecked"in o&&n(0,e=o.isChecked),"question"in o&&n(1,i=o.question)},[e,i,function(){e=this.checked,n(0,e)}]}class Fe extends T{constructor(t){super(),V(this,t,Ee,_e,X,{isChecked:0,question:1})}}function Le(s){let t;return{c(){t=f("p"),t.textContent="Streaming in progress... Please wait for both responses to complete."},m(n,e){I(n,t,e)},p:j,i:j,o:j,d(n){n&&S(t)}}}function Oe(s){let t,n,e,i,o,l,p,h,d,u,A,y,$,x,k,w,m;function M(a){s[12](a)}let q={question:"Which response is formatted better? (e.g. level of detail style, structure)?"};function g(a){s[13](a)}s[2]!==void 0&&(q.selected=s[2]),t=new ae({props:q}),ee.push(()=>te(t,"selected",M));let R={question:"Which response follows your instruction better?"};function G(a){s[14](a)}s[3]!==void 0&&(R.selected=s[3]),i=new ae({props:R}),ee.push(()=>te(i,"selected",g));let K={question:"Which response is better overall?"};function W(a){s[15](a)}s[1]!==void 0&&(K.selected=s[1]),p=new ae({props:K}),ee.push(()=>te(p,"selected",G));let _={question:s[9]};function N(a){s[16](a)}s[5]!==void 0&&(_.isChecked=s[5]),u=new Fe({props:_}),ee.push(()=>te(u,"isChecked",W));let U={question:"Any additional feedback?",placeholder:"Please explain your answers to the above questions."};return s[4]!==void 0&&(U.value=s[4]),$=new Re({props:U}),ee.push(()=>te($,"value",N)),w=new We({props:{label:"Submit",onClick:s[10]}}),{c(){O(t.$$.fragment),e=b(),O(i.$$.fragment),l=b(),O(p.$$.fragment),d=b(),O(u.$$.fragment),y=b(),O($.$$.fragment),k=b(),O(w.$$.fragment)},m(a,C){P(t,a,C),I(a,e,C),P(i,a,C),I(a,l,C),P(p,a,C),I(a,d,C),P(u,a,C),I(a,y,C),P($,a,C),I(a,k,C),P(w,a,C),m=!0},p(a,C){const v={};!n&&4&C&&(n=!0,v.selected=a[2],ne(()=>n=!1)),t.$set(v);const F={};!o&&8&C&&(o=!0,F.selected=a[3],ne(()=>o=!1)),i.$set(F);const Y={};!h&&2&C&&(h=!0,Y.selected=a[1],ne(()=>h=!1)),p.$set(Y);const J={};512&C&&(J.question=a[9]),!A&&32&C&&(A=!0,J.isChecked=a[5],ne(()=>A=!1)),u.$set(J);const Z={};!x&&16&C&&(x=!0,Z.value=a[4],ne(()=>x=!1)),$.$set(Z)},i(a){m||(B(t.$$.fragment,a),B(i.$$.fragment,a),B(p.$$.fragment,a),B(u.$$.fragment,a),B($.$$.fragment,a),B(w.$$.fragment,a),m=!0)},o(a){D(t.$$.fragment,a),D(i.$$.fragment,a),D(p.$$.fragment,a),D(u.$$.fragment,a),D($.$$.fragment,a),D(w.$$.fragment,a),m=!1},d(a){a&&(S(e),S(l),S(d),S(y),S(k)),z(t,a),z(i,a),z(p,a),z(u,a),z($,a),z(w,a)}}}function Pe(s){let t,n,e,i,o,l,p,h,d,u,A,y,$,x,k,w,m,M,q,g,R,G,K,W,_,N;o=new le({props:{markdown:s[0].data.a.message}}),$=new le({props:{markdown:s[8]}}),g=new le({props:{markdown:s[7]}});const U=[Oe,Le],a=[];function C(v,F){return v[6]?0:1}return W=C(s),_=a[W]=U[W](s),{c(){t=f("main"),n=f("div"),e=f("h1"),e.textContent="Input message",i=b(),O(o.$$.fragment),l=b(),p=f("hr"),h=b(),d=f("div"),u=f("div"),A=f("h1"),A.textContent="Option A",y=b(),O($.$$.fragment),x=b(),k=f("div"),w=b(),m=f("div"),M=f("h1"),M.textContent="Option B",q=b(),O(g.$$.fragment),R=b(),G=f("hr"),K=b(),_.c(),c(e,"class","svelte-751nif"),c(p,"class","l-side-by-side svelte-751nif"),c(A,"class","svelte-751nif"),c(u,"class","l-side-by-side__child svelte-751nif"),c(k,"class","divider svelte-751nif"),c(M,"class","svelte-751nif"),c(m,"class","l-side-by-side__child svelte-751nif"),c(d,"class","l-side-by-side svelte-751nif"),c(G,"class","svelte-751nif"),c(n,"class","l-pref svelte-751nif")},m(v,F){I(v,t,F),r(t,n),r(n,e),r(n,i),P(o,n,null),r(n,l),r(n,p),r(n,h),r(n,d),r(d,u),r(u,A),r(u,y),P($,u,null),r(d,x),r(d,k),r(d,w),r(d,m),r(m,M),r(m,q),P(g,m,null),r(n,R),r(n,G),r(n,K),a[W].m(n,null),N=!0},p(v,[F]){const Y={};1&F&&(Y.markdown=v[0].data.a.message),o.$set(Y);const J={};256&F&&(J.markdown=v[8]),$.$set(J);const Z={};128&F&&(Z.markdown=v[7]),g.$set(Z);let oe=W;W=C(v),W===oe?a[W].p(v,F):(re(),D(a[oe],1,1,()=>{a[oe]=null}),ce(),_=a[W],_?_.p(v,F):(_=a[W]=U[W](v),_.c()),B(_,1),_.m(n,null))},i(v){N||(B(o.$$.fragment,v),B($.$$.fragment,v),B(g.$$.fragment,v),B(_),N=!0)},o(v){D(o.$$.fragment,v),D($.$$.fragment,v),D(g.$$.fragment,v),D(_),N=!1},d(v){v&&S(t),z(o),z($),z(g),a[W].d()}}}function ze(s,t,n){let e,i,o,{inputData:l}=t;const p=ve();let h=new we(new Ce(H),H,new ke);xe(h);let d=null,u=null,A=null,y=null,$="",x=!1,k={a:null,b:null},w=l.data.a.response.length>0&&l.data.b.response.length>0;return be(()=>{window.addEventListener("message",m=>{const M=m.data;M.type===Q.chatModelReply?(M.stream==="A"?n(11,k.a=M.data.text,k):M.stream==="B"&&n(11,k.b=M.data.text,k),n(11,k)):M.type===Q.chatStreamDone&&n(6,w=!0)})}),s.$$set=m=>{"inputData"in m&&n(0,l=m.inputData)},s.$$.update=()=>{var m;2&s.$$.dirty&&n(9,e=(m=y)==="="||m===null?"Is this a high quality comparison?":`Are you completely happy with response '${m.startsWith("A")?"A":"B"}'?`),2049&s.$$.dirty&&n(8,i=k.a!==null?k.a:l.data.a.response),2049&s.$$.dirty&&n(7,o=k.b!==null?k.b:l.data.b.response),1&s.$$.dirty&&n(6,w=l.data.a.response.length>0&&l.data.b.response.length>0)},[l,y,d,u,$,x,w,o,i,e,function(){if(A="=",y===null)return void p("notify","Overall rating is required");p("result",{overallRating:y,formattingRating:d||"=",hallucinationRating:A||"=",instructionFollowingRating:u||"=",isHighQuality:x,textFeedback:$})},k,function(m){d=m,n(2,d)},function(m){u=m,n(3,u)},function(m){y=m,n(1,y)},function(m){x=m,n(5,x)},function(m){$=m,n(4,$)}]}class je extends T{constructor(t){super(),V(this,t,ze,Pe,X,{inputData:0})}}function fe(s){let t,n,e=s[0].type==="Chat"&&he(s);return{c(){e&&e.c(),t=ye()},m(i,o){e&&e.m(i,o),I(i,t,o),n=!0},p(i,o){i[0].type==="Chat"?e?(e.p(i,o),1&o&&B(e,1)):(e=he(i),e.c(),B(e,1),e.m(t.parentNode,t)):e&&(re(),D(e,1,1,()=>{e=null}),ce())},i(i){n||(B(e),n=!0)},o(i){D(e),n=!1},d(i){i&&S(t),e&&e.d(i)}}}function he(s){let t,n;return t=new je({props:{inputData:s[0]}}),t.$on("result",s[2]),t.$on("notify",s[3]),{c(){O(t.$$.fragment)},m(e,i){P(t,e,i),n=!0},p(e,i){const o={};1&i&&(o.inputData=e[0]),t.$set(o)},i(e){n||(B(t.$$.fragment,e),n=!0)},o(e){D(t.$$.fragment,e),n=!1},d(e){z(t,e)}}}function He(s){let t,n,e=s[0]&&fe(s);return{c(){t=f("main"),e&&e.c()},m(i,o){I(i,t,o),e&&e.m(t,null),n=!0},p(i,o){i[0]?e?(e.p(i,o),1&o&&B(e,1)):(e=fe(i),e.c(),B(e,1),e.m(t,null)):e&&(re(),D(e,1,1,()=>{e=null}),ce())},i(i){n||(B(e),n=!0)},o(i){D(e),n=!1},d(i){i&&S(t),e&&e.d()}}}function Ne(s){let t,n,e,i;return t=new qe.Root({props:{$$slots:{default:[He]},$$scope:{ctx:s}}}),{c(){O(t.$$.fragment)},m(o,l){P(t,o,l),n=!0,e||(i=L(window,"message",s[1]),e=!0)},p(o,[l]){const p={};17&l&&(p.$$scope={dirty:l,ctx:o}),t.$set(p)},i(o){n||(B(t.$$.fragment,o),n=!0)},o(o){D(t.$$.fragment,o),n=!1},d(o){z(t,o),e=!1,i()}}}function Je(s,t,n){let e;return H.postMessage({type:Q.preferencePanelLoaded}),[e,function(i){const o=i.data;o.type===Q.preferenceInit&&n(0,e=o.data)},function(i){const o=i.detail;H.postMessage({type:Q.preferenceResultMessage,data:o})},function(i){H.postMessage({type:Q.preferenceNotify,data:i.detail})}]}class Qe extends T{constructor(t){super(),V(this,t,Je,Ne,X,{})}}(async function(){H&&H.initialize&&await H.initialize(),new Qe({target:document.getElementById("app")})})();
