[{"identifier": {"id": "github.copilot-chat", "uuid": "7ec7d6e6-b89e-4cc5-a59b-d6c4d238246f"}, "version": "0.28.1", "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/github.copilot-chat-0.28.1", "scheme": "file"}, "relativeLocation": "github.copilot-chat-0.28.1", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1750442927880, "pinned": false, "source": "gallery", "id": "7ec7d6e6-b89e-4cc5-a59b-d6c4d238246f", "publisherId": "7c1c19cd-78eb-4dfb-8999-99caf7679002", "publisherDisplayName": "GitHub", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "github.copilot", "uuid": "23c4aeee-f844-43cd-b53e-1113e483f1a6"}, "version": "1.336.0", "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/github.copilot-1.336.0", "scheme": "file"}, "relativeLocation": "github.copilot-1.336.0", "metadata": {"isApplicationScoped": false, "isMachineScoped": false, "isBuiltin": false, "installedTimestamp": 1750442927879, "pinned": false, "source": "gallery", "id": "23c4aeee-f844-43cd-b53e-1113e483f1a6", "publisherId": "7c1c19cd-78eb-4dfb-8999-99caf7679002", "publisherDisplayName": "GitHub", "targetPlatform": "undefined", "updated": true, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false, "preRelease": false}}, {"identifier": {"id": "augment.vscode-augment", "uuid": "fc0e137d-e132-47ed-9455-c4636fa5b897"}, "version": "0.482.1", "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/augment.vscode-augment-0.482.1", "scheme": "file"}, "relativeLocation": "augment.vscode-augment-0.482.1", "metadata": {"installedTimestamp": 1750443003247, "source": "gallery", "id": "fc0e137d-e132-47ed-9455-c4636fa5b897", "publisherId": "7814b14b-491a-4e83-83ac-9222fa835050", "publisherDisplayName": "Augment Computing", "targetPlatform": "undefined", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "anthropic.claude-code"}, "version": "1.0.11", "location": {"$mid": 1, "path": "/home/<USER>/.vscode-server/extensions/anthropic.claude-code-1.0.11", "scheme": "file"}, "relativeLocation": "anthropic.claude-code-1.0.11", "metadata": {"isMachineScoped": true, "installedTimestamp": 1750443065327, "pinned": true, "source": "vsix", "id": "3c13ae49-babe-45fe-8c48-5e45077a62bf", "publisherDisplayName": "Anthropic", "publisherId": "89769da0-cc4b-40b0-8216-93ffb5a96b56", "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}]