<?xml version="1.0" encoding="utf-8"?>
	<PackageManifest Version="2.0.0" xmlns="http://schemas.microsoft.com/developer/vsx-schema/2011" xmlns:d="http://schemas.microsoft.com/developer/vsx-schema-design/2011">
		<Metadata>
			<Identity Language="en-US" Id="claude-code" Version="1.0.11" Publisher="Anthropic" />
			<DisplayName>Claude Code</DisplayName>
			<Description xml:space="preserve">Claude Code for VS Code: Harness the power of Claude Code without leaving your IDE</Description>
			<Tags>keybindings</Tags>
			<Categories>Other</Categories>
			<GalleryFlags>Public</GalleryFlags>
			
			<Properties>
				<Property Id="Microsoft.VisualStudio.Code.Engine" Value="^1.94.0" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionDependencies" Value="" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionPack" Value="" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionKind" Value="workspace" />
				<Property Id="Microsoft.VisualStudio.Code.LocalizedLanguages" Value="" />
				<Property Id="Microsoft.VisualStudio.Code.EnabledApiProposals" Value="" />
				
				<Property Id="Microsoft.VisualStudio.Code.ExecutesCode" Value="true" />
				
				<Property Id="Microsoft.VisualStudio.Services.Links.Source" Value="https://github.com/anthropic-labs/vscode-mcp.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Getstarted" Value="https://github.com/anthropic-labs/vscode-mcp.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.GitHub" Value="https://github.com/anthropic-labs/vscode-mcp.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Support" Value="https://github.com/anthropic-labs/vscode-mcp/issues" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Learn" Value="https://github.com/anthropic-labs/vscode-mcp#readme" />
				
				
				<Property Id="Microsoft.VisualStudio.Services.GitHubFlavoredMarkdown" Value="true" />
				<Property Id="Microsoft.VisualStudio.Services.Content.Pricing" Value="Free"/>

				
				
			</Properties>
			
			<Icon>extension/resources/claude-logo.png</Icon>
		</Metadata>
		<Installation>
			<InstallationTarget Id="Microsoft.VisualStudio.Code"/>
		</Installation>
		<Dependencies/>
		<Assets>
			<Asset Type="Microsoft.VisualStudio.Code.Manifest" Path="extension/package.json" Addressable="true" />
			<Asset Type="Microsoft.VisualStudio.Services.Content.Details" Path="extension/README.md" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Icons.Default" Path="extension/resources/claude-logo.png" Addressable="true" />
		</Assets>
	</PackageManifest>