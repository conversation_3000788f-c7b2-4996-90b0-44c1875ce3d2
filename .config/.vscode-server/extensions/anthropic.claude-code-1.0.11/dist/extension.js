"use strict";var Ba=Object.create;var Gt=Object.defineProperty;var Va=Object.getOwnPropertyDescriptor;var Ha=Object.getOwnPropertyNames;var za=Object.getPrototypeOf,Wa=Object.prototype.hasOwnProperty;var W=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),Ga=(r,e)=>{for(var t in e)Gt(r,t,{get:e[t],enumerable:!0})},Xn=(r,e,t,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of Ha(e))!Wa.call(r,s)&&s!==t&&Gt(r,s,{get:()=>e[s],enumerable:!(n=Va(e,s))||n.enumerable});return r};var $=(r,e,t)=>(t=r!=null?Ba(za(r)):{},Xn(e||!r||!r.__esModule?Gt(t,"default",{value:r,enumerable:!0}):t,r)),Ja=r=>Xn(Gt({},"__esModule",{value:!0}),r);var ts=W((au,es)=>{"use strict";es.exports=function(e){return e.map(function(t){return t===""?"''":t&&typeof t=="object"?t.op.replace(/(.)/g,"\\$1"):/["\s]/.test(t)&&!/'/.test(t)?"'"+t.replace(/(['\\])/g,"\\$1")+"'":/["'\s]/.test(t)?'"'+t.replace(/(["\\$`!])/g,"\\$1")+'"':String(t).replace(/([A-Za-z]:)?([#!"$&'()*,:;<=>?@[\\\]^`{|}])/g,"$1\\$2")}).join(" ")}});var cs=W((cu,as)=>{"use strict";var rs="(?:"+["\\|\\|","\\&\\&",";;","\\|\\&","\\<\\(","\\<\\<\\<",">>",">\\&","<\\&","[&;()|<>]"].join("|")+")",ns=new RegExp("^"+rs+"$"),ss="|&;()<> \\t",ec='"((\\\\"|[^"])*?)"',tc="'((\\\\'|[^'])*?)'",rc=/^#$/,is="'",os='"',Xr="$",tt="",nc=4294967296;for(Yr=0;Yr<4;Yr++)tt+=(nc*Math.random()).toString(16);var sc=new RegExp("^"+tt);function ic(r,e){for(var t=e.lastIndex,n=[],s;s=e.exec(r);)n.push(s),e.lastIndex===s.index&&(e.lastIndex+=1);return e.lastIndex=t,n}function oc(r,e,t){var n=typeof r=="function"?r(t):r[t];return typeof n>"u"&&t!=""?n="":typeof n>"u"&&(n="$"),typeof n=="object"?e+tt+JSON.stringify(n)+tt:e+n}function ac(r,e,t){t||(t={});var n=t.escape||"\\",s="(\\"+n+`['"`+ss+`]|[^\\s'"`+ss+"])+",i=new RegExp(["("+rs+")","("+s+"|"+ec+"|"+tc+")+"].join("|"),"g"),o=ic(r,i);if(o.length===0)return[];e||(e={});var a=!1;return o.map(function(l){var d=l[0];if(!d||a)return;if(ns.test(d))return{op:d};var u=!1,f=!1,g="",v=!1,h;function x(){h+=1;var D,O,P=d.charAt(h);if(P==="{"){if(h+=1,d.charAt(h)==="}")throw new Error("Bad substitution: "+d.slice(h-2,h+1));if(D=d.indexOf("}",h),D<0)throw new Error("Bad substitution: "+d.slice(h));O=d.slice(h,D),h=D}else if(/[*@#?$!_-]/.test(P))O=P,h+=1;else{var M=d.slice(h);D=M.match(/[^\w\d_]/),D?(O=M.slice(0,D.index),h+=D.index-1):(O=M,h=d.length)}return oc(e,"",O)}for(h=0;h<d.length;h++){var w=d.charAt(h);if(v=v||!u&&(w==="*"||w==="?"),f)g+=w,f=!1;else if(u)w===u?u=!1:u==is?g+=w:w===n?(h+=1,w=d.charAt(h),w===os||w===n||w===Xr?g+=w:g+=n+w):w===Xr?g+=x():g+=w;else if(w===os||w===is)u=w;else{if(ns.test(w))return{op:d};if(rc.test(w)){a=!0;var T={comment:r.slice(l.index+h+1)};return g.length?[g,T]:[T]}else w===n?f=!0:w===Xr?g+=x():g+=w}}return v?{op:"glob",pattern:g}:g}).reduce(function(l,d){return typeof d>"u"?l:l.concat(d)},[])}as.exports=function(e,t,n){var s=ac(e,t,n);return typeof t!="function"?s:s.reduce(function(i,o){if(typeof o=="object")return i.concat(o);var a=o.split(RegExp("("+tt+".*?"+tt+")","g"));return a.length===1?i.concat(a[0]):i.concat(a.filter(Boolean).map(function(l){return sc.test(l)?JSON.parse(l.split(tt)[1]):l}))},[])};var Yr});var ls=W(Kr=>{"use strict";Kr.quote=ts(),Kr.parse=cs()});var Te=W((Hp,Oo)=>{"use strict";var Po=["nodebuffer","arraybuffer","fragments"],Ro=typeof Blob<"u";Ro&&Po.push("blob"),Oo.exports={BINARY_TYPES:Po,EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",hasBlob:Ro,kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}});var jt=W((zp,Dr)=>{"use strict";var{EMPTY_BUFFER:Al}=Te(),wn=Buffer[Symbol.species];function Il(r,e){if(r.length===0)return Al;if(r.length===1)return r[0];let t=Buffer.allocUnsafe(e),n=0;for(let s=0;s<r.length;s++){let i=r[s];t.set(i,n),n+=i.length}return n<e?new wn(t.buffer,t.byteOffset,n):t}function No(r,e,t,n,s){for(let i=0;i<s;i++)t[n+i]=r[i]^e[i&3]}function Do(r,e){for(let t=0;t<r.length;t++)r[t]^=e[t&3]}function Ll(r){return r.length===r.buffer.byteLength?r.buffer:r.buffer.slice(r.byteOffset,r.byteOffset+r.length)}function Sn(r){if(Sn.readOnly=!0,Buffer.isBuffer(r))return r;let e;return r instanceof ArrayBuffer?e=new wn(r):ArrayBuffer.isView(r)?e=new wn(r.buffer,r.byteOffset,r.byteLength):(e=Buffer.from(r),Sn.readOnly=!1),e}if(Dr.exports={concat:Il,mask:No,toArrayBuffer:Ll,toBuffer:Sn,unmask:Do},!process.env.WS_NO_BUFFER_UTIL)try{let r=require("bufferutil");Dr.exports.mask=function(e,t,n,s,i){i<48?No(e,t,n,s,i):r.mask(e,t,n,s,i)},Dr.exports.unmask=function(e,t){e.length<32?Do(e,t):r.unmask(e,t)}}catch{}});var Lo=W((Wp,Io)=>{"use strict";var Ao=Symbol("kDone"),En=Symbol("kRun"),kn=class{constructor(e){this[Ao]=()=>{this.pending--,this[En]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[En]()}[En](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[Ao])}}};Io.exports=kn});var Zt=W((Gp,Fo)=>{"use strict";var Ft=require("zlib"),Mo=jt(),Ml=Lo(),{kStatusCode:$o}=Te(),$l=Buffer[Symbol.species],jl=Buffer.from([0,0,255,255]),Ar=Symbol("permessage-deflate"),Pe=Symbol("total-length"),Ut=Symbol("callback"),Ye=Symbol("buffers"),Ir=Symbol("error"),Lr,Cn=class{constructor(e,t,n){if(this._maxPayload=n|0,this._options=e||{},this._threshold=this._options.threshold!==void 0?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,!Lr){let s=this._options.concurrencyLimit!==void 0?this._options.concurrencyLimit:10;Lr=new Ml(s)}}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:this._options.clientMaxWindowBits==null&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[Ut];this._deflate.close(),this._deflate=null,e&&e(new Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,n=e.find(s=>!(t.serverNoContextTakeover===!1&&s.server_no_context_takeover||s.server_max_window_bits&&(t.serverMaxWindowBits===!1||typeof t.serverMaxWindowBits=="number"&&t.serverMaxWindowBits>s.server_max_window_bits)||typeof t.clientMaxWindowBits=="number"&&!s.client_max_window_bits));if(!n)throw new Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(n.server_no_context_takeover=!0),t.clientNoContextTakeover&&(n.client_no_context_takeover=!0),typeof t.serverMaxWindowBits=="number"&&(n.server_max_window_bits=t.serverMaxWindowBits),typeof t.clientMaxWindowBits=="number"?n.client_max_window_bits=t.clientMaxWindowBits:(n.client_max_window_bits===!0||t.clientMaxWindowBits===!1)&&delete n.client_max_window_bits,n}acceptAsClient(e){let t=e[0];if(this._options.clientNoContextTakeover===!1&&t.client_no_context_takeover)throw new Error('Unexpected parameter "client_no_context_takeover"');if(!t.client_max_window_bits)typeof this._options.clientMaxWindowBits=="number"&&(t.client_max_window_bits=this._options.clientMaxWindowBits);else if(this._options.clientMaxWindowBits===!1||typeof this._options.clientMaxWindowBits=="number"&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw new Error('Unexpected or invalid parameter "client_max_window_bits"');return t}normalizeParams(e){return e.forEach(t=>{Object.keys(t).forEach(n=>{let s=t[n];if(s.length>1)throw new Error(`Parameter "${n}" must have only a single value`);if(s=s[0],n==="client_max_window_bits"){if(s!==!0){let i=+s;if(!Number.isInteger(i)||i<8||i>15)throw new TypeError(`Invalid value for parameter "${n}": ${s}`);s=i}else if(!this._isServer)throw new TypeError(`Invalid value for parameter "${n}": ${s}`)}else if(n==="server_max_window_bits"){let i=+s;if(!Number.isInteger(i)||i<8||i>15)throw new TypeError(`Invalid value for parameter "${n}": ${s}`);s=i}else if(n==="client_no_context_takeover"||n==="server_no_context_takeover"){if(s!==!0)throw new TypeError(`Invalid value for parameter "${n}": ${s}`)}else throw new Error(`Unknown parameter "${n}"`);t[n]=s})}),e}decompress(e,t,n){Lr.add(s=>{this._decompress(e,t,(i,o)=>{s(),n(i,o)})})}compress(e,t,n){Lr.add(s=>{this._compress(e,t,(i,o)=>{s(),n(i,o)})})}_decompress(e,t,n){let s=this._isServer?"client":"server";if(!this._inflate){let i=`${s}_max_window_bits`,o=typeof this.params[i]!="number"?Ft.Z_DEFAULT_WINDOWBITS:this.params[i];this._inflate=Ft.createInflateRaw({...this._options.zlibInflateOptions,windowBits:o}),this._inflate[Ar]=this,this._inflate[Pe]=0,this._inflate[Ye]=[],this._inflate.on("error",Ul),this._inflate.on("data",jo)}this._inflate[Ut]=n,this._inflate.write(e),t&&this._inflate.write(jl),this._inflate.flush(()=>{let i=this._inflate[Ir];if(i){this._inflate.close(),this._inflate=null,n(i);return}let o=Mo.concat(this._inflate[Ye],this._inflate[Pe]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[Pe]=0,this._inflate[Ye]=[],t&&this.params[`${s}_no_context_takeover`]&&this._inflate.reset()),n(null,o)})}_compress(e,t,n){let s=this._isServer?"server":"client";if(!this._deflate){let i=`${s}_max_window_bits`,o=typeof this.params[i]!="number"?Ft.Z_DEFAULT_WINDOWBITS:this.params[i];this._deflate=Ft.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:o}),this._deflate[Pe]=0,this._deflate[Ye]=[],this._deflate.on("data",Fl)}this._deflate[Ut]=n,this._deflate.write(e),this._deflate.flush(Ft.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let i=Mo.concat(this._deflate[Ye],this._deflate[Pe]);t&&(i=new $l(i.buffer,i.byteOffset,i.length-4)),this._deflate[Ut]=null,this._deflate[Pe]=0,this._deflate[Ye]=[],t&&this.params[`${s}_no_context_takeover`]&&this._deflate.reset(),n(null,i)})}};Fo.exports=Cn;function Fl(r){this[Ye].push(r),this[Pe]+=r.length}function jo(r){if(this[Pe]+=r.length,this[Ar]._maxPayload<1||this[Pe]<=this[Ar]._maxPayload){this[Ye].push(r);return}this[Ir]=new RangeError("Max payload size exceeded"),this[Ir].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[Ir][$o]=1009,this.removeListener("data",jo),this.reset()}function Ul(r){this[Ar]._inflate=null,r[$o]=1007,this[Ut](r)}});var xt=W((Jp,Mr)=>{"use strict";var{isUtf8:Uo}=require("buffer"),{hasBlob:Zl}=Te(),ql=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0];function Bl(r){return r>=1e3&&r<=1014&&r!==1004&&r!==1005&&r!==1006||r>=3e3&&r<=4999}function Tn(r){let e=r.length,t=0;for(;t<e;)if(!(r[t]&128))t++;else if((r[t]&224)===192){if(t+1===e||(r[t+1]&192)!==128||(r[t]&254)===192)return!1;t+=2}else if((r[t]&240)===224){if(t+2>=e||(r[t+1]&192)!==128||(r[t+2]&192)!==128||r[t]===224&&(r[t+1]&224)===128||r[t]===237&&(r[t+1]&224)===160)return!1;t+=3}else if((r[t]&248)===240){if(t+3>=e||(r[t+1]&192)!==128||(r[t+2]&192)!==128||(r[t+3]&192)!==128||r[t]===240&&(r[t+1]&240)===128||r[t]===244&&r[t+1]>143||r[t]>244)return!1;t+=4}else return!1;return!0}function Vl(r){return Zl&&typeof r=="object"&&typeof r.arrayBuffer=="function"&&typeof r.type=="string"&&typeof r.stream=="function"&&(r[Symbol.toStringTag]==="Blob"||r[Symbol.toStringTag]==="File")}if(Mr.exports={isBlob:Vl,isValidStatusCode:Bl,isValidUTF8:Tn,tokenChars:ql},Uo)Mr.exports.isValidUTF8=function(r){return r.length<24?Tn(r):Uo(r)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let r=require("utf-8-validate");Mr.exports.isValidUTF8=function(e){return e.length<32?Tn(e):r(e)}}catch{}});var Dn=W((Xp,Wo)=>{"use strict";var{Writable:Hl}=require("stream"),Zo=Zt(),{BINARY_TYPES:zl,EMPTY_BUFFER:qo,kStatusCode:Wl,kWebSocket:Gl}=Te(),{concat:Pn,toArrayBuffer:Jl,unmask:Xl}=jt(),{isValidStatusCode:Yl,isValidUTF8:Bo}=xt(),$r=Buffer[Symbol.species],se=0,Vo=1,Ho=2,zo=3,Rn=4,On=5,jr=6,Nn=class extends Hl{constructor(e={}){super(),this._allowSynchronousEvents=e.allowSynchronousEvents!==void 0?e.allowSynchronousEvents:!0,this._binaryType=e.binaryType||zl[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=e.maxPayload|0,this._skipUTF8Validation=!!e.skipUTF8Validation,this[Gl]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=se}_write(e,t,n){if(this._opcode===8&&this._state==se)return n();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(n)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let n=this._buffers[0];return this._buffers[0]=new $r(n.buffer,n.byteOffset+e,n.length-e),new $r(n.buffer,n.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let n=this._buffers[0],s=t.length-e;e>=n.length?t.set(this._buffers.shift(),s):(t.set(new Uint8Array(n.buffer,n.byteOffset,e),s),this._buffers[0]=new $r(n.buffer,n.byteOffset+e,n.length-e)),e-=n.length}while(e>0);return t}startLoop(e){this._loop=!0;do switch(this._state){case se:this.getInfo(e);break;case Vo:this.getPayloadLength16(e);break;case Ho:this.getPayloadLength64(e);break;case zo:this.getMask();break;case Rn:this.getData(e);break;case On:case jr:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if(t[0]&48){let s=this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3");e(s);return}let n=(t[0]&64)===64;if(n&&!this._extensions[Zo.extensionName]){let s=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");e(s);return}if(this._fin=(t[0]&128)===128,this._opcode=t[0]&15,this._payloadLength=t[1]&127,this._opcode===0){if(n){let s=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");e(s);return}if(!this._fragmented){let s=this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE");e(s);return}this._opcode=this._fragmented}else if(this._opcode===1||this._opcode===2){if(this._fragmented){let s=this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");e(s);return}this._compressed=n}else if(this._opcode>7&&this._opcode<11){if(!this._fin){let s=this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN");e(s);return}if(n){let s=this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1");e(s);return}if(this._payloadLength>125||this._opcode===8&&this._payloadLength===1){let s=this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH");e(s);return}}else{let s=this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE");e(s);return}if(!this._fin&&!this._fragmented&&(this._fragmented=this._opcode),this._masked=(t[1]&128)===128,this._isServer){if(!this._masked){let s=this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK");e(s);return}}else if(this._masked){let s=this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK");e(s);return}this._payloadLength===126?this._state=Vo:this._payloadLength===127?this._state=Ho:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),n=t.readUInt32BE(0);if(n>Math.pow(2,21)-1){let s=this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH");e(s);return}this._payloadLength=n*Math.pow(2,32)+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0)){let t=this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");e(t);return}this._masked?this._state=zo:this._state=Rn}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=Rn}getData(e){let t=qo;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3]&&Xl(t,this._mask)}if(this._opcode>7){this.controlMessage(t,e);return}if(this._compressed){this._state=On,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[Zo.extensionName].decompress(e,this._fin,(s,i)=>{if(s)return t(s);if(i.length){if(this._messageLength+=i.length,this._messageLength>this._maxPayload&&this._maxPayload>0){let o=this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH");t(o);return}this._fragments.push(i)}this.dataMessage(t),this._state===se&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=se;return}let t=this._messageLength,n=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],this._opcode===2){let s;this._binaryType==="nodebuffer"?s=Pn(n,t):this._binaryType==="arraybuffer"?s=Jl(Pn(n,t)):this._binaryType==="blob"?s=new Blob(n):s=n,this._allowSynchronousEvents?(this.emit("message",s,!0),this._state=se):(this._state=jr,setImmediate(()=>{this.emit("message",s,!0),this._state=se,this.startLoop(e)}))}else{let s=Pn(n,t);if(!this._skipUTF8Validation&&!Bo(s)){let i=this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");e(i);return}this._state===On||this._allowSynchronousEvents?(this.emit("message",s,!1),this._state=se):(this._state=jr,setImmediate(()=>{this.emit("message",s,!1),this._state=se,this.startLoop(e)}))}}controlMessage(e,t){if(this._opcode===8){if(e.length===0)this._loop=!1,this.emit("conclude",1005,qo),this.end();else{let n=e.readUInt16BE(0);if(!Yl(n)){let i=this.createError(RangeError,`invalid status code ${n}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE");t(i);return}let s=new $r(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!Bo(s)){let i=this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8");t(i);return}this._loop=!1,this.emit("conclude",n,s),this.end()}this._state=se;return}this._allowSynchronousEvents?(this.emit(this._opcode===9?"ping":"pong",e),this._state=se):(this._state=jr,setImmediate(()=>{this.emit(this._opcode===9?"ping":"pong",e),this._state=se,this.startLoop(t)}))}createError(e,t,n,s,i){this._loop=!1,this._errored=!0;let o=new e(n?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(o,this.createError),o.code=i,o[Wl]=s,o}};Wo.exports=Nn});var Ln=W((Kp,Xo)=>{"use strict";var{Duplex:Yp}=require("stream"),{randomFillSync:Kl}=require("crypto"),Go=Zt(),{EMPTY_BUFFER:Ql,kWebSocket:ed,NOOP:td}=Te(),{isBlob:bt,isValidStatusCode:rd}=xt(),{mask:Jo,toBuffer:dt}=jt(),ie=Symbol("kByteLength"),nd=Buffer.alloc(4),Fr=8*1024,ut,wt=Fr,pe=0,sd=1,id=2,An=class r{constructor(e,t,n){this._extensions=t||{},n&&(this._generateMask=n,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._queue=[],this._state=pe,this.onerror=td,this[ed]=void 0}static frame(e,t){let n,s=!1,i=2,o=!1;t.mask&&(n=t.maskBuffer||nd,t.generateMask?t.generateMask(n):(wt===Fr&&(ut===void 0&&(ut=Buffer.alloc(Fr)),Kl(ut,0,Fr),wt=0),n[0]=ut[wt++],n[1]=ut[wt++],n[2]=ut[wt++],n[3]=ut[wt++]),o=(n[0]|n[1]|n[2]|n[3])===0,i=6);let a;typeof e=="string"?(!t.mask||o)&&t[ie]!==void 0?a=t[ie]:(e=Buffer.from(e),a=e.length):(a=e.length,s=t.mask&&t.readOnly&&!o);let l=a;a>=65536?(i+=8,l=127):a>125&&(i+=2,l=126);let d=Buffer.allocUnsafe(s?a+i:i);return d[0]=t.fin?t.opcode|128:t.opcode,t.rsv1&&(d[0]|=64),d[1]=l,l===126?d.writeUInt16BE(a,2):l===127&&(d[2]=d[3]=0,d.writeUIntBE(a,4,6)),t.mask?(d[1]|=128,d[i-4]=n[0],d[i-3]=n[1],d[i-2]=n[2],d[i-1]=n[3],o?[d,e]:s?(Jo(e,n,d,i,a),[d]):(Jo(e,n,e,0,a),[d,e])):[d,e]}close(e,t,n,s){let i;if(e===void 0)i=Ql;else{if(typeof e!="number"||!rd(e))throw new TypeError("First argument must be a valid error code number");if(t===void 0||!t.length)i=Buffer.allocUnsafe(2),i.writeUInt16BE(e,0);else{let a=Buffer.byteLength(t);if(a>123)throw new RangeError("The message must not be greater than 123 bytes");i=Buffer.allocUnsafe(2+a),i.writeUInt16BE(e,0),typeof t=="string"?i.write(t,2):i.set(t,2)}}let o={[ie]:i.length,fin:!0,generateMask:this._generateMask,mask:n,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._state!==pe?this.enqueue([this.dispatch,i,!1,o,s]):this.sendFrame(r.frame(i,o),s)}ping(e,t,n){let s,i;if(typeof e=="string"?(s=Buffer.byteLength(e),i=!1):bt(e)?(s=e.size,i=!1):(e=dt(e),s=e.length,i=dt.readOnly),s>125)throw new RangeError("The data size must not be greater than 125 bytes");let o={[ie]:s,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:i,rsv1:!1};bt(e)?this._state!==pe?this.enqueue([this.getBlobData,e,!1,o,n]):this.getBlobData(e,!1,o,n):this._state!==pe?this.enqueue([this.dispatch,e,!1,o,n]):this.sendFrame(r.frame(e,o),n)}pong(e,t,n){let s,i;if(typeof e=="string"?(s=Buffer.byteLength(e),i=!1):bt(e)?(s=e.size,i=!1):(e=dt(e),s=e.length,i=dt.readOnly),s>125)throw new RangeError("The data size must not be greater than 125 bytes");let o={[ie]:s,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:i,rsv1:!1};bt(e)?this._state!==pe?this.enqueue([this.getBlobData,e,!1,o,n]):this.getBlobData(e,!1,o,n):this._state!==pe?this.enqueue([this.dispatch,e,!1,o,n]):this.sendFrame(r.frame(e,o),n)}send(e,t,n){let s=this._extensions[Go.extensionName],i=t.binary?2:1,o=t.compress,a,l;typeof e=="string"?(a=Buffer.byteLength(e),l=!1):bt(e)?(a=e.size,l=!1):(e=dt(e),a=e.length,l=dt.readOnly),this._firstFragment?(this._firstFragment=!1,o&&s&&s.params[s._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(o=a>=s._threshold),this._compress=o):(o=!1,i=0),t.fin&&(this._firstFragment=!0);let d={[ie]:a,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:i,readOnly:l,rsv1:o};bt(e)?this._state!==pe?this.enqueue([this.getBlobData,e,this._compress,d,n]):this.getBlobData(e,this._compress,d,n):this._state!==pe?this.enqueue([this.dispatch,e,this._compress,d,n]):this.dispatch(e,this._compress,d,n)}getBlobData(e,t,n,s){this._bufferedBytes+=n[ie],this._state=id,e.arrayBuffer().then(i=>{if(this._socket.destroyed){let a=new Error("The socket was closed while the blob was being read");process.nextTick(In,this,a,s);return}this._bufferedBytes-=n[ie];let o=dt(i);t?this.dispatch(o,t,n,s):(this._state=pe,this.sendFrame(r.frame(o,n),s),this.dequeue())}).catch(i=>{process.nextTick(od,this,i,s)})}dispatch(e,t,n,s){if(!t){this.sendFrame(r.frame(e,n),s);return}let i=this._extensions[Go.extensionName];this._bufferedBytes+=n[ie],this._state=sd,i.compress(e,n.fin,(o,a)=>{if(this._socket.destroyed){let l=new Error("The socket was closed while data was being compressed");In(this,l,s);return}this._bufferedBytes-=n[ie],this._state=pe,n.readOnly=!1,this.sendFrame(r.frame(a,n),s),this.dequeue()})}dequeue(){for(;this._state===pe&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][ie],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][ie],this._queue.push(e)}sendFrame(e,t){e.length===2?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}};Xo.exports=An;function In(r,e,t){typeof t=="function"&&t(e);for(let n=0;n<r._queue.length;n++){let s=r._queue[n],i=s[s.length-1];typeof i=="function"&&i(e)}}function od(r,e,t){In(r,e,t),r.onerror(e)}});var ia=W((Qp,sa)=>{"use strict";var{kForOnEventAttribute:qt,kListener:Mn}=Te(),Yo=Symbol("kCode"),Ko=Symbol("kData"),Qo=Symbol("kError"),ea=Symbol("kMessage"),ta=Symbol("kReason"),St=Symbol("kTarget"),ra=Symbol("kType"),na=Symbol("kWasClean"),Re=class{constructor(e){this[St]=null,this[ra]=e}get target(){return this[St]}get type(){return this[ra]}};Object.defineProperty(Re.prototype,"target",{enumerable:!0}),Object.defineProperty(Re.prototype,"type",{enumerable:!0});var ft=class extends Re{constructor(e,t={}){super(e),this[Yo]=t.code===void 0?0:t.code,this[ta]=t.reason===void 0?"":t.reason,this[na]=t.wasClean===void 0?!1:t.wasClean}get code(){return this[Yo]}get reason(){return this[ta]}get wasClean(){return this[na]}};Object.defineProperty(ft.prototype,"code",{enumerable:!0}),Object.defineProperty(ft.prototype,"reason",{enumerable:!0}),Object.defineProperty(ft.prototype,"wasClean",{enumerable:!0});var Et=class extends Re{constructor(e,t={}){super(e),this[Qo]=t.error===void 0?null:t.error,this[ea]=t.message===void 0?"":t.message}get error(){return this[Qo]}get message(){return this[ea]}};Object.defineProperty(Et.prototype,"error",{enumerable:!0}),Object.defineProperty(Et.prototype,"message",{enumerable:!0});var Bt=class extends Re{constructor(e,t={}){super(e),this[Ko]=t.data===void 0?null:t.data}get data(){return this[Ko]}};Object.defineProperty(Bt.prototype,"data",{enumerable:!0});var ad={addEventListener(r,e,t={}){for(let s of this.listeners(r))if(!t[qt]&&s[Mn]===e&&!s[qt])return;let n;if(r==="message")n=function(i,o){let a=new Bt("message",{data:o?i:i.toString()});a[St]=this,Ur(e,this,a)};else if(r==="close")n=function(i,o){let a=new ft("close",{code:i,reason:o.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});a[St]=this,Ur(e,this,a)};else if(r==="error")n=function(i){let o=new Et("error",{error:i,message:i.message});o[St]=this,Ur(e,this,o)};else if(r==="open")n=function(){let i=new Re("open");i[St]=this,Ur(e,this,i)};else return;n[qt]=!!t[qt],n[Mn]=e,t.once?this.once(r,n):this.on(r,n)},removeEventListener(r,e){for(let t of this.listeners(r))if(t[Mn]===e&&!t[qt]){this.removeListener(r,t);break}}};sa.exports={CloseEvent:ft,ErrorEvent:Et,Event:Re,EventTarget:ad,MessageEvent:Bt};function Ur(r,e,t){typeof r=="object"&&r.handleEvent?r.handleEvent.call(r,t):r.call(e,t)}});var $n=W((em,oa)=>{"use strict";var{tokenChars:Vt}=xt();function we(r,e,t){r[e]===void 0?r[e]=[t]:r[e].push(t)}function cd(r){let e=Object.create(null),t=Object.create(null),n=!1,s=!1,i=!1,o,a,l=-1,d=-1,u=-1,f=0;for(;f<r.length;f++)if(d=r.charCodeAt(f),o===void 0)if(u===-1&&Vt[d]===1)l===-1&&(l=f);else if(f!==0&&(d===32||d===9))u===-1&&l!==-1&&(u=f);else if(d===59||d===44){if(l===-1)throw new SyntaxError(`Unexpected character at index ${f}`);u===-1&&(u=f);let v=r.slice(l,u);d===44?(we(e,v,t),t=Object.create(null)):o=v,l=u=-1}else throw new SyntaxError(`Unexpected character at index ${f}`);else if(a===void 0)if(u===-1&&Vt[d]===1)l===-1&&(l=f);else if(d===32||d===9)u===-1&&l!==-1&&(u=f);else if(d===59||d===44){if(l===-1)throw new SyntaxError(`Unexpected character at index ${f}`);u===-1&&(u=f),we(t,r.slice(l,u),!0),d===44&&(we(e,o,t),t=Object.create(null),o=void 0),l=u=-1}else if(d===61&&l!==-1&&u===-1)a=r.slice(l,f),l=u=-1;else throw new SyntaxError(`Unexpected character at index ${f}`);else if(s){if(Vt[d]!==1)throw new SyntaxError(`Unexpected character at index ${f}`);l===-1?l=f:n||(n=!0),s=!1}else if(i)if(Vt[d]===1)l===-1&&(l=f);else if(d===34&&l!==-1)i=!1,u=f;else if(d===92)s=!0;else throw new SyntaxError(`Unexpected character at index ${f}`);else if(d===34&&r.charCodeAt(f-1)===61)i=!0;else if(u===-1&&Vt[d]===1)l===-1&&(l=f);else if(l!==-1&&(d===32||d===9))u===-1&&(u=f);else if(d===59||d===44){if(l===-1)throw new SyntaxError(`Unexpected character at index ${f}`);u===-1&&(u=f);let v=r.slice(l,u);n&&(v=v.replace(/\\/g,""),n=!1),we(t,a,v),d===44&&(we(e,o,t),t=Object.create(null),o=void 0),a=void 0,l=u=-1}else throw new SyntaxError(`Unexpected character at index ${f}`);if(l===-1||i||d===32||d===9)throw new SyntaxError("Unexpected end of input");u===-1&&(u=f);let g=r.slice(l,u);return o===void 0?we(e,g,t):(a===void 0?we(t,g,!0):n?we(t,a,g.replace(/\\/g,"")):we(t,a,g),we(e,o,t)),e}function ld(r){return Object.keys(r).map(e=>{let t=r[e];return Array.isArray(t)||(t=[t]),t.map(n=>[e].concat(Object.keys(n).map(s=>{let i=n[s];return Array.isArray(i)||(i=[i]),i.map(o=>o===!0?s:`${s}=${o}`).join("; ")})).join("; ")).join(", ")}).join(", ")}oa.exports={format:ld,parse:cd}});var Vr=W((nm,_a)=>{"use strict";var dd=require("events"),ud=require("https"),fd=require("http"),aa=require("net"),hd=require("tls"),{randomBytes:pd,createHash:md}=require("crypto"),{Duplex:tm,Readable:rm}=require("stream"),{URL:jn}=require("url"),Ke=Zt(),gd=Dn(),vd=Ln(),{isBlob:_d}=xt(),{BINARY_TYPES:ca,EMPTY_BUFFER:Zr,GUID:yd,kForOnEventAttribute:Fn,kListener:xd,kStatusCode:bd,kWebSocket:B,NOOP:la}=Te(),{EventTarget:{addEventListener:wd,removeEventListener:Sd}}=ia(),{format:Ed,parse:kd}=$n(),{toBuffer:Cd}=jt(),Td=30*1e3,da=Symbol("kAborted"),Un=[8,13],Oe=["CONNECTING","OPEN","CLOSING","CLOSED"],Pd=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/,j=class r extends dd{constructor(e,t,n){super(),this._binaryType=ca[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=Zr,this._closeTimer=null,this._errorEmitted=!1,this._extensions={},this._paused=!1,this._protocol="",this._readyState=r.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,e!==null?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,t===void 0?t=[]:Array.isArray(t)||(typeof t=="object"&&t!==null?(n=t,t=[]):t=[t]),ua(this,e,t,n)):(this._autoPong=n.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){ca.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,n){let s=new gd({allowSynchronousEvents:n.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:n.maxPayload,skipUTF8Validation:n.skipUTF8Validation}),i=new vd(e,this._extensions,n.generateMask);this._receiver=s,this._sender=i,this._socket=e,s[B]=this,i[B]=this,e[B]=this,s.on("conclude",Nd),s.on("drain",Dd),s.on("error",Ad),s.on("message",Id),s.on("ping",Ld),s.on("pong",Md),i.onerror=$d,e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",ma),e.on("data",Br),e.on("end",ga),e.on("error",va),this._readyState=r.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=r.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[Ke.extensionName]&&this._extensions[Ke.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=r.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==r.CLOSED){if(this.readyState===r.CONNECTING){te(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===r.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=r.CLOSING,this._sender.close(e,t,!this._isServer,n=>{n||(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),pa(this)}}pause(){this.readyState===r.CONNECTING||this.readyState===r.CLOSED||(this._paused=!0,this._socket.pause())}ping(e,t,n){if(this.readyState===r.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(n=e,e=t=void 0):typeof t=="function"&&(n=t,t=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==r.OPEN){Zn(this,e,n);return}t===void 0&&(t=!this._isServer),this._sender.ping(e||Zr,t,n)}pong(e,t,n){if(this.readyState===r.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof e=="function"?(n=e,e=t=void 0):typeof t=="function"&&(n=t,t=void 0),typeof e=="number"&&(e=e.toString()),this.readyState!==r.OPEN){Zn(this,e,n);return}t===void 0&&(t=!this._isServer),this._sender.pong(e||Zr,t,n)}resume(){this.readyState===r.CONNECTING||this.readyState===r.CLOSED||(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,n){if(this.readyState===r.CONNECTING)throw new Error("WebSocket is not open: readyState 0 (CONNECTING)");if(typeof t=="function"&&(n=t,t={}),typeof e=="number"&&(e=e.toString()),this.readyState!==r.OPEN){Zn(this,e,n);return}let s={binary:typeof e!="string",mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[Ke.extensionName]||(s.compress=!1),this._sender.send(e||Zr,s,n)}terminate(){if(this.readyState!==r.CLOSED){if(this.readyState===r.CONNECTING){te(this,this._req,"WebSocket was closed before the connection was established");return}this._socket&&(this._readyState=r.CLOSING,this._socket.destroy())}}};Object.defineProperty(j,"CONNECTING",{enumerable:!0,value:Oe.indexOf("CONNECTING")}),Object.defineProperty(j.prototype,"CONNECTING",{enumerable:!0,value:Oe.indexOf("CONNECTING")}),Object.defineProperty(j,"OPEN",{enumerable:!0,value:Oe.indexOf("OPEN")}),Object.defineProperty(j.prototype,"OPEN",{enumerable:!0,value:Oe.indexOf("OPEN")}),Object.defineProperty(j,"CLOSING",{enumerable:!0,value:Oe.indexOf("CLOSING")}),Object.defineProperty(j.prototype,"CLOSING",{enumerable:!0,value:Oe.indexOf("CLOSING")}),Object.defineProperty(j,"CLOSED",{enumerable:!0,value:Oe.indexOf("CLOSED")}),Object.defineProperty(j.prototype,"CLOSED",{enumerable:!0,value:Oe.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(r=>{Object.defineProperty(j.prototype,r,{enumerable:!0})}),["open","error","close","message"].forEach(r=>{Object.defineProperty(j.prototype,`on${r}`,{enumerable:!0,get(){for(let e of this.listeners(r))if(e[Fn])return e[xd];return null},set(e){for(let t of this.listeners(r))if(t[Fn]){this.removeListener(r,t);break}typeof e=="function"&&this.addEventListener(r,e,{[Fn]:!0})}})}),j.prototype.addEventListener=wd,j.prototype.removeEventListener=Sd,_a.exports=j;function ua(r,e,t,n){let s={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:Un[1],maxPayload:*********,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...n,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(r._autoPong=s.autoPong,!Un.includes(s.protocolVersion))throw new RangeError(`Unsupported protocol version: ${s.protocolVersion} (supported versions: ${Un.join(", ")})`);let i;if(e instanceof jn)i=e;else try{i=new jn(e)}catch{throw new SyntaxError(`Invalid URL: ${e}`)}i.protocol==="http:"?i.protocol="ws:":i.protocol==="https:"&&(i.protocol="wss:"),r._url=i.href;let o=i.protocol==="wss:",a=i.protocol==="ws+unix:",l;if(i.protocol!=="ws:"&&!o&&!a?l=`The URL's protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"`:a&&!i.pathname?l="The URL's pathname is empty":i.hash&&(l="The URL contains a fragment identifier"),l){let x=new SyntaxError(l);if(r._redirects===0)throw x;qr(r,x);return}let d=o?443:80,u=pd(16).toString("base64"),f=o?ud.request:fd.request,g=new Set,v;if(s.createConnection=s.createConnection||(o?Od:Rd),s.defaultPort=s.defaultPort||d,s.port=i.port||d,s.host=i.hostname.startsWith("[")?i.hostname.slice(1,-1):i.hostname,s.headers={...s.headers,"Sec-WebSocket-Version":s.protocolVersion,"Sec-WebSocket-Key":u,Connection:"Upgrade",Upgrade:"websocket"},s.path=i.pathname+i.search,s.timeout=s.handshakeTimeout,s.perMessageDeflate&&(v=new Ke(s.perMessageDeflate!==!0?s.perMessageDeflate:{},!1,s.maxPayload),s.headers["Sec-WebSocket-Extensions"]=Ed({[Ke.extensionName]:v.offer()})),t.length){for(let x of t){if(typeof x!="string"||!Pd.test(x)||g.has(x))throw new SyntaxError("An invalid or duplicated subprotocol was specified");g.add(x)}s.headers["Sec-WebSocket-Protocol"]=t.join(",")}if(s.origin&&(s.protocolVersion<13?s.headers["Sec-WebSocket-Origin"]=s.origin:s.headers.Origin=s.origin),(i.username||i.password)&&(s.auth=`${i.username}:${i.password}`),a){let x=s.path.split(":");s.socketPath=x[0],s.path=x[1]}let h;if(s.followRedirects){if(r._redirects===0){r._originalIpc=a,r._originalSecure=o,r._originalHostOrSocketPath=a?s.socketPath:i.host;let x=n&&n.headers;if(n={...n,headers:{}},x)for(let[w,T]of Object.entries(x))n.headers[w.toLowerCase()]=T}else if(r.listenerCount("redirect")===0){let x=a?r._originalIpc?s.socketPath===r._originalHostOrSocketPath:!1:r._originalIpc?!1:i.host===r._originalHostOrSocketPath;(!x||r._originalSecure&&!o)&&(delete s.headers.authorization,delete s.headers.cookie,x||delete s.headers.host,s.auth=void 0)}s.auth&&!n.headers.authorization&&(n.headers.authorization="Basic "+Buffer.from(s.auth).toString("base64")),h=r._req=f(s),r._redirects&&r.emit("redirect",r.url,h)}else h=r._req=f(s);s.timeout&&h.on("timeout",()=>{te(r,h,"Opening handshake has timed out")}),h.on("error",x=>{h===null||h[da]||(h=r._req=null,qr(r,x))}),h.on("response",x=>{let w=x.headers.location,T=x.statusCode;if(w&&s.followRedirects&&T>=300&&T<400){if(++r._redirects>s.maxRedirects){te(r,h,"Maximum redirects exceeded");return}h.abort();let D;try{D=new jn(w,e)}catch{let P=new SyntaxError(`Invalid URL: ${w}`);qr(r,P);return}ua(r,D,t,n)}else r.emit("unexpected-response",h,x)||te(r,h,`Unexpected server response: ${x.statusCode}`)}),h.on("upgrade",(x,w,T)=>{if(r.emit("upgrade",x),r.readyState!==j.CONNECTING)return;h=r._req=null;let D=x.headers.upgrade;if(D===void 0||D.toLowerCase()!=="websocket"){te(r,w,"Invalid Upgrade header");return}let O=md("sha1").update(u+yd).digest("base64");if(x.headers["sec-websocket-accept"]!==O){te(r,w,"Invalid Sec-WebSocket-Accept header");return}let P=x.headers["sec-websocket-protocol"],M;if(P!==void 0?g.size?g.has(P)||(M="Server sent an invalid subprotocol"):M="Server sent a subprotocol but none was requested":g.size&&(M="Server sent no subprotocol"),M){te(r,w,M);return}P&&(r._protocol=P);let H=x.headers["sec-websocket-extensions"];if(H!==void 0){if(!v){te(r,w,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}let Qe;try{Qe=kd(H)}catch{te(r,w,"Invalid Sec-WebSocket-Extensions header");return}let Ct=Object.keys(Qe);if(Ct.length!==1||Ct[0]!==Ke.extensionName){te(r,w,"Server indicated an extension that was not requested");return}try{v.accept(Qe[Ke.extensionName])}catch{te(r,w,"Invalid Sec-WebSocket-Extensions header");return}r._extensions[Ke.extensionName]=v}r.setSocket(w,T,{allowSynchronousEvents:s.allowSynchronousEvents,generateMask:s.generateMask,maxPayload:s.maxPayload,skipUTF8Validation:s.skipUTF8Validation})}),s.finishRequest?s.finishRequest(h,r):h.end()}function qr(r,e){r._readyState=j.CLOSING,r._errorEmitted=!0,r.emit("error",e),r.emitClose()}function Rd(r){return r.path=r.socketPath,aa.connect(r)}function Od(r){return r.path=void 0,!r.servername&&r.servername!==""&&(r.servername=aa.isIP(r.host)?"":r.host),hd.connect(r)}function te(r,e,t){r._readyState=j.CLOSING;let n=new Error(t);Error.captureStackTrace(n,te),e.setHeader?(e[da]=!0,e.abort(),e.socket&&!e.socket.destroyed&&e.socket.destroy(),process.nextTick(qr,r,n)):(e.destroy(n),e.once("error",r.emit.bind(r,"error")),e.once("close",r.emitClose.bind(r)))}function Zn(r,e,t){if(e){let n=_d(e)?e.size:Cd(e).length;r._socket?r._sender._bufferedBytes+=n:r._bufferedAmount+=n}if(t){let n=new Error(`WebSocket is not open: readyState ${r.readyState} (${Oe[r.readyState]})`);process.nextTick(t,n)}}function Nd(r,e){let t=this[B];t._closeFrameReceived=!0,t._closeMessage=e,t._closeCode=r,t._socket[B]!==void 0&&(t._socket.removeListener("data",Br),process.nextTick(ha,t._socket),r===1005?t.close():t.close(r,e))}function Dd(){let r=this[B];r.isPaused||r._socket.resume()}function Ad(r){let e=this[B];e._socket[B]!==void 0&&(e._socket.removeListener("data",Br),process.nextTick(ha,e._socket),e.close(r[bd])),e._errorEmitted||(e._errorEmitted=!0,e.emit("error",r))}function fa(){this[B].emitClose()}function Id(r,e){this[B].emit("message",r,e)}function Ld(r){let e=this[B];e._autoPong&&e.pong(r,!this._isServer,la),e.emit("ping",r)}function Md(r){this[B].emit("pong",r)}function ha(r){r.resume()}function $d(r){let e=this[B];e.readyState!==j.CLOSED&&(e.readyState===j.OPEN&&(e._readyState=j.CLOSING,pa(e)),this._socket.end(),e._errorEmitted||(e._errorEmitted=!0,e.emit("error",r)))}function pa(r){r._closeTimer=setTimeout(r._socket.destroy.bind(r._socket),Td)}function ma(){let r=this[B];this.removeListener("close",ma),this.removeListener("data",Br),this.removeListener("end",ga),r._readyState=j.CLOSING;let e;!this._readableState.endEmitted&&!r._closeFrameReceived&&!r._receiver._writableState.errorEmitted&&(e=r._socket.read())!==null&&r._receiver.write(e),r._receiver.end(),this[B]=void 0,clearTimeout(r._closeTimer),r._receiver._writableState.finished||r._receiver._writableState.errorEmitted?r.emitClose():(r._receiver.on("error",fa),r._receiver.on("finish",fa))}function Br(r){this[B]._receiver.write(r)||this.pause()}function ga(){let r=this[B];r._readyState=j.CLOSING,r._receiver.end(),this.end()}function va(){let r=this[B];this.removeListener("error",va),this.on("error",la),r&&(r._readyState=j.CLOSING,this.destroy())}});var wa=W((im,ba)=>{"use strict";var sm=Vr(),{Duplex:jd}=require("stream");function ya(r){r.emit("close")}function Fd(){!this.destroyed&&this._writableState.finished&&this.destroy()}function xa(r){this.removeListener("error",xa),this.destroy(),this.listenerCount("error")===0&&this.emit("error",r)}function Ud(r,e){let t=!0,n=new jd({...e,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return r.on("message",function(i,o){let a=!o&&n._readableState.objectMode?i.toString():i;n.push(a)||r.pause()}),r.once("error",function(i){n.destroyed||(t=!1,n.destroy(i))}),r.once("close",function(){n.destroyed||n.push(null)}),n._destroy=function(s,i){if(r.readyState===r.CLOSED){i(s),process.nextTick(ya,n);return}let o=!1;r.once("error",function(l){o=!0,i(l)}),r.once("close",function(){o||i(s),process.nextTick(ya,n)}),t&&r.terminate()},n._final=function(s){if(r.readyState===r.CONNECTING){r.once("open",function(){n._final(s)});return}r._socket!==null&&(r._socket._writableState.finished?(s(),n._readableState.endEmitted&&n.destroy()):(r._socket.once("finish",function(){s()}),r.close()))},n._read=function(){r.isPaused&&r.resume()},n._write=function(s,i,o){if(r.readyState===r.CONNECTING){r.once("open",function(){n._write(s,i,o)});return}r.send(s,o)},n.on("end",Fd),n.on("error",xa),n}ba.exports=Ud});var Ea=W((om,Sa)=>{"use strict";var{tokenChars:Zd}=xt();function qd(r){let e=new Set,t=-1,n=-1,s=0;for(s;s<r.length;s++){let o=r.charCodeAt(s);if(n===-1&&Zd[o]===1)t===-1&&(t=s);else if(s!==0&&(o===32||o===9))n===-1&&t!==-1&&(n=s);else if(o===44){if(t===-1)throw new SyntaxError(`Unexpected character at index ${s}`);n===-1&&(n=s);let a=r.slice(t,n);if(e.has(a))throw new SyntaxError(`The "${a}" subprotocol is duplicated`);e.add(a),t=n=-1}else throw new SyntaxError(`Unexpected character at index ${s}`)}if(t===-1||n!==-1)throw new SyntaxError("Unexpected end of input");let i=r.slice(t,s);if(e.has(i))throw new SyntaxError(`The "${i}" subprotocol is duplicated`);return e.add(i),e}Sa.exports={parse:qd}});var Na=W((cm,Oa)=>{"use strict";var Bd=require("events"),Hr=require("http"),{Duplex:am}=require("stream"),{createHash:Vd}=require("crypto"),ka=$n(),ht=Zt(),Hd=Ea(),zd=Vr(),{GUID:Wd,kWebSocket:Gd}=Te(),Jd=/^[+/0-9A-Za-z]{22}==$/,Ca=0,Ta=1,Pa=2,qn=class extends Bd{constructor(e,t){if(super(),e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:100*1024*1024,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:zd,...e},e.port==null&&!e.server&&!e.noServer||e.port!=null&&(e.server||e.noServer)||e.server&&e.noServer)throw new TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(e.port!=null?(this._server=Hr.createServer((n,s)=>{let i=Hr.STATUS_CODES[426];s.writeHead(426,{"Content-Length":i.length,"Content-Type":"text/plain"}),s.end(i)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let n=this.emit.bind(this,"connection");this._removeListeners=Xd(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(s,i,o)=>{this.handleUpgrade(s,i,o,n)}})}e.perMessageDeflate===!0&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=Ca}address(){if(this.options.noServer)throw new Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(this._state===Pa){e&&this.once("close",()=>{e(new Error("The server is not running"))}),process.nextTick(Ht,this);return}if(e&&this.once("close",e),this._state!==Ta)if(this._state=Ta,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients?this.clients.size?this._shouldEmitClose=!0:process.nextTick(Ht,this):process.nextTick(Ht,this);else{let t=this._server;this._removeListeners(),this._removeListeners=this._server=null,t.close(()=>{Ht(this)})}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((t!==-1?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,n,s){t.on("error",Ra);let i=e.headers["sec-websocket-key"],o=e.headers.upgrade,a=+e.headers["sec-websocket-version"];if(e.method!=="GET"){pt(this,e,t,405,"Invalid HTTP method");return}if(o===void 0||o.toLowerCase()!=="websocket"){pt(this,e,t,400,"Invalid Upgrade header");return}if(i===void 0||!Jd.test(i)){pt(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");return}if(a!==8&&a!==13){pt(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(e)){zt(t,400);return}let l=e.headers["sec-websocket-protocol"],d=new Set;if(l!==void 0)try{d=Hd.parse(l)}catch{pt(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let u=e.headers["sec-websocket-extensions"],f={};if(this.options.perMessageDeflate&&u!==void 0){let g=new ht(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let v=ka.parse(u);v[ht.extensionName]&&(g.accept(v[ht.extensionName]),f[ht.extensionName]=g)}catch{pt(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let g={origin:e.headers[`${a===8?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(this.options.verifyClient.length===2){this.options.verifyClient(g,(v,h,x,w)=>{if(!v)return zt(t,h||401,x,w);this.completeUpgrade(f,i,d,e,t,n,s)});return}if(!this.options.verifyClient(g))return zt(t,401)}this.completeUpgrade(f,i,d,e,t,n,s)}completeUpgrade(e,t,n,s,i,o,a){if(!i.readable||!i.writable)return i.destroy();if(i[Gd])throw new Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>Ca)return zt(i,503);let d=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${Vd("sha1").update(t+Wd).digest("base64")}`],u=new this.options.WebSocket(null,void 0,this.options);if(n.size){let f=this.options.handleProtocols?this.options.handleProtocols(n,s):n.values().next().value;f&&(d.push(`Sec-WebSocket-Protocol: ${f}`),u._protocol=f)}if(e[ht.extensionName]){let f=e[ht.extensionName].params,g=ka.format({[ht.extensionName]:[f]});d.push(`Sec-WebSocket-Extensions: ${g}`),u._extensions=e}this.emit("headers",d,s),i.write(d.concat(`\r
`).join(`\r
`)),i.removeListener("error",Ra),u.setSocket(i,o,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(u),u.on("close",()=>{this.clients.delete(u),this._shouldEmitClose&&!this.clients.size&&process.nextTick(Ht,this)})),a(u,s)}};Oa.exports=qn;function Xd(r,e){for(let t of Object.keys(e))r.on(t,e[t]);return function(){for(let n of Object.keys(e))r.removeListener(n,e[n])}}function Ht(r){r._state=Pa,r.emit("close")}function Ra(){this.destroy()}function zt(r,e,t,n){t=t||Hr.STATUS_CODES[e],n={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(t),...n},r.once("finish",r.destroy),r.end(`HTTP/1.1 ${e} ${Hr.STATUS_CODES[e]}\r
`+Object.keys(n).map(s=>`${s}: ${n[s]}`).join(`\r
`)+`\r
\r
`+t)}function pt(r,e,t,n,s){if(r.listenerCount("wsClientError")){let i=new Error(s);Error.captureStackTrace(i,pt),r.emit("wsClientError",i,t,e)}else zt(t,n,s)}});var nu={};Ga(nu,{activate:()=>eu,deactivate:()=>tu});module.exports=Ja(nu);var et=$(require("child_process")),Yn=$(require("vscode"));async function Xa(){return new Promise(async r=>{try{let e=et.spawn("bash",["-c",'ps -eo pid,ppid,command | grep -E "(node.*bin/claude|/claude)($| )" | grep -v "grep" || echo "no_claude_found"']),t="";e.stdout.on("data",n=>{t+=n.toString()}),e.on("close",()=>{if(t.includes("no_claude_found")||t.trim()===""){console.log("No Claude processes found running"),r([]);return}let n=t.trim().split(`
`).map(s=>{let i=s.trim().split(/\s+/);return{pid:i[0],ppid:i[1]}});console.log(`Found ${n.length} Claude processes running`),r(n)}),e.on("error",n=>{console.error(`Error checking for Claude processes: ${n}`),r([])})}catch(e){console.error(`Error in process check: ${e}`),r([])}})}async function Ya(r){let e="",t=et.spawn("bash",["-c",`pid=${r}; while [ "$pid" -ne 1 ]; do echo $pid; pid=$(ps -o ppid= -p $pid | tr -d ' '); done`]);return t.stdout.on("data",n=>{e+=n.toString()}),new Promise(n=>{t.on("close",()=>{let s=e.trim().split(`
`).filter(i=>i.length>0);n(s)}),t.on("error",s=>{console.error(`Error getting process tree: ${s}`),n([])})})}async function Ka(r){return new Promise(async e=>{try{let t=et.spawn("bash",["-c",`ps -o pid= --ppid ${r}`]),n="";t.stdout.on("data",s=>{n+=s.toString()}),t.on("close",()=>{let s=n.trim().split(`
`).map(i=>i.trim()).filter(i=>i.length>0);e(s)}),t.on("error",s=>{console.error(`Error finding children: ${s}`),e([])})}catch(t){console.error(`Error checking for child processes: ${t}`),e([])}})}async function Kn(r,e){let t={claudeRunning:!1,terminalProcessTree:[],runningClaudeProcesses:[],isChildOfThisTerminal:!1};try{let n=await e.processId;if(!n)return r.info("Could not get terminal process ID"),t;t.runningClaudeProcesses=await Xa();let s=t.runningClaudeProcesses.length>0;t.terminalProcessTree=await Ya(n);let i=await Ka(n);for(let o of t.runningClaudeProcesses){if(o.ppid===n.toString()){t.isChildOfThisTerminal=!0;break}if(i.includes(o.ppid)){t.isChildOfThisTerminal=!0;break}let a=o.ppid;if(a===n.toString()){t.isChildOfThisTerminal=!0;break}if(n.toString()===a){t.isChildOfThisTerminal=!0;break}try{let l=et.spawn("bash",["-c",`ps -p ${a} -o command=`]),d="";l.stdout.on("data",u=>{d+=u.toString()}),await new Promise(u=>{l.on("close",()=>{let f=d.trim();if((f.includes("bash")||f.includes("sh")||f.includes("zsh"))&&e.name!=="Claude Code")if(n.toString()===a)t.isChildOfThisTerminal=!0;else{let g=et.spawn("bash",["-c",`ps -p ${n} -o command=`]),v="";g.stdout.on("data",h=>{v+=h.toString()}),g.on("close",()=>{let h=v.trim();(h.includes("bash")||h.includes("sh")||h.includes("zsh"))&&e.name!=="Claude Code"&&(t.isChildOfThisTerminal=!0)})}u()})})}catch(l){r.error(`Error checking Claude's parent process: ${l}`)}if(t.isChildOfThisTerminal)break}return!t.isChildOfThisTerminal&&e.name==="Claude Code"?(t.claudeRunning=!1,t):(s&&t.isChildOfThisTerminal?t.claudeRunning=!0:t.claudeRunning=!1,t)}catch(n){return r.error("Error checking if Claude is running:",n),t}}async function Qn(){return new Promise(r=>{try{let e=et.spawn("which",["claude"]);e.on("close",t=>{r(t===0)}),e.on("error",()=>{r(!1)})}catch(e){console.error(`Error checking for claude command: ${e}`),r(!1)}})}function De(){try{let r=Yn.window.activeTerminal;r&&r.show()}catch(r){console.error("Error focusing terminal:",r)}}var ae=$(require("vscode"));function Qa(r,e,t){let n="",s="",i="";if(e.isEmpty){if(t){let o=r.lineAt(t.start.line),a=r.lineAt(t.end.line);n=o.text.trim(),s=a.text.trim();let d=r.lineAt(e.active.line).text.trim();t.start.line===t.end.line?i=`line ${t.start.line+1}`:i=`lines ${t.start.line+1}-${t.end.line+1}`,e.active.line!==t.start.line&&e.active.line!==t.end.line&&(i=`line ${e.active.line+1} (cursor)`,n=d,s=d)}}else{let o=r.lineAt(e.start.line),a=r.lineAt(e.end.line);n=o.text.trim(),s=a.text.trim(),e.start.line===e.end.line?i=`line ${e.start.line+1}`:i=`lines ${e.start.line+1}-${e.end.line+1}`}return{rangeDescription:i,firstLineText:n,lastLineText:s}}function Jr(r,e,t,n,s,i,o="",a){let{rangeDescription:l,firstLineText:d,lastLineText:u}=Qa(r,t,n),f="";if(a){let g=a.range.start.line+1;f=`Fix: ${a.severity===ae.DiagnosticSeverity.Error?"Error":a.severity===ae.DiagnosticSeverity.Warning?"Warning":a.severity===ae.DiagnosticSeverity.Information?"Info":"Hint"} in ${s} line ${g}: ${a.message}`,o&&!o.includes(a.message)&&(f+=`

Note: ${o}`)}else if(o){f=`${o}

`,f+=`Context: ${s} (${i}), ${l}:`,d===u?f+=`
\`${d}\``:(f+=`
First line: \`${d}\``,f+=`
Last line: \`${u}\``);let v=ae.languages.getDiagnostics(r.uri).filter(h=>n&&h.range.intersection(n)!==void 0);v.length>0&&(f+=`

There are ${v.length} issues in this section:`,v.forEach(h=>{let x=h.range.start.line+1,w=h.severity===ae.DiagnosticSeverity.Error?"Error":h.severity===ae.DiagnosticSeverity.Warning?"Warning":"Issue";f+=`
- ${w} at line ${x}: ${h.message}`}))}else{f=`Please analyze and fix issues in ${s} (${i}), ${l}:`,d===u?f+=`
\`${d}\``:(f+=`
First line: \`${d}\``,f+=`
Last line: \`${u}\``);let v=ae.languages.getDiagnostics(r.uri).filter(h=>n&&h.range.intersection(n)!==void 0);v.length>0&&(f+=`

Found ${v.length} issues:`,v.forEach(h=>{let x=h.range.start.line+1,w=h.severity===ae.DiagnosticSeverity.Error?"Error":h.severity===ae.DiagnosticSeverity.Warning?"Warning":"Issue";f+=`
- ${w} at line ${x}: ${h.message}`}))}return f}var N=$(require("vscode")),Jt=$(require("path")),us=$(ls());async function ds(r,e,t=!0){let n,s=!1;for(let a of N.window.terminals)if(a.name==="Claude Code"){n=a;try{if(await a.processId){let d=await Kn(r,a);s=d.claudeRunning,r.info("Claude running check result:",d)}}catch(l){r.error("Error checking if Claude is running:",l)}break}if(n&&s)return r.info("Focusing existing Claude terminal"),n.show(),{terminal:n,claudeRunning:s};r.info("Creating new Claude terminal"),t&&N.window.withProgress({location:N.ProgressLocation.Notification,title:"Claude Code launching...",cancellable:!1},async a=>new Promise(l=>setTimeout(l,2e3)));let i=N.window.createTerminal({name:"Claude Code",iconPath:N.Uri.file(Jt.join(e.extensionPath,"resources","claude-logo.svg")),location:{viewColumn:N.ViewColumn.Beside},isTransient:!0});N.window.onDidEndTerminalShellExecution(a=>{a.terminal===i&&a.execution.commandLine.value==="claude"&&(r.info(`Claude terminal closed after executing ${a.execution.commandLine.value}`),i.dispose())});let o=!1;return N.window.onDidChangeTerminalShellIntegration(async a=>{a.terminal===i&&!o&&(o=!0,r.info("Terminal shell integration available"),a.shellIntegration.executeCommand("claude"))}),setTimeout(()=>{!i.shellIntegration&&!o&&(o=!0,i.sendText("claude"))},3e3),i.show(),{terminal:i,claudeRunning:!0}}function fs(r,e,t){let n=async()=>{try{await ds(r,e)}catch(s){r.error("Error running Claude Code:",s),N.window.showErrorMessage(`Failed to run Claude Code: ${s}`)}};e.subscriptions.push(N.commands.registerCommand("claude-code.runClaude",()=>{t.logEvent("run_claude_command"),n()})),e.subscriptions.push(N.commands.registerCommand("claude-code.runClaude.keyboard",()=>{t.logEvent("run_claude_command_keyboard"),n()}))}var Qr="claude-code.runQuickFix";function hs(r,e,t){let n=N.commands.registerCommand(Qr,async(s="",i)=>{t.logEvent("quick_fix_command");try{let o=N.window.activeTextEditor;if(!o)throw new Error("No active text editor");let a=o.document,l=o.selection,d=a.uri.fsPath,u=a.languageId,f=Jt.basename(d),g,v;if(!l.isEmpty)v=l,g=`selected code (lines ${l.start.line+1}-${l.end.line+1})`;else{let T=o.selection.active.line,D=Math.max(0,T-3),O=Math.min(a.lineCount-1,T+3);v=new N.Range(new N.Position(D,0),new N.Position(O,a.lineAt(O).text.length)),g=`code around line ${T+1} (lines ${D+1}-${O+1})`}let h="";if(s&&(h+=`${s}

`),h+=`I'm working on a ${u} file (${f}).
`,h+=`Here's the ${g}:

`,i){let T=i.range.start.line+1,D=i.severity===N.DiagnosticSeverity.Error?"Error":i.severity===N.DiagnosticSeverity.Warning?"Warning":i.severity===N.DiagnosticSeverity.Information?"Info":"Hint";h+=`Please fix this specific issue:
`,h+=`- ${D} at line ${T}: ${i.message}

`,v=i.range}else{let T=N.languages.getDiagnostics(a.uri);if(T.length>0){let D=l.isEmpty?v:l,O=T.filter(P=>P.range.intersection(D)!==void 0);O.length>0&&(h+=`The code has the following issues:
`,O.forEach(P=>{let M=P.range.start.line+1,H=P.severity===N.DiagnosticSeverity.Error?"Error":P.severity===N.DiagnosticSeverity.Warning?"Warning":P.severity===N.DiagnosticSeverity.Information?"Info":"Hint";h+=`- ${H} at line ${M}: ${P.message}
`}),h+=`
`)}}r.info("Enhanced prompt:",h);let{terminal:x}=await ds(r,e,!0),w=!0;if(w){let T=Jr(a,o,l,v,f,u,s,i);x.sendText(T,!1),setTimeout(()=>{x.sendText("")},1e3)}else{let T=`claude "${d}"`;if(l.isEmpty){if(v){let P=`${v.start.line+1}:${v.start.character+1}-${v.end.line+1}:${v.end.character+1}`;T+=` -r "${P}"`}}else{let P=`${l.start.line+1}:${l.start.character+1}-${l.end.line+1}:${l.end.character+1}`;T+=` -r "${P}"`}let D=cc(a,o,l,v,f,u,s,i),O=(0,us.quote)([D])[0];T+=` -p "${O}"`,x.sendText(T)}return{success:!0,message:`Running Claude Code quick fix on ${d}`,hasSelection:!l.isEmpty,existingSession:w}}catch(o){throw r.error("Error running quick fix with Claude Code:",o),N.window.showErrorMessage(`Failed to run quick fix: ${o}`),o}});e.subscriptions.push(n)}function cc(r,e,t,n,s,i,o,a){return Jr(r,e,t,n,s,i,o,a)}function ps(r){let e=new N.EventEmitter;return r.push(N.commands.registerCommand("claude-code.acceptProposedDiff",async()=>{let t=N.window.tabGroups.activeTabGroup.activeTab;e.fire({accepted:!0,activeTab:t}),De()})),r.push(N.commands.registerCommand("claude-code.rejectProposedDiff",async()=>{let t=N.window.tabGroups.activeTabGroup.activeTab;e.fire({accepted:!1,activeTab:t}),De()})),e.event}function ms(r){let e=new N.EventEmitter;return r.push(N.commands.registerCommand("claude-code.insertAtMentioned",async()=>{e.fire({}),De()})),e.event}var I;(function(r){r.assertEqual=s=>s;function e(s){}r.assertIs=e;function t(s){throw new Error}r.assertNever=t,r.arrayToEnum=s=>{let i={};for(let o of s)i[o]=o;return i},r.getValidEnumValues=s=>{let i=r.objectKeys(s).filter(a=>typeof s[s[a]]!="number"),o={};for(let a of i)o[a]=s[a];return r.objectValues(o)},r.objectValues=s=>r.objectKeys(s).map(function(i){return s[i]}),r.objectKeys=typeof Object.keys=="function"?s=>Object.keys(s):s=>{let i=[];for(let o in s)Object.prototype.hasOwnProperty.call(s,o)&&i.push(o);return i},r.find=(s,i)=>{for(let o of s)if(i(o))return o},r.isInteger=typeof Number.isInteger=="function"?s=>Number.isInteger(s):s=>typeof s=="number"&&isFinite(s)&&Math.floor(s)===s;function n(s,i=" | "){return s.map(o=>typeof o=="string"?`'${o}'`:o).join(i)}r.joinValues=n,r.jsonStringifyReplacer=(s,i)=>typeof i=="bigint"?i.toString():i})(I||(I={}));var Xt;(function(r){r.mergeShapes=(e,t)=>({...e,...t})})(Xt||(Xt={}));var y=I.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),ve=r=>{switch(typeof r){case"undefined":return y.undefined;case"string":return y.string;case"number":return isNaN(r)?y.nan:y.number;case"boolean":return y.boolean;case"function":return y.function;case"bigint":return y.bigint;case"symbol":return y.symbol;case"object":return Array.isArray(r)?y.array:r===null?y.null:r.then&&typeof r.then=="function"&&r.catch&&typeof r.catch=="function"?y.promise:typeof Map<"u"&&r instanceof Map?y.map:typeof Set<"u"&&r instanceof Set?y.set:typeof Date<"u"&&r instanceof Date?y.date:y.object;default:return y.unknown}},p=I.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),gs=r=>JSON.stringify(r,null,2).replace(/"([^"]+)":/g,"$1:"),Q=class r extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){let t=e||function(i){return i.message},n={_errors:[]},s=i=>{for(let o of i.issues)if(o.code==="invalid_union")o.unionErrors.map(s);else if(o.code==="invalid_return_type")s(o.returnTypeError);else if(o.code==="invalid_arguments")s(o.argumentsError);else if(o.path.length===0)n._errors.push(t(o));else{let a=n,l=0;for(;l<o.path.length;){let d=o.path[l];l===o.path.length-1?(a[d]=a[d]||{_errors:[]},a[d]._errors.push(t(o))):a[d]=a[d]||{_errors:[]},a=a[d],l++}}};return s(this),n}static assert(e){if(!(e instanceof r))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,I.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){let t={},n=[];for(let s of this.issues)s.path.length>0?(t[s.path[0]]=t[s.path[0]]||[],t[s.path[0]].push(e(s))):n.push(e(s));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}};Q.create=r=>new Q(r);var rt=(r,e)=>{let t;switch(r.code){case p.invalid_type:r.received===y.undefined?t="Required":t=`Expected ${r.expected}, received ${r.received}`;break;case p.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(r.expected,I.jsonStringifyReplacer)}`;break;case p.unrecognized_keys:t=`Unrecognized key(s) in object: ${I.joinValues(r.keys,", ")}`;break;case p.invalid_union:t="Invalid input";break;case p.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${I.joinValues(r.options)}`;break;case p.invalid_enum_value:t=`Invalid enum value. Expected ${I.joinValues(r.options)}, received '${r.received}'`;break;case p.invalid_arguments:t="Invalid function arguments";break;case p.invalid_return_type:t="Invalid function return type";break;case p.invalid_date:t="Invalid date";break;case p.invalid_string:typeof r.validation=="object"?"includes"in r.validation?(t=`Invalid input: must include "${r.validation.includes}"`,typeof r.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${r.validation.position}`)):"startsWith"in r.validation?t=`Invalid input: must start with "${r.validation.startsWith}"`:"endsWith"in r.validation?t=`Invalid input: must end with "${r.validation.endsWith}"`:I.assertNever(r.validation):r.validation!=="regex"?t=`Invalid ${r.validation}`:t="Invalid";break;case p.too_small:r.type==="array"?t=`Array must contain ${r.exact?"exactly":r.inclusive?"at least":"more than"} ${r.minimum} element(s)`:r.type==="string"?t=`String must contain ${r.exact?"exactly":r.inclusive?"at least":"over"} ${r.minimum} character(s)`:r.type==="number"?t=`Number must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${r.minimum}`:r.type==="date"?t=`Date must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(r.minimum))}`:t="Invalid input";break;case p.too_big:r.type==="array"?t=`Array must contain ${r.exact?"exactly":r.inclusive?"at most":"less than"} ${r.maximum} element(s)`:r.type==="string"?t=`String must contain ${r.exact?"exactly":r.inclusive?"at most":"under"} ${r.maximum} character(s)`:r.type==="number"?t=`Number must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="bigint"?t=`BigInt must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="date"?t=`Date must be ${r.exact?"exactly":r.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(r.maximum))}`:t="Invalid input";break;case p.custom:t="Invalid input";break;case p.invalid_intersection_types:t="Intersection results could not be merged";break;case p.not_multiple_of:t=`Number must be a multiple of ${r.multipleOf}`;break;case p.not_finite:t="Number must be finite";break;default:t=e.defaultError,I.assertNever(r)}return{message:t}},vs=rt;function _s(r){vs=r}function Tt(){return vs}var Pt=r=>{let{data:e,path:t,errorMaps:n,issueData:s}=r,i=[...t,...s.path||[]],o={...s,path:i};if(s.message!==void 0)return{...s,path:i,message:s.message};let a="",l=n.filter(d=>!!d).slice().reverse();for(let d of l)a=d(o,{data:e,defaultError:a}).message;return{...s,path:i,message:a}},ys=[];function _(r,e){let t=Tt(),n=Pt({issueData:e,data:r.data,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,t,t===rt?void 0:rt].filter(s=>!!s)});r.common.issues.push(n)}var z=class r{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){let n=[];for(let s of t){if(s.status==="aborted")return E;s.status==="dirty"&&e.dirty(),n.push(s.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){let n=[];for(let s of t){let i=await s.key,o=await s.value;n.push({key:i,value:o})}return r.mergeObjectSync(e,n)}static mergeObjectSync(e,t){let n={};for(let s of t){let{key:i,value:o}=s;if(i.status==="aborted"||o.status==="aborted")return E;i.status==="dirty"&&e.dirty(),o.status==="dirty"&&e.dirty(),i.value!=="__proto__"&&(typeof o.value<"u"||s.alwaysSet)&&(n[i.value]=o.value)}return{status:e.value,value:n}}},E=Object.freeze({status:"aborted"}),nt=r=>({status:"dirty",value:r}),G=r=>({status:"valid",value:r}),Yt=r=>r.status==="aborted",Kt=r=>r.status==="dirty",Ae=r=>r.status==="valid",mt=r=>typeof Promise<"u"&&r instanceof Promise;function Qt(r,e,t,n){if(t==="a"&&!n)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?r!==e||!n:!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?n:t==="a"?n.call(r):n?n.value:e.get(r)}function xs(r,e,t,n,s){if(n==="m")throw new TypeError("Private method is not writable");if(n==="a"&&!s)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?r!==e||!s:!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return n==="a"?s.call(r,t):s?s.value=t:e.set(r,t),t}typeof SuppressedError=="function"&&SuppressedError;var b;(function(r){r.errToObj=e=>typeof e=="string"?{message:e}:e||{},r.toString=e=>typeof e=="string"?e:e?.message})(b||(b={}));var Rt,Ot,re=class{constructor(e,t,n,s){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=s}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}},bs=(r,e)=>{if(Ae(e))return{success:!0,data:e.value};if(!r.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new Q(r.common.issues);return this._error=t,this._error}}};function R(r){if(!r)return{};let{errorMap:e,invalid_type_error:t,required_error:n,description:s}=r;if(e&&(t||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:s}:{errorMap:(o,a)=>{var l,d;let{message:u}=r;return o.code==="invalid_enum_value"?{message:u??a.defaultError}:typeof a.data>"u"?{message:(l=u??n)!==null&&l!==void 0?l:a.defaultError}:o.code!=="invalid_type"?{message:a.defaultError}:{message:(d=u??t)!==null&&d!==void 0?d:a.defaultError}},description:s}}var k=class{get description(){return this._def.description}_getType(e){return ve(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:ve(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new z,ctx:{common:e.parent.common,data:e.data,parsedType:ve(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(mt(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){let t=this._parse(e);return Promise.resolve(t)}parse(e,t){let n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){var n;let s={common:{issues:[],async:(n=t?.async)!==null&&n!==void 0?n:!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ve(e)},i=this._parseSync({data:e,path:s.path,parent:s});return bs(s,i)}"~validate"(e){var t,n;let s={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ve(e)};if(!this["~standard"].async)try{let i=this._parseSync({data:e,path:[],parent:s});return Ae(i)?{value:i.value}:{issues:s.common.issues}}catch(i){!((n=(t=i?.message)===null||t===void 0?void 0:t.toLowerCase())===null||n===void 0)&&n.includes("encountered")&&(this["~standard"].async=!0),s.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:s}).then(i=>Ae(i)?{value:i.value}:{issues:s.common.issues})}async parseAsync(e,t){let n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){let n={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ve(e)},s=this._parse({data:e,path:n.path,parent:n}),i=await(mt(s)?s:Promise.resolve(s));return bs(n,i)}refine(e,t){let n=s=>typeof t=="string"||typeof t>"u"?{message:t}:typeof t=="function"?t(s):t;return this._refinement((s,i)=>{let o=e(s),a=()=>i.addIssue({code:p.custom,...n(s)});return typeof Promise<"u"&&o instanceof Promise?o.then(l=>l?!0:(a(),!1)):o?!0:(a(),!1)})}refinement(e,t){return this._refinement((n,s)=>e(n)?!0:(s.addIssue(typeof t=="function"?t(n,s):t),!1))}_refinement(e){return new X({schema:this,typeName:m.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return Y.create(this,this._def)}nullable(){return le.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return ye.create(this)}promise(){return Ce.create(this,this._def)}or(e){return Ue.create([this,e],this._def)}and(e){return Ze.create(this,e,this._def)}transform(e){return new X({...R(this._def),schema:this,typeName:m.ZodEffects,effect:{type:"transform",transform:e}})}default(e){let t=typeof e=="function"?e:()=>e;return new ze({...R(this._def),innerType:this,defaultValue:t,typeName:m.ZodDefault})}brand(){return new vt({typeName:m.ZodBranded,type:this,...R(this._def)})}catch(e){let t=typeof e=="function"?e:()=>e;return new We({...R(this._def),innerType:this,catchValue:t,typeName:m.ZodCatch})}describe(e){let t=this.constructor;return new t({...this._def,description:e})}pipe(e){return _t.create(this,e)}readonly(){return Ge.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}},lc=/^c[^\s-]{8,}$/i,dc=/^[0-9a-z]+$/,uc=/^[0-9A-HJKMNP-TV-Z]{26}$/i,fc=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,hc=/^[a-z0-9_-]{21}$/i,pc=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,mc=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,gc=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,vc="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$",en,_c=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,yc=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,xc=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,bc=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,wc=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Sc=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,ws="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Ec=new RegExp(`^${ws}$`);function Ss(r){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return r.precision?e=`${e}\\.\\d{${r.precision}}`:r.precision==null&&(e=`${e}(\\.\\d+)?`),e}function kc(r){return new RegExp(`^${Ss(r)}$`)}function tn(r){let e=`${ws}T${Ss(r)}`,t=[];return t.push(r.local?"Z?":"Z"),r.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function Cc(r,e){return!!((e==="v4"||!e)&&_c.test(r)||(e==="v6"||!e)&&xc.test(r))}function Tc(r,e){if(!pc.test(r))return!1;try{let[t]=r.split("."),n=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),s=JSON.parse(atob(n));return!(typeof s!="object"||s===null||!s.typ||!s.alg||e&&s.alg!==e)}catch{return!1}}function Pc(r,e){return!!((e==="v4"||!e)&&yc.test(r)||(e==="v6"||!e)&&bc.test(r))}var Se=class r extends k{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==y.string){let i=this._getOrReturnCtx(e);return _(i,{code:p.invalid_type,expected:y.string,received:i.parsedType}),E}let n=new z,s;for(let i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(s=this._getOrReturnCtx(e,s),_(s,{code:p.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),n.dirty());else if(i.kind==="max")e.data.length>i.value&&(s=this._getOrReturnCtx(e,s),_(s,{code:p.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),n.dirty());else if(i.kind==="length"){let o=e.data.length>i.value,a=e.data.length<i.value;(o||a)&&(s=this._getOrReturnCtx(e,s),o?_(s,{code:p.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):a&&_(s,{code:p.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),n.dirty())}else if(i.kind==="email")gc.test(e.data)||(s=this._getOrReturnCtx(e,s),_(s,{validation:"email",code:p.invalid_string,message:i.message}),n.dirty());else if(i.kind==="emoji")en||(en=new RegExp(vc,"u")),en.test(e.data)||(s=this._getOrReturnCtx(e,s),_(s,{validation:"emoji",code:p.invalid_string,message:i.message}),n.dirty());else if(i.kind==="uuid")fc.test(e.data)||(s=this._getOrReturnCtx(e,s),_(s,{validation:"uuid",code:p.invalid_string,message:i.message}),n.dirty());else if(i.kind==="nanoid")hc.test(e.data)||(s=this._getOrReturnCtx(e,s),_(s,{validation:"nanoid",code:p.invalid_string,message:i.message}),n.dirty());else if(i.kind==="cuid")lc.test(e.data)||(s=this._getOrReturnCtx(e,s),_(s,{validation:"cuid",code:p.invalid_string,message:i.message}),n.dirty());else if(i.kind==="cuid2")dc.test(e.data)||(s=this._getOrReturnCtx(e,s),_(s,{validation:"cuid2",code:p.invalid_string,message:i.message}),n.dirty());else if(i.kind==="ulid")uc.test(e.data)||(s=this._getOrReturnCtx(e,s),_(s,{validation:"ulid",code:p.invalid_string,message:i.message}),n.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{s=this._getOrReturnCtx(e,s),_(s,{validation:"url",code:p.invalid_string,message:i.message}),n.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(s=this._getOrReturnCtx(e,s),_(s,{validation:"regex",code:p.invalid_string,message:i.message}),n.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(s=this._getOrReturnCtx(e,s),_(s,{code:p.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),n.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(s=this._getOrReturnCtx(e,s),_(s,{code:p.invalid_string,validation:{startsWith:i.value},message:i.message}),n.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(s=this._getOrReturnCtx(e,s),_(s,{code:p.invalid_string,validation:{endsWith:i.value},message:i.message}),n.dirty()):i.kind==="datetime"?tn(i).test(e.data)||(s=this._getOrReturnCtx(e,s),_(s,{code:p.invalid_string,validation:"datetime",message:i.message}),n.dirty()):i.kind==="date"?Ec.test(e.data)||(s=this._getOrReturnCtx(e,s),_(s,{code:p.invalid_string,validation:"date",message:i.message}),n.dirty()):i.kind==="time"?kc(i).test(e.data)||(s=this._getOrReturnCtx(e,s),_(s,{code:p.invalid_string,validation:"time",message:i.message}),n.dirty()):i.kind==="duration"?mc.test(e.data)||(s=this._getOrReturnCtx(e,s),_(s,{validation:"duration",code:p.invalid_string,message:i.message}),n.dirty()):i.kind==="ip"?Cc(e.data,i.version)||(s=this._getOrReturnCtx(e,s),_(s,{validation:"ip",code:p.invalid_string,message:i.message}),n.dirty()):i.kind==="jwt"?Tc(e.data,i.alg)||(s=this._getOrReturnCtx(e,s),_(s,{validation:"jwt",code:p.invalid_string,message:i.message}),n.dirty()):i.kind==="cidr"?Pc(e.data,i.version)||(s=this._getOrReturnCtx(e,s),_(s,{validation:"cidr",code:p.invalid_string,message:i.message}),n.dirty()):i.kind==="base64"?wc.test(e.data)||(s=this._getOrReturnCtx(e,s),_(s,{validation:"base64",code:p.invalid_string,message:i.message}),n.dirty()):i.kind==="base64url"?Sc.test(e.data)||(s=this._getOrReturnCtx(e,s),_(s,{validation:"base64url",code:p.invalid_string,message:i.message}),n.dirty()):I.assertNever(i);return{status:n.value,value:e.data}}_regex(e,t,n){return this.refinement(s=>e.test(s),{validation:t,code:p.invalid_string,...b.errToObj(n)})}_addCheck(e){return new r({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...b.errToObj(e)})}url(e){return this._addCheck({kind:"url",...b.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...b.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...b.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...b.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...b.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...b.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...b.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...b.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...b.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...b.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...b.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...b.errToObj(e)})}datetime(e){var t,n;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof e?.precision>"u"?null:e?.precision,offset:(t=e?.offset)!==null&&t!==void 0?t:!1,local:(n=e?.local)!==null&&n!==void 0?n:!1,...b.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof e?.precision>"u"?null:e?.precision,...b.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...b.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...b.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...b.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...b.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...b.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...b.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...b.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...b.errToObj(t)})}nonempty(e){return this.min(1,b.errToObj(e))}trim(){return new r({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new r({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new r({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(let t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}};Se.create=r=>{var e;return new Se({checks:[],typeName:m.ZodString,coerce:(e=r?.coerce)!==null&&e!==void 0?e:!1,...R(r)})};function Rc(r,e){let t=(r.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,s=t>n?t:n,i=parseInt(r.toFixed(s).replace(".","")),o=parseInt(e.toFixed(s).replace(".",""));return i%o/Math.pow(10,s)}var Ie=class r extends k{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==y.number){let i=this._getOrReturnCtx(e);return _(i,{code:p.invalid_type,expected:y.number,received:i.parsedType}),E}let n,s=new z;for(let i of this._def.checks)i.kind==="int"?I.isInteger(e.data)||(n=this._getOrReturnCtx(e,n),_(n,{code:p.invalid_type,expected:"integer",received:"float",message:i.message}),s.dirty()):i.kind==="min"?(i.inclusive?e.data<i.value:e.data<=i.value)&&(n=this._getOrReturnCtx(e,n),_(n,{code:p.too_small,minimum:i.value,type:"number",inclusive:i.inclusive,exact:!1,message:i.message}),s.dirty()):i.kind==="max"?(i.inclusive?e.data>i.value:e.data>=i.value)&&(n=this._getOrReturnCtx(e,n),_(n,{code:p.too_big,maximum:i.value,type:"number",inclusive:i.inclusive,exact:!1,message:i.message}),s.dirty()):i.kind==="multipleOf"?Rc(e.data,i.value)!==0&&(n=this._getOrReturnCtx(e,n),_(n,{code:p.not_multiple_of,multipleOf:i.value,message:i.message}),s.dirty()):i.kind==="finite"?Number.isFinite(e.data)||(n=this._getOrReturnCtx(e,n),_(n,{code:p.not_finite,message:i.message}),s.dirty()):I.assertNever(i);return{status:s.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,b.toString(t))}gt(e,t){return this.setLimit("min",e,!1,b.toString(t))}lte(e,t){return this.setLimit("max",e,!0,b.toString(t))}lt(e,t){return this.setLimit("max",e,!1,b.toString(t))}setLimit(e,t,n,s){return new r({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:b.toString(s)}]})}_addCheck(e){return new r({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:b.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:b.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:b.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:b.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:b.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:b.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:b.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:b.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:b.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&I.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(t===null||n.value>t)&&(t=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}};Ie.create=r=>new Ie({checks:[],typeName:m.ZodNumber,coerce:r?.coerce||!1,...R(r)});var Le=class r extends k{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==y.bigint)return this._getInvalidInput(e);let n,s=new z;for(let i of this._def.checks)i.kind==="min"?(i.inclusive?e.data<i.value:e.data<=i.value)&&(n=this._getOrReturnCtx(e,n),_(n,{code:p.too_small,type:"bigint",minimum:i.value,inclusive:i.inclusive,message:i.message}),s.dirty()):i.kind==="max"?(i.inclusive?e.data>i.value:e.data>=i.value)&&(n=this._getOrReturnCtx(e,n),_(n,{code:p.too_big,type:"bigint",maximum:i.value,inclusive:i.inclusive,message:i.message}),s.dirty()):i.kind==="multipleOf"?e.data%i.value!==BigInt(0)&&(n=this._getOrReturnCtx(e,n),_(n,{code:p.not_multiple_of,multipleOf:i.value,message:i.message}),s.dirty()):I.assertNever(i);return{status:s.value,value:e.data}}_getInvalidInput(e){let t=this._getOrReturnCtx(e);return _(t,{code:p.invalid_type,expected:y.bigint,received:t.parsedType}),E}gte(e,t){return this.setLimit("min",e,!0,b.toString(t))}gt(e,t){return this.setLimit("min",e,!1,b.toString(t))}lte(e,t){return this.setLimit("max",e,!0,b.toString(t))}lt(e,t){return this.setLimit("max",e,!1,b.toString(t))}setLimit(e,t,n,s){return new r({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:b.toString(s)}]})}_addCheck(e){return new r({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:b.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:b.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:b.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:b.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:b.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}};Le.create=r=>{var e;return new Le({checks:[],typeName:m.ZodBigInt,coerce:(e=r?.coerce)!==null&&e!==void 0?e:!1,...R(r)})};var Me=class extends k{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==y.boolean){let n=this._getOrReturnCtx(e);return _(n,{code:p.invalid_type,expected:y.boolean,received:n.parsedType}),E}return G(e.data)}};Me.create=r=>new Me({typeName:m.ZodBoolean,coerce:r?.coerce||!1,...R(r)});var $e=class r extends k{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==y.date){let i=this._getOrReturnCtx(e);return _(i,{code:p.invalid_type,expected:y.date,received:i.parsedType}),E}if(isNaN(e.data.getTime())){let i=this._getOrReturnCtx(e);return _(i,{code:p.invalid_date}),E}let n=new z,s;for(let i of this._def.checks)i.kind==="min"?e.data.getTime()<i.value&&(s=this._getOrReturnCtx(e,s),_(s,{code:p.too_small,message:i.message,inclusive:!0,exact:!1,minimum:i.value,type:"date"}),n.dirty()):i.kind==="max"?e.data.getTime()>i.value&&(s=this._getOrReturnCtx(e,s),_(s,{code:p.too_big,message:i.message,inclusive:!0,exact:!1,maximum:i.value,type:"date"}),n.dirty()):I.assertNever(i);return{status:n.value,value:new Date(e.data.getTime())}}_addCheck(e){return new r({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:b.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:b.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}};$e.create=r=>new $e({checks:[],coerce:r?.coerce||!1,typeName:m.ZodDate,...R(r)});var st=class extends k{_parse(e){if(this._getType(e)!==y.symbol){let n=this._getOrReturnCtx(e);return _(n,{code:p.invalid_type,expected:y.symbol,received:n.parsedType}),E}return G(e.data)}};st.create=r=>new st({typeName:m.ZodSymbol,...R(r)});var je=class extends k{_parse(e){if(this._getType(e)!==y.undefined){let n=this._getOrReturnCtx(e);return _(n,{code:p.invalid_type,expected:y.undefined,received:n.parsedType}),E}return G(e.data)}};je.create=r=>new je({typeName:m.ZodUndefined,...R(r)});var Fe=class extends k{_parse(e){if(this._getType(e)!==y.null){let n=this._getOrReturnCtx(e);return _(n,{code:p.invalid_type,expected:y.null,received:n.parsedType}),E}return G(e.data)}};Fe.create=r=>new Fe({typeName:m.ZodNull,...R(r)});var Ee=class extends k{constructor(){super(...arguments),this._any=!0}_parse(e){return G(e.data)}};Ee.create=r=>new Ee({typeName:m.ZodAny,...R(r)});var _e=class extends k{constructor(){super(...arguments),this._unknown=!0}_parse(e){return G(e.data)}};_e.create=r=>new _e({typeName:m.ZodUnknown,...R(r)});var ne=class extends k{_parse(e){let t=this._getOrReturnCtx(e);return _(t,{code:p.invalid_type,expected:y.never,received:t.parsedType}),E}};ne.create=r=>new ne({typeName:m.ZodNever,...R(r)});var it=class extends k{_parse(e){if(this._getType(e)!==y.undefined){let n=this._getOrReturnCtx(e);return _(n,{code:p.invalid_type,expected:y.void,received:n.parsedType}),E}return G(e.data)}};it.create=r=>new it({typeName:m.ZodVoid,...R(r)});var ye=class r extends k{_parse(e){let{ctx:t,status:n}=this._processInputParams(e),s=this._def;if(t.parsedType!==y.array)return _(t,{code:p.invalid_type,expected:y.array,received:t.parsedType}),E;if(s.exactLength!==null){let o=t.data.length>s.exactLength.value,a=t.data.length<s.exactLength.value;(o||a)&&(_(t,{code:o?p.too_big:p.too_small,minimum:a?s.exactLength.value:void 0,maximum:o?s.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:s.exactLength.message}),n.dirty())}if(s.minLength!==null&&t.data.length<s.minLength.value&&(_(t,{code:p.too_small,minimum:s.minLength.value,type:"array",inclusive:!0,exact:!1,message:s.minLength.message}),n.dirty()),s.maxLength!==null&&t.data.length>s.maxLength.value&&(_(t,{code:p.too_big,maximum:s.maxLength.value,type:"array",inclusive:!0,exact:!1,message:s.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((o,a)=>s.type._parseAsync(new re(t,o,t.path,a)))).then(o=>z.mergeArray(n,o));let i=[...t.data].map((o,a)=>s.type._parseSync(new re(t,o,t.path,a)));return z.mergeArray(n,i)}get element(){return this._def.type}min(e,t){return new r({...this._def,minLength:{value:e,message:b.toString(t)}})}max(e,t){return new r({...this._def,maxLength:{value:e,message:b.toString(t)}})}length(e,t){return new r({...this._def,exactLength:{value:e,message:b.toString(t)}})}nonempty(e){return this.min(1,e)}};ye.create=(r,e)=>new ye({type:r,minLength:null,maxLength:null,exactLength:null,typeName:m.ZodArray,...R(e)});function gt(r){if(r instanceof J){let e={};for(let t in r.shape){let n=r.shape[t];e[t]=Y.create(gt(n))}return new J({...r._def,shape:()=>e})}else return r instanceof ye?new ye({...r._def,type:gt(r.element)}):r instanceof Y?Y.create(gt(r.unwrap())):r instanceof le?le.create(gt(r.unwrap())):r instanceof ce?ce.create(r.items.map(e=>gt(e))):r}var J=class r extends k{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;let e=this._def.shape(),t=I.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==y.object){let d=this._getOrReturnCtx(e);return _(d,{code:p.invalid_type,expected:y.object,received:d.parsedType}),E}let{status:n,ctx:s}=this._processInputParams(e),{shape:i,keys:o}=this._getCached(),a=[];if(!(this._def.catchall instanceof ne&&this._def.unknownKeys==="strip"))for(let d in s.data)o.includes(d)||a.push(d);let l=[];for(let d of o){let u=i[d],f=s.data[d];l.push({key:{status:"valid",value:d},value:u._parse(new re(s,f,s.path,d)),alwaysSet:d in s.data})}if(this._def.catchall instanceof ne){let d=this._def.unknownKeys;if(d==="passthrough")for(let u of a)l.push({key:{status:"valid",value:u},value:{status:"valid",value:s.data[u]}});else if(d==="strict")a.length>0&&(_(s,{code:p.unrecognized_keys,keys:a}),n.dirty());else if(d!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{let d=this._def.catchall;for(let u of a){let f=s.data[u];l.push({key:{status:"valid",value:u},value:d._parse(new re(s,f,s.path,u)),alwaysSet:u in s.data})}}return s.common.async?Promise.resolve().then(async()=>{let d=[];for(let u of l){let f=await u.key,g=await u.value;d.push({key:f,value:g,alwaysSet:u.alwaysSet})}return d}).then(d=>z.mergeObjectSync(n,d)):z.mergeObjectSync(n,l)}get shape(){return this._def.shape()}strict(e){return b.errToObj,new r({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,n)=>{var s,i,o,a;let l=(o=(i=(s=this._def).errorMap)===null||i===void 0?void 0:i.call(s,t,n).message)!==null&&o!==void 0?o:n.defaultError;return t.code==="unrecognized_keys"?{message:(a=b.errToObj(e).message)!==null&&a!==void 0?a:l}:{message:l}}}:{}})}strip(){return new r({...this._def,unknownKeys:"strip"})}passthrough(){return new r({...this._def,unknownKeys:"passthrough"})}extend(e){return new r({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new r({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:m.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new r({...this._def,catchall:e})}pick(e){let t={};return I.objectKeys(e).forEach(n=>{e[n]&&this.shape[n]&&(t[n]=this.shape[n])}),new r({...this._def,shape:()=>t})}omit(e){let t={};return I.objectKeys(this.shape).forEach(n=>{e[n]||(t[n]=this.shape[n])}),new r({...this._def,shape:()=>t})}deepPartial(){return gt(this)}partial(e){let t={};return I.objectKeys(this.shape).forEach(n=>{let s=this.shape[n];e&&!e[n]?t[n]=s:t[n]=s.optional()}),new r({...this._def,shape:()=>t})}required(e){let t={};return I.objectKeys(this.shape).forEach(n=>{if(e&&!e[n])t[n]=this.shape[n];else{let i=this.shape[n];for(;i instanceof Y;)i=i._def.innerType;t[n]=i}}),new r({...this._def,shape:()=>t})}keyof(){return Es(I.objectKeys(this.shape))}};J.create=(r,e)=>new J({shape:()=>r,unknownKeys:"strip",catchall:ne.create(),typeName:m.ZodObject,...R(e)}),J.strictCreate=(r,e)=>new J({shape:()=>r,unknownKeys:"strict",catchall:ne.create(),typeName:m.ZodObject,...R(e)}),J.lazycreate=(r,e)=>new J({shape:r,unknownKeys:"strip",catchall:ne.create(),typeName:m.ZodObject,...R(e)});var Ue=class extends k{_parse(e){let{ctx:t}=this._processInputParams(e),n=this._def.options;function s(i){for(let a of i)if(a.result.status==="valid")return a.result;for(let a of i)if(a.result.status==="dirty")return t.common.issues.push(...a.ctx.common.issues),a.result;let o=i.map(a=>new Q(a.ctx.common.issues));return _(t,{code:p.invalid_union,unionErrors:o}),E}if(t.common.async)return Promise.all(n.map(async i=>{let o={...t,common:{...t.common,issues:[]},parent:null};return{result:await i._parseAsync({data:t.data,path:t.path,parent:o}),ctx:o}})).then(s);{let i,o=[];for(let l of n){let d={...t,common:{...t.common,issues:[]},parent:null},u=l._parseSync({data:t.data,path:t.path,parent:d});if(u.status==="valid")return u;u.status==="dirty"&&!i&&(i={result:u,ctx:d}),d.common.issues.length&&o.push(d.common.issues)}if(i)return t.common.issues.push(...i.ctx.common.issues),i.result;let a=o.map(l=>new Q(l));return _(t,{code:p.invalid_union,unionErrors:a}),E}}get options(){return this._def.options}};Ue.create=(r,e)=>new Ue({options:r,typeName:m.ZodUnion,...R(e)});var ke=r=>r instanceof qe?ke(r.schema):r instanceof X?ke(r.innerType()):r instanceof Be?[r.value]:r instanceof Ve?r.options:r instanceof He?I.objectValues(r.enum):r instanceof ze?ke(r._def.innerType):r instanceof je?[void 0]:r instanceof Fe?[null]:r instanceof Y?[void 0,...ke(r.unwrap())]:r instanceof le?[null,...ke(r.unwrap())]:r instanceof vt||r instanceof Ge?ke(r.unwrap()):r instanceof We?ke(r._def.innerType):[],Nt=class r extends k{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==y.object)return _(t,{code:p.invalid_type,expected:y.object,received:t.parsedType}),E;let n=this.discriminator,s=t.data[n],i=this.optionsMap.get(s);return i?t.common.async?i._parseAsync({data:t.data,path:t.path,parent:t}):i._parseSync({data:t.data,path:t.path,parent:t}):(_(t,{code:p.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),E)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){let s=new Map;for(let i of t){let o=ke(i.shape[e]);if(!o.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let a of o){if(s.has(a))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(a)}`);s.set(a,i)}}return new r({typeName:m.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:s,...R(n)})}};function rn(r,e){let t=ve(r),n=ve(e);if(r===e)return{valid:!0,data:r};if(t===y.object&&n===y.object){let s=I.objectKeys(e),i=I.objectKeys(r).filter(a=>s.indexOf(a)!==-1),o={...r,...e};for(let a of i){let l=rn(r[a],e[a]);if(!l.valid)return{valid:!1};o[a]=l.data}return{valid:!0,data:o}}else if(t===y.array&&n===y.array){if(r.length!==e.length)return{valid:!1};let s=[];for(let i=0;i<r.length;i++){let o=r[i],a=e[i],l=rn(o,a);if(!l.valid)return{valid:!1};s.push(l.data)}return{valid:!0,data:s}}else return t===y.date&&n===y.date&&+r==+e?{valid:!0,data:r}:{valid:!1}}var Ze=class extends k{_parse(e){let{status:t,ctx:n}=this._processInputParams(e),s=(i,o)=>{if(Yt(i)||Yt(o))return E;let a=rn(i.value,o.value);return a.valid?((Kt(i)||Kt(o))&&t.dirty(),{status:t.value,value:a.data}):(_(n,{code:p.invalid_intersection_types}),E)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([i,o])=>s(i,o)):s(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}};Ze.create=(r,e,t)=>new Ze({left:r,right:e,typeName:m.ZodIntersection,...R(t)});var ce=class r extends k{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==y.array)return _(n,{code:p.invalid_type,expected:y.array,received:n.parsedType}),E;if(n.data.length<this._def.items.length)return _(n,{code:p.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),E;!this._def.rest&&n.data.length>this._def.items.length&&(_(n,{code:p.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let i=[...n.data].map((o,a)=>{let l=this._def.items[a]||this._def.rest;return l?l._parse(new re(n,o,n.path,a)):null}).filter(o=>!!o);return n.common.async?Promise.all(i).then(o=>z.mergeArray(t,o)):z.mergeArray(t,i)}get items(){return this._def.items}rest(e){return new r({...this._def,rest:e})}};ce.create=(r,e)=>{if(!Array.isArray(r))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new ce({items:r,typeName:m.ZodTuple,rest:null,...R(e)})};var Dt=class r extends k{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==y.object)return _(n,{code:p.invalid_type,expected:y.object,received:n.parsedType}),E;let s=[],i=this._def.keyType,o=this._def.valueType;for(let a in n.data)s.push({key:i._parse(new re(n,a,n.path,a)),value:o._parse(new re(n,n.data[a],n.path,a)),alwaysSet:a in n.data});return n.common.async?z.mergeObjectAsync(t,s):z.mergeObjectSync(t,s)}get element(){return this._def.valueType}static create(e,t,n){return t instanceof k?new r({keyType:e,valueType:t,typeName:m.ZodRecord,...R(n)}):new r({keyType:Se.create(),valueType:e,typeName:m.ZodRecord,...R(t)})}},ot=class extends k{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==y.map)return _(n,{code:p.invalid_type,expected:y.map,received:n.parsedType}),E;let s=this._def.keyType,i=this._def.valueType,o=[...n.data.entries()].map(([a,l],d)=>({key:s._parse(new re(n,a,n.path,[d,"key"])),value:i._parse(new re(n,l,n.path,[d,"value"]))}));if(n.common.async){let a=new Map;return Promise.resolve().then(async()=>{for(let l of o){let d=await l.key,u=await l.value;if(d.status==="aborted"||u.status==="aborted")return E;(d.status==="dirty"||u.status==="dirty")&&t.dirty(),a.set(d.value,u.value)}return{status:t.value,value:a}})}else{let a=new Map;for(let l of o){let d=l.key,u=l.value;if(d.status==="aborted"||u.status==="aborted")return E;(d.status==="dirty"||u.status==="dirty")&&t.dirty(),a.set(d.value,u.value)}return{status:t.value,value:a}}}};ot.create=(r,e,t)=>new ot({valueType:e,keyType:r,typeName:m.ZodMap,...R(t)});var at=class r extends k{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==y.set)return _(n,{code:p.invalid_type,expected:y.set,received:n.parsedType}),E;let s=this._def;s.minSize!==null&&n.data.size<s.minSize.value&&(_(n,{code:p.too_small,minimum:s.minSize.value,type:"set",inclusive:!0,exact:!1,message:s.minSize.message}),t.dirty()),s.maxSize!==null&&n.data.size>s.maxSize.value&&(_(n,{code:p.too_big,maximum:s.maxSize.value,type:"set",inclusive:!0,exact:!1,message:s.maxSize.message}),t.dirty());let i=this._def.valueType;function o(l){let d=new Set;for(let u of l){if(u.status==="aborted")return E;u.status==="dirty"&&t.dirty(),d.add(u.value)}return{status:t.value,value:d}}let a=[...n.data.values()].map((l,d)=>i._parse(new re(n,l,n.path,d)));return n.common.async?Promise.all(a).then(l=>o(l)):o(a)}min(e,t){return new r({...this._def,minSize:{value:e,message:b.toString(t)}})}max(e,t){return new r({...this._def,maxSize:{value:e,message:b.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}};at.create=(r,e)=>new at({valueType:r,minSize:null,maxSize:null,typeName:m.ZodSet,...R(e)});var At=class r extends k{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==y.function)return _(t,{code:p.invalid_type,expected:y.function,received:t.parsedType}),E;function n(a,l){return Pt({data:a,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,Tt(),rt].filter(d=>!!d),issueData:{code:p.invalid_arguments,argumentsError:l}})}function s(a,l){return Pt({data:a,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,Tt(),rt].filter(d=>!!d),issueData:{code:p.invalid_return_type,returnTypeError:l}})}let i={errorMap:t.common.contextualErrorMap},o=t.data;if(this._def.returns instanceof Ce){let a=this;return G(async function(...l){let d=new Q([]),u=await a._def.args.parseAsync(l,i).catch(v=>{throw d.addIssue(n(l,v)),d}),f=await Reflect.apply(o,this,u);return await a._def.returns._def.type.parseAsync(f,i).catch(v=>{throw d.addIssue(s(f,v)),d})})}else{let a=this;return G(function(...l){let d=a._def.args.safeParse(l,i);if(!d.success)throw new Q([n(l,d.error)]);let u=Reflect.apply(o,this,d.data),f=a._def.returns.safeParse(u,i);if(!f.success)throw new Q([s(u,f.error)]);return f.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new r({...this._def,args:ce.create(e).rest(_e.create())})}returns(e){return new r({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new r({args:e||ce.create([]).rest(_e.create()),returns:t||_e.create(),typeName:m.ZodFunction,...R(n)})}},qe=class extends k{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}};qe.create=(r,e)=>new qe({getter:r,typeName:m.ZodLazy,...R(e)});var Be=class extends k{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return _(t,{received:t.data,code:p.invalid_literal,expected:this._def.value}),E}return{status:"valid",value:e.data}}get value(){return this._def.value}};Be.create=(r,e)=>new Be({value:r,typeName:m.ZodLiteral,...R(e)});function Es(r,e){return new Ve({values:r,typeName:m.ZodEnum,...R(e)})}var Ve=class r extends k{constructor(){super(...arguments),Rt.set(this,void 0)}_parse(e){if(typeof e.data!="string"){let t=this._getOrReturnCtx(e),n=this._def.values;return _(t,{expected:I.joinValues(n),received:t.parsedType,code:p.invalid_type}),E}if(Qt(this,Rt,"f")||xs(this,Rt,new Set(this._def.values),"f"),!Qt(this,Rt,"f").has(e.data)){let t=this._getOrReturnCtx(e),n=this._def.values;return _(t,{received:t.data,code:p.invalid_enum_value,options:n}),E}return G(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return r.create(e,{...this._def,...t})}exclude(e,t=this._def){return r.create(this.options.filter(n=>!e.includes(n)),{...this._def,...t})}};Rt=new WeakMap,Ve.create=Es;var He=class extends k{constructor(){super(...arguments),Ot.set(this,void 0)}_parse(e){let t=I.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==y.string&&n.parsedType!==y.number){let s=I.objectValues(t);return _(n,{expected:I.joinValues(s),received:n.parsedType,code:p.invalid_type}),E}if(Qt(this,Ot,"f")||xs(this,Ot,new Set(I.getValidEnumValues(this._def.values)),"f"),!Qt(this,Ot,"f").has(e.data)){let s=I.objectValues(t);return _(n,{received:n.data,code:p.invalid_enum_value,options:s}),E}return G(e.data)}get enum(){return this._def.values}};Ot=new WeakMap,He.create=(r,e)=>new He({values:r,typeName:m.ZodNativeEnum,...R(e)});var Ce=class extends k{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==y.promise&&t.common.async===!1)return _(t,{code:p.invalid_type,expected:y.promise,received:t.parsedType}),E;let n=t.parsedType===y.promise?t.data:Promise.resolve(t.data);return G(n.then(s=>this._def.type.parseAsync(s,{path:t.path,errorMap:t.common.contextualErrorMap})))}};Ce.create=(r,e)=>new Ce({type:r,typeName:m.ZodPromise,...R(e)});var X=class extends k{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===m.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:n}=this._processInputParams(e),s=this._def.effect||null,i={addIssue:o=>{_(n,o),o.fatal?t.abort():t.dirty()},get path(){return n.path}};if(i.addIssue=i.addIssue.bind(i),s.type==="preprocess"){let o=s.transform(n.data,i);if(n.common.async)return Promise.resolve(o).then(async a=>{if(t.value==="aborted")return E;let l=await this._def.schema._parseAsync({data:a,path:n.path,parent:n});return l.status==="aborted"?E:l.status==="dirty"||t.value==="dirty"?nt(l.value):l});{if(t.value==="aborted")return E;let a=this._def.schema._parseSync({data:o,path:n.path,parent:n});return a.status==="aborted"?E:a.status==="dirty"||t.value==="dirty"?nt(a.value):a}}if(s.type==="refinement"){let o=a=>{let l=s.refinement(a,i);if(n.common.async)return Promise.resolve(l);if(l instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return a};if(n.common.async===!1){let a=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return a.status==="aborted"?E:(a.status==="dirty"&&t.dirty(),o(a.value),{status:t.value,value:a.value})}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(a=>a.status==="aborted"?E:(a.status==="dirty"&&t.dirty(),o(a.value).then(()=>({status:t.value,value:a.value}))))}if(s.type==="transform")if(n.common.async===!1){let o=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!Ae(o))return o;let a=s.transform(o.value,i);if(a instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:a}}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(o=>Ae(o)?Promise.resolve(s.transform(o.value,i)).then(a=>({status:t.value,value:a})):o);I.assertNever(s)}};X.create=(r,e,t)=>new X({schema:r,typeName:m.ZodEffects,effect:e,...R(t)}),X.createWithPreprocess=(r,e,t)=>new X({schema:e,effect:{type:"preprocess",transform:r},typeName:m.ZodEffects,...R(t)});var Y=class extends k{_parse(e){return this._getType(e)===y.undefined?G(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}};Y.create=(r,e)=>new Y({innerType:r,typeName:m.ZodOptional,...R(e)});var le=class extends k{_parse(e){return this._getType(e)===y.null?G(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}};le.create=(r,e)=>new le({innerType:r,typeName:m.ZodNullable,...R(e)});var ze=class extends k{_parse(e){let{ctx:t}=this._processInputParams(e),n=t.data;return t.parsedType===y.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}};ze.create=(r,e)=>new ze({innerType:r,typeName:m.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...R(e)});var We=class extends k{_parse(e){let{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},s=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return mt(s)?s.then(i=>({status:"valid",value:i.status==="valid"?i.value:this._def.catchValue({get error(){return new Q(n.common.issues)},input:n.data})})):{status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new Q(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}};We.create=(r,e)=>new We({innerType:r,typeName:m.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...R(e)});var ct=class extends k{_parse(e){if(this._getType(e)!==y.nan){let n=this._getOrReturnCtx(e);return _(n,{code:p.invalid_type,expected:y.nan,received:n.parsedType}),E}return{status:"valid",value:e.data}}};ct.create=r=>new ct({typeName:m.ZodNaN,...R(r)});var ks=Symbol("zod_brand"),vt=class extends k{_parse(e){let{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}},_t=class r extends k{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{let i=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return i.status==="aborted"?E:i.status==="dirty"?(t.dirty(),nt(i.value)):this._def.out._parseAsync({data:i.value,path:n.path,parent:n})})();{let s=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return s.status==="aborted"?E:s.status==="dirty"?(t.dirty(),{status:"dirty",value:s.value}):this._def.out._parseSync({data:s.value,path:n.path,parent:n})}}static create(e,t){return new r({in:e,out:t,typeName:m.ZodPipeline})}},Ge=class extends k{_parse(e){let t=this._def.innerType._parse(e),n=s=>(Ae(s)&&(s.value=Object.freeze(s.value)),s);return mt(t)?t.then(s=>n(s)):n(t)}unwrap(){return this._def.innerType}};Ge.create=(r,e)=>new Ge({innerType:r,typeName:m.ZodReadonly,...R(e)});function Cs(r,e){let t=typeof r=="function"?r(e):typeof r=="string"?{message:r}:r;return typeof t=="string"?{message:t}:t}function nn(r,e={},t){return r?Ee.create().superRefine((n,s)=>{var i,o;let a=r(n);if(a instanceof Promise)return a.then(l=>{var d,u;if(!l){let f=Cs(e,n),g=(u=(d=f.fatal)!==null&&d!==void 0?d:t)!==null&&u!==void 0?u:!0;s.addIssue({code:"custom",...f,fatal:g})}});if(!a){let l=Cs(e,n),d=(o=(i=l.fatal)!==null&&i!==void 0?i:t)!==null&&o!==void 0?o:!0;s.addIssue({code:"custom",...l,fatal:d})}}):Ee.create()}var Ts={object:J.lazycreate},m;(function(r){r.ZodString="ZodString",r.ZodNumber="ZodNumber",r.ZodNaN="ZodNaN",r.ZodBigInt="ZodBigInt",r.ZodBoolean="ZodBoolean",r.ZodDate="ZodDate",r.ZodSymbol="ZodSymbol",r.ZodUndefined="ZodUndefined",r.ZodNull="ZodNull",r.ZodAny="ZodAny",r.ZodUnknown="ZodUnknown",r.ZodNever="ZodNever",r.ZodVoid="ZodVoid",r.ZodArray="ZodArray",r.ZodObject="ZodObject",r.ZodUnion="ZodUnion",r.ZodDiscriminatedUnion="ZodDiscriminatedUnion",r.ZodIntersection="ZodIntersection",r.ZodTuple="ZodTuple",r.ZodRecord="ZodRecord",r.ZodMap="ZodMap",r.ZodSet="ZodSet",r.ZodFunction="ZodFunction",r.ZodLazy="ZodLazy",r.ZodLiteral="ZodLiteral",r.ZodEnum="ZodEnum",r.ZodEffects="ZodEffects",r.ZodNativeEnum="ZodNativeEnum",r.ZodOptional="ZodOptional",r.ZodNullable="ZodNullable",r.ZodDefault="ZodDefault",r.ZodCatch="ZodCatch",r.ZodPromise="ZodPromise",r.ZodBranded="ZodBranded",r.ZodPipeline="ZodPipeline",r.ZodReadonly="ZodReadonly"})(m||(m={}));var Ps=(r,e={message:`Input not instance of ${r.name}`})=>nn(t=>t instanceof r,e),sn=Se.create,on=Ie.create,Rs=ct.create,Os=Le.create,an=Me.create,Ns=$e.create,Ds=st.create,As=je.create,Is=Fe.create,Ls=Ee.create,Ms=_e.create,$s=ne.create,js=it.create,Fs=ye.create,Us=J.create,Zs=J.strictCreate,qs=Ue.create,Bs=Nt.create,Vs=Ze.create,Hs=ce.create,zs=Dt.create,Ws=ot.create,Gs=at.create,Js=At.create,Xs=qe.create,Ys=Be.create,Ks=Ve.create,Qs=He.create,ei=Ce.create,er=X.create,ti=Y.create,ri=le.create,ni=X.createWithPreprocess,si=_t.create,ii=()=>sn().optional(),oi=()=>on().optional(),ai=()=>an().optional(),ci={string:r=>Se.create({...r,coerce:!0}),number:r=>Ie.create({...r,coerce:!0}),boolean:r=>Me.create({...r,coerce:!0}),bigint:r=>Le.create({...r,coerce:!0}),date:r=>$e.create({...r,coerce:!0})},li=E,c=Object.freeze({__proto__:null,defaultErrorMap:rt,setErrorMap:_s,getErrorMap:Tt,makeIssue:Pt,EMPTY_PATH:ys,addIssueToContext:_,ParseStatus:z,INVALID:E,DIRTY:nt,OK:G,isAborted:Yt,isDirty:Kt,isValid:Ae,isAsync:mt,get util(){return I},get objectUtil(){return Xt},ZodParsedType:y,getParsedType:ve,ZodType:k,datetimeRegex:tn,ZodString:Se,ZodNumber:Ie,ZodBigInt:Le,ZodBoolean:Me,ZodDate:$e,ZodSymbol:st,ZodUndefined:je,ZodNull:Fe,ZodAny:Ee,ZodUnknown:_e,ZodNever:ne,ZodVoid:it,ZodArray:ye,ZodObject:J,ZodUnion:Ue,ZodDiscriminatedUnion:Nt,ZodIntersection:Ze,ZodTuple:ce,ZodRecord:Dt,ZodMap:ot,ZodSet:at,ZodFunction:At,ZodLazy:qe,ZodLiteral:Be,ZodEnum:Ve,ZodNativeEnum:He,ZodPromise:Ce,ZodEffects:X,ZodTransformer:X,ZodOptional:Y,ZodNullable:le,ZodDefault:ze,ZodCatch:We,ZodNaN:ct,BRAND:ks,ZodBranded:vt,ZodPipeline:_t,ZodReadonly:Ge,custom:nn,Schema:k,ZodSchema:k,late:Ts,get ZodFirstPartyTypeKind(){return m},coerce:ci,any:Ls,array:Fs,bigint:Os,boolean:an,date:Ns,discriminatedUnion:Bs,effect:er,enum:Ks,function:Js,instanceof:Ps,intersection:Vs,lazy:Xs,literal:Ys,map:Ws,nan:Rs,nativeEnum:Qs,never:$s,null:Is,nullable:ri,number:on,object:Us,oboolean:ai,onumber:oi,optional:ti,ostring:ii,pipeline:si,preprocess:ni,promise:ei,record:zs,set:Gs,strictObject:Zs,string:sn,symbol:Ds,transformer:er,tuple:Hs,undefined:As,union:qs,unknown:Ms,void:js,NEVER:li,ZodIssueCode:p,quotelessJson:gs,ZodError:Q});var cn="2025-03-26",Si=[cn,"2024-11-05","2024-10-07"],tr="2.0",di=c.union([c.string(),c.number().int()]),ui=c.string(),Oc=c.object({progressToken:c.optional(di)}).passthrough(),de=c.object({_meta:c.optional(Oc)}).passthrough(),ee=c.object({method:c.string(),params:c.optional(de)}),It=c.object({_meta:c.optional(c.object({}).passthrough())}).passthrough(),xe=c.object({method:c.string(),params:c.optional(It)}),ue=c.object({_meta:c.optional(c.object({}).passthrough())}).passthrough(),rr=c.union([c.string(),c.number().int()]),fi=c.object({jsonrpc:c.literal(tr),id:rr}).merge(ee).strict(),Ei=r=>fi.safeParse(r).success,hi=c.object({jsonrpc:c.literal(tr)}).merge(xe).strict(),ki=r=>hi.safeParse(r).success,pi=c.object({jsonrpc:c.literal(tr),id:rr,result:ue}).strict(),ln=r=>pi.safeParse(r).success,U;(function(r){r[r.ConnectionClosed=-32e3]="ConnectionClosed",r[r.RequestTimeout=-32001]="RequestTimeout",r[r.ParseError=-32700]="ParseError",r[r.InvalidRequest=-32600]="InvalidRequest",r[r.MethodNotFound=-32601]="MethodNotFound",r[r.InvalidParams=-32602]="InvalidParams",r[r.InternalError=-32603]="InternalError"})(U||(U={}));var mi=c.object({jsonrpc:c.literal(tr),id:rr,error:c.object({code:c.number().int(),message:c.string(),data:c.optional(c.unknown())})}).strict(),Ci=r=>mi.safeParse(r).success,Ti=c.union([fi,hi,pi,mi]),cr=ue.strict(),lr=xe.extend({method:c.literal("notifications/cancelled"),params:It.extend({requestId:rr,reason:c.string().optional()})}),gi=c.object({name:c.string(),version:c.string()}).passthrough(),Nc=c.object({experimental:c.optional(c.object({}).passthrough()),sampling:c.optional(c.object({}).passthrough()),roots:c.optional(c.object({listChanged:c.optional(c.boolean())}).passthrough())}).passthrough(),dr=ee.extend({method:c.literal("initialize"),params:de.extend({protocolVersion:c.string(),capabilities:Nc,clientInfo:gi})}),vu=r=>dr.safeParse(r).success,Dc=c.object({experimental:c.optional(c.object({}).passthrough()),logging:c.optional(c.object({}).passthrough()),completions:c.optional(c.object({}).passthrough()),prompts:c.optional(c.object({listChanged:c.optional(c.boolean())}).passthrough()),resources:c.optional(c.object({subscribe:c.optional(c.boolean()),listChanged:c.optional(c.boolean())}).passthrough()),tools:c.optional(c.object({listChanged:c.optional(c.boolean())}).passthrough())}).passthrough(),Ac=ue.extend({protocolVersion:c.string(),capabilities:Dc,serverInfo:gi,instructions:c.optional(c.string())}),ur=xe.extend({method:c.literal("notifications/initialized")}),_u=r=>ur.safeParse(r).success,fr=ee.extend({method:c.literal("ping")}),Ic=c.object({progress:c.number(),total:c.optional(c.number())}).passthrough(),hr=xe.extend({method:c.literal("notifications/progress"),params:It.merge(Ic).extend({progressToken:di})}),nr=ee.extend({params:de.extend({cursor:c.optional(ui)}).optional()}),sr=ue.extend({nextCursor:c.optional(ui)}),vi=c.object({uri:c.string(),mimeType:c.optional(c.string())}).passthrough(),_i=vi.extend({text:c.string()}),yi=vi.extend({blob:c.string().base64()}),Lc=c.object({uri:c.string(),name:c.string(),description:c.optional(c.string()),mimeType:c.optional(c.string())}).passthrough(),Mc=c.object({uriTemplate:c.string(),name:c.string(),description:c.optional(c.string()),mimeType:c.optional(c.string())}).passthrough(),pr=nr.extend({method:c.literal("resources/list")}),$c=sr.extend({resources:c.array(Lc)}),mr=nr.extend({method:c.literal("resources/templates/list")}),jc=sr.extend({resourceTemplates:c.array(Mc)}),gr=ee.extend({method:c.literal("resources/read"),params:de.extend({uri:c.string()})}),Fc=ue.extend({contents:c.array(c.union([_i,yi]))}),Uc=xe.extend({method:c.literal("notifications/resources/list_changed")}),Zc=ee.extend({method:c.literal("resources/subscribe"),params:de.extend({uri:c.string()})}),qc=ee.extend({method:c.literal("resources/unsubscribe"),params:de.extend({uri:c.string()})}),Bc=xe.extend({method:c.literal("notifications/resources/updated"),params:It.extend({uri:c.string()})}),Vc=c.object({name:c.string(),description:c.optional(c.string()),required:c.optional(c.boolean())}).passthrough(),Hc=c.object({name:c.string(),description:c.optional(c.string()),arguments:c.optional(c.array(Vc))}).passthrough(),vr=nr.extend({method:c.literal("prompts/list")}),zc=sr.extend({prompts:c.array(Hc)}),_r=ee.extend({method:c.literal("prompts/get"),params:de.extend({name:c.string(),arguments:c.optional(c.record(c.string()))})}),ir=c.object({type:c.literal("text"),text:c.string()}).passthrough(),or=c.object({type:c.literal("image"),data:c.string().base64(),mimeType:c.string()}).passthrough(),ar=c.object({type:c.literal("audio"),data:c.string().base64(),mimeType:c.string()}).passthrough(),xi=c.object({type:c.literal("resource"),resource:c.union([_i,yi])}).passthrough(),Wc=c.object({role:c.enum(["user","assistant"]),content:c.union([ir,or,ar,xi])}).passthrough(),Gc=ue.extend({description:c.optional(c.string()),messages:c.array(Wc)}),Jc=xe.extend({method:c.literal("notifications/prompts/list_changed")}),Xc=c.object({title:c.optional(c.string()),readOnlyHint:c.optional(c.boolean()),destructiveHint:c.optional(c.boolean()),idempotentHint:c.optional(c.boolean()),openWorldHint:c.optional(c.boolean())}).passthrough(),Yc=c.object({name:c.string(),description:c.optional(c.string()),inputSchema:c.object({type:c.literal("object"),properties:c.optional(c.object({}).passthrough())}).passthrough(),annotations:c.optional(Xc)}).passthrough(),yr=nr.extend({method:c.literal("tools/list")}),Kc=sr.extend({tools:c.array(Yc)}),bi=ue.extend({content:c.array(c.union([ir,or,ar,xi])),isError:c.boolean().default(!1).optional()}),yu=bi.or(ue.extend({toolResult:c.unknown()})),xr=ee.extend({method:c.literal("tools/call"),params:de.extend({name:c.string(),arguments:c.optional(c.record(c.unknown()))})}),Qc=xe.extend({method:c.literal("notifications/tools/list_changed")}),wi=c.enum(["debug","info","notice","warning","error","critical","alert","emergency"]),el=ee.extend({method:c.literal("logging/setLevel"),params:de.extend({level:wi})}),tl=xe.extend({method:c.literal("notifications/message"),params:It.extend({level:wi,logger:c.optional(c.string()),data:c.unknown()})}),rl=c.object({name:c.string().optional()}).passthrough(),nl=c.object({hints:c.optional(c.array(rl)),costPriority:c.optional(c.number().min(0).max(1)),speedPriority:c.optional(c.number().min(0).max(1)),intelligencePriority:c.optional(c.number().min(0).max(1))}).passthrough(),sl=c.object({role:c.enum(["user","assistant"]),content:c.union([ir,or,ar])}).passthrough(),il=ee.extend({method:c.literal("sampling/createMessage"),params:de.extend({messages:c.array(sl),systemPrompt:c.optional(c.string()),includeContext:c.optional(c.enum(["none","thisServer","allServers"])),temperature:c.optional(c.number()),maxTokens:c.number().int(),stopSequences:c.optional(c.array(c.string())),metadata:c.optional(c.object({}).passthrough()),modelPreferences:c.optional(nl)})}),dn=ue.extend({model:c.string(),stopReason:c.optional(c.enum(["endTurn","stopSequence","maxTokens"]).or(c.string())),role:c.enum(["user","assistant"]),content:c.discriminatedUnion("type",[ir,or,ar])}),ol=c.object({type:c.literal("ref/resource"),uri:c.string()}).passthrough(),al=c.object({type:c.literal("ref/prompt"),name:c.string()}).passthrough(),br=ee.extend({method:c.literal("completion/complete"),params:de.extend({ref:c.union([al,ol]),argument:c.object({name:c.string(),value:c.string()}).passthrough()})}),cl=ue.extend({completion:c.object({values:c.array(c.string()).max(100),total:c.optional(c.number().int()),hasMore:c.optional(c.boolean())}).passthrough()}),ll=c.object({uri:c.string().startsWith("file://"),name:c.optional(c.string())}).passthrough(),dl=ee.extend({method:c.literal("roots/list")}),un=ue.extend({roots:c.array(ll)}),ul=xe.extend({method:c.literal("notifications/roots/list_changed")}),xu=c.union([fr,dr,br,el,_r,vr,pr,mr,gr,Zc,qc,xr,yr]),bu=c.union([lr,hr,ur,ul]),wu=c.union([cr,dn,un]),Su=c.union([fr,il,dl]),Eu=c.union([lr,hr,tl,Bc,Uc,Qc,Jc]),ku=c.union([cr,Ac,cl,Gc,zc,$c,jc,Fc,bi,Kc]),Z=class extends Error{constructor(e,t,n){super(`MCP error ${e}: ${t}`),this.code=e,this.data=n,this.name="McpError"}};var fl=6e4,wr=class{constructor(e){this._options=e,this._requestMessageId=0,this._requestHandlers=new Map,this._requestHandlerAbortControllers=new Map,this._notificationHandlers=new Map,this._responseHandlers=new Map,this._progressHandlers=new Map,this._timeoutInfo=new Map,this.setNotificationHandler(lr,t=>{let n=this._requestHandlerAbortControllers.get(t.params.requestId);n?.abort(t.params.reason)}),this.setNotificationHandler(hr,t=>{this._onprogress(t)}),this.setRequestHandler(fr,t=>({}))}_setupTimeout(e,t,n,s,i=!1){this._timeoutInfo.set(e,{timeoutId:setTimeout(s,t),startTime:Date.now(),timeout:t,maxTotalTimeout:n,resetTimeoutOnProgress:i,onTimeout:s})}_resetTimeout(e){let t=this._timeoutInfo.get(e);if(!t)return!1;let n=Date.now()-t.startTime;if(t.maxTotalTimeout&&n>=t.maxTotalTimeout)throw this._timeoutInfo.delete(e),new Z(U.RequestTimeout,"Maximum total timeout exceeded",{maxTotalTimeout:t.maxTotalTimeout,totalElapsed:n});return clearTimeout(t.timeoutId),t.timeoutId=setTimeout(t.onTimeout,t.timeout),!0}_cleanupTimeout(e){let t=this._timeoutInfo.get(e);t&&(clearTimeout(t.timeoutId),this._timeoutInfo.delete(e))}async connect(e){this._transport=e,this._transport.onclose=()=>{this._onclose()},this._transport.onerror=t=>{this._onerror(t)},this._transport.onmessage=(t,n)=>{ln(t)||Ci(t)?this._onresponse(t):Ei(t)?this._onrequest(t,n):ki(t)?this._onnotification(t):this._onerror(new Error(`Unknown message type: ${JSON.stringify(t)}`))},await this._transport.start()}_onclose(){var e;let t=this._responseHandlers;this._responseHandlers=new Map,this._progressHandlers.clear(),this._transport=void 0,(e=this.onclose)===null||e===void 0||e.call(this);let n=new Z(U.ConnectionClosed,"Connection closed");for(let s of t.values())s(n)}_onerror(e){var t;(t=this.onerror)===null||t===void 0||t.call(this,e)}_onnotification(e){var t;let n=(t=this._notificationHandlers.get(e.method))!==null&&t!==void 0?t:this.fallbackNotificationHandler;n!==void 0&&Promise.resolve().then(()=>n(e)).catch(s=>this._onerror(new Error(`Uncaught error in notification handler: ${s}`)))}_onrequest(e,t){var n,s,i,o;let a=(n=this._requestHandlers.get(e.method))!==null&&n!==void 0?n:this.fallbackRequestHandler;if(a===void 0){(s=this._transport)===null||s===void 0||s.send({jsonrpc:"2.0",id:e.id,error:{code:U.MethodNotFound,message:"Method not found"}}).catch(u=>this._onerror(new Error(`Failed to send an error response: ${u}`)));return}let l=new AbortController;this._requestHandlerAbortControllers.set(e.id,l);let d={signal:l.signal,sessionId:(i=this._transport)===null||i===void 0?void 0:i.sessionId,_meta:(o=e.params)===null||o===void 0?void 0:o._meta,sendNotification:u=>this.notification(u,{relatedRequestId:e.id}),sendRequest:(u,f,g)=>this.request(u,f,{...g,relatedRequestId:e.id}),authInfo:t?.authInfo,requestId:e.id};Promise.resolve().then(()=>a(e,d)).then(u=>{var f;if(!l.signal.aborted)return(f=this._transport)===null||f===void 0?void 0:f.send({result:u,jsonrpc:"2.0",id:e.id})},u=>{var f,g;if(!l.signal.aborted)return(f=this._transport)===null||f===void 0?void 0:f.send({jsonrpc:"2.0",id:e.id,error:{code:Number.isSafeInteger(u.code)?u.code:U.InternalError,message:(g=u.message)!==null&&g!==void 0?g:"Internal error"}})}).catch(u=>this._onerror(new Error(`Failed to send response: ${u}`))).finally(()=>{this._requestHandlerAbortControllers.delete(e.id)})}_onprogress(e){let{progressToken:t,...n}=e.params,s=Number(t),i=this._progressHandlers.get(s);if(!i){this._onerror(new Error(`Received a progress notification for an unknown token: ${JSON.stringify(e)}`));return}let o=this._responseHandlers.get(s),a=this._timeoutInfo.get(s);if(a&&o&&a.resetTimeoutOnProgress)try{this._resetTimeout(s)}catch(l){o(l);return}i(n)}_onresponse(e){let t=Number(e.id),n=this._responseHandlers.get(t);if(n===void 0){this._onerror(new Error(`Received a response for an unknown message ID: ${JSON.stringify(e)}`));return}if(this._responseHandlers.delete(t),this._progressHandlers.delete(t),this._cleanupTimeout(t),ln(e))n(e);else{let s=new Z(e.error.code,e.error.message,e.error.data);n(s)}}get transport(){return this._transport}async close(){var e;await((e=this._transport)===null||e===void 0?void 0:e.close())}request(e,t,n){let{relatedRequestId:s,resumptionToken:i,onresumptiontoken:o}=n??{};return new Promise((a,l)=>{var d,u,f,g,v;if(!this._transport){l(new Error("Not connected"));return}((d=this._options)===null||d===void 0?void 0:d.enforceStrictCapabilities)===!0&&this.assertCapabilityForMethod(e.method),(u=n?.signal)===null||u===void 0||u.throwIfAborted();let h=this._requestMessageId++,x={...e,jsonrpc:"2.0",id:h};n?.onprogress&&(this._progressHandlers.set(h,n.onprogress),x.params={...e.params,_meta:{progressToken:h}});let w=O=>{var P;this._responseHandlers.delete(h),this._progressHandlers.delete(h),this._cleanupTimeout(h),(P=this._transport)===null||P===void 0||P.send({jsonrpc:"2.0",method:"notifications/cancelled",params:{requestId:h,reason:String(O)}},{relatedRequestId:s,resumptionToken:i,onresumptiontoken:o}).catch(M=>this._onerror(new Error(`Failed to send cancellation: ${M}`))),l(O)};this._responseHandlers.set(h,O=>{var P;if(!(!((P=n?.signal)===null||P===void 0)&&P.aborted)){if(O instanceof Error)return l(O);try{let M=t.parse(O.result);a(M)}catch(M){l(M)}}}),(f=n?.signal)===null||f===void 0||f.addEventListener("abort",()=>{var O;w((O=n?.signal)===null||O===void 0?void 0:O.reason)});let T=(g=n?.timeout)!==null&&g!==void 0?g:fl,D=()=>w(new Z(U.RequestTimeout,"Request timed out",{timeout:T}));this._setupTimeout(h,T,n?.maxTotalTimeout,D,(v=n?.resetTimeoutOnProgress)!==null&&v!==void 0?v:!1),this._transport.send(x,{relatedRequestId:s,resumptionToken:i,onresumptiontoken:o}).catch(O=>{this._cleanupTimeout(h),l(O)})})}async notification(e,t){if(!this._transport)throw new Error("Not connected");this.assertNotificationCapability(e.method);let n={...e,jsonrpc:"2.0"};await this._transport.send(n,t)}setRequestHandler(e,t){let n=e.shape.method.value;this.assertRequestHandlerCapability(n),this._requestHandlers.set(n,(s,i)=>Promise.resolve(t(e.parse(s),i)))}removeRequestHandler(e){this._requestHandlers.delete(e)}assertCanSetRequestHandler(e){if(this._requestHandlers.has(e))throw new Error(`A request handler for ${e} already exists, which would be overridden`)}setNotificationHandler(e,t){this._notificationHandlers.set(e.shape.method.value,n=>Promise.resolve(t(e.parse(n))))}removeNotificationHandler(e){this._notificationHandlers.delete(e)}};function Pi(r,e){return Object.entries(e).reduce((t,[n,s])=>(s&&typeof s=="object"?t[n]=t[n]?{...t[n],...s}:s:t[n]=s,t),{...r})}var Sr=class extends wr{constructor(e,t){var n;super(t),this._serverInfo=e,this._capabilities=(n=t?.capabilities)!==null&&n!==void 0?n:{},this._instructions=t?.instructions,this.setRequestHandler(dr,s=>this._oninitialize(s)),this.setNotificationHandler(ur,()=>{var s;return(s=this.oninitialized)===null||s===void 0?void 0:s.call(this)})}registerCapabilities(e){if(this.transport)throw new Error("Cannot register capabilities after connecting to transport");this._capabilities=Pi(this._capabilities,e)}assertCapabilityForMethod(e){var t,n;switch(e){case"sampling/createMessage":if(!(!((t=this._clientCapabilities)===null||t===void 0)&&t.sampling))throw new Error(`Client does not support sampling (required for ${e})`);break;case"roots/list":if(!(!((n=this._clientCapabilities)===null||n===void 0)&&n.roots))throw new Error(`Client does not support listing roots (required for ${e})`);break;case"ping":break}}assertNotificationCapability(e){switch(e){case"notifications/message":if(!this._capabilities.logging)throw new Error(`Server does not support logging (required for ${e})`);break;case"notifications/resources/updated":case"notifications/resources/list_changed":if(!this._capabilities.resources)throw new Error(`Server does not support notifying about resources (required for ${e})`);break;case"notifications/tools/list_changed":if(!this._capabilities.tools)throw new Error(`Server does not support notifying of tool list changes (required for ${e})`);break;case"notifications/prompts/list_changed":if(!this._capabilities.prompts)throw new Error(`Server does not support notifying of prompt list changes (required for ${e})`);break;case"notifications/cancelled":break;case"notifications/progress":break}}assertRequestHandlerCapability(e){switch(e){case"sampling/createMessage":if(!this._capabilities.sampling)throw new Error(`Server does not support sampling (required for ${e})`);break;case"logging/setLevel":if(!this._capabilities.logging)throw new Error(`Server does not support logging (required for ${e})`);break;case"prompts/get":case"prompts/list":if(!this._capabilities.prompts)throw new Error(`Server does not support prompts (required for ${e})`);break;case"resources/list":case"resources/templates/list":case"resources/read":if(!this._capabilities.resources)throw new Error(`Server does not support resources (required for ${e})`);break;case"tools/call":case"tools/list":if(!this._capabilities.tools)throw new Error(`Server does not support tools (required for ${e})`);break;case"ping":case"initialize":break}}async _oninitialize(e){let t=e.params.protocolVersion;return this._clientCapabilities=e.params.capabilities,this._clientVersion=e.params.clientInfo,{protocolVersion:Si.includes(t)?t:cn,capabilities:this.getCapabilities(),serverInfo:this._serverInfo,...this._instructions&&{instructions:this._instructions}}}getClientCapabilities(){return this._clientCapabilities}getClientVersion(){return this._clientVersion}getCapabilities(){return this._capabilities}async ping(){return this.request({method:"ping"},cr)}async createMessage(e,t){return this.request({method:"sampling/createMessage",params:e},dn,t)}async listRoots(e,t){return this.request({method:"roots/list",params:e},un,t)}async sendLoggingMessage(e){return this.notification({method:"notifications/message",params:e})}async sendResourceUpdated(e){return this.notification({method:"notifications/resources/updated",params:e})}async sendResourceListChanged(){return this.notification({method:"notifications/resources/list_changed"})}async sendToolListChanged(){return this.notification({method:"notifications/tools/list_changed"})}async sendPromptListChanged(){return this.notification({method:"notifications/prompts/list_changed"})}};var Oi=Symbol("Let zodToJsonSchema decide on which parser to use"),Du=(r,e)=>{if(e.description)try{return{...r,...JSON.parse(e.description)}}catch{}return r},Ri={name:void 0,$refStrategy:"root",basePath:["#"],effectStrategy:"input",pipeStrategy:"all",dateStrategy:"format:date-time",mapStrategy:"entries",removeAdditionalStrategy:"passthrough",allowedAdditionalProperties:!0,rejectedAdditionalProperties:!1,definitionPath:"definitions",target:"jsonSchema7",strictUnions:!1,definitions:{},errorMessages:!1,markdownDescription:!1,patternStrategy:"escape",applyRegexFlags:!1,emailStrategy:"format:email",base64Strategy:"contentEncoding:base64",nameStrategy:"ref"},Ni=r=>typeof r=="string"?{...Ri,name:r}:{...Ri,...r};var Di=r=>{let e=Ni(r),t=e.name!==void 0?[...e.basePath,e.definitionPath,e.name]:e.basePath;return{...e,currentPath:t,propertyPath:void 0,seen:new Map(Object.entries(e.definitions).map(([n,s])=>[s._def,{def:s._def,path:[...e.basePath,e.definitionPath,n],jsonSchema:void 0}]))}};function fn(r,e,t,n){n?.errorMessages&&t&&(r.errorMessage={...r.errorMessage,[e]:t})}function L(r,e,t,n,s){r[e]=t,fn(r,e,n,s)}function Ai(){return{}}function Ii(r,e){let t={type:"array"};return r.type?._def&&r.type?._def?.typeName!==m.ZodAny&&(t.items=C(r.type._def,{...e,currentPath:[...e.currentPath,"items"]})),r.minLength&&L(t,"minItems",r.minLength.value,r.minLength.message,e),r.maxLength&&L(t,"maxItems",r.maxLength.value,r.maxLength.message,e),r.exactLength&&(L(t,"minItems",r.exactLength.value,r.exactLength.message,e),L(t,"maxItems",r.exactLength.value,r.exactLength.message,e)),t}function Li(r,e){let t={type:"integer",format:"int64"};if(!r.checks)return t;for(let n of r.checks)switch(n.kind){case"min":e.target==="jsonSchema7"?n.inclusive?L(t,"minimum",n.value,n.message,e):L(t,"exclusiveMinimum",n.value,n.message,e):(n.inclusive||(t.exclusiveMinimum=!0),L(t,"minimum",n.value,n.message,e));break;case"max":e.target==="jsonSchema7"?n.inclusive?L(t,"maximum",n.value,n.message,e):L(t,"exclusiveMaximum",n.value,n.message,e):(n.inclusive||(t.exclusiveMaximum=!0),L(t,"maximum",n.value,n.message,e));break;case"multipleOf":L(t,"multipleOf",n.value,n.message,e);break}return t}function Mi(){return{type:"boolean"}}function Er(r,e){return C(r.type._def,e)}var $i=(r,e)=>C(r.innerType._def,e);function hn(r,e,t){let n=t??e.dateStrategy;if(Array.isArray(n))return{anyOf:n.map((s,i)=>hn(r,e,s))};switch(n){case"string":case"format:date-time":return{type:"string",format:"date-time"};case"format:date":return{type:"string",format:"date"};case"integer":return hl(r,e)}}var hl=(r,e)=>{let t={type:"integer",format:"unix-time"};if(e.target==="openApi3")return t;for(let n of r.checks)switch(n.kind){case"min":L(t,"minimum",n.value,n.message,e);break;case"max":L(t,"maximum",n.value,n.message,e);break}return t};function ji(r,e){return{...C(r.innerType._def,e),default:r.defaultValue()}}function Fi(r,e){return e.effectStrategy==="input"?C(r.schema._def,e):{}}function Ui(r){return{type:"string",enum:Array.from(r.values)}}var pl=r=>"type"in r&&r.type==="string"?!1:"allOf"in r;function Zi(r,e){let t=[C(r.left._def,{...e,currentPath:[...e.currentPath,"allOf","0"]}),C(r.right._def,{...e,currentPath:[...e.currentPath,"allOf","1"]})].filter(i=>!!i),n=e.target==="jsonSchema2019-09"?{unevaluatedProperties:!1}:void 0,s=[];return t.forEach(i=>{if(pl(i))s.push(...i.allOf),i.unevaluatedProperties===void 0&&(n=void 0);else{let o=i;if("additionalProperties"in i&&i.additionalProperties===!1){let{additionalProperties:a,...l}=i;o=l}else n=void 0;s.push(o)}}),s.length?{allOf:s,...n}:void 0}function qi(r,e){let t=typeof r.value;return t!=="bigint"&&t!=="number"&&t!=="boolean"&&t!=="string"?{type:Array.isArray(r.value)?"array":"object"}:e.target==="openApi3"?{type:t==="bigint"?"integer":t,enum:[r.value]}:{type:t==="bigint"?"integer":t,const:r.value}}var pn,fe={cuid:/^[cC][^\s-]{8,}$/,cuid2:/^[0-9a-z]+$/,ulid:/^[0-9A-HJKMNP-TV-Z]{26}$/,email:/^(?!\.)(?!.*\.\.)([a-zA-Z0-9_'+\-\.]*)[a-zA-Z0-9_+-]@([a-zA-Z0-9][a-zA-Z0-9\-]*\.)+[a-zA-Z]{2,}$/,emoji:()=>(pn===void 0&&(pn=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),pn),uuid:/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/,ipv4:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ipv4Cidr:/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,ipv6:/^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,ipv6Cidr:/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,base64:/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,base64url:/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,nanoid:/^[a-zA-Z0-9_-]{21}$/,jwt:/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/};function kr(r,e){let t={type:"string"};if(r.checks)for(let n of r.checks)switch(n.kind){case"min":L(t,"minLength",typeof t.minLength=="number"?Math.max(t.minLength,n.value):n.value,n.message,e);break;case"max":L(t,"maxLength",typeof t.maxLength=="number"?Math.min(t.maxLength,n.value):n.value,n.message,e);break;case"email":switch(e.emailStrategy){case"format:email":he(t,"email",n.message,e);break;case"format:idn-email":he(t,"idn-email",n.message,e);break;case"pattern:zod":K(t,fe.email,n.message,e);break}break;case"url":he(t,"uri",n.message,e);break;case"uuid":he(t,"uuid",n.message,e);break;case"regex":K(t,n.regex,n.message,e);break;case"cuid":K(t,fe.cuid,n.message,e);break;case"cuid2":K(t,fe.cuid2,n.message,e);break;case"startsWith":K(t,RegExp(`^${mn(n.value,e)}`),n.message,e);break;case"endsWith":K(t,RegExp(`${mn(n.value,e)}$`),n.message,e);break;case"datetime":he(t,"date-time",n.message,e);break;case"date":he(t,"date",n.message,e);break;case"time":he(t,"time",n.message,e);break;case"duration":he(t,"duration",n.message,e);break;case"length":L(t,"minLength",typeof t.minLength=="number"?Math.max(t.minLength,n.value):n.value,n.message,e),L(t,"maxLength",typeof t.maxLength=="number"?Math.min(t.maxLength,n.value):n.value,n.message,e);break;case"includes":{K(t,RegExp(mn(n.value,e)),n.message,e);break}case"ip":{n.version!=="v6"&&he(t,"ipv4",n.message,e),n.version!=="v4"&&he(t,"ipv6",n.message,e);break}case"base64url":K(t,fe.base64url,n.message,e);break;case"jwt":K(t,fe.jwt,n.message,e);break;case"cidr":{n.version!=="v6"&&K(t,fe.ipv4Cidr,n.message,e),n.version!=="v4"&&K(t,fe.ipv6Cidr,n.message,e);break}case"emoji":K(t,fe.emoji(),n.message,e);break;case"ulid":{K(t,fe.ulid,n.message,e);break}case"base64":{switch(e.base64Strategy){case"format:binary":{he(t,"binary",n.message,e);break}case"contentEncoding:base64":{L(t,"contentEncoding","base64",n.message,e);break}case"pattern:zod":{K(t,fe.base64,n.message,e);break}}break}case"nanoid":K(t,fe.nanoid,n.message,e);case"toLowerCase":case"toUpperCase":case"trim":break;default:}return t}function mn(r,e){return e.patternStrategy==="escape"?gl(r):r}var ml=new Set("ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvxyz0123456789");function gl(r){let e="";for(let t=0;t<r.length;t++)ml.has(r[t])||(e+="\\"),e+=r[t];return e}function he(r,e,t,n){r.format||r.anyOf?.some(s=>s.format)?(r.anyOf||(r.anyOf=[]),r.format&&(r.anyOf.push({format:r.format,...r.errorMessage&&n.errorMessages&&{errorMessage:{format:r.errorMessage.format}}}),delete r.format,r.errorMessage&&(delete r.errorMessage.format,Object.keys(r.errorMessage).length===0&&delete r.errorMessage)),r.anyOf.push({format:e,...t&&n.errorMessages&&{errorMessage:{format:t}}})):L(r,"format",e,t,n)}function K(r,e,t,n){r.pattern||r.allOf?.some(s=>s.pattern)?(r.allOf||(r.allOf=[]),r.pattern&&(r.allOf.push({pattern:r.pattern,...r.errorMessage&&n.errorMessages&&{errorMessage:{pattern:r.errorMessage.pattern}}}),delete r.pattern,r.errorMessage&&(delete r.errorMessage.pattern,Object.keys(r.errorMessage).length===0&&delete r.errorMessage)),r.allOf.push({pattern:Bi(e,n),...t&&n.errorMessages&&{errorMessage:{pattern:t}}})):L(r,"pattern",Bi(e,n),t,n)}function Bi(r,e){if(!e.applyRegexFlags||!r.flags)return r.source;let t={i:r.flags.includes("i"),m:r.flags.includes("m"),s:r.flags.includes("s")},n=t.i?r.source.toLowerCase():r.source,s="",i=!1,o=!1,a=!1;for(let l=0;l<n.length;l++){if(i){s+=n[l],i=!1;continue}if(t.i){if(o){if(n[l].match(/[a-z]/)){a?(s+=n[l],s+=`${n[l-2]}-${n[l]}`.toUpperCase(),a=!1):n[l+1]==="-"&&n[l+2]?.match(/[a-z]/)?(s+=n[l],a=!0):s+=`${n[l]}${n[l].toUpperCase()}`;continue}}else if(n[l].match(/[a-z]/)){s+=`[${n[l]}${n[l].toUpperCase()}]`;continue}}if(t.m){if(n[l]==="^"){s+=`(^|(?<=[\r
]))`;continue}else if(n[l]==="$"){s+=`($|(?=[\r
]))`;continue}}if(t.s&&n[l]==="."){s+=o?`${n[l]}\r
`:`[${n[l]}\r
]`;continue}s+=n[l],n[l]==="\\"?i=!0:o&&n[l]==="]"?o=!1:!o&&n[l]==="["&&(o=!0)}try{new RegExp(s)}catch{return console.warn(`Could not convert regex pattern at ${e.currentPath.join("/")} to a flag-independent form! Falling back to the flag-ignorant source`),r.source}return s}function Cr(r,e){if(e.target==="openAi"&&console.warn("Warning: OpenAI may not support records in schemas! Try an array of key-value pairs instead."),e.target==="openApi3"&&r.keyType?._def.typeName===m.ZodEnum)return{type:"object",required:r.keyType._def.values,properties:r.keyType._def.values.reduce((n,s)=>({...n,[s]:C(r.valueType._def,{...e,currentPath:[...e.currentPath,"properties",s]})??{}}),{}),additionalProperties:e.rejectedAdditionalProperties};let t={type:"object",additionalProperties:C(r.valueType._def,{...e,currentPath:[...e.currentPath,"additionalProperties"]})??e.allowedAdditionalProperties};if(e.target==="openApi3")return t;if(r.keyType?._def.typeName===m.ZodString&&r.keyType._def.checks?.length){let{type:n,...s}=kr(r.keyType._def,e);return{...t,propertyNames:s}}else{if(r.keyType?._def.typeName===m.ZodEnum)return{...t,propertyNames:{enum:r.keyType._def.values}};if(r.keyType?._def.typeName===m.ZodBranded&&r.keyType._def.type._def.typeName===m.ZodString&&r.keyType._def.type._def.checks?.length){let{type:n,...s}=Er(r.keyType._def,e);return{...t,propertyNames:s}}}return t}function Vi(r,e){if(e.mapStrategy==="record")return Cr(r,e);let t=C(r.keyType._def,{...e,currentPath:[...e.currentPath,"items","items","0"]})||{},n=C(r.valueType._def,{...e,currentPath:[...e.currentPath,"items","items","1"]})||{};return{type:"array",maxItems:125,items:{type:"array",items:[t,n],minItems:2,maxItems:2}}}function Hi(r){let e=r.values,n=Object.keys(r.values).filter(i=>typeof e[e[i]]!="number").map(i=>e[i]),s=Array.from(new Set(n.map(i=>typeof i)));return{type:s.length===1?s[0]==="string"?"string":"number":["string","number"],enum:n}}function zi(){return{not:{}}}function Wi(r){return r.target==="openApi3"?{enum:["null"],nullable:!0}:{type:"null"}}var Lt={ZodString:"string",ZodNumber:"number",ZodBigInt:"integer",ZodBoolean:"boolean",ZodNull:"null"};function Ji(r,e){if(e.target==="openApi3")return Gi(r,e);let t=r.options instanceof Map?Array.from(r.options.values()):r.options;if(t.every(n=>n._def.typeName in Lt&&(!n._def.checks||!n._def.checks.length))){let n=t.reduce((s,i)=>{let o=Lt[i._def.typeName];return o&&!s.includes(o)?[...s,o]:s},[]);return{type:n.length>1?n:n[0]}}else if(t.every(n=>n._def.typeName==="ZodLiteral"&&!n.description)){let n=t.reduce((s,i)=>{let o=typeof i._def.value;switch(o){case"string":case"number":case"boolean":return[...s,o];case"bigint":return[...s,"integer"];case"object":if(i._def.value===null)return[...s,"null"];case"symbol":case"undefined":case"function":default:return s}},[]);if(n.length===t.length){let s=n.filter((i,o,a)=>a.indexOf(i)===o);return{type:s.length>1?s:s[0],enum:t.reduce((i,o)=>i.includes(o._def.value)?i:[...i,o._def.value],[])}}}else if(t.every(n=>n._def.typeName==="ZodEnum"))return{type:"string",enum:t.reduce((n,s)=>[...n,...s._def.values.filter(i=>!n.includes(i))],[])};return Gi(r,e)}var Gi=(r,e)=>{let t=(r.options instanceof Map?Array.from(r.options.values()):r.options).map((n,s)=>C(n._def,{...e,currentPath:[...e.currentPath,"anyOf",`${s}`]})).filter(n=>!!n&&(!e.strictUnions||typeof n=="object"&&Object.keys(n).length>0));return t.length?{anyOf:t}:void 0};function Xi(r,e){if(["ZodString","ZodNumber","ZodBigInt","ZodBoolean","ZodNull"].includes(r.innerType._def.typeName)&&(!r.innerType._def.checks||!r.innerType._def.checks.length))return e.target==="openApi3"?{type:Lt[r.innerType._def.typeName],nullable:!0}:{type:[Lt[r.innerType._def.typeName],"null"]};if(e.target==="openApi3"){let n=C(r.innerType._def,{...e,currentPath:[...e.currentPath]});return n&&"$ref"in n?{allOf:[n],nullable:!0}:n&&{...n,nullable:!0}}let t=C(r.innerType._def,{...e,currentPath:[...e.currentPath,"anyOf","0"]});return t&&{anyOf:[t,{type:"null"}]}}function Yi(r,e){let t={type:"number"};if(!r.checks)return t;for(let n of r.checks)switch(n.kind){case"int":t.type="integer",fn(t,"type",n.message,e);break;case"min":e.target==="jsonSchema7"?n.inclusive?L(t,"minimum",n.value,n.message,e):L(t,"exclusiveMinimum",n.value,n.message,e):(n.inclusive||(t.exclusiveMinimum=!0),L(t,"minimum",n.value,n.message,e));break;case"max":e.target==="jsonSchema7"?n.inclusive?L(t,"maximum",n.value,n.message,e):L(t,"exclusiveMaximum",n.value,n.message,e):(n.inclusive||(t.exclusiveMaximum=!0),L(t,"maximum",n.value,n.message,e));break;case"multipleOf":L(t,"multipleOf",n.value,n.message,e);break}return t}function Ki(r,e){let t=e.target==="openAi",n={type:"object",properties:{}},s=[],i=r.shape();for(let a in i){let l=i[a];if(l===void 0||l._def===void 0)continue;let d=_l(l);d&&t&&(l instanceof Y&&(l=l._def.innerType),l.isNullable()||(l=l.nullable()),d=!1);let u=C(l._def,{...e,currentPath:[...e.currentPath,"properties",a],propertyPath:[...e.currentPath,"properties",a]});u!==void 0&&(n.properties[a]=u,d||s.push(a))}s.length&&(n.required=s);let o=vl(r,e);return o!==void 0&&(n.additionalProperties=o),n}function vl(r,e){if(r.catchall._def.typeName!=="ZodNever")return C(r.catchall._def,{...e,currentPath:[...e.currentPath,"additionalProperties"]});switch(r.unknownKeys){case"passthrough":return e.allowedAdditionalProperties;case"strict":return e.rejectedAdditionalProperties;case"strip":return e.removeAdditionalStrategy==="strict"?e.allowedAdditionalProperties:e.rejectedAdditionalProperties}}function _l(r){try{return r.isOptional()}catch{return!0}}var Qi=(r,e)=>{if(e.currentPath.toString()===e.propertyPath?.toString())return C(r.innerType._def,e);let t=C(r.innerType._def,{...e,currentPath:[...e.currentPath,"anyOf","1"]});return t?{anyOf:[{not:{}},t]}:{}};var eo=(r,e)=>{if(e.pipeStrategy==="input")return C(r.in._def,e);if(e.pipeStrategy==="output")return C(r.out._def,e);let t=C(r.in._def,{...e,currentPath:[...e.currentPath,"allOf","0"]}),n=C(r.out._def,{...e,currentPath:[...e.currentPath,"allOf",t?"1":"0"]});return{allOf:[t,n].filter(s=>s!==void 0)}};function to(r,e){return C(r.type._def,e)}function ro(r,e){let n={type:"array",uniqueItems:!0,items:C(r.valueType._def,{...e,currentPath:[...e.currentPath,"items"]})};return r.minSize&&L(n,"minItems",r.minSize.value,r.minSize.message,e),r.maxSize&&L(n,"maxItems",r.maxSize.value,r.maxSize.message,e),n}function no(r,e){return r.rest?{type:"array",minItems:r.items.length,items:r.items.map((t,n)=>C(t._def,{...e,currentPath:[...e.currentPath,"items",`${n}`]})).reduce((t,n)=>n===void 0?t:[...t,n],[]),additionalItems:C(r.rest._def,{...e,currentPath:[...e.currentPath,"additionalItems"]})}:{type:"array",minItems:r.items.length,maxItems:r.items.length,items:r.items.map((t,n)=>C(t._def,{...e,currentPath:[...e.currentPath,"items",`${n}`]})).reduce((t,n)=>n===void 0?t:[...t,n],[])}}function so(){return{not:{}}}function io(){return{}}var oo=(r,e)=>C(r.innerType._def,e);var ao=(r,e,t)=>{switch(e){case m.ZodString:return kr(r,t);case m.ZodNumber:return Yi(r,t);case m.ZodObject:return Ki(r,t);case m.ZodBigInt:return Li(r,t);case m.ZodBoolean:return Mi();case m.ZodDate:return hn(r,t);case m.ZodUndefined:return so();case m.ZodNull:return Wi(t);case m.ZodArray:return Ii(r,t);case m.ZodUnion:case m.ZodDiscriminatedUnion:return Ji(r,t);case m.ZodIntersection:return Zi(r,t);case m.ZodTuple:return no(r,t);case m.ZodRecord:return Cr(r,t);case m.ZodLiteral:return qi(r,t);case m.ZodEnum:return Ui(r);case m.ZodNativeEnum:return Hi(r);case m.ZodNullable:return Xi(r,t);case m.ZodOptional:return Qi(r,t);case m.ZodMap:return Vi(r,t);case m.ZodSet:return ro(r,t);case m.ZodLazy:return()=>r.getter()._def;case m.ZodPromise:return to(r,t);case m.ZodNaN:case m.ZodNever:return zi();case m.ZodEffects:return Fi(r,t);case m.ZodAny:return Ai();case m.ZodUnknown:return io();case m.ZodDefault:return ji(r,t);case m.ZodBranded:return Er(r,t);case m.ZodReadonly:return oo(r,t);case m.ZodCatch:return $i(r,t);case m.ZodPipeline:return eo(r,t);case m.ZodFunction:case m.ZodVoid:case m.ZodSymbol:return;default:return(n=>{})(e)}};function C(r,e,t=!1){let n=e.seen.get(r);if(e.override){let a=e.override?.(r,e,n,t);if(a!==Oi)return a}if(n&&!t){let a=yl(n,e);if(a!==void 0)return a}let s={def:r,path:e.currentPath,jsonSchema:void 0};e.seen.set(r,s);let i=ao(r,r.typeName,e),o=typeof i=="function"?C(i(),e):i;if(o&&bl(r,e,o),e.postProcess){let a=e.postProcess(o,r,e);return s.jsonSchema=o,a}return s.jsonSchema=o,o}var yl=(r,e)=>{switch(e.$refStrategy){case"root":return{$ref:r.path.join("/")};case"relative":return{$ref:xl(e.currentPath,r.path)};case"none":case"seen":return r.path.length<e.currentPath.length&&r.path.every((t,n)=>e.currentPath[n]===t)?(console.warn(`Recursive reference detected at ${e.currentPath.join("/")}! Defaulting to any`),{}):e.$refStrategy==="seen"?{}:void 0}},xl=(r,e)=>{let t=0;for(;t<r.length&&t<e.length&&r[t]===e[t];t++);return[(r.length-t).toString(),...e.slice(t)].join("/")},bl=(r,e,t)=>(r.description&&(t.description=r.description,e.markdownDescription&&(t.markdownDescription=r.description)),t);var Mt=(r,e)=>{let t=Di(e),n=typeof e=="object"&&e.definitions?Object.entries(e.definitions).reduce((l,[d,u])=>({...l,[d]:C(u._def,{...t,currentPath:[...t.basePath,t.definitionPath,d]},!0)??{}}),{}):void 0,s=typeof e=="string"?e:e?.nameStrategy==="title"?void 0:e?.name,i=C(r._def,s===void 0?t:{...t,currentPath:[...t.basePath,t.definitionPath,s]},!1)??{},o=typeof e=="object"&&e.name!==void 0&&e.nameStrategy==="title"?e.name:void 0;o!==void 0&&(i.title=o);let a=s===void 0?n?{...i,[t.definitionPath]:n}:i:{$ref:[...t.$refStrategy==="relative"?[]:t.basePath,t.definitionPath,s].join("/"),[t.definitionPath]:{...n,[s]:i}};return t.target==="jsonSchema7"?a.$schema="http://json-schema.org/draft-07/schema#":(t.target==="jsonSchema2019-09"||t.target==="openAi")&&(a.$schema="https://json-schema.org/draft/2019-09/schema#"),t.target==="openAi"&&("anyOf"in a||"oneOf"in a||"allOf"in a||"type"in a&&Array.isArray(a.type))&&console.warn("Warning: OpenAI may not support schemas with unions as roots! Try wrapping it in an object property."),a};var Rh=Mt;var gn;(function(r){r.Completable="McpCompletable"})(gn||(gn={}));var lt=class extends k{_parse(e){let{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}};lt.create=(r,e)=>new lt({type:r,typeName:gn.Completable,complete:e.complete,...wl(e)});function mp(r,e){return lt.create(r,{...r._def,complete:e})}function wl(r){if(!r)return{};let{errorMap:e,invalid_type_error:t,required_error:n,description:s}=r;if(e&&(t||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:s}:{errorMap:(o,a)=>{var l,d;let{message:u}=r;return o.code==="invalid_enum_value"?{message:u??a.defaultError}:typeof a.data>"u"?{message:(l=u??n)!==null&&l!==void 0?l:a.defaultError}:o.code!=="invalid_type"?{message:a.defaultError}:{message:(d=u??t)!==null&&d!==void 0?d:a.defaultError}},description:s}}var vp=1e6,_p=1e6,yp=1e4,xp=1e6,Tr=class r{static isTemplate(e){return/\{[^}\s]+\}/.test(e)}static validateLength(e,t,n){if(e.length>t)throw new Error(`${n} exceeds maximum length of ${t} characters (got ${e.length})`)}get variableNames(){return this.parts.flatMap(e=>typeof e=="string"?[]:e.names)}constructor(e){r.validateLength(e,1e6,"Template"),this.template=e,this.parts=this.parse(e)}toString(){return this.template}parse(e){let t=[],n="",s=0,i=0;for(;s<e.length;)if(e[s]==="{"){n&&(t.push(n),n="");let o=e.indexOf("}",s);if(o===-1)throw new Error("Unclosed template expression");if(i++,i>1e4)throw new Error("Template contains too many expressions (max 10000)");let a=e.slice(s+1,o),l=this.getOperator(a),d=a.includes("*"),u=this.getNames(a),f=u[0];for(let g of u)r.validateLength(g,1e6,"Variable name");t.push({name:f,operator:l,names:u,exploded:d}),s=o+1}else n+=e[s],s++;return n&&t.push(n),t}getOperator(e){return["+","#",".","/","?","&"].find(n=>e.startsWith(n))||""}getNames(e){let t=this.getOperator(e);return e.slice(t.length).split(",").map(n=>n.replace("*","").trim()).filter(n=>n.length>0)}encodeValue(e,t){return r.validateLength(e,1e6,"Variable value"),t==="+"||t==="#"?encodeURI(e):encodeURIComponent(e)}expandPart(e,t){if(e.operator==="?"||e.operator==="&"){let o=e.names.map(l=>{let d=t[l];if(d===void 0)return"";let u=Array.isArray(d)?d.map(f=>this.encodeValue(f,e.operator)).join(","):this.encodeValue(d.toString(),e.operator);return`${l}=${u}`}).filter(l=>l.length>0);return o.length===0?"":(e.operator==="?"?"?":"&")+o.join("&")}if(e.names.length>1){let o=e.names.map(a=>t[a]).filter(a=>a!==void 0);return o.length===0?"":o.map(a=>Array.isArray(a)?a[0]:a).join(",")}let n=t[e.name];if(n===void 0)return"";let i=(Array.isArray(n)?n:[n]).map(o=>this.encodeValue(o,e.operator));switch(e.operator){case"":return i.join(",");case"+":return i.join(",");case"#":return"#"+i.join(",");case".":return"."+i.join(".");case"/":return"/"+i.join("/");default:return i.join(",")}}expand(e){let t="",n=!1;for(let s of this.parts){if(typeof s=="string"){t+=s;continue}let i=this.expandPart(s,e);i&&((s.operator==="?"||s.operator==="&")&&n?t+=i.replace("?","&"):t+=i,(s.operator==="?"||s.operator==="&")&&(n=!0))}return t}escapeRegExp(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}partToRegExp(e){let t=[];for(let i of e.names)r.validateLength(i,1e6,"Variable name");if(e.operator==="?"||e.operator==="&"){for(let i=0;i<e.names.length;i++){let o=e.names[i],a=i===0?"\\"+e.operator:"&";t.push({pattern:a+this.escapeRegExp(o)+"=([^&]+)",name:o})}return t}let n,s=e.name;switch(e.operator){case"":n=e.exploded?"([^/]+(?:,[^/]+)*)":"([^/,]+)";break;case"+":case"#":n="(.+)";break;case".":n="\\.([^/,]+)";break;case"/":n="/"+(e.exploded?"([^/]+(?:,[^/]+)*)":"([^/,]+)");break;default:n="([^/]+)"}return t.push({pattern:n,name:s}),t}match(e){r.validateLength(e,1e6,"URI");let t="^",n=[];for(let a of this.parts)if(typeof a=="string")t+=this.escapeRegExp(a);else{let l=this.partToRegExp(a);for(let{pattern:d,name:u}of l)t+=d,n.push({name:u,exploded:a.exploded})}t+="$",r.validateLength(t,1e6,"Generated regex pattern");let s=new RegExp(t),i=e.match(s);if(!i)return null;let o={};for(let a=0;a<n.length;a++){let{name:l,exploded:d}=n[a],u=i[a+1],f=l.replace("*","");d&&u.includes(",")?o[f]=u.split(","):o[f]=u}return o}};var Rr=class{constructor(e,t){this._registeredResources={},this._registeredResourceTemplates={},this._registeredTools={},this._registeredPrompts={},this._toolHandlersInitialized=!1,this._completionHandlerInitialized=!1,this._resourceHandlersInitialized=!1,this._promptHandlersInitialized=!1,this.server=new Sr(e,t)}async connect(e){return await this.server.connect(e)}async close(){await this.server.close()}setToolRequestHandlers(){this._toolHandlersInitialized||(this.server.assertCanSetRequestHandler(yr.shape.method.value),this.server.assertCanSetRequestHandler(xr.shape.method.value),this.server.registerCapabilities({tools:{listChanged:!0}}),this.server.setRequestHandler(yr,()=>({tools:Object.entries(this._registeredTools).filter(([,e])=>e.enabled).map(([e,t])=>({name:e,description:t.description,inputSchema:t.inputSchema?Mt(t.inputSchema,{strictUnions:!0}):Sl,annotations:t.annotations}))})),this.server.setRequestHandler(xr,async(e,t)=>{let n=this._registeredTools[e.params.name];if(!n)throw new Z(U.InvalidParams,`Tool ${e.params.name} not found`);if(!n.enabled)throw new Z(U.InvalidParams,`Tool ${e.params.name} disabled`);if(n.inputSchema){let s=await n.inputSchema.safeParseAsync(e.params.arguments);if(!s.success)throw new Z(U.InvalidParams,`Invalid arguments for tool ${e.params.name}: ${s.error.message}`);let i=s.data,o=n.callback;try{return await Promise.resolve(o(i,t))}catch(a){return{content:[{type:"text",text:a instanceof Error?a.message:String(a)}],isError:!0}}}else{let s=n.callback;try{return await Promise.resolve(s(t))}catch(i){return{content:[{type:"text",text:i instanceof Error?i.message:String(i)}],isError:!0}}}}),this._toolHandlersInitialized=!0)}setCompletionRequestHandler(){this._completionHandlerInitialized||(this.server.assertCanSetRequestHandler(br.shape.method.value),this.server.setRequestHandler(br,async e=>{switch(e.params.ref.type){case"ref/prompt":return this.handlePromptCompletion(e,e.params.ref);case"ref/resource":return this.handleResourceCompletion(e,e.params.ref);default:throw new Z(U.InvalidParams,`Invalid completion reference: ${e.params.ref}`)}}),this._completionHandlerInitialized=!0)}async handlePromptCompletion(e,t){let n=this._registeredPrompts[t.name];if(!n)throw new Z(U.InvalidParams,`Prompt ${t.name} not found`);if(!n.enabled)throw new Z(U.InvalidParams,`Prompt ${t.name} disabled`);if(!n.argsSchema)return Pr;let s=n.argsSchema.shape[e.params.argument.name];if(!(s instanceof lt))return Pr;let o=await s._def.complete(e.params.argument.value);return lo(o)}async handleResourceCompletion(e,t){let n=Object.values(this._registeredResourceTemplates).find(o=>o.resourceTemplate.uriTemplate.toString()===t.uri);if(!n){if(this._registeredResources[t.uri])return Pr;throw new Z(U.InvalidParams,`Resource template ${e.params.ref.uri} not found`)}let s=n.resourceTemplate.completeCallback(e.params.argument.name);if(!s)return Pr;let i=await s(e.params.argument.value);return lo(i)}setResourceRequestHandlers(){this._resourceHandlersInitialized||(this.server.assertCanSetRequestHandler(pr.shape.method.value),this.server.assertCanSetRequestHandler(mr.shape.method.value),this.server.assertCanSetRequestHandler(gr.shape.method.value),this.server.registerCapabilities({resources:{listChanged:!0}}),this.server.setRequestHandler(pr,async(e,t)=>{let n=Object.entries(this._registeredResources).filter(([i,o])=>o.enabled).map(([i,o])=>({uri:i,name:o.name,...o.metadata})),s=[];for(let i of Object.values(this._registeredResourceTemplates)){if(!i.resourceTemplate.listCallback)continue;let o=await i.resourceTemplate.listCallback(t);for(let a of o.resources)s.push({...a,...i.metadata})}return{resources:[...n,...s]}}),this.server.setRequestHandler(mr,async()=>({resourceTemplates:Object.entries(this._registeredResourceTemplates).map(([t,n])=>({name:t,uriTemplate:n.resourceTemplate.uriTemplate.toString(),...n.metadata}))})),this.server.setRequestHandler(gr,async(e,t)=>{let n=new URL(e.params.uri),s=this._registeredResources[n.toString()];if(s){if(!s.enabled)throw new Z(U.InvalidParams,`Resource ${n} disabled`);return s.readCallback(n,t)}for(let i of Object.values(this._registeredResourceTemplates)){let o=i.resourceTemplate.uriTemplate.match(n.toString());if(o)return i.readCallback(n,o,t)}throw new Z(U.InvalidParams,`Resource ${n} not found`)}),this.setCompletionRequestHandler(),this._resourceHandlersInitialized=!0)}setPromptRequestHandlers(){this._promptHandlersInitialized||(this.server.assertCanSetRequestHandler(vr.shape.method.value),this.server.assertCanSetRequestHandler(_r.shape.method.value),this.server.registerCapabilities({prompts:{listChanged:!0}}),this.server.setRequestHandler(vr,()=>({prompts:Object.entries(this._registeredPrompts).filter(([,e])=>e.enabled).map(([e,t])=>({name:e,description:t.description,arguments:t.argsSchema?El(t.argsSchema):void 0}))})),this.server.setRequestHandler(_r,async(e,t)=>{let n=this._registeredPrompts[e.params.name];if(!n)throw new Z(U.InvalidParams,`Prompt ${e.params.name} not found`);if(!n.enabled)throw new Z(U.InvalidParams,`Prompt ${e.params.name} disabled`);if(n.argsSchema){let s=await n.argsSchema.safeParseAsync(e.params.arguments);if(!s.success)throw new Z(U.InvalidParams,`Invalid arguments for prompt ${e.params.name}: ${s.error.message}`);let i=s.data,o=n.callback;return await Promise.resolve(o(i,t))}else{let s=n.callback;return await Promise.resolve(s(t))}}),this.setCompletionRequestHandler(),this._promptHandlersInitialized=!0)}resource(e,t,...n){let s;typeof n[0]=="object"&&(s=n.shift());let i=n[0];if(typeof t=="string"){if(this._registeredResources[t])throw new Error(`Resource ${t} is already registered`);let o={name:e,metadata:s,readCallback:i,enabled:!0,disable:()=>o.update({enabled:!1}),enable:()=>o.update({enabled:!0}),remove:()=>o.update({uri:null}),update:a=>{typeof a.uri<"u"&&a.uri!==t&&(delete this._registeredResources[t],a.uri&&(this._registeredResources[a.uri]=o)),typeof a.name<"u"&&(o.name=a.name),typeof a.metadata<"u"&&(o.metadata=a.metadata),typeof a.callback<"u"&&(o.readCallback=a.callback),typeof a.enabled<"u"&&(o.enabled=a.enabled),this.sendResourceListChanged()}};return this._registeredResources[t]=o,this.setResourceRequestHandlers(),this.sendResourceListChanged(),o}else{if(this._registeredResourceTemplates[e])throw new Error(`Resource template ${e} is already registered`);let o={resourceTemplate:t,metadata:s,readCallback:i,enabled:!0,disable:()=>o.update({enabled:!1}),enable:()=>o.update({enabled:!0}),remove:()=>o.update({name:null}),update:a=>{typeof a.name<"u"&&a.name!==e&&(delete this._registeredResourceTemplates[e],a.name&&(this._registeredResourceTemplates[a.name]=o)),typeof a.template<"u"&&(o.resourceTemplate=a.template),typeof a.metadata<"u"&&(o.metadata=a.metadata),typeof a.callback<"u"&&(o.readCallback=a.callback),typeof a.enabled<"u"&&(o.enabled=a.enabled),this.sendResourceListChanged()}};return this._registeredResourceTemplates[e]=o,this.setResourceRequestHandlers(),this.sendResourceListChanged(),o}}tool(e,...t){if(this._registeredTools[e])throw new Error(`Tool ${e} is already registered`);let n=d=>typeof d!="object"||d===null?!1:Object.values(d).some(u=>u instanceof k),s;typeof t[0]=="string"&&(s=t.shift());let i,o;if(t.length>1){let d=t[0];n(d)?(i=t.shift(),t.length>1&&typeof t[0]=="object"&&t[0]!==null&&!n(t[0])&&(o=t.shift())):typeof d=="object"&&d!==null&&(o=t.shift())}let a=t[0],l={description:s,inputSchema:i===void 0?void 0:c.object(i),annotations:o,callback:a,enabled:!0,disable:()=>l.update({enabled:!1}),enable:()=>l.update({enabled:!0}),remove:()=>l.update({name:null}),update:d=>{typeof d.name<"u"&&d.name!==e&&(delete this._registeredTools[e],d.name&&(this._registeredTools[d.name]=l)),typeof d.description<"u"&&(l.description=d.description),typeof d.paramsSchema<"u"&&(l.inputSchema=c.object(d.paramsSchema)),typeof d.callback<"u"&&(l.callback=d.callback),typeof d.annotations<"u"&&(l.annotations=d.annotations),typeof d.enabled<"u"&&(l.enabled=d.enabled),this.sendToolListChanged()}};return this._registeredTools[e]=l,this.setToolRequestHandlers(),this.sendToolListChanged(),l}prompt(e,...t){if(this._registeredPrompts[e])throw new Error(`Prompt ${e} is already registered`);let n;typeof t[0]=="string"&&(n=t.shift());let s;t.length>1&&(s=t.shift());let i=t[0],o={description:n,argsSchema:s===void 0?void 0:c.object(s),callback:i,enabled:!0,disable:()=>o.update({enabled:!1}),enable:()=>o.update({enabled:!0}),remove:()=>o.update({name:null}),update:a=>{typeof a.name<"u"&&a.name!==e&&(delete this._registeredPrompts[e],a.name&&(this._registeredPrompts[a.name]=o)),typeof a.description<"u"&&(o.description=a.description),typeof a.argsSchema<"u"&&(o.argsSchema=c.object(a.argsSchema)),typeof a.callback<"u"&&(o.callback=a.callback),typeof a.enabled<"u"&&(o.enabled=a.enabled),this.sendPromptListChanged()}};return this._registeredPrompts[e]=o,this.setPromptRequestHandlers(),this.sendPromptListChanged(),o}isConnected(){return this.server.transport!==void 0}sendResourceListChanged(){this.isConnected()&&this.server.sendResourceListChanged()}sendToolListChanged(){this.isConnected()&&this.server.sendToolListChanged()}sendPromptListChanged(){this.isConnected()&&this.server.sendPromptListChanged()}},co=class{constructor(e,t){this._callbacks=t,this._uriTemplate=typeof e=="string"?new Tr(e):e}get uriTemplate(){return this._uriTemplate}get listCallback(){return this._callbacks.list}completeCallback(e){var t;return(t=this._callbacks.complete)===null||t===void 0?void 0:t[e]}},Sl={type:"object"};function El(r){return Object.entries(r.shape).map(([e,t])=>({name:e,description:t.description,required:!t.isOptional()}))}function lo(r){return{completion:{values:r.slice(0,100),total:r.length,hasMore:r.length>100}}}var Pr={completion:{values:[],hasMore:!1}};var Je=$(require("vscode"));function kl(r){return r.map(e=>({message:e.message,severity:Je.DiagnosticSeverity[e.severity],range:{start:{line:e.range.start.line,character:e.range.start.character},end:{line:e.range.end.line,character:e.range.end.character}},source:e.source,code:e.code?.toString()}))}function uo(r){return(r?(()=>{let n=Je.Uri.parse(r);return[[n,Je.languages.getDiagnostics(n)]]})():Je.languages.getDiagnostics()).map(([n,s])=>({uri:n.toString(),diagnostics:kl(s)}))}var $t=class r{static instance;listener;clients;clientCounter;output;constructor(e){this.clients=new Map,this.clientCounter=0,this.output=e}static getInstance(e){return r.instance||(r.instance=new r(e)),r.instance}registerClient(e){let t=`client_${this.clientCounter++}`;return this.clients.set(t,e),this.startStreaming(),this.output.info(`[DiagnosticStreamManager] Registered client ${t}. Total clients: ${this.clients.size}`),t}unregisterClient(e){this.clients.delete(e)&&(this.output.info(`[DiagnosticStreamManager] Unregistered client ${e}. Total clients: ${this.clients.size}`),this.clients.size===0&&this.stopStreaming())}startStreaming(){this.listener||(this.listener=Je.languages.onDidChangeDiagnostics(e=>{this.notifyDiagnosticsChanged(e.uris)}),this.output.info("[DiagnosticStreamManager] Started streaming diagnostics"))}stopStreaming(){this.listener&&(this.listener.dispose(),this.listener=void 0,this.output.info("[DiagnosticStreamManager] Stopped streaming diagnostics"))}notifyDiagnosticsChanged(e){if(this.clients.size===0)return;let t=e.map(n=>n.toString());this.output.info(`[DiagnosticStreamManager] Notifying ${this.clients.size} clients about diagnostics change for ${e.length} files`),this.clients.forEach((n,s)=>{try{n(t)}catch(i){this.output.error(`[DiagnosticStreamManager] Error notifying client ${s}: ${i}`)}})}dispose(){this.clients.clear(),this.stopStreaming()}},Rp=$t;var S=$(require("vscode")),Xe=$(require("path"));async function fo(r={}){try{let e=uo(r.uri);return{content:[{type:"text",text:JSON.stringify(e,null,2)}]}}catch(e){throw console.error("Error getting diagnostics through MCP:",e),e}}async function ho({filePath:r,preview:e,startText:t,endText:n,selectToEndOfLine:s,makeFrontmost:i=!0}){try{if(!r)throw new Error("File path is required");let o;if(!Xe.isAbsolute(r)&&S.workspace.workspaceFolders&&S.workspace.workspaceFolders.length>0){let a=S.workspace.workspaceFolders[0].uri.fsPath,l=Xe.join(a,r);o=S.Uri.file(l),console.log(`Converted relative path '${r}' to absolute: '${l}'`)}else o=S.Uri.file(r);try{await S.workspace.fs.stat(o);let a=await S.workspace.openTextDocument(o),l=S.window.visibleTextEditors.some(f=>f.document.uri.toString()===o.toString()),d;if(i||!l?d=await S.window.showTextDocument(a,{preview:e,preserveFocus:!i}):d=S.window.visibleTextEditors.find(f=>f.document.uri.toString()===o.toString()),t&&d){let f=a.getText(),g={success:!0,filePath:o.fsPath,message:`Opened file: ${o.fsPath}`},v=f.indexOf(t);if(v!==-1){let h=a.positionAt(v),x;if(n){let T=f.substring(v+t.length).indexOf(n);if(T!==-1){let D=v+t.length+T+n.length;x=a.positionAt(D),s&&(x=new S.Position(x.line,Number.MAX_SAFE_INTEGER)),d.selection=new S.Selection(h,x),d.revealRange(new S.Range(h,x),S.TextEditorRevealType.InCenter),g.message=`Opened file and selected text from "${t}" to "${n}"`}else d.selection=new S.Selection(h,h),d.revealRange(new S.Range(h,h),S.TextEditorRevealType.InCenter),g.message=`Opened file and positioned at "${t}" (end text "${n}" not found)`}else x=a.positionAt(v+t.length),d.selection=new S.Selection(h,x),d.revealRange(new S.Range(h,x),S.TextEditorRevealType.InCenter),g.message=`Opened file and selected text "${t}"`}else g.message=`Opened file, but text "${t}" not found`;return{content:[{type:"text",text:g.message}]}}let u={success:!0,filePath:o.fsPath,fileUrl:a.uri.toString(),message:`Opened file: ${o.fsPath}`};return i||(u.languageId=a.languageId,u.lineCount=a.lineCount,u.isDirty=a.isDirty,u.isUntitled=a.isUntitled,u.isClosed=a.isClosed),{content:[{type:"text",text:i?u.message:JSON.stringify(u,null,2)}]}}catch{throw new Error(`File not found: ${o.fsPath}`)}}catch(o){throw console.error("Error opening file through MCP:",o),o}}async function po(){try{let r=S.window.activeTextEditor,e=[];for(let n of S.window.tabGroups.all)for(let s of n.tabs)if(s.input instanceof S.TabInputText){let i=s.input.uri,o=S.workspace.textDocuments.find(l=>l.uri.toString()===i.toString()),a={uri:i.toString(),isActive:s.isActive,isPinned:s.isPinned,isPreview:s.isPreview,isDirty:s.isDirty,label:s.label,groupIndex:n.viewColumn?n.viewColumn-1:0,viewColumn:n.viewColumn,isGroupActive:n.isActive};o&&(a.fileName=o.fileName,a.languageId=o.languageId,a.lineCount=o.lineCount,a.isUntitled=o.isUntitled,r&&r.document.uri.toString()===i.toString()&&(a.selection={start:{line:r.selection.start.line,character:r.selection.start.character},end:{line:r.selection.end.line,character:r.selection.end.character},isReversed:r.selection.isReversed})),e.push(a)}return{content:[{type:"text",text:JSON.stringify({tabs:e},null,2)}]}}catch(r){throw console.error("Error getting open editors through MCP:",r),r}}async function Ap(r){try{let e=r?.prompt||"",t=r?.codeActionKind||"quickfix",n=await S.commands.executeCommand(Qr,e,t);return{content:[{type:"text",text:typeof n=="string"?n:JSON.stringify(n,null,2)}]}}catch(e){throw console.error("Error running quick fix through MCP:",e),e}}async function mo(){try{let r=S.window.activeTextEditor;if(!r)return{content:[{type:"text",text:JSON.stringify({success:!1,message:"No active editor found"},null,2)}]};let e=r.selection,t=r.document,n=t.getText(e),s=t.uri.fsPath;return{content:[{type:"text",text:JSON.stringify({success:!0,text:n,filePath:s,fileUrl:t.uri.toString(),selection:{start:{line:e.start.line,character:e.start.character},end:{line:e.end.line,character:e.end.character},isEmpty:e.isEmpty}},null,2)}]}}catch(r){throw console.error("Error getting current selection through MCP:",r),r}}async function go(){try{let e=(S.workspace.workspaceFolders||[]).map(t=>({name:t.name,uri:t.uri.toString(),path:t.uri.fsPath,index:t.index}));return{content:[{type:"text",text:JSON.stringify({success:!0,folders:e,rootPath:S.workspace.rootPath||null,workspaceFile:S.workspace.workspaceFile?.toString()||null},null,2)}]}}catch(r){throw console.error("Error getting workspace folders through MCP:",r),r}}async function vo({filePath:r}){try{if(!r)throw new Error("File path is required");let e;if(!Xe.isAbsolute(r)&&S.workspace.workspaceFolders&&S.workspace.workspaceFolders.length>0){let n=S.workspace.workspaceFolders[0].uri.fsPath,s=Xe.join(n,r);e=S.Uri.file(s),console.log(`Converted relative path '${r}' to absolute: '${s}'`)}else e=S.Uri.file(r);let t=S.workspace.textDocuments.find(n=>n.uri.toString()===e.toString());return t?{content:[{type:"text",text:JSON.stringify({success:!0,filePath:e.fsPath,isDirty:t.isDirty,isUntitled:t.isUntitled},null,2)}]}:{content:[{type:"text",text:JSON.stringify({success:!1,message:`Document not open: ${e.fsPath}`},null,2)}]}}catch(e){throw console.error("Error checking document dirty state through MCP:",e),e}}async function _o({filePath:r}){try{if(!r)throw new Error("File path is required");let e;if(!Xe.isAbsolute(r)&&S.workspace.workspaceFolders&&S.workspace.workspaceFolders.length>0){let s=S.workspace.workspaceFolders[0].uri.fsPath,i=Xe.join(s,r);e=S.Uri.file(i),console.log(`Converted relative path '${r}' to absolute: '${i}'`)}else e=S.Uri.file(r);let t=S.workspace.textDocuments.find(s=>s.uri.toString()===e.toString());if(!t)return{content:[{type:"text",text:JSON.stringify({success:!1,message:`Document not open: ${e.fsPath}`},null,2)}]};let n=await t.save();return{content:[{type:"text",text:JSON.stringify({success:!0,filePath:e.fsPath,saved:n,message:n?"Document saved successfully":"Document was not dirty or save failed"},null,2)}]}}catch(e){throw console.error("Error saving document through MCP:",e),e}}var F=$(require("vscode")),vn;async function Cl(){if(!vn){let r=F.extensions.getExtension("ms-toolsai.jupyter");if(!r)throw new Error("Jupyter extension not installed");vn=Promise.resolve(r.activate())}return vn}function yt(r,e){return r.error(e),{content:[{type:"text",text:e}]}}async function yo(r,e){let t=F.window.activeNotebookEditor;if(!t)return yt(r,"No active notebook editor found.");let n;try{n=await Cl()}catch{return yt(r,"Unable to request Jupyter extension API. It is either not installed or not activated.")}let s=await n.kernels.getKernel(t.notebook.uri);if(!s)return yt(r,"No kernel found for the active notebook. Please connect to a kernel.");if(s.language!=="python")return yt(r,`Kernel language is ${s?.language}, not python.`);let i={content:[]};try{r.info(`Executing ${e}`);let o=new TextDecoder,a=await Pl(e,t.notebook),l=t.notebook.getCells().find(f=>f.metadata.id===a);if(!l)return yt(r,"No cell found in the notebook.");let d=t.notebook.getCells().indexOf(l);if(t.revealRange(new F.NotebookRange(d,d+1),F.NotebookEditorRevealType.InCenter),!await Tl(r))return yt(r,"Code execution cancelled by user. Ask the user how they would like to proceed.");await F.commands.executeCommand("notebook.cell.execute",{ranges:[{start:d,end:d+1}],document:t.notebook.uri});for(let f of l.outputs)for(let g of f.items)if(g.mime===Rl){let v=JSON.parse(o.decode(g.data));r.appendLine(`Error executing code ${v.name}: ${v.message},/n ${v.stack}`),i.content.push({type:"text",text:`Error: ${v.name}: ${v.message},/n ${v.stack}`})}else if(g.mime===Ol){let v=Buffer.from(g.data).toString("base64");i.content.push({type:"image",data:v,mimeType:g.mime})}else i.content.push({type:"text",text:o.decode(g.data)});r.info("Code execution completed")}catch(o){r.error(`Code execution failed with an error '${o}'`)}return i}async function Tl(r){return(await F.window.showQuickPick([{label:"Execute",description:"Run the code in the notebook"},{label:"Cancel",description:"Do not execute the code"}],{title:"Claude Code Execution",placeHolder:"Choose whether to execute the code"}))?.label==="Execute"}async function Pl(r,e){let t=new F.NotebookCellData(F.NotebookCellKind.Code,r,"python"),n=Math.random().toString(36).substring(2,15);t.metadata={id:n};let s=e.getCells().length,i=F.NotebookEdit.insertCells(s,[t]),o=new F.WorkspaceEdit;return o.set(e.uri,[i]),await F.workspace.applyEdit(o),n}var Rl=F.NotebookCellOutputItem.error(new Error("")).mime,Ol="image/png";var Nr=$(require("vscode")),be=$(require("fs")),Or=$(require("path")),bo=$(require("os")),wo=$(require("http"));function Nl(){return Math.floor(Math.random()*55536)+1e4}function xo(){let r=Or.join(bo.homedir(),".claude","ide");return be.existsSync(r)||be.mkdirSync(r,{recursive:!0}),r}function _n(r){let e=xo(),t=Or.join(e,`${r}.lock`),n=be.existsSync(t),s=Nr.workspace.workspaceFolders?.map(o=>o.uri.fsPath)||[],i={pid:process.ppid,workspaceFolders:s,ideName:Nr.env.appName,transport:"ws"};return be.writeFileSync(t,JSON.stringify(i)),n&&console.log(`Updated lock file with new workspace folders: ${t}`),t}function So(r){let e=xo(),t=Or.join(e,`${r}.lock`);if(be.existsSync(t))try{be.unlinkSync(t)}catch(n){console.error(`Failed to delete lock file ${t}:`,n)}}function Dl(r){return new Promise(e=>{let t=wo.createServer();t.once("error",()=>{e(!1)}),t.once("listening",()=>{t.close(),e(!0)}),t.listen(r)})}async function Eo(){for(let r=0;r<50;r++){let e=Nl();if(await Dl(e))return e}throw new Error("Failed to find an available port after multiple attempts")}var q=$(require("vscode")),Co=$(require("fs"));async function To(r,e,t,n,s,i,o,a,l){r.info("diff from",n,"to",s,"as",o);let d=q.Uri.file(n);try{if((await q.workspace.openTextDocument(d)).isDirty){let ge=Co.readFileSync(n,"utf8");d=e.createFile(n,ge).uri}}catch{r.info("leftTempFileProvider.createFile",n),d=e.createFile(n,"").uri}let f=t.createFile(s,i).uri,g=await q.workspace.openTextDocument(f),v=A=>A.input instanceof q.TabInputTextDiff&&A.input.modified.toString()===f.toString(),h=yn().filter(A=>v(A.tab));for(let A of h)r.info("Closing existing Claude Code diff tab:",A.label),await q.window.tabGroups.close(A.tab);h.length>0&&await new Promise(A=>setTimeout(A,200));let x={preview:!1},w=i,T=i,D;q.workspace.onDidChangeTextDocument(A=>{A.document.uri.toString()===f.toString()&&(w=T,T=A.document.getText(),A.contentChanges.length>3&&A.reason!==q.TextDocumentChangeReason.Undo&&A.reason!==q.TextDocumentChangeReason.Redo&&(D={time:Date.now(),contents:w}))});let O=new Promise(A=>{l.push(q.workspace.onWillSaveTextDocument(ge=>{if(ge.document.uri.toString()===f.toString()){let Jn=ge.document.getText();D&&Date.now()-D.time<500&&(Jn=D.contents),A(Jn)}}))}),P=await q.commands.executeCommand("vscode.diff",d,f,o,x);r.info("diff result",P,o,d,f,x),await ko(()=>yn().some(A=>A.isDiff&&A.label===o),1e3),De();let M=new Promise(A=>{l.push(a(ge=>{ge.activeTab&&v(ge.activeTab)&&A(ge)}))}).then(A=>A.accepted?(r.info("diff_accepted",o),{content:[{type:"text",text:"FILE_SAVED"},{type:"text",text:g.getText()}]}):{content:[{type:"text",text:"DIFF_REJECTED"},{type:"text",text:o}]}),H=ko(()=>!yn().some(A=>A.isDiff&&A.label===o)).then(()=>(r.info("tab_closed",o),{content:[{type:"text",text:"DIFF_REJECTED"},{type:"text",text:o}]})),Qe=O.then(async A=>(r.info("file_saved",o),{content:[{type:"text",text:"FILE_SAVED"},{type:"text",text:A}]})),Ct=[H,M];return q.workspace.getConfiguration("files").get("autoSave")==="off"&&Ct.push(Qe),await Promise.race(Ct)}function yn(){return q.window.tabGroups.all.flatMap(r=>r.tabs.map(e=>({tab:e,label:e.label,isDiff:e.input instanceof q.TabInputTextDiff,viewColumn:r.viewColumn,isActive:e.isActive})))}function ko(r,e){return new Promise((t,n)=>{let s,i=setInterval(()=>{r()&&(clearInterval(i),s&&clearTimeout(s),t())},100);e&&(s=setTimeout(()=>{clearInterval(i),n(new Error(`Timeout waiting after ${e}ms`))},e))})}async function xn(r,e,t){r.environmentVariableCollection.replace(e,t),console.log(`Set ${e}=${t} in terminal environment (in-memory)`)}async function bn(r,e){r.environmentVariableCollection.get(e)&&(r.environmentVariableCollection.delete(e),console.log(`Cleared ${e} from terminal environment (in-memory)`))}function Bp(r,e){return r.environmentVariableCollection.get(e)?.value}var Da=$(wa(),1),Aa=$(Dn(),1),Ia=$(Ln(),1),Bn=$(Vr(),1),zr=$(Na(),1),kt=Bn.default;var Wr=class{constructor(e){this.ws=e;this.opened=new Promise((t,n)=>{this.ws.readyState===kt.OPEN?t():(this.ws.on("open",()=>{t()}),this.ws.on("error",s=>{n(s)}))}),this.ws.on("message",this.onMessageHandler),this.ws.on("error",this.onErrorHandler),this.ws.on("close",this.onCloseHandler)}started=!1;opened;onclose;onerror;onmessage;onMessageHandler=e=>{try{let t=JSON.parse(e.toString("utf-8")),n=Ti.parse(t);this.onmessage?.(n)}catch(t){this.onErrorHandler(t)}};onErrorHandler=e=>{this.onerror?.(e instanceof Error?e:new Error("Failed to process message"))};onCloseHandler=()=>{this.onclose?.(),this.ws.off("message",this.onMessageHandler),this.ws.off("error",this.onErrorHandler),this.ws.off("close",this.onCloseHandler)};async start(){if(this.started)throw new Error("Start can only be called once per transport.");if(await this.opened,this.ws.readyState!==kt.OPEN)throw new Error("WebSocket is not open. Cannot start transport.");this.started=!0}async close(){(this.ws.readyState===kt.OPEN||this.ws.readyState===kt.CONNECTING)&&this.ws.close(),this.onCloseHandler()}async send(e){if(this.ws.readyState!==kt.OPEN)throw new Error("WebSocket is not open. Cannot send message.");let t=JSON.stringify(e);try{await new Promise((n,s)=>{this.ws.send(t,i=>{i?s(i):n()})})}catch(n){throw this.onErrorHandler(n),n}}};var Vn=$(require("vscode")),Ne=null;function Hn(){return Ne}function La(r,e){let t=null,n=Vn.window.onDidChangeTextEditorSelection(s=>{let i=s.textEditor,o=i.selection,a=i.document,d={text:a.getText(o),filePath:a.uri.fsPath,fileUrl:a.uri.toString(),selection:{start:{line:o.start.line,character:o.start.character},end:{line:o.end.line,character:o.end.character},isEmpty:o.isEmpty}},u=!Ne||Ne.text!==d.text||Ne.filePath!==d.filePath||Ne.selection.start.line!==d.selection.start.line||Ne.selection.start.character!==d.selection.start.character||Ne.selection.end.line!==d.selection.end.line||Ne.selection.end.character!==d.selection.end.character;Ne=d;let f=e();u&&f&&r.server&&(t&&clearTimeout(t),t=setTimeout(()=>{let g=e();g&&zn(d,g)},300))});return{dispose:()=>{n.dispose(),t&&clearTimeout(t)}}}function Ma(r,e,t){r(()=>{let n=Vn.window.activeTextEditor;if(!n)return;let s=n.selection,i={filePath:n.document.uri.fsPath};s.isEmpty||(i.lineStart=s.start.line,i.lineEnd=s.end.line);let o=t();o&&e.server&&$a(i,o)})}var oe=$(require("vscode")),Fa=$(require("http"));function Yd(){let r=oe.env.appName,e;switch(r.toLowerCase()){case"visual studio code":e="Claude Code VSCode MCP";break;case"cursor":e="Claude Code Cursor MCP";break;case"windsurf":e="Claude Code Windsurf MCP";break;default:e=`Claude Code ${r} MCP`}return e}function Kd(r,e,t,n){r.logged.event(s=>{let i=n();i&&e.server?ja(t,s,i):Wn.push(s)})}function Ua(r,e,t,n,s,i,o){let a=new Rr({name:Yd(),version:r.extension.packageJSON.version||"0.0.1"});a.tool("openDiff","Open a git diff for the file",{old_file_path:c.string().describe("Path to the file to show diff for. If not provided, uses active editor."),new_file_path:c.string().describe("Path to the file to show diff for. If not provided, uses active editor."),new_file_contents:c.string().describe("Contents of the new file. If not provided then the current file contents of new_file_path will be used."),tab_name:c.string().describe("Path to the file to show diff for. If not provided, uses active editor.")},async({old_file_path:T,new_file_path:D,new_file_contents:O,tab_name:P})=>{let M=[];try{return await To(n,e,t,T,D,O,P,s,M)}catch(H){throw n.error(`Error opening diff: ${H}`),H}finally{for(let H of M)H.dispose()}}),a.tool("getDiagnostics","Get language diagnostics from VS Code",{uri:c.string().optional().describe("Optional file URI to get diagnostics for. If not provided, gets diagnostics for all files.")},fo),a.tool("close_tab",{tab_name:c.string()},async({tab_name:T})=>(await Qd(T),setTimeout(()=>{De()},500),{content:[{type:"text",text:"TAB_CLOSED"}]})),a.tool("openFile","Open a file in the editor and optionally select a range of text",{filePath:c.string().describe("Path to the file to open"),preview:c.boolean().describe("Whether to open the file in preview mode").default(!1),startText:c.string().describe("Text pattern to find the start of the selection range. Selects from the beginning of this match."),endText:c.string().describe("Text pattern to find the end of the selection range. Selects up to the end of this match. If not provided, only the startText match will be selected."),selectToEndOfLine:c.boolean().describe("If true, selection will extend to the end of the line containing the endText match.").default(!1),makeFrontmost:c.boolean().describe("Whether to make the file the active editor tab. If false, the file will be opened in the background without changing focus.").default(!0)},{readOnlyHint:!0},ho),a.tool("getOpenEditors","Get information about currently open editors",{},po),a.tool("getWorkspaceFolders","Get all workspace folders currently open in the IDE",{},go),a.tool("getCurrentSelection","Get the current text selection in the active editor",{},mo),a.tool("checkDocumentDirty","Check if a document has unsaved changes (is dirty)",{filePath:c.string().describe("Path to the file to check")},vo),a.tool("saveDocument","Save a document with unsaved changes",{filePath:c.string().describe("Path to the file to save")},_o),a.tool("getLatestSelection","Get the most recent text selection (even if not in the active editor)",{},async()=>({content:[{type:"text",text:JSON.stringify(Hn()||{success:!1,message:"No selection available"},null,2)}]})),a.tool("executeCode",`Execute python code in the Jupyter kernel for the current notebook file.
    
    All code will be executed in the current Jupyter kernel.
    
    Avoid declaring variables or modifying the state of the kernel unless the user
    explicitly asks for it.
    
    Any code executed will persist across calls to this tool, unless the kernel
    has been restarted.`,{code:c.string().describe("The code to be executed on the kernel.")},({code:T})=>yo(n,T));let l=Fa.createServer(),d=new zr.default({server:l}),u=null,f=null,g=$t.getInstance(n);d.on("connection",function(T,D){if(n.info("New WS connection from:",D.url||"unknown"),u){n.info("Disconnecting previous WebSocket client"),f&&(g.unregisterClient(f),n.info(`Unregistered previous diagnostic client: ${f}`),f=null);try{u.close()}catch(M){n.error("Error closing previous transport:",M)}}let O=new Wr(T);u=O;let P=null;a.connect(O).then(()=>{n.info("MCP server connected to transport"),P=g.registerClient(M=>{O.send({jsonrpc:"2.0",method:"diagnostics_changed",params:{uris:M}}).catch(H=>{n.error("Error sending diagnostics notification:",H.message)})}),f=P,n.info(`Registered diagnostic client: ${P}`),setTimeout(()=>{let M=Hn();M&&zn(M,O);let H=[...Wn];Wn.length=0;for(let Qe of H)ja(n,Qe,O)},500)}).catch(M=>{console.error("Error connecting transport:",M),u=null;try{T.close()}catch(H){n.error("Error closing WebSocket:",H)}}),T.on("close",()=>{n.info("WS client disconnected"),u===O&&(u=null,P&&f===P&&(g.unregisterClient(P),n.info(`Unregistered diagnostic client: ${P}`),f=null))})});let v=()=>u;Kd(o,a,n,v),Ma(i,a,v);let h=La(a,v),x=null;l.on("listening",()=>{let T=l.address();T&&typeof T!="string"&&(x=T.port)});let w=oe.workspace.onDidChangeWorkspaceFolders(()=>{x!==null&&(_n(x),n.info(`Updated lock file for port ${x} with new workspace folders`))});return{httpServer:l,dispose:async()=>{x!==null&&(So(x),n.info(`Deleted lock file for port ${x}`),await bn(r,"CLAUDE_CODE_SSE_PORT"),await bn(r,"ENABLE_IDE_INTEGRATION")),h.dispose(),w.dispose(),g.dispose(),l.close(),typeof a.close=="function"&&await a.close()}}}async function Za(r,e,t){try{let n=await Eo(),s=null;return new Promise((i,o)=>{r.listen(n,"127.0.0.1",async()=>{t.info(`MCP Server running on port ${n} (localhost only)`),s=_n(n),console.log(`Created lock file at ${s}`);try{await xn(e,"CLAUDE_CODE_SSE_PORT",String(n)),await xn(e,"ENABLE_IDE_INTEGRATION","true"),i(n)}catch(a){console.error("Failed to set environment variables:",a),o(a)}}).on("error",a=>{console.error("Failed to start MCP server:",a),oe.window.showErrorMessage(`Failed to start MCP server: ${a}`),o(a)})})}catch(n){throw console.error("Failed to start MCP server:",n),oe.window.showErrorMessage(`Failed to start MCP server: ${n}`),n}}async function Qd(r){for(let e of oe.window.tabGroups.all)for(let t of e.tabs)if(t.label===r){let n=t.input;if(n instanceof oe.TabInputTextDiff)try{await(await oe.workspace.openTextDocument(n.modified)).save()}catch(s){console.error("Error saving modified file:",s)}return await oe.window.tabGroups.close(t),!0}return!1}function ja(r,e,t){r.info("Logging event:",e.eventName,e.eventData);let n={eventName:e.eventName,eventData:e.eventData??{}};t.send({jsonrpc:"2.0",method:"log_event",params:n}).catch(s=>{r.error("Error sending log event:",s)})}var Wn=[];async function zn(r,e){try{e.send({jsonrpc:"2.0",method:"selection_changed",params:r}).catch(t=>{console.error("Error sending selection change notification:",t)})}catch(t){console.error("Error sending selection change notification:",t)}}async function $a(r,e){try{e.send({jsonrpc:"2.0",method:"at_mentioned",params:r}).catch(t=>{console.error("Error sending at-mentioned notification:",t)})}catch(t){console.error("Error sending at-mentioned notification:",t)}}var V=$(require("vscode")),Gn=class{constructor(e,t){this.uri=e;this.contents=t;this.ctime=this.modifiedTime=Date.now()}type=V.FileType.File;ctime;modifiedTime;get mtime(){return this.modifiedTime}get size(){return this.data.length}get data(){return this.contents}write(e){this.modifiedTime=Date.now(),this.contents=e,this.emitter.fire()}emitter=new V.EventEmitter;onDidSave=this.emitter.event},Wt=class{constructor(e){this.scheme=e}documents=new Map;createFile(e,t){let n=V.Uri.from({scheme:this.scheme,path:e}),s=this.documents.get(n.path);return s?(this.writeFile(n,new TextEncoder().encode(t)),s):(s=new Gn(n,new TextEncoder().encode(t)),this.documents.set(e,s),s)}emitter=new V.EventEmitter;onDidChangeFile=this.emitter.event;watch(e){return new V.Disposable(()=>{})}stat(e){return this.find(e)}find(e){let t=this.documents.get(e.path);if(!t)throw V.FileSystemError.FileNotFound(e);return t}readDirectory(e){throw V.FileSystemError.FileNotFound(e)}readFile(e){let t=this.find(e).data;if(t)return t;throw V.FileSystemError.FileNotFound(e)}writeFile(e,t){this.find(e).write(t),this.emitter.fire([{type:V.FileChangeType.Changed,uri:e}])}createDirectory(e){throw V.FileSystemError.Unavailable()}delete(e){throw V.FileSystemError.Unavailable()}rename(e,t,n){throw V.FileSystemError.Unavailable()}};var qa=$(require("vscode")),Gr=class{logged=new qa.EventEmitter;logEvent(e,t){this.logged.fire({eventName:e,eventData:t})}};var me=$(require("vscode"));function eu(r){let e=me.window.createOutputChannel("Claude Code",{log:!0});r.subscriptions.push(e);let t=new Gr;e.info("Claude code extension is now active!");let n=new Wt("_claude_fs_left");r.subscriptions.push(me.workspace.registerFileSystemProvider(n.scheme,n));let s=new Wt("_claude_fs_right");r.subscriptions.push(me.workspace.registerFileSystemProvider(s.scheme,s)),r.subscriptions.push(ru(s.scheme));let i=ps(r.subscriptions);Qn().then(a=>{me.commands.executeCommand("setContext","claude-code.hasClaudeInPath",a)});let o=ms(r.subscriptions);fs(e,r,t),hs(e,r,t);try{let{httpServer:a,dispose:l}=Ua(r,n,s,e,i,o,t);Za(a,r,e).catch(d=>{e.error("Failed to start MCP server:",d),me.window.showErrorMessage(`Failed to start MCP server: ${d}`)}),r.subscriptions.push({dispose:async()=>{await l()}})}catch(a){e.error("Error during extension activation:",a),me.window.showErrorMessage(`Failed to activate Claude Code extension: ${a}`)}}function tu(){console.log("Claude code extension is now deactivated")}function ru(r){return me.window.onDidChangeVisibleTextEditors(e=>{let t=e.some(n=>n?.document.uri.scheme===r);me.commands.executeCommand("setContext","claude-code.viewingProposedDiff",t)})}0&&(module.exports={activate,deactivate});
