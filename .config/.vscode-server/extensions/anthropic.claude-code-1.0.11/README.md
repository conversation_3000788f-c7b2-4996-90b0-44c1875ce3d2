# Claude Code Extension for VS Code

[<PERSON>](https://docs.anthropic.com/en/docs/agents-and-tools/claude-code) seamlessly integrates with popular Integrated Development Environments (IDEs) to enhance your coding workflow. This integration allows you to leverage <PERSON>’s capabilities directly within your preferred development environment.

## Features

- Auto-installation: When you launch Claude Code from within VSCode’s terminal, it automatically detects and installs the extension
- Selection context: Selected text in the editor is automatically added to <PERSON>’s context
- Diff viewing: Code changes can be displayed directly in VSCode’s diff viewer instead of the terminal
- Keyboard shortcuts: Support for shortcuts like Alt+Cmd+K to push selected code into <PERSON>’s prompt
- Tab awareness: <PERSON> can see which files you have open in the editor
- Configuration: Set diff tool to auto in /config to enable IDE integration features
  ​

## Requirements

- VS Code 1.98.0 or higher

## Known Issues

This is an early release and may contain bugs or incomplete features.
