# 📐 Frontend Development Guidelines

## 🎯 Design System Components Reference

### **UI Components Syntax**

#### **Buttons**
```html
<!-- Basic Button -->
{% ui_button "Text" %}

<!-- With Variants -->
{% ui_button "Save" variant="default" %}
{% ui_button "Cancel" variant="secondary" %}
{% ui_button "Delete" variant="destructive" %}
{% ui_button "Edit" variant="outline" %}
{% ui_button "More" variant="ghost" %}

<!-- With Sizes -->
{% ui_button "Small" size="sm" %}
{% ui_button "Default" size="default" %}
{% ui_button "Large" size="lg" %}

<!-- With Actions -->
{% ui_button "Submit" onclick="submitForm()" %}
{% ui_button "Save" type="submit" class="w-full" %}
```

#### **Cards**
```html
<!-- Simple Card -->
{% ui_card title="Card Title" content="<p>Card content here</p>" %}

<!-- Full Card -->
{% ui_card 
    title="Project Details" 
    description="Manage project information"
    content="<div class='space-y-4'>Content here</div>"
    footer="<div class='flex justify-end space-x-2'>Footer buttons</div>"
%}
```

#### **Alerts**
```html
<!-- Basic Alert -->
{% ui_alert "Success message" variant="success" %}
{% ui_alert "Warning message" variant="warning" %}
{% ui_alert "Error message" variant="destructive" %}

<!-- With Title -->
{% ui_alert "Message" variant="success" title="Success!" %}

<!-- Dismissible -->
{% ui_alert "Message" variant="info" dismissible=True %}
```

#### **Forms**
```html
<!-- Input Field -->
{% ui_input name="title" label="Project Title" placeholder="Enter title" required=True %}

<!-- Textarea -->
{% ui_textarea name="description" label="Description" rows=4 %}

<!-- Select -->
{% ui_select name="status" label="Status" options=status_choices %}

<!-- Checkbox -->
{% ui_checkbox name="active" label="Active Project" %}
```

## 🏗️ Template Patterns

### **Pattern 1: Dashboard**
```html
{% extends 'ui/base_modern.html' %}
{% load ui_components %}

{% block title %}Dashboard{% endblock %}

{% block content %}
<div class="flex-1 space-y-4 p-8 pt-6">
    <!-- Header Section -->
    <div class="flex items-center justify-between">
        <h2 class="text-3xl font-bold tracking-tight">Dashboard</h2>
        <div class="flex items-center space-x-2">
            {% ui_button "New Project" variant="default" onclick="location.href='{% url 'projects:create' %}'" %}
        </div>
    </div>
    
    <!-- Stats Grid -->
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {% ui_card 
            title="Total Projects" 
            content="<div class='text-2xl font-bold'>{{ stats.total_projects }}</div>"
        %}
        {% ui_card 
            title="Active Projects" 
            content="<div class='text-2xl font-bold text-green-600'>{{ stats.active_projects }}</div>"
        %}
        {% ui_card 
            title="Pending Tasks" 
            content="<div class='text-2xl font-bold text-yellow-600'>{{ stats.pending_tasks }}</div>"
        %}
        {% ui_card 
            title="Completed" 
            content="<div class='text-2xl font-bold text-blue-600'>{{ stats.completed }}</div>"
        %}
    </div>
    
    <!-- Content Sections -->
    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <!-- Main Content -->
        <div class="col-span-4">
            {% ui_card 
                title="Recent Projects"
                content="<div class='space-y-4'>Recent projects list here</div>"
            %}
        </div>
        
        <!-- Sidebar -->
        <div class="col-span-3">
            {% ui_card 
                title="Quick Actions"
                content="<div class='space-y-2'>Quick action buttons here</div>"
            %}
        </div>
    </div>
</div>
{% endblock %}
```

### **Pattern 2: List View**
```html
{% extends 'ui/base_modern.html' %}
{% load ui_components %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="flex-1 space-y-4 p-8 pt-6">
    <!-- Header with Actions -->
    <div class="flex items-center justify-between">
        <h2 class="text-3xl font-bold tracking-tight">{{ page_title }}</h2>
        <div class="flex items-center space-x-2">
            {% ui_button "Add New" variant="default" onclick="location.href='{{ create_url }}'" %}
            {% ui_button "Export" variant="outline" %}
        </div>
    </div>
    
    <!-- Filters -->
    <div class="flex items-center space-x-4">
        <div class="flex-1">
            {% ui_input name="search" placeholder="Search..." %}
        </div>
        <div>
            {% ui_select name="status" options=status_options %}
        </div>
        {% ui_button "Filter" variant="secondary" %}
    </div>
    
    <!-- Data Table -->
    {% ui_card 
        content="<div class='overflow-x-auto'>Table content here</div>"
    %}
    
    <!-- Pagination -->
    <div class="flex items-center justify-between">
        <p class="text-sm text-muted-foreground">
            Showing {{ start_index }} to {{ end_index }} of {{ total_count }} results
        </p>
        <div class="flex items-center space-x-2">
            <!-- Pagination buttons -->
        </div>
    </div>
</div>
{% endblock %}
```

### **Pattern 3: Form View**
```html
{% extends 'ui/base_modern.html' %}
{% load ui_components %}

{% block title %}{{ form_title }}{% endblock %}

{% block content %}
<div class="flex-1 space-y-4 p-8 pt-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
        <h2 class="text-3xl font-bold tracking-tight">{{ form_title }}</h2>
        <div class="flex items-center space-x-2">
            {% ui_button "Cancel" variant="outline" onclick="history.back()" %}
        </div>
    </div>
    
    <!-- Form Card -->
    <div class="max-w-2xl">
        {% ui_card 
            title="Form Details"
            content="
            <form method='post' class='space-y-6'>
                {% csrf_token %}
                <!-- Form fields here -->
                <div class='flex justify-end space-x-2'>
                    {% ui_button 'Cancel' variant='outline' onclick='history.back()' %}
                    {% ui_button 'Save' type='submit' variant='default' %}
                </div>
            </form>
            "
        %}
    </div>
</div>
{% endblock %}
```

## ✅ Development Checklist

### **Before Writing Template:**
- [ ] Identify pattern type (Dashboard/List/Form/Detail)
- [ ] Check required context variables from view
- [ ] Plan component usage
- [ ] Consider responsive design
- [ ] Plan theme compatibility

### **Template Structure:**
- [ ] Extends `ui/base_modern.html`
- [ ] Loads `ui_components`
- [ ] Sets proper `{% block title %}`
- [ ] Uses consistent spacing classes
- [ ] Follows mobile-first approach

### **Component Usage:**
- [ ] Correct syntax (positional args first)
- [ ] Proper variant selection
- [ ] Consistent sizing
- [ ] Accessible labels
- [ ] Theme-compatible colors

### **Testing:**
- [ ] Test with different screen sizes
- [ ] Test with light/dark themes
- [ ] Test with empty data
- [ ] Test with long content
- [ ] Test form validation

## 🎨 Theme Configuration

### **Color Classes (Theme-aware):**
```html
<!-- Text Colors -->
text-foreground          <!-- Primary text -->
text-muted-foreground    <!-- Secondary text -->
text-destructive         <!-- Error text -->
text-success            <!-- Success text -->

<!-- Background Colors -->
bg-background           <!-- Main background -->
bg-card                <!-- Card background -->
bg-muted               <!-- Muted background -->

<!-- Border Colors -->
border-border          <!-- Default border -->
border-input          <!-- Input border -->
```

### **Spacing System:**
```html
<!-- Consistent spacing -->
space-y-4             <!-- Vertical spacing -->
space-x-2             <!-- Horizontal spacing -->
p-8 pt-6             <!-- Padding -->
gap-4                <!-- Grid gap -->
```

## 🚀 Quick Start Template

```html
{% extends 'ui/base_modern.html' %}
{% load ui_components %}

{% block title %}Page Title{% endblock %}

{% block content %}
<div class="flex-1 space-y-4 p-8 pt-6">
    <!-- Your content here -->
</div>
{% endblock %}
```
