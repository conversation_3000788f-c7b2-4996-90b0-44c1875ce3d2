#!/usr/bin/env python
"""
Script per creare dati demo per ExProject API
"""
import os
import sys
import django
from decimal import Decimal
from datetime import date, datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'exproject.settings.base')
django.setup()

from django.contrib.auth.models import User
from apps.core.models import (
    Entity, Project, Parcel, Owner, ParcelOwnership, 
    Template, Document, Signature
)

def create_demo_data():
    print("🚀 Creazione dati demo per ExProject...")
    
    # Crea un utente demo se non esiste
    user, created = User.objects.get_or_create(
        username='demo',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Demo',
            'last_name': 'User',
            'is_staff': True,
            'is_superuser': False
        }
    )
    if created:
        user.set_password('demo123')
        user.save()
        print("✅ Utente demo creato")
    
    # Crea entità demo
    entity, created = Entity.objects.get_or_create(
        code='COM_TORINO',
        defaults={
            'name': 'Comune di Torino',
            'entity_type': 'municipality',
            'fiscal_code': '80087670016',
            'legal_address': 'Piazza Palazzo di Città 1, 10122 Torino TO',
            'administrative_address': 'Piazza Palazzo di Città 1, 10122 Torino TO',
            'pec_email': '<EMAIL>',
            'phone': '+39 ************',
            'website': 'https://www.comune.torino.it',
            'legal_representative': 'Stefano Lo Russo',
            'created_by': user
        }
    )
    if created:
        print("✅ Entità Comune di Torino creata")
    
    # Crea progetti demo
    projects_data = [
        {
            'code': 'ESP-2025-TOR-001',
            'name': 'Ampliamento Metropolitana Linea 2',
            'description': 'Progetto di ampliamento della linea metropolitana 2 con espropri per stazioni e binari',
            'project_type': 'railway',
            'priority': 'high',
            'budget_total': Decimal('50000000.00'),
            'budget_expropriation': Decimal('15000000.00'),
            'budget_spent': Decimal('2500000.00'),
            'budget_reserved': Decimal('8000000.00'),
        },
        {
            'code': 'ESP-2025-TOR-002', 
            'name': 'Nuova Tangenziale Sud',
            'description': 'Costruzione di una nuova tangenziale per alleggerire il traffico urbano',
            'project_type': 'road',
            'priority': 'medium',
            'budget_total': Decimal('25000000.00'),
            'budget_expropriation': Decimal('8000000.00'),
            'budget_spent': Decimal('1200000.00'),
            'budget_reserved': Decimal('3500000.00'),
        }
    ]
    
    projects = []
    for project_data in projects_data:
        project, created = Project.objects.get_or_create(
            code=project_data['code'],
            defaults={
                **project_data,
                'primary_entity': entity,
                'responsible_person': user,
                'procedure_responsible': user,
                'created_by': user,
                'start_date': date.today() - timedelta(days=90),
                'expected_end_date': date.today() + timedelta(days=730),
                'public_utility_declaration_date': date.today() - timedelta(days=30),
            }
        )
        projects.append(project)
        if created:
            print(f"✅ Progetto {project.name} creato")
    
    # Crea proprietari demo
    owners_data = [
        {
            'owner_type': 'individual',
            'fiscal_code': '****************',
            'first_name': 'Mario',
            'last_name': 'Rossi',
            'birth_date': date(1980, 1, 1),
            'birth_place': 'Torino',
            'residence_address': 'Via Roma 123, 10121 Torino',
            'phone': '+39 338 1234567',
            'email': '<EMAIL>',
        },
        {
            'owner_type': 'individual',
            'fiscal_code': '****************',
            'first_name': 'Giovanna',
            'last_name': 'Bianchi',
            'birth_date': date(1975, 7, 1),
            'birth_place': 'Torino',
            'residence_address': 'Corso Francia 456, 10138 Torino',
            'mobile': '+39 347 7654321',
            'email': '<EMAIL>',
        },
        {
            'owner_type': 'company',
            'vat_number': '12345678901',
            'company_name': 'Immobiliare Piemonte S.r.l.',
            'legal_form': 'Società a Responsabilità Limitata',
            'legal_representative': 'Giuseppe Verdi',
            'legal_address': 'Via Garibaldi 789, 10122 Torino',
            'pec_email': '<EMAIL>',
        }
    ]
    
    owners = []
    for owner_data in owners_data:
        fiscal_code = owner_data.get('fiscal_code')
        vat_number = owner_data.get('vat_number')
        
        if fiscal_code:
            owner, created = Owner.objects.get_or_create(
                fiscal_code=fiscal_code,
                defaults={**owner_data, 'created_by': user, 'is_verified': True}
            )
        else:
            owner, created = Owner.objects.get_or_create(
                vat_number=vat_number,
                defaults={**owner_data, 'created_by': user, 'is_verified': True}
            )
        
        owners.append(owner)
        if created:
            display_name = owner.get_display_name()
            print(f"✅ Proprietario {display_name} creato")
    
    # Crea particelle demo
    parcels_data = [
        {
            'project': projects[0],  # Metropolitana
            'cadastral_municipality': 'Torino',
            'administrative_municipality': 'Torino',
            'sheet': '150',
            'parcel_number': '1024',
            'total_area': Decimal('2500.00'),
            'expropriated_area': Decimal('1800.00'),
            'cadastral_category': 'A/3',
            'cadastral_class': '4',
            'cadastral_income': Decimal('1250.00'),
            'market_value_per_sqm': Decimal('850.00'),
            'status': 'surveyed',
            'has_buildings': True,
            'building_description': 'Capannone industriale di 800 mq',
        },
        {
            'project': projects[0],  # Metropolitana
            'cadastral_municipality': 'Torino', 
            'administrative_municipality': 'Torino',
            'sheet': '150',
            'parcel_number': '1025',
            'total_area': Decimal('1200.00'),
            'expropriated_area': Decimal('950.00'),
            'temporary_occupation_area': Decimal('200.00'),
            'cadastral_category': 'C/2',
            'market_value_per_sqm': Decimal('650.00'),
            'status': 'valued',
        },
        {
            'project': projects[1],  # Tangenziale
            'cadastral_municipality': 'Torino',
            'administrative_municipality': 'Torino',
            'sheet': '85',
            'parcel_number': '567',
            'total_area': Decimal('5500.00'),
            'expropriated_area': Decimal('3200.00'),
            'easement_area': Decimal('800.00'),
            'cadastral_category': 'A/2',
            'agricultural_value_per_sqm': Decimal('25.00'),
            'status': 'notification_sent',
            'cultivation_type': 'Seminativo',
            'landscape_constraints': True,
        }
    ]
    
    parcels = []
    for parcel_data in parcels_data:
        cadastral_id = f"{parcel_data['sheet']}-{parcel_data['parcel_number']}"
        parcel, created = Parcel.objects.get_or_create(
            project=parcel_data['project'],
            cadastral_id=cadastral_id,
            defaults={**parcel_data, 'created_by': user, 'survey_completed': True, 'survey_date': date.today() - timedelta(days=15)}
        )
        parcels.append(parcel)
        if created:
            print(f"✅ Particella {parcel.cadastral_id} creata")
    
    # Crea proprietà (ParcelOwnership)
    ownerships_data = [
        # Particella 150-1024 (Metropolitana) - proprietà condivisa
        {'parcel': parcels[0], 'owner': owners[0], 'percentage': Decimal('60.00'), 'type': 'full_ownership'},
        {'parcel': parcels[0], 'owner': owners[1], 'percentage': Decimal('40.00'), 'type': 'full_ownership'},
        
        # Particella 150-1025 (Metropolitana) - proprietà singola
        {'parcel': parcels[1], 'owner': owners[1], 'percentage': Decimal('100.00'), 'type': 'full_ownership'},
        
        # Particella 85-567 (Tangenziale) - proprietà società
        {'parcel': parcels[2], 'owner': owners[2], 'percentage': Decimal('100.00'), 'type': 'full_ownership'},
    ]
    
    for ownership_data in ownerships_data:
        ownership, created = ParcelOwnership.objects.get_or_create(
            parcel=ownership_data['parcel'],
            owner=ownership_data['owner'],
            ownership_type=ownership_data['type'],
            defaults={
                'ownership_percentage': ownership_data['percentage'],
                'start_date': date.today() - timedelta(days=365),
                'is_current': True,
                'verified': True,
                'verification_date': datetime.now(),
                'created_by': user
            }
        )
        if created:
            print(f"✅ Proprietà {ownership.parcel.cadastral_id} - {ownership.owner.get_display_name()} creata")
    
    # Crea templates demo
    templates_data = [
        {
            'name': 'Avviso di Esproprio',
            'category': 'notifiche',
            'content': '''
COMUNE DI TORINO
AVVISO DI ESPROPRIO

Particella: {{cadastral_id}}
Proprietario: {{owner_name}}
Progetto: {{project_name}}

Si comunica che la particella catastale {{cadastral_id}} è soggetta ad esproprio 
per pubblica utilità nell'ambito del progetto "{{project_name}}".

Area interessata: {{expropriated_area}} mq
Indennità proposta: €{{compensation_amount}}

Data: {{notification_date}}
Il Responsabile del Procedimento
{{responsible_person}}
            ''',
            'variables': ['cadastral_id', 'owner_name', 'project_name', 'expropriated_area', 'compensation_amount', 'notification_date', 'responsible_person']
        },
        {
            'name': 'Decreto di Esproprio',
            'category': 'decreti',
            'content': '''
DECRETO DI ESPROPRIO N. {{decree_number}}

IL SINDACO

VISTO il progetto "{{project_name}}"
VISTA la dichiarazione di pubblica utilità del {{public_utility_date}}
VISTO l'art. 23 del DPR 327/2001

DECRETA

l'espropriazione della particella {{cadastral_id}} di proprietà di {{owner_name}}
per una superficie di {{expropriated_area}} mq.

Indennità di espropriazione: €{{final_compensation}}

Torino, {{decree_date}}
Il Sindaco
            ''',
            'variables': ['decree_number', 'project_name', 'public_utility_date', 'cadastral_id', 'owner_name', 'expropriated_area', 'final_compensation', 'decree_date']
        }
    ]
    
    templates = []
    for template_data in templates_data:
        template, created = Template.objects.get_or_create(
            name=template_data['name'],
            category=template_data['category'],
            defaults=template_data
        )
        templates.append(template)
        if created:
            print(f"✅ Template {template.name} creato")
    
    # Crea documenti demo
    document, created = Document.objects.get_or_create(
        parcel=parcels[0],
        template=templates[0],
        defaults={
            'content': templates[0].content.replace('{{cadastral_id}}', parcels[0].cadastral_id)
                                          .replace('{{project_name}}', parcels[0].project.name)
                                          .replace('{{owner_name}}', owners[0].get_display_name()),
            'status': 'approved',
            'version': 1
        }
    )
    if created:
        print(f"✅ Documento demo creato per particella {parcels[0].cadastral_id}")
    
    print(f"\n🎉 Dati demo creati con successo!")
    print(f"📊 Riepilogo:")
    print(f"   • Entità: {Entity.objects.count()}")
    print(f"   • Progetti: {Project.objects.count()}")
    print(f"   • Particelle: {Parcel.objects.count()}")
    print(f"   • Proprietari: {Owner.objects.count()}")
    print(f"   • Proprietà: {ParcelOwnership.objects.count()}")
    print(f"   • Templates: {Template.objects.count()}")
    print(f"   • Documenti: {Document.objects.count()}")
    print(f"\n🌐 Accedi alla documentazione API su:")
    print(f"   • Swagger UI: http://localhost:8000/api/docs/")
    print(f"   • ReDoc: http://localhost:8000/api/schema/redoc/")

if __name__ == '__main__':
    create_demo_data()