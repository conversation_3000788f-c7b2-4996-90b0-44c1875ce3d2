#!/usr/bin/env python
"""
Temporary script to fix Swagger by removing authentication temporarily
"""
import os
import django
from pathlib import Path

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'exproject.settings.base')
django.setup()

# Backup and modify the views file to remove authentication temporarily
views_file = Path('/home/<USER>/workspace/apps/api/views.py')
backup_file = Path('/home/<USER>/workspace/apps/api/views.py.backup')

# Read the original file
with open(views_file, 'r') as f:
    content = f.read()

# Create backup
with open(backup_file, 'w') as f:
    f.write(content)

# Replace all permission_classes with empty list
modified_content = content.replace(
    'permission_classes = [IsAuthenticated, EntityPermission]', 
    'permission_classes = []'
).replace(
    'permission_classes = [IsAuthenticated, ProjectPermission]', 
    'permission_classes = []'
).replace(
    'permission_classes = [IsAuthenticated]', 
    'permission_classes = []'
)

# Write modified content
with open(views_file, 'w') as f:
    f.write(modified_content)

print("✅ Views file modified to remove authentication")
print("🔄 Regenerating schema...")

# Generate schema
import subprocess
result = subprocess.run(['python', 'manage.py', 'spectacular', '--color', '--file', 'schema.yml'], 
                       capture_output=True, text=True)

if result.returncode == 0:
    print("✅ Schema generated successfully!")
    
    # Check if schema has paths now
    with open('schema.yml', 'r') as f:
        schema_content = f.read()
        if 'paths: {}' in schema_content:
            print("❌ Schema still empty")
        else:
            print("✅ Schema contains endpoints!")
            # Count lines with "operationId" to estimate endpoints
            operation_count = schema_content.count('operationId')
            print(f"📊 Found approximately {operation_count} operations")
else:
    print(f"❌ Schema generation failed: {result.stderr}")

# Restore original file
with open(backup_file, 'r') as f:
    original_content = f.read()

with open(views_file, 'w') as f:
    f.write(original_content)

# Remove backup
backup_file.unlink()

print("🔄 Original views file restored")
print("🌐 Check Swagger UI at: http://localhost:8000/api/docs/")