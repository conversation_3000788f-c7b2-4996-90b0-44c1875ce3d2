#!/usr/bin/env python
"""
Create a working OpenAPI schema manually to demonstrate the API
"""
import yaml

# Create a complete OpenAPI schema with the ExProject endpoints
schema = {
    'openapi': '3.0.3',
    'info': {
        'title': 'ExProject API',
        'version': '1.0.0',
        'description': '''
API REST per ExProject - Sistema di gestione procedure espropriative secondo DPR 327/2001.

## Autenticazione

L'API utilizza Token Authentication. Includi l'header:
```
Authorization: Token your_token_here
```

## Token Demo
```
Token 96aa0e34b83140cee09eb521df33d4caccf9cfbe
```
        ''',
        'contact': {
            'name': 'ExProject Team',
            'email': '<EMAIL>'
        }
    },
    'servers': [
        {
            'url': 'http://localhost:8000/api/v1',
            'description': 'Development server'
        }
    ],
    'security': [
        {'tokenAuth': []}
    ],
    'components': {
        'securitySchemes': {
            'tokenAuth': {
                'type': 'apiKey',
                'in': 'header',
                'name': 'Authorization',
                'description': 'Token-based authentication. Format: "Token your_token_here"'
            }
        },
        'schemas': {
            'Entity': {
                'type': 'object',
                'properties': {
                    'id': {'type': 'string', 'format': 'uuid'},
                    'code': {'type': 'string', 'example': 'COM_TORINO'},
                    'name': {'type': 'string', 'example': 'Comune di Torino'},
                    'entity_type': {'type': 'string', 'enum': ['municipality', 'province', 'region', 'state_agency', 'private_company']},
                    'fiscal_code': {'type': 'string', 'example': '80087670016'},
                    'legal_address': {'type': 'string'},
                    'pec_email': {'type': 'string', 'format': 'email'},
                    'phone': {'type': 'string'},
                    'is_active': {'type': 'boolean'}
                }
            },
            'Project': {
                'type': 'object',
                'properties': {
                    'id': {'type': 'string', 'format': 'uuid'},
                    'code': {'type': 'string', 'example': 'ESP-2025-TOR-001'},
                    'name': {'type': 'string', 'example': 'Ampliamento Metropolitana Linea 2'},
                    'description': {'type': 'string'},
                    'project_type': {'type': 'string', 'enum': ['infrastructure', 'road', 'railway', 'utility', 'urban']},
                    'priority': {'type': 'string', 'enum': ['low', 'medium', 'high', 'critical']},
                    'status': {'type': 'string', 'enum': ['planning', 'approved', 'active', 'suspended', 'completed', 'archived']},
                    'budget_total': {'type': 'number', 'format': 'decimal'},
                    'budget_spent': {'type': 'number', 'format': 'decimal'},
                    'start_date': {'type': 'string', 'format': 'date'}
                }
            },
            'Parcel': {
                'type': 'object',
                'properties': {
                    'id': {'type': 'string', 'format': 'uuid'},
                    'cadastral_id': {'type': 'string', 'example': '150-1024'},
                    'cadastral_municipality': {'type': 'string', 'example': 'Torino'},
                    'sheet': {'type': 'string', 'example': '150'},
                    'parcel_number': {'type': 'string', 'example': '1024'},
                    'total_area': {'type': 'number', 'format': 'decimal', 'example': 2500.00},
                    'expropriated_area': {'type': 'number', 'format': 'decimal', 'example': 1800.00},
                    'status': {'type': 'string', 'enum': ['identified', 'surveyed', 'valued', 'notification_sent', 'completed']},
                    'market_value_per_sqm': {'type': 'number', 'format': 'decimal'},
                    'has_buildings': {'type': 'boolean'}
                }
            }
        }
    },
    'paths': {
        '/entities/': {
            'get': {
                'tags': ['Entities'],
                'summary': 'Lista enti',
                'description': 'Ritorna la lista di tutti gli enti pubblici e privati',
                'security': [{'tokenAuth': []}],
                'parameters': [
                    {
                        'name': 'entity_type',
                        'in': 'query',
                        'schema': {'type': 'string'},
                        'description': 'Filtra per tipo ente'
                    },
                    {
                        'name': 'search',
                        'in': 'query', 
                        'schema': {'type': 'string'},
                        'description': 'Cerca per nome, codice o indirizzo'
                    }
                ],
                'responses': {
                    '200': {
                        'description': 'Lista enti',
                        'content': {
                            'application/json': {
                                'schema': {
                                    'type': 'array',
                                    'items': {'$ref': '#/components/schemas/Entity'}
                                }
                            }
                        }
                    }
                }
            },
            'post': {
                'tags': ['Entities'],
                'summary': 'Crea nuovo ente',
                'security': [{'tokenAuth': []}],
                'requestBody': {
                    'content': {
                        'application/json': {
                            'schema': {'$ref': '#/components/schemas/Entity'}
                        }
                    }
                },
                'responses': {
                    '201': {
                        'description': 'Ente creato',
                        'content': {
                            'application/json': {
                                'schema': {'$ref': '#/components/schemas/Entity'}
                            }
                        }
                    }
                }
            }
        },
        '/projects/': {
            'get': {
                'tags': ['Projects'],
                'summary': 'Lista progetti',
                'description': 'Ritorna la lista di tutti i progetti di esproprio',
                'security': [{'tokenAuth': []}],
                'parameters': [
                    {
                        'name': 'status',
                        'in': 'query',
                        'schema': {'type': 'string'},
                        'description': 'Filtra per stato progetto'
                    },
                    {
                        'name': 'priority',
                        'in': 'query',
                        'schema': {'type': 'string'},
                        'description': 'Filtra per priorità'
                    }
                ],
                'responses': {
                    '200': {
                        'description': 'Lista progetti',
                        'content': {
                            'application/json': {
                                'schema': {
                                    'type': 'array',
                                    'items': {'$ref': '#/components/schemas/Project'}
                                }
                            }
                        }
                    }
                }
            },
            'post': {
                'tags': ['Projects'],
                'summary': 'Crea nuovo progetto',
                'security': [{'tokenAuth': []}],
                'requestBody': {
                    'content': {
                        'application/json': {
                            'schema': {'$ref': '#/components/schemas/Project'}
                        }
                    }
                },
                'responses': {
                    '201': {
                        'description': 'Progetto creato',
                        'content': {
                            'application/json': {
                                'schema': {'$ref': '#/components/schemas/Project'}
                            }
                        }
                    }
                }
            }
        },
        '/parcels/': {
            'get': {
                'tags': ['Parcels'],
                'summary': 'Lista particelle',
                'description': 'Ritorna la lista di tutte le particelle catastali',
                'security': [{'tokenAuth': []}],
                'parameters': [
                    {
                        'name': 'status',
                        'in': 'query',
                        'schema': {'type': 'string'},
                        'description': 'Filtra per stato particella'
                    },
                    {
                        'name': 'has_buildings',
                        'in': 'query',
                        'schema': {'type': 'boolean'},
                        'description': 'Filtra per presenza fabbricati'
                    }
                ],
                'responses': {
                    '200': {
                        'description': 'Lista particelle',
                        'content': {
                            'application/json': {
                                'schema': {
                                    'type': 'array',
                                    'items': {'$ref': '#/components/schemas/Parcel'}
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    'tags': [
        {'name': 'Entities', 'description': 'Gestione enti pubblici e privati'},
        {'name': 'Projects', 'description': 'Gestione progetti di espropriazione'}, 
        {'name': 'Parcels', 'description': 'Gestione particelle catastali'}
    ]
}

# Write the schema to file
with open('/home/<USER>/workspace/working_schema.yml', 'w') as f:
    yaml.dump(schema, f, default_flow_style=False, allow_unicode=True)

print("✅ Working schema created at: working_schema.yml")
print("🔧 To use this schema temporarily:")
print("   1. Copy working_schema.yml to schema.yml")
print("   2. Refresh Swagger UI at http://localhost:8000/api/docs/")
print("   3. You'll see the Authorize button with token: 96aa0e34b83140cee09eb521df33d4caccf9cfbe")